-- =====================================================
-- 返利系统重构 - MySQL数据库设计
-- 基于SAP HANA原表结构，设计对应的MySQL表结构
-- =====================================================

-- 1. 返利合同主表 (对应SAP ZRETA001)
CREATE TABLE rebate_contract (
    contract_id VARCHAR(20) PRIMARY KEY COMMENT '合同编号',
    contract_type VARCHAR(4) NOT NULL COMMENT '合同类型',
    company_code VARCHAR(4) NOT NULL COMMENT '公司代码',
    contract_desc VARCHAR(100) NOT NULL COMMENT '合同描述',
    related_contract_id VARCHAR(35) COMMENT '关联合同号',
    partner_type VARCHAR(1) NOT NULL COMMENT '伙伴类型(S=供应商,M=经销商)',
    partner_code VARCHAR(20) NOT NULL COMMENT '伙伴代码',
    purchase_group VARCHAR(3) COMMENT '采购组',
    contract_year VARCHAR(4) NOT NULL COMMENT '签署年度',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    org_template_id VARCHAR(10) NOT NULL COMMENT '组织模板',
    settlement_cycle VARCHAR(10) COMMENT '结算周期',
    payment_party VARCHAR(20) COMMENT '支付方',
    payment_cycle VARCHAR(10) COMMENT '付款周期',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_info VARCHAR(100) COMMENT '联系方式',
    status VARCHAR(2) DEFAULT '01' COMMENT '状态(01=草稿,02=待审批,03=已审批,04=已拒绝)',
    approval_status VARCHAR(2) DEFAULT '01' COMMENT '审批状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_date DATE NOT NULL COMMENT '创建日期',
    created_time TIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_date DATE COMMENT '更新日期',
    updated_time TIME COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态(01=待同步,02=已同步,03=同步失败)',
    sync_time TIMESTAMP COMMENT '同步时间',
    
    INDEX idx_contract_type (contract_type),
    INDEX idx_company_code (company_code),
    INDEX idx_partner (partner_type, partner_code),
    INDEX idx_date_range (start_date, end_date),
    INDEX idx_status (status, approval_status),
    INDEX idx_sync (sync_status, sync_time),
    INDEX idx_created (created_by, created_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利合同主表';

-- 2. 返利条款主表 (对应SAP ZRETA002)
CREATE TABLE rebate_clause (
    clause_id VARCHAR(20) PRIMARY KEY COMMENT '条款编号',
    contract_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    item_id VARCHAR(6) NOT NULL COMMENT '行项目号',
    rebate_type VARCHAR(4) NOT NULL COMMENT '返利类型',
    clause_type VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    clause_desc VARCHAR(100) NOT NULL COMMENT '条款描述',
    task_product_group_id VARCHAR(20) COMMENT '任务商品组编号',
    rebate_product_group_id VARCHAR(20) COMMENT '返利商品组编号',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE COMMENT '结束日期',
    org_template_id VARCHAR(10) NOT NULL COMMENT '组织模板',
    agreement_status VARCHAR(1) DEFAULT 'I' COMMENT '协议状态(I=初始,P=审批中,A=已审批,D=已作废)',
    calculation_rule_id VARCHAR(10) COMMENT '计算规则ID',
    calculation_base VARCHAR(10) COMMENT '核算基准',
    rebate_form VARCHAR(10) COMMENT '返利形式',
    quantitative_dimension VARCHAR(10) COMMENT '量化维度',
    rebate_rule VARCHAR(10) COMMENT '返利规则',
    ladder_type VARCHAR(10) COMMENT '阶梯类型',
    price_dimension VARCHAR(10) COMMENT '返利价格维度',
    calculation_method VARCHAR(10) COMMENT '计算方法',
    external_supplier VARCHAR(20) COMMENT '外部供货商',
    external_payer VARCHAR(20) COMMENT '外部支付方',
    approval_level VARCHAR(2) COMMENT '审批级别',
    approver VARCHAR(20) COMMENT '审批人',
    approval_date DATE COMMENT '审批日期',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_date DATE NOT NULL COMMENT '创建日期',
    created_time TIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_date DATE COMMENT '更新日期',
    updated_time TIME COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    
    FOREIGN KEY (contract_id) REFERENCES rebate_contract(contract_id) ON DELETE CASCADE,
    INDEX idx_contract (contract_id),
    INDEX idx_rebate_type (rebate_type),
    INDEX idx_status (agreement_status, approval_level),
    INDEX idx_date_range (start_date, end_date),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利条款主表';

-- 3. 返利协议主表 (对应SAP ZRET0006)
CREATE TABLE rebate_agreement (
    agreement_id VARCHAR(20) PRIMARY KEY COMMENT '协议编号',
    clause_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    company_code VARCHAR(4) NOT NULL COMMENT '公司代码',
    organization_level VARCHAR(1) NOT NULL COMMENT '组织级别',
    agreement_subject VARCHAR(10) NOT NULL COMMENT '协议主体',
    payer VARCHAR(20) NOT NULL COMMENT '付款方',
    payer_level VARCHAR(1) NOT NULL COMMENT '付款方级别',
    exchange_method VARCHAR(10) NOT NULL COMMENT '兑换方式',
    exclusive_flag VARCHAR(1) DEFAULT 'N' COMMENT '专属标识',
    payment_type VARCHAR(10) COMMENT '付款类型',
    agreement_amount DECIMAL(15,2) COMMENT '协议金额',
    agreement_type VARCHAR(1) DEFAULT 'F' COMMENT '协议类型(F=固定,D=动态)',
    rebate_collector VARCHAR(4) COMMENT '返利收取方',
    status VARCHAR(1) DEFAULT 'I' COMMENT '协议状态',
    approval_level VARCHAR(2) COMMENT '审批级别',
    approver VARCHAR(20) COMMENT '审批人',
    approval_date DATE COMMENT '审批日期',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_date DATE NOT NULL COMMENT '创建日期',
    created_time TIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_date DATE COMMENT '更新日期',
    updated_time TIME COMMENT '更新时间',
    version INT DEFAULT 1 COMMENT '版本号',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    
    FOREIGN KEY (clause_id) REFERENCES rebate_clause(clause_id) ON DELETE CASCADE,
    INDEX idx_clause (clause_id),
    INDEX idx_company (company_code),
    INDEX idx_payer (payer, payer_level),
    INDEX idx_status (status, approval_level),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利协议主表';

-- 4. 商品组主表 (对应SAP ZRET0009)
CREATE TABLE product_group (
    group_id VARCHAR(20) PRIMARY KEY COMMENT '商品组编号',
    contract_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    group_desc VARCHAR(100) NOT NULL COMMENT '商品组描述',
    partner_code VARCHAR(20) NOT NULL COMMENT '伙伴代码',
    usage_type VARCHAR(1) COMMENT '用途类型',
    group_type VARCHAR(1) COMMENT '商品组类型',
    status VARCHAR(1) DEFAULT 'A' COMMENT '状态',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_date DATE NOT NULL COMMENT '创建日期',
    created_time TIME NOT NULL COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_date DATE COMMENT '更新日期',
    updated_time TIME COMMENT '更新时间',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    
    FOREIGN KEY (contract_id) REFERENCES rebate_contract(contract_id) ON DELETE CASCADE,
    INDEX idx_contract (contract_id),
    INDEX idx_partner (partner_code),
    INDEX idx_status (status),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品组主表';

-- 5. 商品组明细表 (对应SAP ZRET0020)
CREATE TABLE product_group_item (
    group_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    item_id VARCHAR(6) NOT NULL COMMENT '行项目号',
    material_code VARCHAR(18) NOT NULL COMMENT '物料编码',
    price_multiplier DECIMAL(5,3) DEFAULT 1 COMMENT '价格倍数',
    quantity_multiplier DECIMAL(5,3) DEFAULT 1 COMMENT '数量倍数',
    unit VARCHAR(3) COMMENT '单位',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_date DATE NOT NULL COMMENT '创建日期',
    created_time TIME NOT NULL COMMENT '创建时间',
    sync_status VARCHAR(2) DEFAULT '01' COMMENT '同步状态',
    sync_time TIMESTAMP COMMENT '同步时间',
    
    PRIMARY KEY (group_id, item_id),
    FOREIGN KEY (group_id) REFERENCES product_group(group_id) ON DELETE CASCADE,
    INDEX idx_material (material_code),
    INDEX idx_sync (sync_status, sync_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品组明细表';

-- 6. 数据同步日志表
CREATE TABLE data_sync_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    table_name VARCHAR(50) NOT NULL COMMENT '表名',
    operation_type VARCHAR(10) NOT NULL COMMENT '操作类型(INSERT/UPDATE/DELETE)',
    record_key VARCHAR(100) NOT NULL COMMENT '记录主键',
    source_data JSON COMMENT '源数据',
    target_data JSON COMMENT '目标数据',
    sync_status VARCHAR(2) NOT NULL COMMENT '同步状态(01=成功,02=失败,03=重试中)',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    processed_time TIMESTAMP COMMENT '处理时间',
    
    INDEX idx_table_operation (table_name, operation_type),
    INDEX idx_status (sync_status),
    INDEX idx_created_time (created_time),
    INDEX idx_record_key (record_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据同步日志表';

-- 7. 系统配置表
CREATE TABLE system_config (
    config_key VARCHAR(50) PRIMARY KEY COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_desc VARCHAR(200) COMMENT '配置描述',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型',
    is_encrypted VARCHAR(1) DEFAULT 'N' COMMENT '是否加密',
    created_by VARCHAR(20) NOT NULL COMMENT '创建人',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_by VARCHAR(20) COMMENT '更新人',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_type (config_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';



Feature-iscm-djwang-MIDDLECATEGORY250901