import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg"></div>
            <h1 className="text-4xl font-bold text-white">返利系统架构图集</h1>
            <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
          </div>
          <p className="text-xl text-slate-300 max-w-3xl mx-auto">
            从现状分析到重构规划，全面展示返利系统的架构设计与技术演进路径
          </p>
        </div>

        {/* Architecture Diagrams Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Existing Diagrams */}
          <Link href="/diagram1" className="group block p-6 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利二级产品功能框架</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
            </div>
            <p className="text-blue-100 text-sm leading-relaxed">
              SAP系统中的返利功能模块详细架构，展示返利维护、返利功能、底层逻辑三大核心模块的完整功能体系。
            </p>
            <div className="mt-4 flex items-center text-blue-200 text-sm">
              <span className="mr-2">🏗️</span>
              <span>功能架构 • 模块设计 • SAP系统</span>
            </div>
          </Link>

          <Link href="/diagram2" className="group block p-6 bg-gradient-to-br from-green-600 to-emerald-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利数据流程</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <p className="text-green-100 text-sm leading-relaxed">
              BDP、SAP、其他系统间的数据流转过程，详细展示数据采集、处理、计算、分摊的完整数据链路。
            </p>
            <div className="mt-4 flex items-center text-green-200 text-sm">
              <span className="mr-2">🔄</span>
              <span>数据流程 • 系统集成 • ETL处理</span>
            </div>
          </Link>

          <Link href="/diagram3" className="group block p-6 bg-gradient-to-br from-orange-600 to-red-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利一级产品框架</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z" />
                </svg>
              </div>
            </div>
            <p className="text-orange-100 text-sm leading-relaxed">
              高层次系统架构和模块关系图，展示返利系统在企业整体IT架构中的定位和与其他系统的集成关系。
            </p>
            <div className="mt-4 flex items-center text-orange-200 text-sm">
              <span className="mr-2">🎯</span>
              <span>系统架构 • 模块关系 • 集成设计</span>
            </div>
          </Link>

          <Link href="/diagram4" className="group block p-6 bg-gradient-to-br from-purple-600 to-pink-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利应用算法初步梳理</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <p className="text-purple-100 text-sm leading-relaxed">
              详细的算法配置界面和参数设置，展示返利计算的核心算法逻辑和配置体系的完整设计。
            </p>
            <div className="mt-4 flex items-center text-purple-200 text-sm">
              <span className="mr-2">🧮</span>
              <span>算法设计 • 参数配置 • 计算逻辑</span>
            </div>
          </Link>

          {/* New Architecture Diagrams */}
          <Link href="/diagram5" className="group block p-6 bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利引擎四期改造战略</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
            <p className="text-emerald-100 text-sm leading-relaxed">
              展示从SAP集中式架构向云原生微服务架构的四期渐进式改造战略，突出返利引擎构建过程和核心能力建设路径。
            </p>
            <div className="mt-4 flex items-center text-emerald-200 text-sm">
              <span className="mr-2">🚀</span>
              <span>战略规划 • 能力建设 • ROI分析</span>
            </div>
          </Link>

          <Link href="/diagram6" className="group block p-6 bg-gradient-to-br from-indigo-600 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利引擎改造前后对比</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
              </div>
            </div>
            <p className="text-indigo-100 text-sm leading-relaxed">
              单屏对比图展示SAP集中式架构vs返利引擎微服务架构，突出ABAP→Java迁移和性能提升。
            </p>
            <div className="mt-4 flex items-center text-indigo-200 text-sm">
              <span className="mr-2">⚖️</span>
              <span>前后对比 • 性能提升 • 技术突破</span>
            </div>
          </Link>

          {/* New Technical Architecture Diagrams */}
          <Link href="/diagram7" className="group block p-6 bg-gradient-to-br from-cyan-600 to-blue-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">战略技术演进架构</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
            <p className="text-cyan-100 text-sm leading-relaxed">
              技术架构师视角的演进架构，突出消息总线、现有基础设施能力、存量改造和系统间技术协同关系。
            </p>
            <div className="mt-4 flex items-center text-cyan-200 text-sm">
              <span className="mr-2">🔧</span>
              <span>技术演进 • 基础设施 • 系统协同</span>
            </div>
          </Link>

          <Link href="/diagram8" className="group block p-6 bg-gradient-to-br from-teal-600 to-emerald-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-bold text-white">返利引擎一期技术架构图</h3>
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center group-hover:bg-white/30 transition-colors">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <p className="text-teal-100 text-sm leading-relaxed">
              单屏技术架构图，突出返利引擎能力构建、双数据库异构表设计、双写机制和稳定性保障措施。
            </p>
            <div className="mt-4 flex items-center text-teal-200 text-sm">
              <span className="mr-2">🚀</span>
              <span>返利引擎 • 双写架构 • 能力构建</span>
            </div>
          </Link>
        </div>

        {/* Footer */}
        <div className="text-center text-slate-400 text-sm mt-12 p-6 border-t border-slate-700">
          <div className="flex items-center justify-center gap-2 mb-2">
            <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
            <span className="text-red-400 font-bold">商济健康</span>
            <span className="text-red-300">COWELL HEALTH</span>
          </div>
          <p>返利系统架构设计与重构规划 | 企业数字化转型核心项目 | 内部资料，严格保密</p>
        </div>
      </div>
    </div>
  )
}
