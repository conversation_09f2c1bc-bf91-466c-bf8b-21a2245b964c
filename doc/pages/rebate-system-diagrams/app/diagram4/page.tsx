export default function Diagram4() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-black"></div>
            <h1 className="text-2xl font-bold">返利应用算法初步梳理</h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-red-600 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-600 font-bold text-sm">商济健康</div>
              <div className="text-red-600 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Section 1: 返利形式 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">1.返利形式</div>
                <div className="bg-cyan-100 border-2 border-gray-400 p-4 flex-1">
                  <div className="text-center font-bold mb-2">金额</div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Section 2: 核算基准 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">2.核算基准</div>
                <div className="flex-1 space-y-4">
                  {/* 基准数据 */}
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-3">基准数据</div>
                    <div className="grid grid-cols-6 gap-2">
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">采购 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">销售 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">配送 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">返利券 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">付款返利-基于应付 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">付款返利-基于支付 □</div>
                    </div>
                  </div>

                  {/* 取值逻辑 */}
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-3">取值逻辑</div>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">最大值 □</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">最小值 □</div>
                    </div>
                  </div>

                  {/* 示意 */}
                  <div className="bg-pink-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-3">示意</div>
                    <div className="grid grid-cols-4 gap-2">
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">销售&配送取低值</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">采购&配送取低值</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">付款返利-基于应付</div>
                      <div className="bg-white border border-gray-400 p-2 text-xs text-center">
                        采购&配送取低值-单品
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Section 3: 价格维度 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">3.价格维度</div>
                <div className="flex-1 grid grid-cols-3 gap-4">
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">采购价：批次入库采购价</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">核算价：商品组维护价格</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">零售价：门店标签价</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">实收价：销售实际售价</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">批次价：待确认</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Section 4: 计算方法 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">4.计算方法</div>
                <div className="flex-1 grid grid-cols-3 gap-4">
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">比例：计算金额后按比例</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">梯度：金额阶梯</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">单价</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">固定值</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">补差</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Section 5: 阶梯类型 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">5.阶梯类型</div>
                <div className="flex-1 grid grid-cols-2 gap-4">
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">全量</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">增量</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Section 6: 分摊 */}
          <div className="flex gap-4">
            <div className="w-4 bg-red-600"></div>
            <div className="flex-1">
              <div className="flex gap-4 items-start">
                <div className="bg-pink-200 border-2 border-gray-400 p-3 w-32 text-center font-bold">
                  6.分摊
                  <br />
                  <span className="text-xs">内部资料，严格保密</span>
                </div>
                <div className="flex-1 grid grid-cols-4 gap-4">
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">渠道</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">商品</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">业务域</div>
                  </div>
                  <div className="bg-cyan-100 border-2 border-gray-400 p-4">
                    <div className="text-center font-bold mb-2">加盟</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Page number */}
          <div className="text-right text-sm text-gray-600 mt-8">6</div>
        </div>
      </div>
    </div>
  )
}
