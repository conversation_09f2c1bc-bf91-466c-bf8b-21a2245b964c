export default function Diagram2() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-black"></div>
            <h1 className="text-2xl font-bold">返利数据流程</h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-red-600 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-600 font-bold text-sm">商济健康</div>
              <div className="text-red-600 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Flow Diagram */}
        <div className="space-y-8">
          {/* System Headers */}
          <div className="grid grid-cols-4 gap-4">
            <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">BDP</div>
            <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">SAP</div>
            <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">BDP</div>
            <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">其他系统</div>
          </div>

          {/* Data Flow Content */}
          <div className="grid grid-cols-4 gap-4 items-start">
            {/* BDP Column 1 */}
            <div className="space-y-4">
              <div className="border-2 border-red-600 p-4">
                <div className="space-y-2">
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    采购(后期会停用)
                    <br />
                    SAP本身也汇总采购数据
                  </div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    配送(后期会停用)
                    <br />
                    SAP本身也汇总配送数据
                  </div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    销售（Car）
                    <br />
                    接天&店&商品合并
                  </div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    促销（SAP&Car）
                    <br />
                    按销售单&商品合并
                  </div>
                </div>
              </div>
            </div>

            {/* SAP Column */}
            <div className="space-y-4">
              <div className="border-2 border-red-600 p-4">
                <div className="space-y-2">
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    返利分摊：项目公司+商品
                  </div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">返利分摊：门店+商品</div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">返利分摊：渠道+商品</div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">返利月结：支付方维度</div>
                </div>
              </div>

              {/* Lower SAP Section */}
              <div className="border-2 border-red-600 p-4">
                <div className="grid grid-cols-3 gap-2">
                  <div className="space-y-2">
                    <div className="bg-green-200 border border-gray-400 p-2 text-xs text-center">
                      兑换方式
                      <br />
                      返现
                    </div>
                    <div className="bg-green-200 border border-gray-400 p-2 text-xs text-center">
                      兑换方式
                      <br />
                      票折
                    </div>
                    <div className="bg-green-200 border border-gray-400 p-2 text-xs text-center">
                      兑换方式
                      <br />
                      账扣
                    </div>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="text-2xl">→</div>
                  </div>
                  <div className="space-y-2">
                    <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">
                      SAP财务收款凭证
                      <br />
                      返利系统勾稽
                    </div>
                    <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">SRM</div>
                    <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">客商算平台</div>
                  </div>
                </div>
              </div>
            </div>

            {/* BDP Column 2 */}
            <div className="space-y-4">
              <div className="border-2 border-red-600 p-4">
                <div className="space-y-2">
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                    单月返利
                    <br />
                    年累计
                  </div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">毛利还原</div>
                </div>
              </div>
            </div>

            {/* Other Systems Column */}
            <div className="space-y-4">
              <div className="border-2 border-red-600 p-4">
                <div className="space-y-2">
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">丰货（管报）</div>
                  <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">智店通</div>
                </div>
              </div>
            </div>
          </div>

          {/* Data Flow Arrows */}
          <div className="grid grid-cols-4 gap-4 items-center">
            <div className="flex justify-end">
              <div className="text-red-600 font-bold">数据抽取</div>
              <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent ml-2"></div>
            </div>
            <div></div>
            <div className="flex justify-end">
              <div className="text-red-600 font-bold">数据抽取</div>
              <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent ml-2"></div>
            </div>
            <div></div>
          </div>

          <div className="grid grid-cols-4 gap-4 items-center">
            <div></div>
            <div></div>
            <div className="flex justify-end">
              <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
            </div>
            <div></div>
          </div>

          {/* Footer */}
          <div className="text-left text-sm text-gray-600 mt-8">内部资料，严格保密</div>
        </div>
      </div>
    </div>
  )
}
