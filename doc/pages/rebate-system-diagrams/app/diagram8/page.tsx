'use client'

import React from 'react'

export default function Diagram8() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-4">
      <div className="max-w-[1800px] mx-auto h-screen flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-bold text-white">一期迁移技术实现架构图</h1>
            <div className="px-3 py-1 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full text-white text-xs font-semibold">
              Phase 1 Migration Architecture
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-white rounded-full flex items-center justify-center">
                <div className="w-3 h-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-sm">商济健康</div>
              <div className="text-red-300 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Architecture Diagram */}
        <div className="flex-1 bg-gradient-to-br from-slate-800/50 to-gray-800/50 rounded-2xl p-6 border border-slate-600/50 relative overflow-hidden">
          {/* Architecture SVG */}
          <svg viewBox="0 0 1600 900" className="w-full h-full">
            <defs>
              {/* Gradients */}
              <linearGradient id="sapGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#dc2626" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#991b1b" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="syncGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#0891b2" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#0e7490" stopOpacity="0.9"/>
              </linearGradient>
              <linearGradient id="newGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#059669" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#047857" stopOpacity="0.9"/>
              </linearGradient>

              {/* Arrow markers */}
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#0891b2"/>
              </marker>
            </defs>

            {/* Background grid */}
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#374151" strokeWidth="1" opacity="0.3"/>
            </pattern>
            <rect width="1600" height="900" fill="url(#grid)"/>

            {/* Title */}
            <text x="800" y="40" textAnchor="middle" fontSize="28" fontWeight="bold" fill="#f8fafc">
              一期合同管理迁移技术实现架构
            </text>

            {/* SAP Current System */}
            <g id="sapSystem">
              <rect x="50" y="80" width="400" height="700" rx="15" fill="url(#sapGrad)" stroke="#dc2626" strokeWidth="3"/>
              <text x="250" y="110" textAnchor="middle" fontSize="20" fontWeight="bold" fill="white">SAP 现有系统</text>

              {/* SAP GUI */}
              <rect x="80" y="130" width="340" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="250" y="155" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">SAP GUI 界面</text>
              <text x="90" y="175" fontSize="12" fill="white">• 传统桌面客户端 • 用户体验陈旧 • 移动端不支持</text>
              <text x="90" y="195" fontSize="12" fill="white">• 响应时间: 3-5秒 • 并发限制  • 合同创建: 20分钟</text>

              {/* ABAP Programs */}
              <rect x="80" y="230" width="110" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="135" y="255" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">ZRED0040</text>
              <text x="135" y="275" textAnchor="middle" fontSize="12" fill="white">合同管理</text>
              <text x="90" y="295" fontSize="10" fill="white">• 创建合同</text>
              <text x="90" y="310" fontSize="10" fill="white">• 修改合同</text>
              <text x="90" y="325" fontSize="10" fill="white">• 审批流程</text>
              <text x="90" y="340" fontSize="10" fill="white">• 状态管理</text>

              <rect x="200" y="230" width="110" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="255" y="255" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">ZRED0041</text>
              <text x="255" y="275" textAnchor="middle" fontSize="12" fill="white">条款管理</text>
              <text x="210" y="295" fontSize="10" fill="white">• 条款配置</text>
              <text x="210" y="310" fontSize="10" fill="white">• 算法设置</text>
              <text x="210" y="325" fontSize="10" fill="white">• 参数验证</text>
              <text x="210" y="340" fontSize="10" fill="white">• 效果分析</text>

              <rect x="320" y="230" width="110" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="375" y="255" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">ZRED0056</text>
              <text x="375" y="275" textAnchor="middle" fontSize="12" fill="white">协议管理</text>
              <text x="330" y="295" fontSize="10" fill="white">• 协议生成</text>
              <text x="330" y="310" fontSize="10" fill="white">• 批量处理</text>
              <text x="330" y="325" fontSize="10" fill="white">• 组织管理</text>
              <text x="330" y="340" fontSize="10" fill="white">• 付款方验证</text>

              {/* SAP HANA Database */}
              <rect x="80" y="370" width="340" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="250" y="395" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">SAP HANA 数据库</text>
              <text x="90" y="415" fontSize="12" fill="white">• ZRET0001 合同主表 • ZRET0002 条款表 • ZRET0006 协议表</text>
              <text x="90" y="435" fontSize="12" fill="white">• ZRET0007 组织表 • 触发器机制 • 实时数据捕获</text>
              <text x="90" y="455" fontSize="12" fill="white">• 变更日志记录 • 数据完整性约束 • 零延迟触发</text>
              <text x="90" y="475" fontSize="12" fill="white">• CREATE TRIGGER sync_contract_changes</text>

              {/* Migration Details */}
              <rect x="80" y="510" width="340" height="250" rx="8" fill="rgba(255,255,255,0.1)" stroke="white" strokeWidth="1"/>
              <text x="250" y="535" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">迁移技术细节</text>

              <text x="90" y="560" fontSize="14" fontWeight="bold" fill="#fbbf24">ABAP → Java 迁移映射</text>
              <text x="90" y="580" fontSize="11" fill="white">FORM create_contract → ContractController.create()</text>
              <text x="90" y="595" fontSize="11" fill="white">CALL FUNCTION 'APPROVAL' → WorkflowService.approve()</text>
              <text x="90" y="610" fontSize="11" fill="white">UPDATE zret0001 → ContractStateService.update()</text>
              <text x="90" y="625" fontSize="11" fill="white">FORM config_clause → ClauseConfigService.config()</text>
              <text x="90" y="640" fontSize="11" fill="white">CALL FUNCTION 'ALGORITHM' → AlgorithmService.setup()</text>
              <text x="90" y="655" fontSize="11" fill="white">PERFORM validate → ValidationEngine.validate()</text>
              <text x="90" y="670" fontSize="11" fill="white">FORM generate_agreement → AgreementGenerator.generate()</text>
              <text x="90" y="685" fontSize="11" fill="white">LOOP AT itab → BatchProcessService.process()</text>
              <text x="90" y="700" fontSize="11" fill="white">SELECT FROM zret0007 → OrganizationService.manage()</text>

              <text x="90" y="730" fontSize="14" fontWeight="bold" fill="#f87171">性能瓶颈</text>
              <text x="90" y="750" fontSize="11" fill="white">响应时间: 3-5秒 | 并发限制: 50用户 | 合同创建: 30分钟</text>
            </g>

            {/* Data Sync Layer */}
            <g id="syncLayer">
              <rect x="500" y="200" width="300" height="500" rx="15" fill="url(#syncGrad)" stroke="#0891b2" strokeWidth="3"/>
              <text x="650" y="230" textAnchor="middle" fontSize="20" fontWeight="bold" fill="white">数据同步层</text>

              {/* Trigger Mechanism */}
              <rect x="520" y="250" width="260" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="650" y="275" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">HANA 触发器</text>
              <text x="530" y="295" fontSize="11" fill="white">• INSERT/UPDATE/DELETE 事件捕获</text>
              <text x="530" y="310" fontSize="11" fill="white">• 零延迟数据变更检测</text>
              <text x="530" y="325" fontSize="11" fill="white">• 实时消息发布到 RabbitMQ</text>

              {/* Message Queue */}
              <rect x="520" y="350" width="260" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="650" y="375" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">RabbitMQ 消息队列</text>
              <text x="530" y="395" fontSize="11" fill="white">• 异步消息处理机制</text>
              <text x="530" y="410" fontSize="11" fill="white">• 消息持久化存储</text>
              <text x="530" y="425" fontSize="11" fill="white">• 失败重试 + 死信队列</text>

              {/* Data Validation */}
              <rect x="520" y="450" width="260" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="650" y="475" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">数据校验与补偿</text>
              <text x="530" y="495" fontSize="11" fill="white">• 数据一致性检查</text>
              <text x="530" y="510" fontSize="11" fill="white">• MD5 哈希值比对</text>
              <text x="530" y="525" fontSize="11" fill="white">• 自动差异修复机制</text>

              {/* Sync Monitor */}
              <rect x="520" y="550" width="260" height="80" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="650" y="575" textAnchor="middle" fontSize="14" fontWeight="bold" fill="white">同步监控</text>
              <text x="530" y="595" fontSize="11" fill="white">• 实时状态监控</text>
              <text x="530" y="610" fontSize="11" fill="white">• 告警通知机制</text>
              <text x="530" y="625" fontSize="11" fill="white">• 性能指标统计</text>

              {/* Performance */}
              <rect x="520" y="650" width="260" height="40" rx="8" fill="rgba(34, 197, 94, 0.3)" stroke="#22c55e" strokeWidth="2"/>
              <text x="650" y="675" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">同步性能: &lt;10秒延迟 | 99.99%一致性</text>
            </g>

            {/* New Rebate Engine System */}
            <g id="rebateEngine">
              <rect x="850" y="80" width="700" height="700" rx="15" fill="url(#newGrad)" stroke="#059669" strokeWidth="3"/>
              <text x="1200" y="110" textAnchor="middle" fontSize="20" fontWeight="bold" fill="white">返利引擎 - 一期能力构建</text>

              {/* Amis Low-code Frontend */}
              <rect x="880" y="130" width="640" height="70" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1200" y="155" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">Amis 低代码前端层</text>
              <text x="890" y="175" fontSize="11" fill="white">• 合同管理页面 • 条款配置页面 • 协议管理页面 • 数据同步监控页面</text>
              <text x="890" y="190" fontSize="11" fill="white">• 替代SAP GUI • 现代化用户体验 • 移动端支持 • 响应时间&lt;500ms</text>

              {/* Rebate Engine Core Services */}
              <rect x="880" y="220" width="640" height="180" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1200" y="245" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">返利引擎核心服务</text>

              <rect x="900" y="260" width="200" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1000" y="280" textAnchor="middle" fontSize="13" fontWeight="bold" fill="white">合同管理引擎</text>
              <text x="910" y="300" fontSize="10" fill="white">• ContractController.create()</text>
              <text x="910" y="315" fontSize="10" fill="white">• WorkflowService.approve()</text>
              <text x="910" y="330" fontSize="10" fill="white">• ContractStateService.update()</text>
              <text x="910" y="345" fontSize="10" fill="white">• 合同生命周期管理</text>
              <text x="910" y="360" fontSize="10" fill="white">• 审批流程引擎</text>
              <text x="910" y="375" fontSize="10" fill="white">• 状态机管理</text>

              <rect x="1120" y="260" width="200" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1220" y="280" textAnchor="middle" fontSize="13" fontWeight="bold" fill="white">条款配置引擎</text>
              <text x="1130" y="300" fontSize="10" fill="white">• ClauseConfigService.config()</text>
              <text x="1130" y="315" fontSize="10" fill="white">• AlgorithmService.setup()</text>
              <text x="1130" y="330" fontSize="10" fill="white">• ValidationEngine.validate()</text>
              <text x="1130" y="345" fontSize="10" fill="white">• 返利算法配置</text>
              <text x="1130" y="360" fontSize="10" fill="white">• 参数验证引擎</text>
              <text x="1130" y="375" fontSize="10" fill="white">• 效果分析模块</text>

              <rect x="1340" y="260" width="170" height="130" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1425" y="280" textAnchor="middle" fontSize="13" fontWeight="bold" fill="white">协议生成引擎</text>
              <text x="1350" y="300" fontSize="10" fill="white">• AgreementGenerator</text>
              <text x="1350" y="315" fontSize="10" fill="white">• BatchProcessService</text>
              <text x="1350" y="330" fontSize="10" fill="white">• OrganizationService</text>
              <text x="1350" y="345" fontSize="10" fill="white">• 协议模板引擎</text>
              <text x="1350" y="360" fontSize="10" fill="white">• 批量处理能力</text>
              <text x="1350" y="375" fontSize="10" fill="white">• 组织架构管理</text>

              {/* Dual Database Architecture */}
              <rect x="880" y="420" width="640" height="140" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1200" y="445" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">双数据库架构 - 异构表设计</text>

              <rect x="900" y="465" width="280" height="85" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1040" y="485" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">MySQL 异构表结构</text>
              <text x="910" y="505" fontSize="10" fill="white">• rebate_contract (合同表) ← ZRET0001</text>
              <text x="910" y="520" fontSize="10" fill="white">• rebate_clause (条款表) ← ZRET0002</text>
              <text x="910" y="535" fontSize="10" fill="white">• rebate_agreement (协议表) ← ZRET0006</text>

              <rect x="1200" y="465" width="310" height="85" rx="6" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1355" y="485" textAnchor="middle" fontSize="12" fontWeight="bold" fill="white">双写机制技术实现</text>
              <text x="1210" y="505" fontSize="10" fill="white">• 实时双写：SAP HANA + MySQL 同步写入</text>
              <text x="1210" y="520" fontSize="10" fill="white">• 数据校验：MD5哈希值比对 + 差异检测</text>
              <text x="1210" y="535" fontSize="10" fill="white">• 补偿事务：Saga模式 + 自动修复机制</text>

              {/* Data Consistency & Stability */}
              <rect x="880" y="580" width="640" height="120" rx="8" fill="rgba(255,255,255,0.15)" stroke="white" strokeWidth="2"/>
              <text x="1200" y="605" textAnchor="middle" fontSize="16" fontWeight="bold" fill="white">稳定性保障机制</text>

              <rect x="900" y="625" width="200" height="65" rx="6" fill="rgba(34, 197, 94, 0.2)" stroke="#22c55e" strokeWidth="1"/>
              <text x="1000" y="645" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">数据一致性保障</text>
              <text x="910" y="660" fontSize="9" fill="white">• 实时双写机制</text>
              <text x="910" y="675" fontSize="9" fill="white">• 数据校验机制</text>
              <text x="910" y="685" fontSize="9" fill="white">• 补偿事务处理</text>

              <rect x="1120" y="625" width="200" height="65" rx="6" fill="rgba(59, 130, 246, 0.2)" stroke="#3b82f6" strokeWidth="1"/>
              <text x="1220" y="645" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">系统稳定性保障</text>
              <text x="1130" y="660" fontSize="9" fill="white">• 灰度发布策略</text>
              <text x="1130" y="675" fontSize="9" fill="white">• 双系统并行</text>
              <text x="1130" y="685" fontSize="9" fill="white">• 故障自愈机制</text>

              <rect x="1340" y="625" width="170" height="65" rx="6" fill="rgba(245, 158, 11, 0.2)" stroke="#f59e0b" strokeWidth="1"/>
              <text x="1425" y="645" textAnchor="middle" fontSize="11" fontWeight="bold" fill="white">风险控制措施</text>
              <text x="1350" y="660" fontSize="9" fill="white">• 实时监控告警</text>
              <text x="1350" y="675" fontSize="9" fill="white">• 应急响应机制</text>
              <text x="1350" y="685" fontSize="9" fill="white">• 数据备份恢复</text>

              {/* Phase 1 Scope */}
              <rect x="880" y="720" width="640" height="50" rx="8" fill="rgba(255,255,255,0.1)" stroke="white" strokeWidth="1"/>
              <text x="1200" y="740" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#fbbf24">一期改造范围</text>
              <text x="890" y="760" fontSize="11" fill="white">✓ 表结构保持在SAP  ✓ 管理页面迁移到自研系统  ✓ 连接SAP HANA数据库  ✓ 建设异构表备份  ✓ 实现双写机制  ✓ 触发器同步功能</text>
            </g>

            {/* Data Flow Arrows */}
            <path d="M 450 450 L 500 450" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="475" y="440" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#0891b2">实时触发</text>

            <path d="M 800 450 L 850 450" stroke="#0891b2" strokeWidth="4" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="825" y="440" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#0891b2">异步同步</text>

            {/* Bidirectional Sync */}
            <path d="M 650 700 Q 650 820 250 820 Q 250 780 250 760" stroke="#f59e0b" strokeWidth="3" fill="none" markerEnd="url(#arrowhead)"/>
            <text x="450" y="840" textAnchor="middle" fontSize="11" fontWeight="bold" fill="#f59e0b">双向数据校验与补偿</text>

            {/* Timeline at bottom */}
            <rect x="50" y="820" width="1500" height="60" rx="8" fill="#1f2937" stroke="#374151" strokeWidth="2"/>
            <text x="800" y="840" textAnchor="middle" fontSize="14" fontWeight="bold" fill="#f8fafc">核心步骤时间线</text>

            <text x="80" y="860" fontSize="10" fill="#22c55e"> 基础设施</text>
            <text x="280" y="860" fontSize="10" fill="#3b82f6"> 核心开发</text>
            <text x="480" y="860" fontSize="10" fill="#f59e0b"> 数据同步</text>
            <text x="680" y="860" fontSize="10" fill="#8b5cf6"> 集成测试</text>
            <text x="880" y="860" fontSize="10" fill="#ef4444"> 灰度上线</text>
            <text x="1080" y="860" fontSize="10" fill="#10b981"> 同步运行</text>

            <text x="1300" y="860" fontSize="10" fill="#f8fafc">风险控制: 双系统并行 | 零业务中断 | 快速回滚</text>
          </svg>
        </div>
      </div>
    </div>
  )
}
