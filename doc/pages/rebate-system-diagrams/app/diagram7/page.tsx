'use client'

import React from 'react'

export default function Diagram7() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 p-6">
      <div className="max-w-[1600px] mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-8">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-gradient-to-r from-cyan-400 to-blue-500 rounded"></div>
            <h1 className="text-4xl font-bold text-white">返利引擎分阶段重构架构图</h1>
            <div className="px-4 py-2 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full text-white text-sm font-semibold">
              4-Phase Reconstruction Architecture
            </div>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center">
              <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                <div className="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-400 font-bold text-lg">商济健康</div>
              <div className="text-red-300 text-sm">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* SVG Architecture Diagram */}
        <div className="bg-white rounded-2xl p-8 shadow-2xl mb-8">
          <svg viewBox="0 0 1400 900" className="w-full h-auto">
            {/* Background Grid */}
            <defs>
              <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" strokeWidth="0.5"/>
              </pattern>
              
              {/* Gradients */}
              <linearGradient id="currentGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#ef4444" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#dc2626" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase1Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#10b981" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#059669" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase2Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#1d4ed8" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase3Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#d97706" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="phase4Grad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#7c3aed" stopOpacity="0.9"/>
              </linearGradient>
              
              <linearGradient id="middlewareGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.8"/>
                <stop offset="100%" stopColor="#0891b2" stopOpacity="0.9"/>
              </linearGradient>
            </defs>
            
            <rect width="1400" height="900" fill="url(#grid)"/>
            
            {/* Title */}
            <text x="700" y="40" textAnchor="middle" className="text-2xl font-bold" fill="#1f2937">
              返利引擎分阶段重构架构图 (2025-2026)
            </text>
            
            {/* Core Data Sources */}
            <g id="dataSources">
              <rect x="50" y="80" width="280" height="180" rx="10" fill="url(#currentGrad)" stroke="#dc2626" strokeWidth="2"/>
              <text x="190" y="105" textAnchor="middle" className="text-lg font-bold" fill="white">核心数据源 - 返利计算基础</text>

              {/* Data Source Components */}
              <rect x="70" y="120" width="240" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="190" y="135" textAnchor="middle" className="text-sm" fill="white">🔵 BDP推送预处理销售单</text>
              <text x="190" y="148" textAnchor="middle" className="text-xs" fill="white">实时销售数据 | 门店销售明细 | 商品销售统计</text>

              <rect x="70" y="165" width="240" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="190" y="180" textAnchor="middle" className="text-sm" fill="white">🔵 SAP采购单据</text>
              <text x="190" y="193" textAnchor="middle" className="text-xs" fill="white">采购订单数据 | 供应商结算 | 采购价格信息</text>

              <rect x="70" y="210" width="240" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="190" y="225" textAnchor="middle" className="text-sm" fill="white">🔵 SAP配送单据</text>
              <text x="190" y="238" textAnchor="middle" className="text-xs" fill="white">配送明细数据 | 物流成本 | 配送服务费用</text>
            </g>
            
            {/* Data Integration Layer */}
            <g id="dataIntegration">
              <rect x="50" y="300" width="1300" height="80" rx="10" fill="url(#middlewareGrad)" stroke="#0891b2" strokeWidth="2"/>
              <text x="700" y="325" textAnchor="middle" className="text-lg font-bold" fill="white">数据集成层 - 核心数据源统一处理</text>

              {/* Data Integration Components */}
              <rect x="80" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="140" y="360" textAnchor="middle" className="text-sm" fill="white">Kafka消息队列</text>

              <rect x="220" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="280" y="360" textAnchor="middle" className="text-sm" fill="white">RFC接口调用</text>

              <rect x="360" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="420" y="360" textAnchor="middle" className="text-sm" fill="white">ETL数据处理</text>

              <rect x="500" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="560" y="360" textAnchor="middle" className="text-sm" fill="white">数据质量监控</text>

              <rect x="640" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="700" y="360" textAnchor="middle" className="text-sm" fill="white">ZRET0017累计表</text>

              <rect x="780" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="840" y="360" textAnchor="middle" className="text-sm" fill="white">ZREFM0026转换</text>

              <rect x="920" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="980" y="360" textAnchor="middle" className="text-sm" fill="white">ZREFM0048转换</text>

              <rect x="1060" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1120" y="360" textAnchor="middle" className="text-sm" fill="white">双写同步机制</text>

              <rect x="1200" y="340" width="120" height="30" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1260" y="360" textAnchor="middle" className="text-sm" fill="white">数据一致性校验</text>
            </g>
            
            {/* Phase 1 - 2025 Q4 */}
            <g id="phase1">
              <rect x="50" y="420" width="320" height="200" rx="10" fill="url(#phase1Grad)" stroke="#059669" strokeWidth="2"/>
              <text x="210" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">第一阶段 (2025 Q4)</text>

              {/* Phase 1 Deliverables */}
              <rect x="70" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="115" y="475" textAnchor="middle" className="text-xs" fill="white">合同&条款&协议</text>
              <text x="115" y="488" textAnchor="middle" className="text-xs" fill="white">功能界面设计</text>
              <text x="115" y="501" textAnchor="middle" className="text-xs" fill="white">模板导入交付</text>

              <rect x="170" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="215" y="475" textAnchor="middle" className="text-xs" fill="white">返利计算&分摊</text>
              <text x="215" y="488" textAnchor="middle" className="text-xs" fill="white">SAP算法分析</text>
              <text x="215" y="501" textAnchor="middle" className="text-xs" fill="white">Java脚本转换</text>

              <rect x="270" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="315" y="475" textAnchor="middle" className="text-xs" fill="white">计提&月结&年结</text>
              <text x="315" y="488" textAnchor="middle" className="text-xs" fill="white">逻辑梳理</text>
              <text x="315" y="501" textAnchor="middle" className="text-xs" fill="white">技术方式设计</text>

              {/* Additional Deliverables */}
              <rect x="70" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="115" y="535" textAnchor="middle" className="text-xs" fill="white">结算&返利免付</text>
              <text x="115" y="548" textAnchor="middle" className="text-xs" fill="white">产品设计</text>
              <text x="115" y="561" textAnchor="middle" className="text-xs" fill="white">接口设计</text>

              <rect x="170" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="215" y="535" textAnchor="middle" className="text-xs" fill="white">返利报表</text>
              <text x="215" y="548" textAnchor="middle" className="text-xs" fill="white">业务逻辑梳理</text>
              <text x="215" y="561" textAnchor="middle" className="text-xs" fill="white">底层表设计</text>

              <rect x="270" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="315" y="535" textAnchor="middle" className="text-xs" fill="white">SAP现有功能</text>
              <text x="315" y="548" textAnchor="middle" className="text-xs" fill="white">并行运行</text>
              <text x="315" y="561" textAnchor="middle" className="text-xs" fill="white">逐步替换</text>

              <text x="70" y="590" className="text-xs" fill="white">核心目标: 基础架构搭建 + 算法转换 + 产品设计</text>
            </g>
            
            {/* Phase 2 - 2025 Q4 */}
            <g id="phase2">
              <rect x="390" y="420" width="320" height="200" rx="10" fill="url(#phase2Grad)" stroke="#1d4ed8" strokeWidth="2"/>
              <text x="550" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">第二阶段 (2025 Q4)</text>

              {/* Phase 2 Deliverables */}
              <rect x="410" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="455" y="475" textAnchor="middle" className="text-xs" fill="white">返利计算&分摊</text>
              <text x="455" y="488" textAnchor="middle" className="text-xs" fill="white">与SAP并行计算</text>
              <text x="455" y="501" textAnchor="middle" className="text-xs" fill="white">对比计算结果</text>

              <rect x="510" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="555" y="475" textAnchor="middle" className="text-xs" fill="white">计提&月结&年结</text>
              <text x="555" y="488" textAnchor="middle" className="text-xs" fill="white">产品设计&交付</text>
              <text x="555" y="501" textAnchor="middle" className="text-xs" fill="white">SAP功能并行</text>

              <rect x="610" y="460" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="655" y="475" textAnchor="middle" className="text-xs" fill="white">结算&返利免付</text>
              <text x="655" y="488" textAnchor="middle" className="text-xs" fill="white">产品设计完成</text>
              <text x="655" y="501" textAnchor="middle" className="text-xs" fill="white">接口交付</text>

              {/* Additional Phase 2 */}
              <rect x="410" y="520" width="140" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="480" y="535" textAnchor="middle" className="text-xs" fill="white">返利报表</text>
              <text x="480" y="548" textAnchor="middle" className="text-xs" fill="white">报表交付，数字共享交付</text>
              <text x="480" y="561" textAnchor="middle" className="text-xs" fill="white">与SAP数据对比</text>

              <rect x="560" y="520" width="140" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="630" y="535" textAnchor="middle" className="text-xs" fill="white">SAP现有功能</text>
              <text x="630" y="548" textAnchor="middle" className="text-xs" fill="white">并行运行，逐步替换</text>
              <text x="630" y="561" textAnchor="middle" className="text-xs" fill="white">功能验证对比</text>

              <text x="410" y="590" className="text-xs" fill="white">核心目标: 并行计算验证 + 产品交付 + 接口完成</text>
            </g>
            
            {/* Phase 3 - 2026 Q1 */}
            <g id="phase3">
              <rect x="730" y="420" width="320" height="200" rx="10" fill="url(#phase3Grad)" stroke="#d97706" strokeWidth="2"/>
              <text x="890" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">第三阶段 (2026 Q1)</text>

              {/* Phase 3 Deliverables */}
              <rect x="750" y="460" width="140" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="820" y="475" textAnchor="middle" className="text-xs" fill="white">计提&月结&年结</text>
              <text x="820" y="488" textAnchor="middle" className="text-xs" fill="white">产品交付，接口交付</text>
              <text x="820" y="501" textAnchor="middle" className="text-xs" fill="white">完整功能上线</text>

              <rect x="900" y="460" width="140" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="970" y="475" textAnchor="middle" className="text-xs" fill="white">返利报表</text>
              <text x="970" y="488" textAnchor="middle" className="text-xs" fill="white">报表交付，数据共享</text>
              <text x="970" y="501" textAnchor="middle" className="text-xs" fill="white">与SAP数据对比</text>

              {/* Core Data Sources Integration */}
              <rect x="750" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="795" y="535" textAnchor="middle" className="text-xs" fill="white">BDP销售数据</text>
              <text x="795" y="548" textAnchor="middle" className="text-xs" fill="white">完整集成</text>
              <text x="795" y="561" textAnchor="middle" className="text-xs" fill="white">实时处理</text>

              <rect x="850" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="895" y="535" textAnchor="middle" className="text-xs" fill="white">SAP采购数据</text>
              <text x="895" y="548" textAnchor="middle" className="text-xs" fill="white">完整集成</text>
              <text x="895" y="561" textAnchor="middle" className="text-xs" fill="white">实时处理</text>

              <rect x="950" y="520" width="90" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="995" y="535" textAnchor="middle" className="text-xs" fill="white">SAP配送数据</text>
              <text x="995" y="548" textAnchor="middle" className="text-xs" fill="white">完整集成</text>
              <text x="995" y="561" textAnchor="middle" className="text-xs" fill="white">实时处理</text>

              <text x="750" y="590" className="text-xs" fill="white">核心目标: 完整产品交付 + 核心数据源全面集成</text>
            </g>
            
            {/* Phase 4 - 2026 Q2 */}
            <g id="phase4">
              <rect x="1070" y="420" width="280" height="200" rx="10" fill="url(#phase4Grad)" stroke="#7c3aed" strokeWidth="2"/>
              <text x="1210" y="445" textAnchor="middle" className="text-lg font-bold" fill="white">第四阶段 (2026 Q2)</text>

              {/* Phase 4 Deliverables */}
              <rect x="1090" y="460" width="120" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1150" y="475" textAnchor="middle" className="text-xs" fill="white">操作便利性</text>
              <text x="1150" y="488" textAnchor="middle" className="text-xs" fill="white">持续改善</text>
              <text x="1150" y="501" textAnchor="middle" className="text-xs" fill="white">用户体验优化</text>

              <rect x="1220" y="460" width="120" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1280" y="475" textAnchor="middle" className="text-xs" fill="white">遗留问题</text>
              <text x="1280" y="488" textAnchor="middle" className="text-xs" fill="white">快速解决</text>
              <text x="1280" y="501" textAnchor="middle" className="text-xs" fill="white">系统稳定性</text>

              <rect x="1090" y="520" width="120" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1150" y="535" textAnchor="middle" className="text-xs" fill="white">承接新需求</text>
              <text x="1150" y="548" textAnchor="middle" className="text-xs" fill="white">产品快速迭代</text>
              <text x="1150" y="561" textAnchor="middle" className="text-xs" fill="white">敏捷开发</text>

              <rect x="1220" y="520" width="120" height="50" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1280" y="535" textAnchor="middle" className="text-xs" fill="white">产品口碑优化</text>
              <text x="1280" y="548" textAnchor="middle" className="text-xs" fill="white">业务关系改善</text>
              <text x="1280" y="561" textAnchor="middle" className="text-xs" fill="white">持续运营</text>

              <text x="1090" y="590" className="text-xs" fill="white">核心目标: 持续优化 + 快速迭代 + 业务价值最大化</text>
            </g>

            {/* Infrastructure Foundation - Bottom Layer */}
            <g id="infrastructure">
              <rect x="50" y="750" width="1300" height="120" rx="10" fill="#374151" stroke="#6b7280" strokeWidth="2"/>
              <text x="700" y="775" textAnchor="middle" className="text-lg font-bold" fill="white">技术基础设施支撑层 - 贯穿四个阶段</text>

              {/* Infrastructure Components */}
              <rect x="80" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="130" y="805" textAnchor="middle" className="text-sm" fill="white">SpringBoot</text>
              <text x="130" y="818" textAnchor="middle" className="text-xs" fill="white">微服务框架</text>

              <rect x="200" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="250" y="805" textAnchor="middle" className="text-sm" fill="white">Amis低代码</text>
              <text x="250" y="818" textAnchor="middle" className="text-xs" fill="white">前端平台</text>

              <rect x="320" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="370" y="805" textAnchor="middle" className="text-sm" fill="white">Easy建模</text>
              <text x="370" y="818" textAnchor="middle" className="text-xs" fill="white">MDD服务</text>

              <rect x="440" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="490" y="805" textAnchor="middle" className="text-sm" fill="white">MySQL</text>
              <text x="490" y="818" textAnchor="middle" className="text-xs" fill="white">数据库</text>

              <rect x="560" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="610" y="805" textAnchor="middle" className="text-sm" fill="white">Redis</text>
              <text x="610" y="818" textAnchor="middle" className="text-xs" fill="white">缓存</text>

              <rect x="680" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="730" y="805" textAnchor="middle" className="text-sm" fill="white">RabbitMQ</text>
              <text x="730" y="818" textAnchor="middle" className="text-xs" fill="white">消息队列</text>

              <rect x="800" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="850" y="805" textAnchor="middle" className="text-sm" fill="white">Kubernetes</text>
              <text x="850" y="818" textAnchor="middle" className="text-xs" fill="white">容器编排</text>

              <rect x="920" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="970" y="805" textAnchor="middle" className="text-sm" fill="white">Prometheus</text>
              <text x="970" y="818" textAnchor="middle" className="text-xs" fill="white">监控</text>

              <rect x="1040" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1090" y="805" textAnchor="middle" className="text-sm" fill="white">Grafana</text>
              <text x="1090" y="818" textAnchor="middle" className="text-xs" fill="white">可视化</text>

              <rect x="1160" y="790" width="100" height="35" rx="5" fill="rgba(255,255,255,0.2)" stroke="white" strokeWidth="1"/>
              <text x="1210" y="805" textAnchor="middle" className="text-sm" fill="white">Docker</text>
              <text x="1210" y="818" textAnchor="middle" className="text-xs" fill="white">容器化</text>

              <text x="80" y="850" className="text-xs" fill="white">技术支撑: 现代化技术栈 + 云原生架构 + 完整监控体系</text>
            </g>
            
            {/* Data Flow Arrows */}
            <defs>
              <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                <polygon points="0 0, 10 3.5, 0 7" fill="#374151"/>
              </marker>
            </defs>
            
            {/* Current to Phase 1 */}
            <path d="M 190 260 Q 190 290 210 320" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            
            {/* Phase 1 to Phase 2 */}
            <path d="M 370 520 Q 380 520 390 520" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            
            {/* Phase 2 to Phase 3 */}
            <path d="M 710 520 Q 720 520 730 520" stroke="#374151" strokeWidth="2" fill="none" markerEnd="url(#arrowhead)"/>
            

            
            {/* Message Bus Connections */}
            <path d="M 210 380 L 210 420" stroke="#0891b2" strokeWidth="2" fill="none"/>
            <path d="M 550 380 L 550 420" stroke="#0891b2" strokeWidth="2" fill="none"/>
            <path d="M 890 380 L 890 420" stroke="#0891b2" strokeWidth="2" fill="none"/>

            {/* Infrastructure Support Connections */}
            <path d="M 210 620 L 210 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            <path d="M 550 620 L 550 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            <path d="M 890 620 L 890 750" stroke="#7c3aed" strokeWidth="2" fill="none" strokeDasharray="5,5"/>
            

            
            {/* Technical Focus */}
            <g id="techFocus">
              <text x="50" y="720" className="text-sm font-bold" fill="#374151">实施重点:</text>
              <text x="210" y="720" className="text-sm" fill="#059669">2025Q4-基础搭建</text>
              <text x="550" y="720" className="text-sm" fill="#1d4ed8">2025Q4-并行验证</text>
              <text x="890" y="720" className="text-sm" fill="#d97706">2026Q1-完整交付</text>
              <text x="1210" y="720" className="text-sm" fill="#7c3aed">2026Q2-持续优化</text>

              <text x="50" y="735" className="text-sm font-bold" fill="#374151">核心数据源:</text>
              <text x="210" y="735" className="text-xs" fill="#059669">BDP销售数据</text>
              <text x="550" y="735" className="text-xs" fill="#1d4ed8">SAP采购数据</text>
              <text x="890" y="735" className="text-xs" fill="#d97706">SAP配送数据</text>
              <text x="1210" y="735" className="text-xs" fill="#7c3aed">统一数据模型</text>
            </g>
          </svg>
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-2 gap-6">
          <div className="p-6 bg-gradient-to-r from-slate-800 to-gray-800 rounded-2xl border border-slate-600">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <span className="text-2xl">🔧</span>
              核心技术栈演进
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-300">核心数据源:</span>
                <span className="text-cyan-400">BDP销售 + SAP采购 + SAP配送</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">算法转换:</span>
                <span className="text-cyan-400">ZREFM0026/0048 → Java脚本</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">计算引擎:</span>
                <span className="text-cyan-400">SAP ABAP → SpringBoot微服务</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">数据架构:</span>
                <span className="text-cyan-400">SAP HANA + MySQL双写同步</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">前端技术:</span>
                <span className="text-cyan-400">SAP GUI → Amis低代码平台</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">实施策略:</span>
                <span className="text-cyan-400">4阶段渐进式重构</span>
              </div>
            </div>
          </div>
          
          <div className="p-6 bg-gradient-to-r from-slate-800 to-gray-800 rounded-2xl border border-slate-600">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <span className="text-2xl">📊</span>
              系统协同能力
            </h3>
            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-slate-300">第一阶段目标:</span>
                <span className="text-green-400">基础架构 + 算法转换</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">第二阶段目标:</span>
                <span className="text-green-400">并行计算 + 结果验证</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">第三阶段目标:</span>
                <span className="text-green-400">完整交付 + 数据集成</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">第四阶段目标:</span>
                <span className="text-green-400">持续优化 + 快速迭代</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-300">预期收益:</span>
                <span className="text-green-400">效率提升50% + 成本降低30%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-slate-400 text-sm mt-8 p-4 border-t border-slate-700">
          返利引擎分阶段重构架构图 | 核心数据源: BDP销售+SAP采购+SAP配送 | 4阶段渐进式实施 (2025-2026)
        </div>
      </div>
    </div>
  )
}
