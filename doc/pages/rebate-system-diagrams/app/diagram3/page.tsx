export default function Diagram3() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-black"></div>
            <h1 className="text-2xl font-bold">返利一级产品框架</h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-red-600 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-600 font-bold text-sm">商济健康</div>
              <div className="text-red-600 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* System Headers */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">其他系统</div>
          <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">BDP</div>
          <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">SAP</div>
          <div className="bg-red-600 text-white text-center py-4 font-bold text-lg">其他系统</div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-4 gap-4 mb-8">
          {/* Column 1 - Other Systems */}
          <div className="space-y-4">
            {/* 丰货(管报) */}
            <div className="bg-red-600 text-white text-center py-3 font-bold">丰货（管报）</div>
            <div className="border-2 border-red-600 p-2">
              <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">丰货（管报）</div>
            </div>

            {/* 智店通 */}
            <div className="bg-red-600 text-white text-center py-3 font-bold">智店通</div>
            <div className="border-2 border-red-600 p-2">
              <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">数据展示</div>
            </div>
          </div>

          {/* Column 2 - BDP */}
          <div className="space-y-4">
            <div className="border-2 border-red-600 p-4">
              <div className="space-y-2">
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  数据核对
                  <br />
                  分摊与总数-数性
                </div>
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  报表合并
                  <br />
                  日结、月结、1-8日结
                </div>
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  返利再次分摊
                  <br />
                  SAP分摊基础上分摊至支付
                </div>
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  返利计算
                  <br />
                  单月返利、年度返利
                </div>
              </div>
            </div>

            {/* Lower BDP Section */}
            <div className="border-2 border-red-600 p-4">
              <div className="space-y-2">
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  毛利还原-销售单据制库
                  <br />
                  内部大仓高并、商业高并等
                </div>
                <div className="bg-pink-200 border border-gray-400 p-3 text-sm text-center">
                  毛利还原-门店+商品+日+渠道
                  <br />
                  促销返利、销售返利
                </div>
              </div>
            </div>

            {/* Bottom BDP Sections */}
            <div className="grid grid-cols-2 gap-2">
              <div className="border-2 border-red-600 p-2">
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利报表</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利预测</div>
              </div>
              <div className="border-2 border-red-600 p-2">
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">月结</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">年结</div>
              </div>
            </div>
          </div>

          {/* Column 3 - SAP */}
          <div className="space-y-4">
            {/* Contract Section */}
            <div className="border-2 border-red-600 p-3">
              <div className="grid grid-cols-3 gap-2">
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">合同</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">条款</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">协议</div>
              </div>
            </div>

            {/* Arrow pointing right */}
            <div className="flex justify-center">
              <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
            </div>

            {/* Rebate Calculation Section */}
            <div className="border-2 border-red-600 p-3">
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利计算</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">结算单-内部</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">
                  返利分摊
                  <br />
                  门店+商品
                  <br />
                  项目公司+商品
                  <br />
                  渠道+商品
                </div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">结算单-外部</div>
                <div></div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">结算单-加盟</div>
              </div>
            </div>

            {/* Yellow dotted box */}
            <div className="border-4 border-dashed border-yellow-400 bg-yellow-100 p-3">
              <div className="bg-yellow-200 border border-gray-400 p-2 text-xs text-center">返利调整单</div>
            </div>

            {/* Collection and Registration */}
            <div className="border-2 border-red-600 p-3">
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">收款凭证</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利兑付-返现</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返现登记</div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利兑付-票折</div>
                <div></div>
                <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利兑付-账扣</div>
              </div>
            </div>

            {/* Rebate Reminder */}
            <div className="border-2 border-red-600 p-3">
              <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">返利计提</div>
            </div>
          </div>

          {/* Column 4 - Other Systems */}
          <div className="space-y-4">
            {/* 金蝶 */}
            <div className="bg-red-600 text-white text-center py-3 font-bold">金蝶</div>
            <div className="border-2 border-red-600 p-2">
              <div className="bg-pink-200 border border-gray-400 p-2 text-xs text-center">开票</div>
            </div>

            {/* SRM */}
            <div className="bg-red-600 text-white text-center py-3 font-bold">SRM</div>
            <div className="border-2 border-red-600 p-2">
              <div className="space-y-1">
                <div className="bg-pink-200 border border-gray-400 p-1 text-xs text-center">勾票对账</div>
                <div className="bg-pink-200 border border-gray-400 p-1 text-xs text-center">发票校验</div>
              </div>
            </div>

            {/* 客商结算平台 */}
            <div className="bg-red-600 text-white text-center py-3 font-bold">客商结算平台</div>
            <div className="border-2 border-red-600 p-2">
              <div className="space-y-1">
                <div className="bg-pink-200 border border-gray-400 p-1 text-xs text-center">付款申请-账扣金额和减</div>
                <div className="bg-pink-200 border border-gray-400 p-1 text-xs text-center">资金系统付款</div>
              </div>
            </div>
          </div>
        </div>

        {/* Arrows between columns */}
        <div className="grid grid-cols-4 gap-4 items-center mb-8">
          <div className="flex justify-end">
            <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
          </div>
          <div className="flex justify-end">
            <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
          </div>
          <div className="flex justify-end">
            <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
          </div>
          <div></div>
        </div>

        {/* Footer */}
        <div className="border-t-2 border-dashed border-gray-400 pt-4">
          <div className="text-left text-sm text-gray-600">内部资料，严格保密</div>
        </div>
      </div>
    </div>
  )
}
