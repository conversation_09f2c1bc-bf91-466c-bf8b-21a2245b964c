export default function Diagram1() {
  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-black"></div>
            <h1 className="text-2xl font-bold">返利二级产品功能框架</h1>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
              <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                <div className="w-4 h-4 bg-red-600 rounded-full"></div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-red-600 font-bold text-sm">商济健康</div>
              <div className="text-red-600 text-xs">COWELL HEALTH</div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="space-y-8">
          {/* First Row */}
          <div className="flex gap-4">
            {/* Left Side Label */}
            <div className="w-24 flex flex-col items-center justify-center bg-gray-200 border-2 border-gray-400 h-48">
              <div className="text-center font-bold text-sm leading-tight">
                返利
                <br />
                功能
                <br />
                (SAP)
              </div>
            </div>

            {/* Content Boxes */}
            <div className="flex-1 flex gap-4">
              {/* 审批条款审批 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-32">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">审批</div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">条款审批</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议审批</div>
                </div>
              </div>

              {/* 返利计算 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 flex-1">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  返利计算
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利计算</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">次年预估</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利期间累计汇总</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">未达量计算</div>
                </div>
                <div className="mt-2 space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利业务域分类</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利加盟业务分类</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">促销返利小票数据</div>
                </div>
                <div className="mt-2">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利计提</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利计提</div>
                </div>
              </div>

              {/* 返利兑换管理 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 flex-1">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  返利兑换管理（采购组）
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">线下票折</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返现</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">线下收款单创建</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返现返利登记、修改</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">线下收款单查询</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利返利认领</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">线下收款单管理</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返现返利认领审批</div>
                </div>
                <div className="mt-2">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返现返利报表</div>
                </div>
              </div>

              {/* 结算单 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-32">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  结算单（采购组）
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">结算单创建</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">实收返利结算补录</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">结算单审核</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">结算单查询</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">结算单开票</div>
                </div>
              </div>

              {/* 报表管理-应收 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-32">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  报表管理-应收
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利统计分析</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利数据明细</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利商品累计</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利业务域分类</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">......</div>
                </div>
              </div>

              {/* 报表管理-应付 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-32">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  报表管理-应付
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">加盟店-返利报表</div>
                </div>
                <div className="mt-4 space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">未达量管理</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">未达量损失</div>
                </div>
              </div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Second Row */}
          <div className="flex gap-4">
            {/* Left Side Label */}
            <div className="w-24 flex flex-col items-center justify-center bg-gray-200 border-2 border-gray-400 h-96">
              <div className="text-center font-bold text-sm leading-tight">
                返利
                <br />
                维护
                <br />
                (SAP)
              </div>
            </div>

            {/* Content Boxes */}
            <div className="flex-1 flex gap-4">
              {/* 合同 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-32">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">合同</div>
                <div className="space-y-2">
                  <div className="space-y-1">
                    <div className="bg-pink-300 border border-gray-400 p-1 text-xs text-center font-medium">条款1</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议1</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议...</div>
                  </div>

                  <div className="space-y-1">
                    <div className="bg-pink-300 border border-gray-400 p-1 text-xs text-center font-medium">条款2</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议2</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议...</div>
                  </div>

                  <div className="space-y-1">
                    <div className="bg-pink-300 border border-gray-400 p-1 text-xs text-center font-medium">
                      条款...
                    </div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议3</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议...</div>
                  </div>
                </div>
              </div>

              {/* Arrow */}
              <div className="flex items-center justify-center w-8">
                <div className="w-0 h-0 border-l-8 border-l-red-600 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
              </div>

              {/* Large Contract Management Section */}
              <div className="flex-1 bg-yellow-100 border-4 border-yellow-400 p-4">
                <div className="text-center font-bold text-lg mb-4">合同管理（返利组）</div>

                <div className="grid grid-cols-4 gap-4 mb-4">
                  {/* Contract Type Section */}
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">合同类型</div>
                    <div className="bg-white border border-gray-400 p-2 text-xs text-center">合同主体</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">伙伴类型</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">伙伴编码</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">采购组</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">签署年度</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">开始日期</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">结束日期</div>
                  </div>

                  {/* Contract Name Section */}
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">合同名称</div>
                    <div className="space-y-1">
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">结算周期</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">支付方</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款周期</div>
                    </div>
                    <div className="bg-white border border-gray-400 p-2 text-xs text-center">附加数据</div>
                  </div>

                  {/* Default Parameters Section */}
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">默认参数</div>
                    <div className="space-y-1">
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">核算周期</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">兑换方式</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">周期类型</div>
                    </div>
                  </div>

                  {/* Business Group Section */}
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">商品组</div>
                    <div className="space-y-1">
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">选择商品组</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">新建商品组</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">修改商品组</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">删除商品组</div>
                    </div>
                    <div className="space-y-1">
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">商品组明细</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">促销返利规则</div>
                      <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">计算或付款规则</div>
                    </div>
                  </div>
                </div>

                {/* Return Conditions Section */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">返利条件导入</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">计算类条款</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">固定类条款</div>
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">促销类条款</div>
                  </div>
                  <div className="space-y-2">
                    <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">返利条件导入</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Third Row - Contract Management Details */}
          <div className="flex gap-4">
            <div className="w-24"></div> {/* Spacer */}
            <div className="flex-1 grid grid-cols-3 gap-4">
              {/* Contract Management Section */}
              <div className="bg-yellow-100 border-4 border-yellow-400 p-4">
                <div className="text-center font-bold text-base mb-3">条款管理（返利组）</div>

                <div className="grid grid-cols-2 gap-2 mb-3">
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">合同信息</div>
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">外部供货商</div>
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">外部支付方</div>
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">任务商品组</div>
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">组织模板</div>
                  <div className="bg-cyan-100 border border-gray-400 p-2 text-xs text-center">附加数据</div>
                </div>

                <div className="bg-white border border-gray-400 p-2 text-xs text-center mb-3">条款主体</div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">条款描述</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">外站类型</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利类型</div>
                  </div>
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">开始条款时间</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">谈判方</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">收取部门</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">核算基准</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利形式</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">量化维度</div>
                  </div>
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利规则</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">阶梯类型</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利价格维度</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-2">
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">阶梯类型</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">计算方法</div>
                  </div>
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">防错价格维度</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">开票现价(含税价格)</div>
                  </div>
                </div>
              </div>

              {/* Agreement Management Section */}
              <div className="bg-yellow-100 border-4 border-yellow-400 p-4">
                <div className="text-center font-bold text-base mb-3">协议管理（返利组）</div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">组织级别</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款方</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">专属标识</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">防错规则</div>
                  </div>
                  <div className="space-y-1">
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">协议主体</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款方级别</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">兑换方式</div>
                    <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">附加协议</div>
                  </div>
                </div>

                <div className="mt-3 space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">收款方</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款类型</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">附加协议</div>
                </div>
              </div>

              {/* Empty space for alignment */}
              <div></div>
            </div>
          </div>

          {/* Divider */}
          <div className="border-t-2 border-dashed border-gray-400"></div>

          {/* Bottom Row */}
          <div className="flex gap-4">
            <div className="w-24 flex flex-col items-center justify-center bg-gray-200 border-2 border-gray-400 h-24">
              <div className="text-center font-bold text-sm leading-tight">
                底层
                <br />
                逻辑
              </div>
            </div>

            <div className="flex-1 flex gap-4">
              {/* 返利应收应付 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-40">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  返利应收应付
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">应收返利</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">应付返利</div>
                </div>
              </div>

              {/* 组织模板 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-40">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  组织模板（返利组）
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">新建、修改、查询</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">导入</div>
                </div>
              </div>

              {/* 返利兑换数据传输 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 min-w-40">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  返利兑换数据传输
                </div>
                <div className="space-y-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">
                    线上票折-结算单下发SRM
                  </div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">账扣-各商算平台</div>
                </div>
              </div>

              {/* 核算基准 */}
              <div className="bg-pink-200 border-2 border-gray-400 p-3 flex-1">
                <div className="bg-pink-300 border border-gray-400 p-2 mb-2 text-center text-sm font-medium">
                  核算基准（返利组）
                </div>
                <div className="grid grid-cols-4 gap-1">
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">采购</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">销售</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">配送</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">返利券</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款返利-基于支付</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">付款返利-基于应付</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">采购&配送取低值-单品</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">销售&配送取低值</div>
                  <div className="bg-cyan-100 border border-gray-400 p-1 text-xs text-center">采购&配送取低值-单品</div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="text-left text-sm text-gray-600 mt-8">内部资料，严格保密</div>
        </div>
      </div>
    </div>
  )
}
