# SAP返利管理系统产品文档

## 1. 系统概述

### 1.1 系统简介
SAP返利管理系统是一个企业级的返利业务管理平台，基于SAP ABAP技术开发，用于管理供应商返利、促销返利、固定返利等各类返利业务。系统支持返利合同、条款、协议的全生命周期管理，包括创建、修改、审批、执行和结算等环节，具备完整的权限控制、审批流程和批量处理能力。

**核心特性：**
- 支持多种返利类型：计算类(A/T/V/P)、固定类(F/Q)、促销类等
- 完整的三级审批流程：条款审批、协议审批、最终审批
- 灵活的权限控制：基于合同类型、公司代码、采购组的多维度权限管理
- 强大的批量处理：支持Excel导入、批量创建、批量修改
- 实时数据计算：自动计算返利金额、累计数量等业务指标

### 1.2 系统架构
```
返利管理系统
├── 返利合同管理 (ZRED0040)
├── 返利条款管理 (ZRED0041)
├── 返利协议管理 (ZRED0056/0057/0059)
├── 返利查询报表 (ZRER0103/0013)
└── 返利数据处理 (ZRED0063/0072)
```

### 1.3 核心业务对象

#### 1.3.1 主要数据表
- **返利合同 (ZRETA001)**: 合同主表，定义合同基本信息、签约方、有效期等
- **返利条款 (ZRETA002)**: 条款主表，定义具体返利规则、计算方式、审批状态
- **返利协议 (ZRET0006)**: 协议主表，定义执行细节、组织范围、结算方式
- **返利明细 (ZRET0007/0008)**: 协议明细表，包含动态协议和固定协议的具体条目
- **商品组 (ZRET0009/0020)**: 商品范围定义，支持层次化商品分组管理
- **组织结构 (ZRET0014)**: 适用组织范围，包括公司代码、工厂、销售组织
- **附加条款 (ZRETA003)**: 条款附加规则，支持复杂业务逻辑和条件设置
- **返利阶梯 (ZRETA005)**: 促销返利阶梯设置，支持多级返利计算
- **生效日期 (ZRETA006)**: 促销返利生效日期管理
- **商品阶梯 (ZRET0058)**: 商品级别的阶梯返利设置

#### 1.3.2 业务关系
```
返利合同 (ZRETA001)
    ├── 返利条款 (ZRETA002) [1:N]
    │   ├── 返利协议 (ZRET0006) [1:N]
    │   │   ├── 动态协议明细 (ZRET0007) [1:N]
    │   │   └── 固定协议明细 (ZRET0008) [1:N]
    │   ├── 附加条款 (ZRETA003) [1:N]
    │   ├── 返利阶梯 (ZRETA005) [1:N]
    │   └── 商品阶梯 (ZRET0058) [1:N]
    └── 商品组 (ZRET0009) [1:N]
        └── 商品明细 (ZRET0020) [1:N]
```

## 2. 功能模块详述

### 2.1 返利合同管理模块 (ZRED0040)

#### 2.1.1 功能概述
返利合同管理是整个返利体系的基础，负责管理返利业务的顶层合同框架。该模块提供合同的全生命周期管理，包括创建、修改、显示和商品组管理功能。

#### 2.1.2 主要功能
- **合同创建 (ZRED0040A)**: 新建返利合同，支持参考创建和次年预估功能
- **合同修改 (ZRED0040B)**: 修改现有合同信息，包括基本信息和商品组
- **合同显示 (ZRED0040C)**: 查看合同详细信息，只读模式展示所有数据

#### 2.1.3 核心字段详解
**基本信息字段：**
- `ZHT_ID`: 合同编号，系统自动生成（格式：ZRE0009+序号）
- `ZHT_TXT`: 合同描述，必填字段
- `ZHTLX`: 合同类型，关联域值表ZREM_ZHTLX
- `ZBUKRS`: 合同主体公司，必须是有效的公司代码
- `ZHTYEAR`: 合同年度，默认当前年度
- `ZBEGIN/ZEND`: 合同有效期，开始日期不能大于结束日期

**伙伴信息字段：**
- `ZBPTYPE`: 伙伴类型 (S-供应商, M-客户)
- `ZBPCODE`: 伙伴编码，根据伙伴类型验证
- `ZLXR`: 联系人
- `ZLXFS`: 联系方式

**业务控制字段：**
- `EKGRP`: 采购组，可选字段
- `ZDFFS`: 兑付方式 (O-其他)
- `ZFLZFF`: 支付方，必填字段
- `ZPAYDAY`: 付款期间
- `ZHSTYPE`: 核算类型 (A-年度, B-期间)
- `ZHSZQ`: 核算周期，默认1M
- `ZJSZQ`: 结算周期，根据核算类型自动设置

**特殊功能字段：**
- `ZCNYGSG`: 次年预估标识
- `ZCNYGHT_ID`: 次年预估参考合同号
- `ZHTID`: 关联合同号（来自ZRED0065）

#### 2.1.4 业务规则详解
1. **合同编号生成规则**：
   - 使用编号范围ZRE0009自动生成
   - 格式：年度+序号，确保唯一性

2. **数据验证规则**：
   - 合同主体必须存在于T001表
   - 伙伴编码根据伙伴类型验证（供应商验证LFA1，客户验证BUT000）
   - 采购组如填写必须存在于T024表
   - 支付方必须未被冻结或删除

3. **权限控制规则**：
   - 基于合同类型的权限检查（ZREAR006）
   - 基于公司代码的权限检查（ZREAR007）
   - 基于采购组的权限检查（ZREAR008）

4. **日期控制规则**：
   - 只允许创建指定日期之后的合同
   - 开始日期不能大于结束日期
   - 合同年度自动从开始日期提取

5. **重复性检查**：
   - 对于特定合同类型（0003/0004），检查同一伙伴在同一时间段内是否已有合同

#### 2.1.5 商品组管理
合同支持商品组管理功能，通过商品组定义返利适用的商品范围：

**商品组结构：**
- 商品组头表（ZRET0009）：定义商品组基本信息
- 商品组明细表（ZRET0020）：具体的商品清单

**商品组操作：**
- 新增商品组：创建新的商品分组
- 编辑商品组：修改商品组信息和商品清单
- 删除商品组：删除不再使用的商品组（需检查是否被条款引用）
- 商品组详情：查看商品组的详细信息

**Excel集成：**
- 支持从Excel粘贴商品数据
- 提供商品选择ALV界面
- 自动验证商品主数据有效性

#### 2.1.6 界面交互特性
- **多标签页设计**：基本信息、商品组管理分别在不同标签页
- **动态屏幕控制**：根据操作类型（创建/修改/显示）动态控制字段可编辑性
- **智能默认值**：自动设置合同年度、核算周期等默认值
- **实时验证**：输入时即时验证数据有效性
- **搜索帮助集成**：关键字段提供F4搜索帮助

### 2.2 返利条款管理模块 (ZRED0041)

#### 2.2.1 功能概述
返利条款管理是系统的核心模块，负责定义具体的返利规则、条件和计算方式。该模块支持多种返利类型，包括计算类、固定类、促销类等，并提供完整的审批流程管理。

#### 2.2.2 主要功能
- **条款创建 (ZRED0041A)**: 新建返利条款，支持参考创建和模板应用
- **条款修改 (ZRED0041B)**: 修改条款内容，包括基本信息和协议明细
- **条款显示 (ZRED0041C)**: 查看条款详情，只读模式展示所有信息
- **条款审批 (ZRED0041D)**: 审批流程处理，支持批量审批
- **新增行项目 (ZRED0041E)**: 为已审批条款添加新的协议明细
- **批量调整 (ZRED0041F)**: 固定协议预付返利批量调整

#### 2.2.3 核心字段详解
**基本信息字段：**
- `ZTK_ID`: 条款编号，系统自动生成
- `ZTK_TXT`: 条款描述，必填字段
- `ZHT_ID`: 所属合同编号，关联ZRETA001
- `ZFLLX`: 返利类型，关联ZRET0002配置表
- `ZXYBSTYP`: 协议类型 (A-任务, T-任务, V-任务, P-促销, F-固定, Q-固定)
- `ZTKTYPE`: 是否附加条款标识
- `ZTMPID`: 组织模板编号，关联ZRETC001

**时间控制字段：**
- `ZBEGIN/ZEND`: 条款有效期
- `ZFLDFSJ`: 返利兑付时间

**审批控制字段：**
- `ZXYZT`: 协议状态 (草稿/审批中/已审批/已作废)
- `FRGSX`: 审批策略，系统自动确定
- `KOLNR`: 当前审批步骤
- `FRGC1`: 审批代码

**业务控制字段：**
- `ZSPZ_ID`: 任务商品组
- `ZFLSPZ_ID`: 返利商品组
- `ZCLRID`: 处理规则ID
- `ZLEIB`: 类别标识 (R-授权类)
- `EKGRP`: 采购组

#### 2.2.4 业务规则详解
1. **审批流程规则**：
   - 根据合同类型、公司代码、采购组、返利类型、条款类型自动确定审批策略
   - 使用ZREFM0025函数获取审批策略和审批步骤
   - 支持多级审批，每个步骤需要相应权限的用户审批

2. **状态控制规则**：
   - 草稿状态：可以修改所有信息
   - 审批中状态：不可修改，只能进行审批操作
   - 已审批状态：基本信息不可修改，可以新增协议明细
   - 已作废状态：不可进行任何操作

3. **数据验证规则**：
   - 条款描述、组织模板、支付方为必填字段
   - 开始日期不能大于结束日期
   - 商品组必须存在且有效
   - 处理规则必须配置正确

4. **权限控制规则**：
   - 基于合同类型的权限检查（ZREAR006）
   - 基于公司代码的权限检查（ZREAR007）
   - 基于采购组的权限检查（ZREAR008）
   - 协议级别的权限检查（ZREAR009）

#### 2.2.5 协议管理功能
条款下可以创建多个协议，每个协议定义具体的执行细节：

**协议类型支持：**
- **动态协议（ZRET0007）**：基于业务数据动态计算返利
- **固定协议（ZRET0008）**：固定金额或数量的返利

**协议字段：**
- `ZXY_ID`: 协议编号，系统自动生成
- `ZXYZT`: 协议状态，独立于条款状态
- `ZJE`: 协议金额
- `ZSL`: 协议数量
- `ZBUKRS`: 适用公司代码
- `ZFLSQF`: 返利申请方

**协议操作：**
- 新增协议：为条款添加新的执行协议
- 修改协议：调整协议的执行参数
- 删除协议：删除不再需要的协议
- 协议审批：独立的协议级审批流程

#### 2.2.6 附加条款功能
支持为主条款添加附加条款，实现复杂的业务逻辑：

**附加条款类型：**
- 基于ZRETA003表管理
- 支持多种附加条款类型（ZATKTP）
- 可设置附加条款规则（ZATKRL）
- 支持条件分组（ZCTGR）

**附加条款操作：**
- 添加附加条款：为条款增加额外的业务规则
- 修改附加条款：调整附加条款的参数
- 删除附加条款：移除不需要的附加规则

#### 2.2.7 促销返利特殊功能
对于促销类返利（ZXYBSTYP = 'P'），系统提供特殊的管理功能：

**返利阶梯管理（ZRETA005）：**
- 支持多级返利阶梯设置
- 可设置不同的返利比例或金额
- 支持基于销售额、数量等不同维度的阶梯

**生效日期管理（ZRETA006）：**
- 灵活的生效日期设置
- 支持多个生效日期
- 可按日期分段设置不同的返利规则

**商品阶梯管理（ZRET0058）：**
- 商品级别的阶梯返利
- 支持不同商品不同阶梯
- 与返利阶梯联动计算

#### 2.2.8 批量处理功能
系统提供强大的批量处理能力：

**Excel导入功能：**
- 支持固定条款批量导入创建
- 支持固定条款批量导入新增协议
- 支持条款批量修改
- 提供Excel模板下载

**批量操作选项：**
- 批量创建：一次性创建多个条款
- 批量修改：批量更新条款信息
- 批量审批：批量处理审批流程
- 不需审批选项：直接设置为审批通过状态

**数据验证：**
- Excel数据格式验证
- 业务规则验证
- 重复数据检查
- 错误信息详细提示

#### 2.2.9 界面交互特性
**多模式操作：**
- 创建模式：支持参考创建和模板创建
- 修改模式：可修改未审批的条款
- 显示模式：只读查看所有信息
- 审批模式：专门的审批界面

**动态屏幕控制：**
- 根据返利类型动态显示相关字段
- 根据协议类型控制界面布局
- 根据用户权限控制操作按钮
- 实时验证和错误提示

**ALV集成：**
- 条款列表ALV显示
- 协议明细ALV编辑
- 附加条款ALV管理
- 支持热点点击和双击事件

### 2.3 返利协议管理模块

#### 2.3.1 ZRED0056 - 促销返利条款批量导入
**功能概述：**
促销返利条款批量导入程序，支持通过Excel文件批量创建促销返利条款和新增协议。

**主要功能：**
- **批量创建条款**：通过Excel模板批量创建促销返利条款
- **批量新增协议**：为现有条款批量新增协议明细
- **数据验证**：完整的数据格式和业务规则验证
- **错误处理**：详细的错误信息提示和处理建议

**核心特性：**
- 支持Excel文件上传和解析
- 自动数据转换和验证
- 批量数据处理和保存
- 权限检查和审批流程集成

**配置管理：**
- 公司代码配置表维护（ZRETCM05）
- Excel模板下载功能
- 批导参数配置

#### 2.3.2 ZRED0057 - 返利协议维护
**功能概述：**
返利协议维护程序，提供协议数据的新增、修改、显示等维护功能。

**主要功能：**
- 协议数据的CRUD操作
- 可编辑ALV网格显示
- 数据变更事件处理
- 集成业务验证规则

#### 2.3.3 ZRED0059 - 返利协议处理
**功能概述：**
返利协议的特殊处理程序，包含复杂的业务逻辑处理和计算功能。

**主要功能：**
- 协议数据的特殊处理逻辑
- 集成返利计算功能组
- 支持复杂的业务规则处理
- 数据同步和更新功能

### 2.4 返利查询报表模块

#### 2.4.1 ZRER0013 - 返利条款审批查询
**功能概述：**
返利条款审批查询程序，提供条款审批的批量查询和处理功能。

**主要功能：**
- **条款查询**：基于多维度条件查询待审批条款
- **批量审批**：支持批量审批通过和审批拒绝操作
- **权限控制**：基于审批代码进行严格的权限检查
- **状态跟踪**：实时显示审批状态和处理结果

**查询条件：**
- 合同编号范围（s_zht_id）
- 合同类型（s_zhtlx）
- 合同主体公司（s_zbukrs）
- 伙伴类型和编码（s_bptype, s_bpcode）
- 条款编号（s_ztk_id）
- 返利类型（s_zfllx）
- 协议类型（s_zxybtp）
- 采购组（s_ekgrp）
- 条款状态（p_zxyzt）：必填
- 审批策略（p_frgsx）：必填
- 审批代码（p_frgc1）：必填

**显示内容：**
- 合同基本信息：合同号、描述、类型、主体公司
- 伙伴信息：伙伴类型、编码、名称
- 条款信息：条款号、描述、返利类型、协议类型
- 审批信息：审批策略、审批代码、审批状态
- 业务信息：采购组、创建人、次年预估标识

**权限控制：**
- 合同类型权限检查（ZREAR006）
- 采购组权限检查（ZREAR008）
- 公司代码权限检查（ZREAR007）
- 基于审批代码的操作权限控制

**批量操作：**
- 全选/取消全选功能
- 批量审批通过（仅支持固定类/促销类协议）
- 批量审批拒绝
- BDC调用ZRED0041D执行具体审批
- 详细的操作结果反馈

**特殊功能：**
- 支持供应商和客户的F4搜索帮助
- 实时状态更新和刷新
- 错误信息收集和展示
- 审批结果消息列表显示

#### 2.4.2 ZRER0103 - 返利合同查询
**功能概述：**
返利合同查询报表程序，提供合同数据的查询和展示功能。

**主要功能：**
- 合同数据查询和展示
- 多维度筛选和排序
- ALV报表输出
- 数据导出功能

### 2.5 返利数据处理模块

#### 2.5.1 ZRED0063 - 返利数据统计
**功能概述：**
返利数据统计程序，提供返利协议数据的统计分析功能。

**主要功能：**
- 返利协议数据的统计分析
- 支持多维度数据查询
- 提供月结锁定期间控制
- 数据汇总和报表输出

#### 2.5.2 ZRED0072 - 返利数据处理
**功能概述：**
返利数据处理程序，提供返利相关数据的批量处理功能。

**主要功能：**
- 返利相关数据的批量处理
- 数据清理和整理功能
- 数据同步和更新
- 异常数据处理

## 3. 技术特性

### 3.1 数据结构设计

#### 3.1.1 核心表结构
**主表关系层次：**
```
ZRETA001 (返利合同)
    ├── ZRETA002 (返利条款) [1:N]
    │   ├── ZRET0006 (返利协议) [1:N]
    │   │   ├── ZRET0007 (动态协议明细) [1:N]
    │   │   └── ZRET0008 (固定协议明细) [1:N]
    │   ├── ZRETA003 (附加条款) [1:N]
    │   ├── ZRETA005 (返利阶梯) [1:N]
    │   └── ZRET0058 (商品阶梯) [1:N]
    └── ZRET0009 (商品组) [1:N]
        └── ZRET0020 (商品明细) [1:N]
```

**配置表体系：**
- **ZRET0002**: 返利类型配置
- **ZRET0003**: 核算基准配置
- **ZRET0004**: 结算周期配置
- **ZRET0005**: 核算周期配置
- **ZRETC001**: 组织模板配置
- **ZRETC007**: 审批策略配置
- **ZRETC009**: 协议类型配置

#### 3.1.2 数据完整性设计
- **外键约束**: 确保数据关联的完整性
- **状态控制**: 通过状态字段控制数据流转
- **审计字段**: 创建人、创建时间、修改人、修改时间
- **软删除**: 通过状态标识实现软删除

### 3.2 权限控制体系

#### 3.2.1 权限对象设计
- **ZREAR006**: 合同类型权限控制
  - ZHTLX: 合同类型
  - ACTVT: 活动类型（01-创建, 02-修改, 03-显示）

- **ZREAR007**: 公司代码权限控制
  - BUKRS: 公司代码
  - ACTVT: 活动类型

- **ZREAR008**: 采购组权限控制
  - EKGRP: 采购组
  - ACTVT: 活动类型

- **ZREAR009**: 协议权限控制
  - BUKRS: 公司代码
  - ACTVT: 活动类型

#### 3.2.2 权限检查机制
- **多层权限验证**: 合同、条款、协议三级权限检查
- **操作权限控制**: 基于审批代码的操作权限
- **数据访问控制**: 基于组织架构的数据可见性
- **动态权限检查**: 运行时动态验证用户权限

### 3.3 审批流程引擎

#### 3.3.1 审批策略配置
- **自动策略确定**: 基于合同类型、公司代码、采购组、返利类型、条款类型自动确定审批策略
- **ZREFM0025函数**: 核心的审批策略获取函数
- **多维度配置**: 支持复杂的业务规则配置

#### 3.3.2 审批流程控制
- **状态驱动**: 基于状态机模式的流程控制
- **步骤管理**: 支持多步骤审批流程
- **权限验证**: 每个审批步骤的权限验证
- **并行审批**: 支持协议级别的并行审批

#### 3.3.3 审批操作功能
- **单个审批**: 逐个处理审批请求
- **批量审批**: 批量处理多个审批请求
- **审批拒绝**: 支持审批拒绝和退回
- **审批日志**: 完整的审批操作日志记录

### 3.4 集成特性

#### 3.4.1 Excel集成功能
- **模板下载**: 提供标准化的Excel导入模板
- **数据解析**: 支持多种Excel格式的数据解析
- **批量导入**: 大批量数据的高效导入处理
- **错误处理**: 详细的导入错误信息和处理建议

#### 3.4.2 搜索帮助集成
- **标准搜索帮助**: 集成SAP标准的搜索帮助
- **自定义搜索帮助**: 业务特定的搜索帮助
- **动态值列表**: 基于上下文的动态值列表
- **F4支持**: 完整的F4搜索帮助支持

#### 3.4.3 消息处理机制
- **统一消息框架**: 使用SAP标准消息框架
- **多语言支持**: 支持多语言消息显示
- **消息分类**: 错误、警告、信息、成功消息分类
- **消息列表**: 批量操作的消息列表显示

#### 3.4.4 事务集成
- **BDC调用**: 使用BDC技术调用其他事务
- **函数调用**: 集成业务函数和计算逻辑
- **工作流集成**: 与SAP工作流的集成
- **接口集成**: 与外部系统的接口集成

## 4. 用户界面设计

### 4.1 选择屏幕设计

#### 4.1.1 布局设计原则
- **分块布局**: 逻辑相关的字段分组显示
- **层次结构**: 主要参数和次要参数分层展示
- **用户友好**: 直观的字段标签和帮助文本
- **响应式设计**: 根据用户操作动态调整界面

#### 4.1.2 动态控制机制
- **字段可见性**: 根据业务逻辑动态显示/隐藏字段
- **字段可编辑性**: 根据操作模式控制字段是否可编辑
- **必填字段控制**: 动态设置必填字段
- **默认值设置**: 智能的默认值设置

#### 4.1.3 用户交互功能
- **用户命令响应**: 实时响应用户的选择和输入
- **功能键支持**: 丰富的功能键快捷操作
- **搜索帮助**: 关键字段的F4搜索帮助
- **值列表**: 下拉列表和值域检查

### 4.2 ALV显示设计

#### 4.2.1 ALV网格功能
- **可编辑网格**: 支持单元格级别的编辑
- **排序和筛选**: 多列排序和高级筛选
- **批量操作**: 支持多行选择和批量操作
- **热点点击**: 关键字段的热点点击功能

#### 4.2.2 ALV事件处理
- **数据变更事件**: 实时处理数据变更
- **工具栏事件**: 自定义工具栏按钮事件
- **双击事件**: 行双击和单元格双击事件
- **用户命令事件**: 用户操作命令处理

#### 4.2.3 ALV布局控制
- **字段目录**: 动态字段目录配置
- **布局设置**: 列宽、对齐、格式设置
- **变式管理**: 用户个性化变式保存
- **导出功能**: 数据导出到Excel等格式

### 4.3 多标签页设计

#### 4.3.1 标签页组织
- **基本信息标签**: 主要业务数据录入
- **明细信息标签**: 详细数据和配置
- **附加信息标签**: 扩展信息和特殊设置
- **历史信息标签**: 变更历史和审批记录

#### 4.3.2 标签页交互
- **动态标签**: 根据业务逻辑动态显示标签
- **数据联动**: 标签页间的数据联动更新
- **验证控制**: 跨标签页的数据验证
- **状态同步**: 标签页状态的同步更新

## 5. 业务流程

### 5.1 标准业务流程

#### 5.1.1 返利合同创建流程
```
1. 用户登录系统，选择合同创建功能
2. 填写合同基本信息（合同类型、主体公司、伙伴信息等）
3. 设置合同有效期和业务参数
4. 配置商品组（可选）
5. 数据验证和权限检查
6. 保存合同，系统自动生成合同编号
7. 合同创建完成，可进行后续条款创建
```

#### 5.1.2 返利条款创建流程
```
1. 选择已创建的返利合同
2. 填写条款基本信息（条款描述、返利类型、协议类型等）
3. 设置条款有效期和业务规则
4. 配置组织模板和处理规则
5. 添加协议明细（动态协议或固定协议）
6. 设置附加条款（可选）
7. 配置返利阶梯（促销类条款）
8. 数据验证和业务规则检查
9. 保存条款，系统自动确定审批策略
10. 提交审批流程
```

#### 5.1.3 返利条款审批流程
```
1. 系统根据业务规则自动确定审批策略
2. 条款进入审批队列，状态变为"审批中"
3. 审批人员查询待审批条款
4. 审批人员审查条款内容和业务合理性
5. 执行审批操作（通过/拒绝）
6. 系统更新条款状态和审批步骤
7. 如有多级审批，继续下一审批步骤
8. 最终审批完成，条款状态变为"已审批"
9. 触发后续业务处理（数据计算、门店商品清单更新等）
```

#### 5.1.4 返利协议管理流程
```
1. 基于已审批的条款创建协议
2. 设置协议的具体执行参数
3. 配置适用的组织范围
4. 设置协议明细和计算规则
5. 协议数据验证和检查
6. 提交协议审批（如需要）
7. 协议生效，开始执行返利计算
```

### 5.2 特殊业务流程

#### 5.2.1 批量导入流程
```
1. 下载Excel导入模板
2. 按模板格式准备数据
3. 上传Excel文件到系统
4. 系统解析和验证Excel数据
5. 显示验证结果和错误信息
6. 修正错误数据（如有）
7. 确认导入，系统批量创建数据
8. 显示导入结果和处理日志
```

#### 5.2.2 次年预估流程
```
1. 选择参考合同和条款
2. 设置次年预估参数
3. 系统复制参考数据
4. 调整次年业务参数
5. 验证和保存次年预估数据
6. 提交审批流程
```

#### 5.2.3 条款修改流程
```
1. 检查条款当前状态
2. 验证修改权限
3. 锁定条款数据
4. 修改条款信息
5. 重新验证业务规则
6. 保存修改，更新审批状态
7. 如需要，重新提交审批
```

## 6. 系统配置

### 6.1 基础配置

#### 6.1.1 返利类型配置（ZRET0002）
- **配置内容**: 返利类型代码、描述、排序ID
- **业务意义**: 定义系统支持的返利类型
- **配置示例**: RB01-任务返利, RB02-固定返利, RB03-促销返利等
- **维护方式**: 通过SM30维护视图进行配置

#### 6.1.2 协议类型配置（ZRETC009）
- **配置内容**: 协议类型代码、返利类型关联、业务规则
- **业务意义**: 定义协议的业务类型和处理规则
- **配置示例**: A-任务协议, F-固定协议, P-促销协议等
- **关联关系**: 与返利类型建立关联关系

#### 6.1.3 核算基准配置（ZRET0003）
- **配置内容**: 核算基准代码、描述、计算规则标识
- **业务意义**: 定义返利计算的基准数据来源
- **配置示例**: 采购金额、销售金额、分销金额等
- **计算标识**: 最低值、最高值、采购、分销、销售等标识

#### 6.1.4 周期配置
- **结算周期（ZRET0004）**: 定义返利结算的时间周期
- **核算周期（ZRET0005）**: 定义返利核算的时间周期
- **配置内容**: 周期代码、描述、时间长度
- **业务应用**: 用于控制返利的计算和结算时间

### 6.2 权限配置

#### 6.2.1 权限对象配置
- **ZREAR006**: 合同类型权限对象
  - 字段ZHTLX: 合同类型授权
  - 字段ACTVT: 活动类型授权（01-创建, 02-修改, 03-显示）

- **ZREAR007**: 公司代码权限对象
  - 字段BUKRS: 公司代码授权
  - 字段ACTVT: 活动类型授权

- **ZREAR008**: 采购组权限对象
  - 字段EKGRP: 采购组授权
  - 字段ACTVT: 活动类型授权

- **ZREAR009**: 协议权限对象
  - 字段BUKRS: 公司代码授权
  - 字段ACTVT: 活动类型授权

#### 6.2.2 权限角色设计
- **返利管理员角色**: 拥有所有功能的完整权限
- **返利操作员角色**: 拥有创建和修改权限，无审批权限
- **返利审批员角色**: 拥有审批权限，可查看和审批条款
- **返利查询员角色**: 只有查询和显示权限

### 6.3 审批策略配置

#### 6.3.1 审批策略表（ZRETC007）
- **配置维度**:
  - 审批策略代码（FRGSX）
  - 审批步骤（KOLNR）
  - 审批代码（FRGC1）
  - 审批描述（ZFRGTX）

- **策略确定规则**:
  - 基于合同类型、公司代码、采购组、返利类型、条款类型
  - 使用ZREFM0025函数自动确定审批策略

- **审批流程控制**:
  - 支持多级审批流程
  - 每个步骤可设置不同的审批权限
  - 支持并行和串行审批模式

#### 6.3.2 审批规则配置（ZRES0042）
- **配置内容**: 审批规则的详细参数
- **规则维度**: 合同类型、公司代码、采购组、返利类型、条款类型
- **输出结果**: 审批策略代码
- **维护方式**: 通过配置表维护审批规则

### 6.4 组织模板配置

#### 6.4.1 模板主表（ZRETC001）
- **配置内容**:
  - 模板ID（ZTMPID）
  - 模板描述（ZTMPTXT）
  - 适用公司代码（ZBUKRS）
  - 适用合同类型（ZHTLX）
  - 条款类型（ZTKTYPE）
  - 模板类型（ZTMPTY）

- **业务意义**: 定义返利条款的组织结构模板
- **应用场景**: 条款创建时选择适用的组织模板

#### 6.4.2 模板明细表（ZRETC002）
- **配置内容**:
  - 模板ID（ZTMPID）
  - 行项目号（ZITEMS）
  - 组织类型和代码
  - 状态标识（ZSTATUS）

- **组织类型**: 公司代码、工厂、销售组织、分销渠道等
- **状态控制**: 有效、无效、删除等状态

## 7. 详细功能说明

### 7.1 合同管理功能详解

#### 7.1.1 合同创建功能（ZRED0040A）
**操作步骤：**
1. 进入合同创建界面
2. 选择合同类型（必填）
3. 选择合同主体公司（必填）
4. 填写伙伴信息（伙伴类型、伙伴编码）
5. 设置合同有效期
6. 填写业务参数（采购组、支付方、付款期间等）
7. 设置核算和结算参数
8. 保存合同

**验证规则：**
- 合同类型必须存在于配置表
- 合同主体必须是有效的公司代码
- 伙伴编码根据伙伴类型验证存在性
- 开始日期不能大于结束日期
- 支付方不能是冻结或删除状态
- 采购组如填写必须存在

**特殊功能：**
- 次年预估功能：可参考历史合同创建次年预估合同
- 参考创建功能：可参考现有合同创建新合同
- 自动编号：系统自动生成合同编号
- 默认值设置：智能设置默认值

#### 7.1.2 合同修改功能（ZRED0040B）
**操作步骤：**
1. 输入要修改的合同编号
2. 系统加载合同数据
3. 修改需要变更的字段
4. 保存修改

**修改限制：**
- 已有条款的合同某些字段不可修改
- 需要相应的修改权限
- 修改后需要重新验证业务规则

#### 7.1.3 合同显示功能（ZRED0040C）
**显示内容：**
- 合同基本信息
- 伙伴信息详情
- 商品组信息
- 关联的条款列表
- 审计信息（创建人、创建时间、修改人、修改时间）

**界面特性：**
- 只读模式，所有字段不可编辑
- 支持数据导出
- 提供打印功能
- 状态指示

### 4.3 多标签页
- 信息分类展示
- 动态子屏幕
- 表格控制
- 数据联动

## 5. 业务流程

### 5.1 标准流程
1. **合同创建** -> **条款创建** -> **条款审批** -> **协议生成** -> **执行监控**

### 5.2 特殊流程
- **批量导入流程**: Excel模板 -> 数据验证 -> 批量创建/修改
- **审批流程**: 提交审批 -> 多级审批 -> 状态更新
- **调整流程**: 协议调整 -> 重新审批 -> 生效执行

## 6. 系统配置

### 6.1 基础配置
- 返利类型配置
- 协议类型配置
- 模板配置
- 审批策略配置

### 6.2 权限配置
- 用户角色分配
- 权限对象配置
- 审批权限设置

### 6.3 集成配置
- 搜索帮助配置
- 消息类配置
- 编号范围配置

## 7. 详细功能说明

### 7.1 返利合同创建流程

#### 7.1.1 操作步骤
1. 执行事务码 ZRED0040A 或选择"新增"单选按钮
2. 选择合同类型 (ZHTLX)
3. 输入合同主体公司 (ZBUKRS)
4. 系统自动生成合同编号 (ZHT_ID)
5. 填写合同基本信息
6. 设置合同有效期
7. 选择伙伴类型和伙伴编码
8. 保存合同

#### 7.1.2 验证规则
- 合同主体必须是有效的公司代码
- 伙伴编码必须与伙伴类型匹配
- 合同有效期不能为空
- 采购组必须存在于系统中

#### 7.1.3 特殊功能
- **次年预估**: 勾选"次年预估"复选框，可参考历史合同创建次年预估合同
- **模板复制**: 可基于现有合同模板快速创建新合同

### 7.2 返利条款创建流程

#### 7.2.1 操作步骤
1. 执行事务码 ZRED0041A 或选择"新增"单选按钮
2. 选择返利类型 (ZFLLX)
3. 选择协议类型 (ZXYBSTYP)
4. 输入或选择返利合同 (ZHT_ID)
5. 选择模板 (ZTMPID) 或选择参考创建
6. 填写条款详细信息
7. 设置条款有效期和计算规则
8. 保存条款

#### 7.2.2 协议类型说明
- **F - 固定类**: 固定金额或比例的返利
- **P - 促销类**: 基于促销活动的返利
- **C - 计算类**: 基于复杂计算规则的返利

#### 7.2.3 审批流程
1. 条款创建后状态为"草稿"
2. 提交审批后状态变为"审批中"
3. 根据审批策略进行多级审批
4. 审批通过后状态变为"已审批"
5. 可以拒绝审批，状态变为"已拒绝"

### 7.3 批量导入功能

#### 7.3.1 支持的导入类型
- **固定条款批量创建**: 批量创建固定类型的返利条款
- **固定条款批量新增协议**: 为现有条款批量新增协议
- **条款批量修改**: 批量修改现有条款信息
- **固定协议批量调整**: 批量调整固定协议的金额等信息

#### 7.3.2 操作流程
1. 选择相应的导入类型
2. 下载对应的Excel模板
3. 按模板格式填写数据
4. 上传Excel文件
5. 系统进行数据验证
6. 确认无误后执行批量操作

#### 7.3.3 数据验证
- 必填字段检查
- 数据格式验证
- 业务规则验证
- 重复数据检查
- 权限验证

## 8. 错误处理和验证

### 8.1 常见错误类型
- **数据验证错误**: 必填字段为空、格式不正确等
- **业务规则错误**: 违反业务逻辑规则
- **权限错误**: 用户无相应操作权限
- **系统错误**: 数据库连接、系统配置等问题

### 8.2 错误处理机制
- 实时验证和提示
- 批量操作错误汇总
- 详细错误信息显示
- 错误日志记录

### 8.3 数据完整性检查
- 主外键关系检查
- 数据一致性验证
- 状态流转控制
- 并发操作控制

## 9. 性能优化

### 9.1 查询优化
- 合理使用索引
- 分页查询
- 条件筛选优化
- 缓存机制

### 9.2 批量处理优化
- 分批处理大数据量
- 异步处理机制
- 进度显示
- 错误恢复

## 10. 系统维护

### 10.1 日常维护
- 数据备份
- 日志清理
- 性能监控
- 用户权限审查

### 10.2 配置维护
- 基础数据维护
- 审批策略调整
- 权限配置更新
- 系统参数优化

## 11. 事务码清单

### 11.1 返利合同管理
- **ZRED0040A**: 返利合同创建
- **ZRED0040B**: 返利合同修改
- **ZRED0040C**: 返利合同显示

### 11.2 返利条款管理
- **ZRED0041A**: 返利条款创建
- **ZRED0041B**: 返利条款修改
- **ZRED0041C**: 返利条款显示
- **ZRED0041D**: 返利条款审批
- **ZRED0041E**: 返利条款新增行项目
- **ZRED0041F**: 固定协议批量调整

### 11.3 返利协议管理
- **ZRED0056**: 返利协议查询
- **ZRED0057**: 返利协议维护
- **ZRED0059**: 返利协议处理

### 11.4 返利查询报表
- **ZRER0103**: 返利合同查询
- **ZRER0013**: 返利条款审批查询

### 11.5 返利数据处理
- **ZRED0063**: 返利数据统计
- **ZRED0072**: 返利数据处理

## 12. 关键表结构

### 12.1 主要业务表
- **ZRETA001**: 返利合同主表
- **ZRETA002**: 返利条款主表
- **ZRETA003**: 返利条款附加信息表
- **ZRETA004**: 返利条款附加明细表
- **ZRET0006**: 返利协议主表
- **ZRET0007**: 返利协议明细表(动态)
- **ZRET0008**: 返利协议明细表(固定)

### 12.2 配置表
- **ZRET0002**: 返利类型配置
- **ZRET0003**: 核算基准配置
- **ZRET0004**: 计算周期配置
- **ZRET0005**: 核算周期配置
- **ZRETC001**: 返利模板配置
- **ZRETC007**: 审批策略配置
- **ZRETC009**: 协议类型配置

### 12.3 组织结构表
- **ZRET0012**: 渠道供应商表
- **ZRET0013**: 外部供货方表
- **ZRET0014**: 协议组织结构表
- **ZRET0015**: 核算期间表

## 13. 搜索帮助清单

### 13.1 主要搜索帮助
- **ZRESH0016**: 返利合同搜索帮助
- **ZRESH0018**: 返利条款搜索帮助
- **ZRESH0005**: 返利协议搜索帮助
- **ZRESH0011**: 返利类型搜索帮助
- **ZRESH0012**: 返利模板搜索帮助
- **ZRESH0006**: 商品组搜索帮助

### 13.2 标准搜索帮助
- **C_T001**: 公司代码搜索帮助
- **H_T024**: 采购组搜索帮助

## 14. 权限对象清单

### 14.1 返利专用权限对象
- **ZREAR006**: 返利合同类型权限
- **ZREAR007**: 返利公司权限
- **ZREAR008**: 返利采购组权限

### 14.2 权限字段说明
- **ZHTLX**: 合同类型权限控制
- **BUKRS**: 公司代码权限控制
- **EKGRP**: 采购组权限控制
- **ACTVT**: 活动类型权限控制

## 15. 功能增强点

### 15.1 已实现的增强
- Excel批量导入功能
- 次年预估功能
- 附加条款功能
- 批量审批功能
- 月结锁定控制

### 15.2 可扩展功能
- 移动端支持
- 工作流集成
- 报表自定义
- 数据分析功能
- 接口集成

## 16. 注意事项

### 16.1 操作注意事项
1. 审批通过的条款不能随意修改
2. 批量导入前务必验证数据格式
3. 权限设置要严格按照业务需求
4. 定期备份重要业务数据

### 16.2 系统限制
1. 单次批量导入建议不超过1000条记录
2. 审批流程最多支持10级审批
3. 附加条款最多支持99个
4. 协议有效期不能超过5年

### 16.3 最佳实践
1. 建议使用模板创建标准化条款
2. 定期清理无效的草稿数据
3. 合理设置审批策略避免流程冗长
4. 充分利用搜索帮助提高操作效率

## 17. 常见问题解答

### 17.1 操作问题
**Q: 为什么无法修改已审批的条款？**
A: 已审批的条款处于锁定状态，需要先撤销审批或创建新版本。

**Q: 批量导入失败如何处理？**
A: 检查Excel格式是否正确，数据是否符合验证规则，确认用户权限是否充足。

**Q: 如何查看审批历史？**
A: 在条款显示界面可以查看完整的审批历史记录。

### 17.2 配置问题
**Q: 如何新增返利类型？**
A: 在ZRET0002表中维护新的返利类型配置。

**Q: 审批策略如何配置？**
A: 通过ZRETC007表配置审批策略，包括审批步骤和权限设置。

### 17.3 权限问题
**Q: 用户无法看到某些合同怎么办？**
A: 检查用户的ZREAR006、ZREAR007、ZREAR008权限对象配置。

**Q: 如何设置审批权限？**
A: 通过ZREAR006等权限对象的ACTVT字段控制审批权限。

---

**文档版本**: 1.0
**最后更新**: 2024年
**编写人**: 系统分析师
**审核人**: 技术负责人