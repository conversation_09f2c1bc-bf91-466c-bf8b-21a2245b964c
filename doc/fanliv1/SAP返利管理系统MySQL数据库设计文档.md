# SAP返利管理系统MySQL数据库设计文档

## 1. 概述

本文档基于SAP返利管理系统的深度代码分析，完整梳理了所有业务表结构，并提供了详细的MySQL建表语句。系统采用分层设计，包含核心业务表、配置表、辅助表和审计表，完整支持返利业务流程管理。

## 2. 数据库设计原则

### 2.1 设计原则
- **保持原有结构**: 与SAP原表结构保持一致，便于系统迁移
- **规范化设计**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理设置索引，优化查询性能
- **数据完整性**: 通过外键约束保证数据一致性
- **审计追踪**: 完整的创建和修改记录

### 2.2 命名规范
- 表名保持与SAP原表名一致(如ZRETA001)
- 字段名采用SAP原字段名，保持业务连续性
- 主键使用业务主键，与SAP保持一致
- 索引命名采用idx_前缀

## 3. 核心业务表

### 3.1 返利合同主表 (zreta001)

```sql
-- =====================================================
-- 返利合同主表 - 基于ZRETA001表结构深度分析
-- =====================================================
CREATE TABLE zreta001 (
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zhtlx VARCHAR(4) NOT NULL COMMENT '合同类型',
    zbukrs VARCHAR(4) NOT NULL COMMENT '公司代码',zjt
    zht_txt VARCHAR(40) NOT NULL COMMENT '合同描述',
    zhtid VARCHAR(35) COMMENT '关联合同号',
    zbptype VARCHAR(1) NOT NULL COMMENT '伙伴类型(S=供应商,M=经销商)',
    zbpcode VARCHAR(10) NOT NULL COMMENT '伙伴代码',
    ekgrp VARCHAR(3) COMMENT '采购组',
    zhtyear VARCHAR(4) NOT NULL COMMENT '签署年度',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    ztmpid VARCHAR(10) NOT NULL COMMENT '组织模板',
    zhstype VARCHAR(1) DEFAULT 'A' COMMENT '核算类型(A=年度,B=期间)',
    zhszq VARCHAR(3) DEFAULT '1M' COMMENT '核算周期',
    zjszq VARCHAR(3) COMMENT '结算周期',
    zdffs VARCHAR(1) DEFAULT 'O' COMMENT '兑付方式(O=线下,N=线上)',
    zflzff VARCHAR(10) NOT NULL COMMENT '支付方',
    zpayday VARCHAR(2) COMMENT '付款期间',
    zlxr VARCHAR(30) COMMENT '联系人',
    zlxfs VARCHAR(30) COMMENT '联系方式',
    zcnyght_id VARCHAR(20) COMMENT '次年预估合同ID',
    zcnygsg VARCHAR(1) COMMENT '次年预估标识(X=是)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    
    PRIMARY KEY (zht_id),
    INDEX idx_zbukrs_zhtlx (zbukrs, zhtlx),
    INDEX idx_zbpcode (zbpcode),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_zcjrq (zcjrq),
    INDEX idx_zhtyear (zhtyear),
    INDEX idx_ztmpid (ztmpid)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利合同主表';
```

### 3.2 返利条款主表 (zreta002)

```sql
-- =====================================================
-- 返利条款主表 - 基于ZRETA002表结构深度分析
-- =====================================================
CREATE TABLE zreta002 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zitem_id VARCHAR(6) NOT NULL COMMENT '行项目号',
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型',
    ztktype VARCHAR(1) COMMENT '条款类型(空=普通,P=附加)',
    ztk_txt VARCHAR(40) NOT NULL COMMENT '条款描述',
    zspz_id VARCHAR(20) COMMENT '任务商品组编号',
    zflspz_id VARCHAR(20) COMMENT '返利商品组编号',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    ztmpid VARCHAR(10) NOT NULL COMMENT '组织模板',
    zxyzt VARCHAR(1) DEFAULT 'I' COMMENT '协议状态(I=初始,P=审批中,A=已审批,D=已作废)',
    zclrid VARCHAR(10) COMMENT '计算规则ID',
    zleib VARCHAR(1) COMMENT '类别(R=授权类)',
    zxybstyp VARCHAR(1) NOT NULL COMMENT '协议类型(A=数量,T=金额,V=比例,P=促销,F=固定,Q=定额)',
    zhstype VARCHAR(1) COMMENT '核算类型',
    zhszq VARCHAR(3) COMMENT '核算周期',
    zjszq VARCHAR(3) COMMENT '结算周期',
    zpayday VARCHAR(2) COMMENT '付款期间',
    zfldfsj VARCHAR(2) COMMENT '返利兑付时间',
    frgsx VARCHAR(10) COMMENT '审批策略',
    kolnr VARCHAR(3) DEFAULT '000' COMMENT '当前审批步骤',
    frgc1 VARCHAR(2) COMMENT '审批代码',
    zcnygsg VARCHAR(1) COMMENT '次年预估标识(X=是)',
    zjt_id VARCHAR(20) COMMENT '协议ID',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    
    PRIMARY KEY (ztk_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zfllx (zfllx),
    INDEX idx_zspz_id (zspz_id),
    INDEX idx_zxyzt (zxyzt),
    INDEX idx_zxybstyp (zxybstyp),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_frgsx_kolnr (frgsx, kolnr)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利条款主表';
```

### 3.3 附加条款主表 (zreta003)

```sql
-- =====================================================
-- 附加条款主表 - 基于ZRETA003表结构分析
-- =====================================================
CREATE TABLE zreta003 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zatktp VARCHAR(4) NOT NULL COMMENT '附加条款类型',
    ztk_id_plus VARCHAR(20) COMMENT '附加条款编号',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',
    
    PRIMARY KEY (ztk_id, zitems),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zatktp (zatktp),
    INDEX idx_ztk_id_plus (ztk_id_plus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附加条款主表';
```

### 3.4 附加条款明细表 (zreta004)

```sql
-- =====================================================
-- 附加条款明细表 - 基于ZRETA004表结构分析
-- =====================================================
CREATE TABLE zreta004 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zatkrl VARCHAR(10) NOT NULL COMMENT '附加条款规则',
    zctgr VARCHAR(10) NOT NULL COMMENT '条件组',
    zatkrl_val VARCHAR(50) COMMENT '规则值',
    zctgr_val VARCHAR(50) COMMENT '条件值',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    
    PRIMARY KEY (ztk_id, zitems, zatkrl, zctgr),
    FOREIGN KEY (ztk_id, zitems) REFERENCES zreta003(ztk_id, zitems) ON DELETE CASCADE,
    INDEX idx_zatkrl (zatkrl),
    INDEX idx_zctgr (zctgr)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='附加条款明细表';
```

### 3.5 促销返利阶梯表 (zreta005)

```sql
-- =====================================================
-- 促销返利阶梯表 - 基于ZRETA005表结构分析
-- =====================================================
CREATE TABLE zreta005 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zstep VARCHAR(3) NOT NULL COMMENT '阶梯序号',
    zmin_val DECIMAL(15,3) DEFAULT 0 COMMENT '最小值',
    zmax_val DECIMAL(15,3) DEFAULT 0 COMMENT '最大值',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '返利比例',
    zamount DECIMAL(15,2) DEFAULT 0 COMMENT '返利金额',
    zunit VARCHAR(3) COMMENT '单位',
    zdesc VARCHAR(40) COMMENT '阶梯描述',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    
    PRIMARY KEY (ztk_id, zitems, zstep),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zmin_zmax (zmin_val, zmax_val)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='促销返利阶梯表';
```

### 3.6 促销返利生效日期表 (zreta006)

```sql
-- =====================================================
-- 促销返利生效日期表 - 基于ZRETA006表结构分析
-- =====================================================
CREATE TABLE zreta006 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zbegin DATE NOT NULL COMMENT '生效开始日期',
    zend DATE NOT NULL COMMENT '生效结束日期',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '生效期间返利比例',
    zamount DECIMAL(15,2) DEFAULT 0 COMMENT '生效期间返利金额',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    
    PRIMARY KEY (ztk_id, zitems, zbegin),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='促销返利生效日期表';
```

### 3.7 返利协议关联表 (zret0006)

```sql
-- =====================================================
-- 返利协议关联表 - 基于ZRET0006表结构分析
-- =====================================================
CREATE TABLE zret0006 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款ID',
    zbukrs VARCHAR(4) NOT NULL COMMENT '公司代码',
    zflsqf VARCHAR(4) COMMENT '返利收取方',
    zxyzt VARCHAR(1) DEFAULT 'I' COMMENT '协议状态',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    
    PRIMARY KEY (zxy_id, ztk_id),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_zflsqf (zflsqf),
    INDEX idx_zxyzt (zxyzt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利协议关联表';
```

### 3.8 动态协议明细表 (zret0007)

```sql
-- =====================================================
-- 动态协议明细表 - 基于ZRET0007表结构分析
-- =====================================================
CREATE TABLE zret0007 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zspz_id VARCHAR(20) COMMENT '商品组编号',
    werks VARCHAR(4) COMMENT '工厂',
    lgort VARCHAR(4) COMMENT '库存地点',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '返利比例',
    zamount DECIMAL(15,2) DEFAULT 0 COMMENT '返利金额',
    zunit VARCHAR(3) COMMENT '单位',
    zprice DECIMAL(15,3) DEFAULT 0 COMMENT '核算价格',
    zqty DECIMAL(15,3) DEFAULT 0 COMMENT '数量',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxy_id, zitems, matnr),
    FOREIGN KEY (zxy_id) REFERENCES zret0006(zxy_id) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_zspz_id (zspz_id),
    INDEX idx_werks (werks),
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='动态协议明细表';
```

### 3.9 固定协议明细表 (zret0008)

```sql
-- =====================================================
-- 固定协议明细表 - 基于ZRET0008表结构分析
-- =====================================================
CREATE TABLE zret0008 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    zamount DECIMAL(15,2) DEFAULT 0 COMMENT '固定返利金额',
    zdesc VARCHAR(40) COMMENT '描述',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxy_id, zitems),
    FOREIGN KEY (zxy_id) REFERENCES zret0006(zxy_id) ON DELETE CASCADE,
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='固定协议明细表';
```

### 3.10 商品组主表 (zret0009)

```sql
-- =====================================================
-- 商品组主表 - 基于ZRET0009表结构深度分析
-- =====================================================
CREATE TABLE zret0009 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    zht_id VARCHAR(20) NOT NULL COMMENT '合同编号',
    zbpcode VARCHAR(10) NOT NULL COMMENT '伙伴代码',
    zspzid_txt VARCHAR(40) NOT NULL COMMENT '商品组描述',
    zspztype VARCHAR(1) DEFAULT 'N' COMMENT '商品组类型(N=普通,S=特殊)',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',

    PRIMARY KEY (zspz_id),
    FOREIGN KEY (zht_id) REFERENCES zreta001(zht_id) ON DELETE CASCADE,
    INDEX idx_zht_id (zht_id),
    INDEX idx_zbpcode (zbpcode),
    INDEX idx_zspztype (zspztype),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品组主表';
```

### 3.11 商品组明细表 (zret0020)

```sql
-- =====================================================
-- 商品组明细表 - 基于ZRET0020表结构深度分析
-- =====================================================
CREATE TABLE zret0020 (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    werks VARCHAR(4) COMMENT '工厂',
    lgort VARCHAR(4) COMMENT '库存地点',
    zpeinh_q DECIMAL(5,0) DEFAULT 1 COMMENT '价格倍数',
    zpeinh DECIMAL(5,0) DEFAULT 1 COMMENT '数量倍数',
    zbegin DATE COMMENT '开始日期',
    zend DATE COMMENT '结束日期',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zspz_id, zitems),
    FOREIGN KEY (zspz_id) REFERENCES zret0009(zspz_id) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_werks (werks),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品组明细表';
```

### 3.12 商品组分段价格表 (zret0020_item)

```sql
-- =====================================================
-- 商品组分段价格表 - 基于ZRET0020_ITEM表结构分析
-- =====================================================
CREATE TABLE zret0020_item (
    zspz_id VARCHAR(20) NOT NULL COMMENT '商品组编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zbegin DATE NOT NULL COMMENT '价格开始日期',
    zend DATE NOT NULL COMMENT '价格结束日期',
    zprice DECIMAL(15,3) DEFAULT 0 COMMENT '核算价格',
    zunit VARCHAR(3) COMMENT '价格单位',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zspz_id, zitems, matnr, zbegin),
    FOREIGN KEY (zspz_id, zitems) REFERENCES zret0020(zspz_id, zitems) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品组分段价格表';
```

### 3.13 渠道供应商表 (zret0012)

```sql
-- =====================================================
-- 渠道供应商表 - 基于ZRET0012表结构分析
-- =====================================================
CREATE TABLE zret0012 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    lifnr VARCHAR(10) NOT NULL COMMENT '供应商编号',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '分配比例',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxy_id, zitems, lifnr),
    INDEX idx_lifnr (lifnr),
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道供应商表';
```

### 3.14 外部供货方表 (zret0013)

```sql
-- =====================================================
-- 外部供货方表 - 基于ZRET0013表结构分析
-- =====================================================
CREATE TABLE zret0013 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    lifnr VARCHAR(10) NOT NULL COMMENT '供应商编号',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '分配比例',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxy_id, zitems, lifnr),
    INDEX idx_lifnr (lifnr),
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='外部供货方表';
```

## 4. 配置和辅助表

### 4.1 组织模板主表 (zretc001)

```sql
-- =====================================================
-- 组织模板主表 - 基于ZRETC001表结构分析
-- =====================================================
CREATE TABLE zretc001 (
    ztmpid VARCHAR(10) NOT NULL COMMENT '模板编号',
    ztmp_txt VARCHAR(40) NOT NULL COMMENT '模板描述',
    zbukrs VARCHAR(4) NOT NULL COMMENT '公司代码',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',

    PRIMARY KEY (ztmpid),
    INDEX idx_zbukrs (zbukrs),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织模板主表';
```

### 4.2 组织模板明细表 (zretc002)

```sql
-- =====================================================
-- 组织模板明细表 - 基于ZRETC002表结构分析
-- =====================================================
CREATE TABLE zretc002 (
    ztmpid VARCHAR(10) NOT NULL COMMENT '模板编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zorgtype VARCHAR(4) NOT NULL COMMENT '组织类型',
    zorgid VARCHAR(10) NOT NULL COMMENT '组织ID',
    zorg_txt VARCHAR(40) COMMENT '组织描述',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '分配比例',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (ztmpid, zitems),
    FOREIGN KEY (ztmpid) REFERENCES zretc001(ztmpid) ON DELETE CASCADE,
    INDEX idx_zorgtype (zorgtype),
    INDEX idx_zorgid (zorgid),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织模板明细表';
```

### 4.3 返利类型配置表 (zretc003)

```sql
-- =====================================================
-- 返利类型配置表 - 基于业务分析
-- =====================================================
CREATE TABLE zretc003 (
    zfllx VARCHAR(4) NOT NULL COMMENT '返利类型代码',
    zfllx_txt VARCHAR(40) NOT NULL COMMENT '返利类型描述',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zdefault_xybstyp VARCHAR(1) COMMENT '默认协议类型',
    zdefault_zhszq VARCHAR(3) COMMENT '默认核算周期',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',
    zxgr VARCHAR(12) COMMENT '修改人',
    zxgrq DATE COMMENT '修改日期',
    zxgsj TIME COMMENT '修改时间',

    PRIMARY KEY (zfllx),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='返利类型配置表';
```

### 4.4 协议类型配置表 (zretc004)

```sql
-- =====================================================
-- 协议类型配置表 - 基于业务分析
-- =====================================================
CREATE TABLE zretc004 (
    zxybstyp VARCHAR(1) NOT NULL COMMENT '协议类型代码',
    zxybstyp_txt VARCHAR(40) NOT NULL COMMENT '协议类型描述',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zhas_rate BOOLEAN DEFAULT FALSE COMMENT '是否支持比例',
    zhas_amount BOOLEAN DEFAULT FALSE COMMENT '是否支持金额',
    zhas_step BOOLEAN DEFAULT FALSE COMMENT '是否支持阶梯',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxybstyp),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协议类型配置表';
```

### 4.5 审批策略配置表 (zretc005)

```sql
-- =====================================================
-- 审批策略配置表 - 基于FRGSX字段分析
-- =====================================================
CREATE TABLE zretc005 (
    frgsx VARCHAR(10) NOT NULL COMMENT '审批策略代码',
    frgsx_txt VARCHAR(40) NOT NULL COMMENT '审批策略描述',
    zsteps INT DEFAULT 1 COMMENT '审批步骤数',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (frgsx),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批策略配置表';
```

### 4.6 审批步骤配置表 (zretc006)

```sql
-- =====================================================
-- 审批步骤配置表 - 基于KOLNR字段分析
-- =====================================================
CREATE TABLE zretc006 (
    frgsx VARCHAR(10) NOT NULL COMMENT '审批策略代码',
    kolnr VARCHAR(3) NOT NULL COMMENT '审批步骤',
    zstep_txt VARCHAR(40) NOT NULL COMMENT '步骤描述',
    zrole VARCHAR(20) COMMENT '审批角色',
    zuser_group VARCHAR(20) COMMENT '用户组',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (frgsx, kolnr),
    FOREIGN KEY (frgsx) REFERENCES zretc005(frgsx) ON DELETE CASCADE,
    INDEX idx_zrole (zrole),
    INDEX idx_zuser_group (zuser_group),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批步骤配置表';
```

### 4.7 协议组织结构表 (zret0014)

```sql
-- =====================================================
-- 协议组织结构表 - 基于ZRET0014表结构分析
-- =====================================================
CREATE TABLE zret0014 (
    zxy_id VARCHAR(20) NOT NULL COMMENT '协议ID',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zorgtype VARCHAR(4) NOT NULL COMMENT '组织类型',
    zorgid VARCHAR(10) NOT NULL COMMENT '组织ID',
    lifnr VARCHAR(10) COMMENT '供应商编号',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '分配比例',
    zbegin DATE NOT NULL COMMENT '开始日期',
    zend DATE NOT NULL COMMENT '结束日期',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (zxy_id, zitems, zorgtype, zorgid),
    INDEX idx_zorgtype (zorgtype),
    INDEX idx_zorgid (zorgid),
    INDEX idx_lifnr (lifnr),
    INDEX idx_zbegin_zend (zbegin, zend)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协议组织结构表';
```

### 4.8 核算期间表 (zret0015)

```sql
-- =====================================================
-- 核算期间表 - 基于ZRET0015表结构分析
-- =====================================================
CREATE TABLE zret0015 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zbegin DATE NOT NULL COMMENT '期间开始日期',
    zend DATE NOT NULL COMMENT '期间结束日期',
    zperiod VARCHAR(7) NOT NULL COMMENT '期间(YYYY/MM)',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (ztk_id, zitems, zbegin),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zperiod (zperiod),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核算期间表';
```

### 4.9 结算期间表 (zret0016)

```sql
-- =====================================================
-- 结算期间表 - 基于ZRET0016表结构分析
-- =====================================================
CREATE TABLE zret0016 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zbegin DATE NOT NULL COMMENT '结算开始日期',
    zend DATE NOT NULL COMMENT '结算结束日期',
    zperiod VARCHAR(7) NOT NULL COMMENT '结算期间(YYYY/MM)',
    zstatus VARCHAR(1) DEFAULT 'A' COMMENT '状态(A=活动,I=非活动)',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (ztk_id, zitems, zbegin),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zperiod (zperiod),
    INDEX idx_zbegin_zend (zbegin, zend),
    INDEX idx_zstatus (zstatus)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算期间表';
```

### 4.10 商品阶梯组表 (zret0058)

```sql
-- =====================================================
-- 商品阶梯组表 - 基于ZRET0058表结构分析
-- =====================================================
CREATE TABLE zret0058 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    matnr VARCHAR(18) NOT NULL COMMENT '物料编号',
    zstep VARCHAR(3) NOT NULL COMMENT '阶梯序号',
    zmin_qty DECIMAL(15,3) DEFAULT 0 COMMENT '最小数量',
    zmax_qty DECIMAL(15,3) DEFAULT 0 COMMENT '最大数量',
    zrate DECIMAL(5,4) DEFAULT 0 COMMENT '返利比例',
    zamount DECIMAL(15,2) DEFAULT 0 COMMENT '返利金额',
    zunit VARCHAR(3) COMMENT '单位',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (ztk_id, zitems, matnr, zstep),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_matnr (matnr),
    INDEX idx_zmin_zmax (zmin_qty, zmax_qty)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品阶梯组表';
```

### 4.11 条款修改说明表 (zret0076)

```sql
-- =====================================================
-- 条款修改说明表 - 基于ZRET0076表结构分析
-- =====================================================
CREATE TABLE zret0076 (
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    zitems VARCHAR(6) NOT NULL COMMENT '行项目号',
    zchange_type VARCHAR(10) NOT NULL COMMENT '修改类型',
    zold_value VARCHAR(100) COMMENT '原值',
    znew_value VARCHAR(100) COMMENT '新值',
    zchange_desc TEXT COMMENT '修改说明',
    zcjr VARCHAR(12) NOT NULL COMMENT '创建人',
    zcjrq DATE NOT NULL COMMENT '创建日期',
    zcjsj TIME NOT NULL COMMENT '创建时间',

    PRIMARY KEY (ztk_id, zitems, zchange_type, zcjrq, zcjsj),
    FOREIGN KEY (ztk_id) REFERENCES zreta002(ztk_id) ON DELETE CASCADE,
    INDEX idx_zchange_type (zchange_type),
    INDEX idx_zcjrq (zcjrq)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='条款修改说明表';
```

## 5. 审计和日志表

### 5.1 审批日志表 (zret_approval_log)

```sql
-- =====================================================
-- 审批日志表 - 基于审批流程分析
-- =====================================================
CREATE TABLE zret_approval_log (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    ztk_id VARCHAR(20) NOT NULL COMMENT '条款编号',
    frgsx VARCHAR(10) NOT NULL COMMENT '审批策略',
    kolnr VARCHAR(3) NOT NULL COMMENT '审批步骤',
    frgc1 VARCHAR(2) COMMENT '审批代码',
    approval_action VARCHAR(10) NOT NULL COMMENT '审批动作(APPROVE/REJECT/RETURN)',
    approval_user VARCHAR(12) NOT NULL COMMENT '审批人',
    approval_date DATE NOT NULL COMMENT '审批日期',
    approval_time TIME NOT NULL COMMENT '审批时间',
    approval_comment TEXT COMMENT '审批意见',
    old_status VARCHAR(1) COMMENT '原状态',
    new_status VARCHAR(1) COMMENT '新状态',

    INDEX idx_ztk_id (ztk_id),
    INDEX idx_approval_user (approval_user),
    INDEX idx_approval_date (approval_date),
    INDEX idx_frgsx_kolnr (frgsx, kolnr)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审批日志表';
```

### 5.2 数据变更日志表 (zret_change_log)

```sql
-- =====================================================
-- 数据变更日志表 - 基于业务需求分析
-- =====================================================
CREATE TABLE zret_change_log (
    log_id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    table_name VARCHAR(30) NOT NULL COMMENT '表名',
    record_key VARCHAR(100) NOT NULL COMMENT '记录主键',
    change_type VARCHAR(10) NOT NULL COMMENT '变更类型(INSERT/UPDATE/DELETE)',
    field_name VARCHAR(30) COMMENT '字段名',
    old_value TEXT COMMENT '原值',
    new_value TEXT COMMENT '新值',
    change_user VARCHAR(12) NOT NULL COMMENT '变更人',
    change_date DATE NOT NULL COMMENT '变更日期',
    change_time TIME NOT NULL COMMENT '变更时间',
    change_program VARCHAR(30) COMMENT '变更程序',

    INDEX idx_table_record (table_name, record_key),
    INDEX idx_change_user (change_user),
    INDEX idx_change_date (change_date),
    INDEX idx_change_type (change_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据变更日志表';
```

## 6. 数据库设计总结

### 6.1 表关系概览

```
核心业务表关系：
zreta001 (合同)
    ├── zreta002 (条款)
    │   ├── zreta003 (附加条款)
    │   │   └── zreta004 (附加条款明细)
    │   ├── zreta005 (促销返利阶梯)
    │   ├── zreta006 (促销返利生效日期)
    │   ├── zret0015 (核算期间)
    │   ├── zret0016 (结算期间)
    │   └── zret0058 (商品阶梯组)
    ├── zret0009 (商品组)
    │   ├── zret0020 (商品组明细)
    │   └── zret0020_item (分段价格)
    └── zret0006 (协议关联)
        ├── zret0007 (动态协议明细)
        ├── zret0008 (固定协议明细)
        ├── zret0012 (渠道供应商)
        ├── zret0013 (外部供货方)
        └── zret0014 (协议组织结构)

配置表关系：
zretc001 (组织模板)
    └── zretc002 (组织模板明细)
zretc005 (审批策略)
    └── zretc006 (审批步骤)
```

### 6.2 索引策略

1. **主键索引**: 所有表都有明确的主键
2. **外键索引**: 所有外键字段都建立了索引
3. **业务索引**: 基于查询频率建立复合索引
4. **日期索引**: 日期范围查询优化
5. **状态索引**: 状态字段查询优化

### 6.3 数据完整性

1. **外键约束**: 确保数据引用完整性
2. **检查约束**: 状态字段值域限制
3. **非空约束**: 关键业务字段不允许为空
4. **唯一约束**: 业务编号唯一性保证

### 6.4 性能优化建议

1. **分区策略**: 大表按日期分区
2. **索引优化**: 定期分析索引使用情况
3. **查询优化**: 避免全表扫描
4. **数据归档**: 历史数据定期归档

### 6.5 扩展性设计

1. **预留字段**: 关键表预留扩展字段
2. **版本控制**: 支持数据结构版本升级
3. **配置驱动**: 业务规则配置化
4. **插件机制**: 支持功能模块扩展
```
```
```
