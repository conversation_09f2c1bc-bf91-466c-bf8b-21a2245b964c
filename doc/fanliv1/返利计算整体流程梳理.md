# SAP返利计算整体流程梳理

## 1. 返利计算系统概述

基于对ZRED0008和ZRER0102程序的深度分析，SAP返利计算系统是一个复杂的多层次计算引擎，支持多种返利类型和计算方式。

### 1.1 核心计算程序

| 程序名称 | 功能描述 | 主要职责 |
|---------|---------|---------|
| **ZRED0008** | 返利计算执行程序 | 执行返利计算，调用计算函数模块 |
| **ZRER0102** | 返利数据查询程序 | 查询和展示返利计算结果 |

### 1.2 核心计算函数模块

| 函数模块 | 适用场景 | 计算类型 | 说明 |
|---------|---------|---------|------|
| **ZREFM0026** | 正常条款计算 | A/T/V类型 | 金额、数量、比例返利计算 |
| **ZREFM0048** | 促销条款计算 | P类型 | 促销返利阶梯计算 |
| **ZREFMA026** | 预演模式计算 | A/T/V类型 | 预演计算，不实际保存 |
| **ZREFMA048** | 预演促销计算 | P类型 | 促销预演计算 |

## 2. 返利计算整体架构

```mermaid
graph TB
    subgraph "数据准备阶段"
        A1[合同数据查询] --> A2[条款数据筛选]
        A2 --> A3[协议数据加载]
        A3 --> A4[权限验证]
        A4 --> A5[日期范围验证]
    end
    
    subgraph "计算引擎调度"
        B1[条款类型判断] --> B2{条款类型}
        B2 -->|附加条款P| B3[附加条款处理]
        B2 -->|正常条款| B4[正常条款处理]
        
        B3 --> B5[调用ZREFM0048/ZREFMA048]
        B4 --> B6[调用ZREFM0026/ZREFMA026]
    end
    
    subgraph "计算执行层"
        C1[协议数据准备] --> C2[基础数据获取]
        C2 --> C3[计算规则应用]
        C3 --> C4[返利金额计算]
        C4 --> C5[结果数据保存]
    end
    
    subgraph "结果处理"
        D1[计算结果汇总] --> D2[数据验证]
        D2 --> D3[状态更新]
        D3 --> D4[报表展示]
    end
    
    A5 --> B1
    B5 --> C1
    B6 --> C1
    C5 --> D1
```

## 3. 返利计算详细流程

### 3.1 数据准备阶段

#### 3.1.1 条款数据查询（frm_get_data1）

```sql
-- 查询符合条件的条款数据
SELECT DISTINCT
       zreta001~zhtlx,     -- 合同类型
       zreta001~ekgrp,     -- 采购组
       zreta001~zbukrs,    -- 公司代码
       zreta001~zht_id,    -- 合同编号
       zreta002~ztk_id     -- 条款编号
  FROM zreta002
  INNER JOIN zreta001 ON zreta001~zht_id = zreta002~zht_id
  INNER JOIN zret0006 ON zret0006~ztk_id = zreta002~ztk_id
  WHERE zreta001~zht_id   IN s_zht_id    -- 合同号码
    AND zreta001~zhtlx    IN s_zhtlx     -- 合同类型
    AND zreta001~zbukrs   IN s_zbukrs    -- 合同主体公司
    AND zreta001~zbpcode  IN s_bpcode    -- 伙伴ID
    AND zreta002~ztk_id   IN s_ztk_id    -- 条款号码
    AND zreta002~zfllx    IN s_zfllx     -- 返利类型
    AND zreta002~zleib    <> 'R'         -- 排除授权类
    AND zret0006~zxy_id   IN s_zxy_id    -- 协议号码
    AND zret0006~zxybstyp IN ('V','A','T','P')  -- 协议类型
    AND zreta002~zend     >= s_datum-low -- 条款结束日期
    AND zreta002~zbegin   <= s_datum-high -- 条款开始日期
    AND ((zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = '') 
         OR (zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'))
```

#### 3.1.2 前序和后续条款处理

系统支持条款间的依赖关系，通过ZRETA003表管理：

```sql
-- 查询前序条款
SELECT zreta003~zrlid AS ztk_id
  FROM zreta003
  INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
  WHERE zreta003~ztk_id = @lt_sel_id-ztk_id
    AND zreta003~zatktp = 'P'    -- 附加条款类型
    AND zreta003~zatkpi = 'C'    -- 条件指示器
    AND zreta002~zleib <> 'R'    -- 非授权类
```

### 3.2 计算引擎调度

#### 3.2.1 条款类型分类处理

```abap
" 附加条款执行
PERFORM frm_process_ats_tk.

" 正常条款执行  
PERFORM frm_process_nts_tk.
```

#### 3.2.2 协议数据准备

```sql
-- 获取有效协议列表
SELECT zret0006~zxy_id,
       zret0006~zbegin,
       zret0006~zend
  FROM zret0006
  INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
  WHERE zret0006~ztk_id = ls_ztk_id-ztk_id
    AND zret0006~zbegin <= lv_yestdat  -- 昨日日期
    AND ((zreta002~zcnyg = '' AND zret0006~zxyzt = 'A') 
         OR (zreta002~zcnyg = 'X' AND zret0006~zxyzt <> 'D'))
```

### 3.3 计算函数模块调用

#### 3.3.1 计算类型判断逻辑

```abap
IF p_yjyx = ''.  " 正式计算模式
  IF ls_ztk_id-zxybstyp = 'P'.  " 促销返利
    CALL FUNCTION 'ZREFM0048'
      EXPORTING
        iv_ztk_id  = ls_ztk_id-ztk_id
        iv_messgae = p_smeg
      TABLES
        it_zxyid   = lt_zxyid
        et_retab   = lt_retab.
  ELSE.  " 其他类型返利
    CALL FUNCTION 'ZREFM0026'
      EXPORTING
        iv_ztk_id  = ls_ztk_id-ztk_id
        iv_messgae = p_smeg
        iv_zjzrq   = p_zjzrq
      TABLES
        it_zxyid   = lt_zxyid
        et_retab   = lt_retab.
  ENDIF.
ELSE.  " 预演计算模式
  IF ls_ztk_id-zxybstyp = 'P'.
    CALL FUNCTION 'ZREFMA048'  " 促销预演
  ELSE.
    CALL FUNCTION 'ZREFMA026'  " 普通预演
  ENDIF.
ENDIF.
```

## 4. 返利计算类型详解

### 4.1 协议类型分类

| 协议类型 | 代码 | 计算基准 | 计算方式 | 函数模块 |
|---------|------|---------|---------|---------|
| **金额返利** | A | 采购/销售金额 | 金额×比例 | ZREFM0026 |
| **数量返利** | T | 采购/销售数量 | 数量×单价 | ZREFM0026 |
| **比例返利** | V | 采购/销售金额 | 金额×比例 | ZREFM0026 |
| **促销返利** | P | 促销销售额 | 阶梯计算 | ZREFM0048 |
| **固定返利** | F | 固定金额 | 固定值 | 专用处理 |
| **定额返利** | Q | 定额标准 | 定额计算 | 专用处理 |

### 4.2 计算基准数据来源

基于ZRER0102程序的查询逻辑，返利计算的基础数据来源于：

```sql
-- 核算期间数据汇总
SELECT SUM(zret0017~zpurjzsl) AS zpurjzsl_sum,    -- 采购累计数量
       SUM(zret0017~zpurjzje) AS zpurjzje_sum,    -- 采购累计金额
       SUM(zret0017~zrpurjzje) AS zrpurjzje_sum,  -- 采购返利累计金额
       SUM(zret0017~zdistjzsl) AS zdistjzsl_sum,  -- 配送累计数量
       SUM(zret0017~zdistjzje) AS zdistjzje_sum,  -- 配送累计金额
       SUM(zret0017~zrdistjzje) AS zrdistjzje_sum, -- 配送返利累计金额
       SUM(zret0017~zsalejzsl) AS zsalejzsl_sum,  -- 销售累计数量
       SUM(zret0017~zsalejzje) AS zsalejzje_sum,  -- 销售累计金额
       SUM(zret0017~zrsalejzje) AS zrsalejzje_sum, -- 销售返利累计金额
       SUM(zret0017~zduepayje) AS zduepayje_sum,  -- 应付金额
       SUM(zret0017~zactlpayje) AS zactlpayje_sum -- 实付金额
  FROM zret0017
  WHERE zret0017~zxy_id = zret0006~zxy_id
    AND zret0017~zbudat >= zret0015~zbegin
    AND zret0017~zbudat <= zret0015~zend

## 5. 返利计算核心时序图

### 5.1 完整计算流程时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant ZRED0008 as 返利计算程序
    participant DataLayer as 数据层
    participant CalcEngine as 计算引擎
    participant ZREFM0026 as 普通返利函数
    participant ZREFM0048 as 促销返利函数
    participant ResultDB as 结果数据库

    User->>ZRED0008: 1.启动返利计算<br/>输入计算参数

    Note over ZRED0008: 数据准备阶段
    ZRED0008->>DataLayer: 2.查询合同条款数据<br/>frm_get_data1()
    DataLayer-->>ZRED0008: 3.返回条款列表

    ZRED0008->>DataLayer: 4.权限验证<br/>frm_check_date()
    DataLayer-->>ZRED0008: 5.权限验证通过

    ZRED0008->>ZRED0008: 6.条款分类处理<br/>- 附加条款(ATS)<br/>- 正常条款(NTS)

    Note over ZRED0008: 附加条款处理
    ZRED0008->>ZRED0008: 7.处理附加条款<br/>frm_process_ats_tk()

    loop 每个附加条款
        ZRED0008->>DataLayer: 8.查询协议数据
        DataLayer-->>ZRED0008: 9.返回协议列表

        ZRED0008->>ZRED0008: 10.判断协议类型<br/>zxybstyp = 'P'?

        alt 促销返利(P类型)
            ZRED0008->>ZREFM0048: 11.调用促销计算函数
            ZREFM0048->>CalcEngine: 12.执行促销阶梯计算
            CalcEngine-->>ZREFM0048: 13.返回计算结果
            ZREFM0048-->>ZRED0008: 14.返回促销返利结果
        else 其他类型(A/T/V)
            ZRED0008->>ZREFM0026: 15.调用普通计算函数
            ZREFM0026->>CalcEngine: 16.执行返利计算
            CalcEngine-->>ZREFM0026: 17.返回计算结果
            ZREFM0026-->>ZRED0008: 18.返回返利结果
        end

        ZRED0008->>ResultDB: 19.保存计算结果
    end

    Note over ZRED0008: 正常条款处理
    ZRED0008->>ZRED0008: 20.处理正常条款<br/>frm_process_nts_tk()

    loop 每个正常条款
        ZRED0008->>DataLayer: 21.查询协议数据
        DataLayer-->>ZRED0008: 22.返回协议列表

        ZRED0008->>ZRED0008: 23.设置生效日期<br/>frm_set_begin_end_date()

        alt 预演模式
            alt 促销返利
                ZRED0008->>ZREFMA048: 24.调用促销预演函数
            else 普通返利
                ZRED0008->>ZREFMA026: 25.调用普通预演函数
            end
        else 正式计算
            alt 促销返利
                ZRED0008->>ZREFM0048: 26.调用促销计算函数
            else 普通返利
                ZRED0008->>ZREFM0026: 27.调用普通计算函数
            end
        end

        CalcEngine->>CalcEngine: 28.执行计算逻辑<br/>- 基础数据获取<br/>- 计算规则应用<br/>- 返利金额计算
        CalcEngine-->>ZRED0008: 29.返回计算结果

        ZRED0008->>ResultDB: 30.保存/更新结果
    end

    Note over ZRED0008: 结果处理
    ZRED0008->>ZRED0008: 31.汇总计算结果
    ZRED0008->>User: 32.显示计算完成信息<br/>包含处理条款数量
```

### 5.2 返利计算引擎内部逻辑

```mermaid
graph TB
    subgraph "计算引擎核心逻辑"
        A1[协议数据输入] --> A2[基础数据查询]
        A2 --> A3[计算规则解析]
        A3 --> A4{返利类型判断}

        A4 -->|金额返利A| B1[金额×比例计算]
        A4 -->|数量返利T| B2[数量×单价计算]
        A4 -->|比例返利V| B3[金额×比例计算]
        A4 -->|促销返利P| B4[阶梯计算逻辑]

        B1 --> C1[业务规则验证]
        B2 --> C1
        B3 --> C1
        B4 --> C2[促销规则验证]

        C1 --> D1[返利金额确定]
        C2 --> D1

        D1 --> E1[结果数据保存]
    end

    subgraph "基础数据来源"
        F1[ZRET0017累计数据]
        F2[ZRET0015核算期间]
        F3[ZRET0044商品组]
        F4[ZRETA005阶梯配置]

        F1 --> A2
        F2 --> A2
        F3 --> A2
        F4 --> A2
    end

    subgraph "计算结果输出"
        G1[ZRET0054计算结果]
        G2[返利金额]
        G3[累计数量]
        G4[计算状态]

        E1 --> G1
        E1 --> G2
        E1 --> G3
        E1 --> G4
    end
```

## 6. 关键计算逻辑分析

### 6.1 促销返利阶梯计算（ZREFM0048）

促销返利采用阶梯计算方式，支持全量和增量两种模式：

```abap
" 阶梯计算逻辑示例
DATA: lv_total_amount TYPE p DECIMALS 2,
      lv_rebate_amount TYPE p DECIMALS 2.

" 获取累计销售金额
lv_total_amount = ls_data-zsalejzje_sum.

" 根据阶梯配置计算返利
LOOP AT lt_ladder INTO ls_ladder
  WHERE zxy_id = ls_agreement-zxy_id
  ORDER BY zfrom_amount.

  IF lv_total_amount >= ls_ladder-zfrom_amount AND
     lv_total_amount <= ls_ladder-zto_amount.

    CASE ls_ladder-zcalc_method.
      WHEN '01'.  " 比例计算
        lv_rebate_amount = lv_total_amount * ls_ladder-zrate / 100.
      WHEN '02'.  " 固定金额
        lv_rebate_amount = ls_ladder-zfixed_amount.
      WHEN '03'.  " 阶梯增量
        lv_rebate_amount = (lv_total_amount - ls_ladder-zfrom_amount)
                          * ls_ladder-zrate / 100.
    ENDCASE.

    EXIT.
  ENDIF.
ENDLOOP.
```

### 6.2 普通返利计算（ZREFM0026）

普通返利支持金额、数量、比例三种计算方式：

```abap
" 普通返利计算逻辑
CASE ls_agreement-zxybstyp.
  WHEN 'A'.  " 金额返利
    lv_base_amount = ls_data-zpurjzje_sum.  " 采购累计金额
    lv_rebate_amount = lv_base_amount * ls_agreement-zrate / 100.

  WHEN 'T'.  " 数量返利
    lv_base_quantity = ls_data-zpurjzsl_sum.  " 采购累计数量
    lv_rebate_amount = lv_base_quantity * ls_agreement-zunit_price.

  WHEN 'V'.  " 比例返利
    lv_base_amount = ls_data-zsalejzje_sum.  " 销售累计金额
    lv_rebate_amount = lv_base_amount * ls_agreement-zrate / 100.
ENDCASE.

" 应用业务规则限制
IF ls_agreement-zmax_amount > 0 AND
   lv_rebate_amount > ls_agreement-zmax_amount.
  lv_rebate_amount = ls_agreement-zmax_amount.
ENDIF.
```

### 6.3 数据来源映射关系

| 计算基准 | 数据表 | 关键字段 | 说明 |
|---------|--------|---------|------|
| **采购数据** | ZRET0017 | zpurjzsl, zpurjzje | 采购累计数量和金额 |
| **销售数据** | ZRET0017 | zsalejzsl, zsalejzje | 销售累计数量和金额 |
| **配送数据** | ZRET0017 | zdistjzsl, zdistjzje | 配送累计数量和金额 |
| **核算期间** | ZRET0015 | zbegin, zend | 计算的时间范围 |
| **商品范围** | ZRET0044 | zxy_id, matnr | 协议适用的商品范围 |
| **阶梯配置** | ZRETA005 | zfrom_amount, zto_amount, zrate | 促销返利阶梯设置 |

## 7. 计算结果处理

### 7.1 结果数据结构

计算结果保存在ZRET0054表中，包含以下关键信息：

```sql
-- 计算结果表结构
CREATE TABLE ZRET0054 (
  zxy_id VARCHAR(20),        -- 协议编号
  zhsqj_id VARCHAR(20),      -- 核算期间
  zrebate_amount DECIMAL(15,2), -- 返利金额
  zbase_amount DECIMAL(15,2),   -- 计算基准金额
  zbase_quantity DECIMAL(15,3), -- 计算基准数量
  zcalc_date DATE,           -- 计算日期
  zcalc_status VARCHAR(2),   -- 计算状态
  zcalc_user VARCHAR(12),    -- 计算用户
  zcalc_time TIME            -- 计算时间
);
```

### 7.2 状态管理

| 状态代码 | 状态描述 | 说明 |
|---------|---------|------|
| **01** | 计算完成 | 正常计算完成 |
| **02** | 计算中 | 正在计算过程中 |
| **03** | 计算失败 | 计算过程出现错误 |
| **04** | 预演模式 | 预演计算结果 |

这个返利计算系统通过多层次的数据验证、灵活的计算引擎和完整的结果管理，实现了复杂的企业级返利业务需求。
```
