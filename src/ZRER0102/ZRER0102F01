*&---------------------------------------------------------------------*
*& 包含               ZRED0015F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_get_data .

  IF p_incld = ''.
    s_tktype[] = VALUE #( ( sign = 'I' option = 'NE'  low = 'P'   high = '' ) ).
  ENDIF.

  SELECT
         zret0006~zxy_id,
         zret0006~zhtlx   ,
         zret0006~zflsqf  ,
         zret0006~zflzff AS xy_zflzff   ,
         zret0006~zbukrs  ,
         zret0006~zflzff  ,
         zret0006~zfllx   ,
         zret0006~zxybstyp   ,
         zret0006~zitzff   ,
         zreta001~zht_id ,
         zreta001~zbptype ,
         zreta001~zbpcode ,
         zreta002~ztk_id  ,
         zreta002~ztk_txt,
         zreta002~ekgrp   ,
         zreta001~ekgrp AS ht_ekgrp   ,
         zreta002~zclrid  ,
         zreta002~zflzff AS tk_zflzff  ,
         zreta002~zbarb   ,
         zreta002~ztktype,
         zret0002~zfllx_txt,
         zret0015~zhsqj_id ,
         zret0015~zbegin   ,
         zret0015~zend     ,
         zreta002~zfldfsj   ,
          SUM( zret0017~zpurjzsl   ) AS zpurjzsl_sum  ,
          SUM( zret0017~zpurjzje   ) AS zpurjzje_sum  ,
          SUM( zret0017~zrpurjzje   ) AS zrpurjzje_sum  ,
          SUM( zret0017~zdistjzsl  ) AS zdistjzsl_sum ,
          SUM( zret0017~zdistjzje  ) AS zdistjzje_sum ,
          SUM( zret0017~zrdistjzje  ) AS zrdistjzje_sum ,
          SUM( zret0017~zsalejzsl  ) AS zsalejzsl_sum ,
          SUM( zret0017~zsalejzje  ) AS zsalejzje_sum ,
          SUM( zret0017~zrsalejzje  ) AS zrsalejzje_sum ,
          SUM( zret0017~zduepayje  ) AS zduepayje_sum ,
          SUM( zret0017~zactlpayje ) AS zactlpayje_sum,
          SUM( zret0017~zpuryssl   ) AS zpuryssl_sum  ,
          SUM( zret0017~zpurysje   ) AS zpurysje_sum  ,
          SUM( zret0017~zdistyssl  ) AS zdistyssl_sum ,
          SUM( zret0017~zdistysje  ) AS zdistysje_sum ,
          SUM( zret0017~zsaleyssl  ) AS zsaleyssl_sum ,
          SUM( zret0017~zsaleysje  ) AS zsaleysje_sum ,
          SUM( zret0017~zduercv    ) AS zduercv_sum   ,
          SUM( zret0017~zactlrcv   ) AS zactlrcv_sum
        INTO CORRESPONDING FIELDS OF  TABLE @gt_data
        FROM zret0006
        INNER JOIN  zret0015 ON zret0015~zxy_id   = zret0006~zxy_id
        INNER JOIN  zreta002 ON zreta002~ztk_id   = zret0006~ztk_id
        INNER JOIN  zreta001 ON zreta001~zht_id   = zreta002~zht_id
        LEFT  JOIN  zret0017 ON zret0017~zxy_id   = zret0006~zxy_id
                            AND zret0017~zbudat  >= zret0015~zbegin AND
                                zret0017~zbudat  <= zret0015~zend
        LEFT  JOIN  zret0002 ON zret0002~zfllx    = zreta002~zfllx
        WHERE zret0006~zxy_id   IN @s_zxy_id
         AND  zret0006~zbukrs   IN @s_zbukxy
         AND  zreta001~zbukrs   IN @s_zbukht
         AND  zret0006~zhtlx    IN @s_zhtlx
         AND  zreta001~ekgrp    IN @s_ekgrp
         AND  zreta001~zbptype  IN @s_bptype
         AND  zreta001~zbpcode  IN @s_bpcode
         AND  zret0006~zxyzt    IN @s_zxyzt
         AND  zret0015~zhsqj_id IN @s_hsqjid
         AND  zret0017~zbudat   IN @s_zbudat
         AND  zreta002~ztk_id   IN @s_ztk_id
         AND  zreta001~zht_id   IN @s_zht_id
         AND  zreta002~ztk_id   IN @s_ekgrp
         AND  zreta002~ztktype  IN @s_tktype
         AND  zreta002~zleib    <> 'R'
         AND  zret0006~zxybstyp NOT IN ('F','Q')
    GROUP BY  zret0006~zxy_id,
              zret0006~zhtlx   ,
              zret0006~zflsqf  ,
              zret0006~zbukrs  ,
              zret0006~zflzff  ,
              zret0006~zfllx   ,
              zret0006~zxybstyp   ,
              zret0006~zitzff  ,
              zreta001~zht_id ,
              zreta001~zbptype ,
              zreta001~zbpcode ,
              zreta002~ztk_id  ,
              zreta002~ztk_txt,
              zreta002~ekgrp   ,
              zreta002~zclrid  ,
              zreta002~zflzff  ,
              zreta002~zbarb   ,
              zreta002~ztktype ,
              zret0002~zfllx_txt,
              zret0015~zhsqj_id  ,
              zret0015~zbegin  ,
              zret0015~zend    ,
              zreta001~ekgrp    ,
              zreta002~zfldfsj
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029.

  DATA(lt_zclridon) = gt_zclridon.
  lt_zclridon =  CORRESPONDING #( gt_data MAPPING zclrid  =  ztk_id ) .
  APPEND LINES OF lt_zclridon TO gt_zclridon.
  lt_zclridon =  CORRESPONDING #( gt_data MAPPING zclrid  =  zclrid ) .
  APPEND LINES OF lt_zclridon TO gt_zclridon.
  SORT gt_zclridon .
  DELETE ADJACENT DUPLICATES FROM gt_zclridon .

  SELECT  *
    INTO TABLE gt_zretc005
    FROM zretc005
    FOR ALL ENTRIES IN gt_zclridon
   WHERE zclrid = gt_zclridon-zclrid
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029..

  DATA(lt_zxy_idno) = gt_zxy_idno.
  lt_zxy_idno =  CORRESPONDING #( gt_data MAPPING zxy_id  =  ztk_id ) .
  APPEND LINES OF lt_zxy_idno TO gt_zxy_idno.
  lt_zxy_idno =  CORRESPONDING #( gt_data MAPPING zxy_id  =  zxy_id ) .
  APPEND LINES OF lt_zxy_idno TO gt_zxy_idno.
  SORT gt_zxy_idno .
  DELETE ADJACENT DUPLICATES FROM gt_zxy_idno .


  SELECT *
    INTO TABLE gt_zret0044
    FROM zret0044
    FOR ALL ENTRIES IN gt_zxy_idno
   WHERE zxy_id = gt_zxy_idno-zxy_id
     AND zflzff IS NOT NULL
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .

  DATA(lt_lifnron) = gt_lifnron.
  lt_lifnron =  CORRESPONDING #( gt_data MAPPING lifnr = xy_zflzff ) .
  APPEND LINES OF lt_lifnron TO gt_lifnron .
  lt_lifnron = CORRESPONDING #( gt_data MAPPING lifnr = tk_zflzff ) .
  APPEND LINES OF lt_lifnron TO gt_lifnron .
  lt_lifnron = CORRESPONDING #( gt_data MAPPING lifnr = zbpcode ) .
  APPEND LINES OF lt_lifnron TO gt_lifnron .
  lt_lifnron = CORRESPONDING #( gt_zret0044 MAPPING lifnr = zflzff ) .
  APPEND LINES OF lt_lifnron TO gt_lifnron .
  SORT gt_lifnron .
  DELETE ADJACENT DUPLICATES FROM gt_lifnron .

  SELECT lifnr name1
    INTO TABLE gt_lfa1
    FROM  lfa1
     FOR ALL ENTRIES IN gt_lifnron
   WHERE lifnr = gt_lifnron-lifnr
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029..


  gt_bpcode = CORRESPONDING #( gt_data MAPPING zbpcode = zbpcode ) .
  DELETE ADJACENT DUPLICATES FROM gt_bpcode .

*  SELECT a~*
*  FROM  zmmt0345 AS a
*  INNER JOIN  @gt_bpcode AS b ON  a~codeitemid = b~zbpcode
*  INTO TABLE @gt_zmmt0345.


  SELECT * INTO TABLE gt_t001 FROM t001 %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .
  SELECT * INTO TABLE gt_t024 FROM t024 %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .
  SELECT * INTO TABLE gt_zret0003 FROM zret0003 %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .
  SELECT * INTO TABLE gt_dd07t FROM dd07t WHERE domname = 'ZREM_ZHTLX' %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PROCESS_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_process_data .

  DATA:lv_notauthor.
  DATA: BEGIN OF lt_value OCCURS 0,
          value TYPE p DECIMALS 2,
          ztype TYPE c,
        END OF lt_value.

  DATA:lv_value TYPE ttet_amount_pd,
       lv_ljje  TYPE ttet_amount_pd.

  DEFINE add_value.
    lt_value-value = &1 .
    lt_value-ztype = &2 .
  APPEND lt_value.
  END-OF-DEFINITION.



  SELECT * FROM zretcm04 INTO  TABLE @DATA(lt_zretcm04) %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .

  SORT lt_zretcm04 BY zfldfsj.
  SORT gt_data BY zxy_id zhsqj_id .

  LOOP AT  gt_data INTO gs_data .

    PERFORM frm_authority_check  USING gs_data CHANGING lv_notauthor.
    IF lv_notauthor = 'X' .
      DELETE gt_data.
      CONTINUE.
    ENDIF.

    MOVE-CORRESPONDING gs_data TO gs_alv.
    gs_alv-zflzff = gs_data-xy_zflzff.

    IF gs_alv-zflzff IS INITIAL .
      READ TABLE gt_zret0044 INTO  gs_zret0044 WITH  KEY zxy_id = gs_data-zxy_id .
      IF sy-subrc = 0.
        gs_alv-zflzff = gs_zret0044-zflzff.
      ENDIF.
    ENDIF.

    IF gs_alv-zflzff IS INITIAL AND gs_data-zitzff = 'X'.
      gs_alv-zflzff = gs_data-tk_zflzff.
      IF gs_alv-zflzff IS INITIAL.
        READ TABLE gt_zret0044 INTO  gs_zret0044 WITH  KEY zxy_id = gs_data-ztk_id .
        IF sy-subrc = 0.
          gs_alv-zflzff = gs_zret0044-zflzff.
        ENDIF.
      ENDIF.
    ENDIF.


    READ TABLE gt_zretc005 INTO gs_zretc005 WITH KEY zclrid =  gs_data-ztk_id.
    IF sy-subrc = 0.
      gs_alv-zhsjz   =  gs_zretc005-zhsjz.
      gs_alv-zflhsjz =  gs_zretc005-zflhsjz.
      gs_alv-zflxs   =  gs_zretc005-zflxs.
    ELSE.
      READ TABLE gt_zretc005 INTO gs_zretc005 WITH KEY zclrid =  gs_data-zclrid.
      IF sy-subrc = 0.
        gs_alv-zhsjz   =  gs_zretc005-zflhsjz..
        gs_alv-zflhsjz =  gs_zretc005-zflhsjz.
        gs_alv-zflxs   =  gs_zretc005-zflxs.
      ENDIF.
    ENDIF.

    IF gs_data-zbarb = 'X' AND gs_alv-zflhsjz IS INITIAL.
      gs_alv-zflhsjz = gs_alv-zhsjz.
    ENDIF.

    IF gs_alv-zhtlx = 'RB06' .
      gs_alv-zflxs = 'M'.
    ENDIF.

    CASE gs_alv-zflxs.
      WHEN 'M'.
        gs_alv-zflxs_txt = '金额'.
      WHEN 'Q'.
        gs_alv-zflxs_txt = '数量'.
      WHEN OTHERS.
    ENDCASE.
*伙伴编码名称
    CASE gs_alv-zbptype .
      WHEN 'S' .
        READ TABLE gt_lfa1 INTO gs_lfa1 WITH KEY lifnr =  gs_alv-zbpcode.
        IF sy-subrc = 0.
          gs_alv-zbpcode_txt =  gs_lfa1-name1.
        ENDIF.
      WHEN 'M'.
*        READ TABLE gt_zmmt0345 INTO gs_zmmt0345 WITH KEY codeitemid =  gs_alv-zbpcode.
*        IF sy-subrc = 0.
*          gs_alv-zbpcode_txt =  gs_zmmt0345-codeitemdesc.
*        ENDIF.

        SELECT SINGLE  name_org1 INTO gs_alv-zbpcode_txt  FROM but000 WHERE partner = gs_alv-zbpcode %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029..

      WHEN OTHERS.
    ENDCASE.

*返利支付方名称
    READ TABLE gt_lfa1 INTO gs_lfa1 WITH KEY lifnr =  gs_alv-zflzff.
    IF sy-subrc = 0.
      gs_alv-name1 =  gs_lfa1-name1.
    ENDIF.
*返利收取方名称
    READ TABLE gt_t001 INTO gs_t001 WITH KEY bukrs =  gs_alv-zflsqf.
    IF sy-subrc = 0.
      gs_alv-butxt =  gs_t001-butxt.
    ENDIF.

*协议主体描述
    READ TABLE gt_t001 INTO gs_t001 WITH KEY bukrs =  gs_alv-zbukrs.
    IF sy-subrc = 0.
      gs_alv-zbukrs_t =  gs_t001-butxt.
    ENDIF.

*采购组的描述
    READ TABLE gt_t024 INTO gs_t024 WITH KEY ekgrp =  gs_alv-ekgrp.
    IF sy-subrc = 0.
      gs_alv-eknam =  gs_t024-eknam.
    ENDIF.

    READ TABLE gt_dd07t INTO gs_dd07t WITH KEY domvalue_l =  gs_alv-zhtlx.
    IF sy-subrc = 0.
      gs_alv-zhtlx_txt =  gs_dd07t-ddtext.
    ENDIF.

    READ TABLE gt_zret0003 INTO gs_zret0003 WITH KEY zhsjz =  gs_alv-zhsjz.
    IF sy-subrc = 0.
      REFRESH lt_value.
      IF gs_zret0003-zpur = 'X'.
        add_value: gs_alv-zpurysje_sum 'P'.
      ENDIF.
      IF gs_zret0003-zsale = 'X'.
        add_value: gs_alv-zsaleysje_sum 'S'.
      ENDIF.
      IF gs_zret0003-zdist  = 'X'.
        add_value: gs_alv-zdistysje_sum 'D'.
      ENDIF.
      IF gs_zret0003-zactlpay  = 'X'.
        add_value : gs_alv-zactlrcv_sum  'A'.
      ENDIF.
      IF gs_zret0003-zduepay = 'X'.
        add_value: gs_alv-zduercv_sum   'U'.
      ENDIF.

      IF gs_alv-zxybstyp = 'T'.
        add_value: gs_alv-zsaleysje_sum   'T'.
      ENDIF.


      CASE 'X'.
        WHEN gs_zret0003-zlower .
          SORT lt_value BY  value ASCENDING ."最小值
        WHEN gs_zret0003-zhigher .
          SORT lt_value BY  value DESCENDING ."最大值
        WHEN OTHERS.
          SORT lt_value BY  value DESCENDING . "最大值
      ENDCASE.

      READ TABLE lt_value INDEX 1.
      IF sy-subrc = 0.
        lv_value = lt_value-value.
      ENDIF.
    ENDIF.

    IF gs_alv-zhstype = '' OR  gs_alv-zhstype = 'A'.
      "固定起始日期
      gs_alv-zfldqje = lv_value - lv_ljje.
      lv_ljje = lv_value.
      gs_alv-zflljje = lv_ljje.
    ELSE.
      "连续期间
      gs_alv-zfldqje = lv_value.    "当期值
      lv_ljje = lv_ljje + lv_value."协议累计值  累计值 + 当期值
      gs_alv-zflljje = lv_ljje.    "累计值
    ENDIF.

    READ TABLE lt_zretcm04 INTO DATA(ls_zretcm04) WITH KEY zfldfsj =  gs_alv-zfldfsj BINARY SEARCH.
    IF sy-subrc = 0.
      gs_alv-zfldfsj_t =  ls_zretcm04-zfldfsj_t.
    ENDIF.

    APPEND gs_alv TO gt_alv.
    CLEAR:gs_data,gs_alv.

    AT END OF zxy_id .
      CLEAR:lv_ljje,lv_value.
    ENDAT.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHORITY_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> <FS_DATA>
*&      <-- LV_NOTAUTHOR
*&---------------------------------------------------------------------*
FORM frm_authority_check  USING    ps_data   STRUCTURE gs_data
                          CHANGING lv_notauthor.

  CLEAR:lv_notauthor.
*  返利合同-合同类型
  AUTHORITY-CHECK OBJECT 'ZREAR006'
                    ID 'ZHTLX' FIELD ps_data-zhtlx
                    ID 'ACTVT' FIELD '03'.

  IF sy-subrc NE 0.
    lv_notauthor = 'X' .
    EXIT.
  ENDIF.

* 返利合同-采购组（返利合同采购组、返利条款采购组都要校验）
  AUTHORITY-CHECK OBJECT 'ZREAR008'
                      ID 'EKGRP' FIELD ps_data-ekgrp
                      ID 'ACTVT' FIELD '03'.
  IF sy-subrc NE 0.
    lv_notauthor = 'X' .
    EXIT.
  ENDIF.

  AUTHORITY-CHECK OBJECT 'ZREAR008'
                    ID 'EKGRP' FIELD ps_data-ht_ekgrp
                    ID 'ACTVT' FIELD '03'.
  IF sy-subrc NE 0.
    lv_notauthor = 'X' .
    EXIT.
  ENDIF.

*返利条款-协议主体
  AUTHORITY-CHECK OBJECT 'ZREAR009'
                      ID 'BUKRS' FIELD ps_data-zbukrs
                      ID 'ACTVT' FIELD '03'.
  IF sy-subrc NE 0.
    lv_notauthor = 'X' .
    EXIT.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SHOW_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_show_data .
  gs_layout-zebra        = 'X'.
  gs_layout-cwidth_opt   = 'X'.
  gs_layout-sel_mode     = 'A'.
  gs_layout-box_fname    = 'SEL'.
  SET TITLEBAR 'T1000'.
*内表字段
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZXY_ID'         '协议号码'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZTK_ID'         '返利条款编码'                  '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHT_ID'         '返利合同编码'                  '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZTK_TXT'        '条款描述'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZTKTYPE'        '条款类型'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBPTYPE'        '伙伴类型'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBPCODE'        '伙伴编码'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBPCODE_TXT'    '伙伴编码描述'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLSQF'         '返利收取方'              '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'BUTXT'          '返利收取方名称'          '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLZFF'         '返利支付方'              '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'NAME1'          '返利支付方名称'          '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBUKRS'          '协议主体'               '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBUKRS_T'       '协议主体名称'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'EKGRP'          '采购组'                  '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'EKGRP1'         '采购组名称'              '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLLX'          '返利类型'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLLX_TXT'      '返利类型描述'            '10'.

  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHTLX'          '合同类型'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHTLX_TXT'      '合同类型描述'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZXYBSTYP'       '协议类型'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHSJZ'          '核算基准'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHSJZ_TXT'      '核算基准描述'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLXS'          '返利形式'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLXS_TXT'      '返利形式描述'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLDFSJ'      '返利兑付时间'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLDFSJ_T'      '返利兑付时间描述'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHSQJ_ID'       '核算期间ID'              '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBEGIN'         '开始日期'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZEND'           '结束日期'                '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZPURJZSL_SUM'   '期间采购数量'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZPURJZJE_SUM'   '期间采购总额'            '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDISTJZSL_SUM'   '期间配送数量'           '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDISTJZJE_SUM'   '期间配送总额'           '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZSALEJZSL_SUM'   '期间销售数量'           '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZSALEJZJE_SUM'   '期间销售总额'           '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDUEPAYJE_SUM'   '总应付金额'             '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZACTLPAYJE_SUM'  '总实付金额'             '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZPURYSSL_SUM'    '期间应收数量(采购)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZPURYSJE_SUM'    '期间应收金额(采购)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZRPURJZJE_SUM'   '期间采购总额(返利纬度)' '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDISTYSSL_SUM'   '期间应收数量(配送)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDISTYSJE_SUM'   '期间应收金额(配送)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING ' ZRDISTJZJE_SUM' '期间配送总额(返利纬度)' '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZSALEYSSL_SUM'   '期间应收数量(销售)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZSALEYSJE_SUM'   '期间应收金额(销售)'     '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZRSALEJZJE_SUM'  '期间销售总额(返利纬度)' '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZDUERCV_SUM'     '应收金额(应付)'         '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZACTLRCV_SUM'    '应收金额(实付)'         '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZZJSBS'          '已结算'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLDQJE'          '当期值'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLLJJE'          '累计值'                 '10'.

  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = gs_layout
      it_fieldcat_lvc          = gt_fieldcat
      i_callback_pf_status_set = 'FRM_SET_STATUS'
      i_callback_user_command  = 'FRM_USER_COMMAND'
*     I_DEFAULT                = 'X'
      i_save                   = 'U'
    TABLES
      t_outtab                 = gt_alv
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_FIELDCAT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_set_fieldcat  TABLES pt_fieldcat TYPE lvc_t_fcat
                        USING i_fieldname TYPE char20
                              i_seltext   TYPE char40
                              i_outputlen TYPE lvc_outlen.

  DATA:ls_fieldcat TYPE lvc_s_fcat.


  IF i_fieldname = 'SEL'  .
    ls_fieldcat-checkbox = 'X'..
    ls_fieldcat-edit     = 'X'.
  ENDIF.
  ls_fieldcat-fieldname = i_fieldname.
  ls_fieldcat-scrtext_l = i_seltext.
  ls_fieldcat-outputlen = i_outputlen.
  ls_fieldcat-no_zero = 'X'.

  CASE i_fieldname .
    WHEN 'MATNR'  .
      ls_fieldcat-convexit = 'MATN1'.
    WHEN 'ZFLSQF' OR 'ZFLZFF'  .
      ls_fieldcat-convexit = 'ALPHA'.
    WHEN 'ZXYBSTYP' .
      ls_fieldcat-convexit  = 'ZXYBS'.
    WHEN: 'MEINS' .
      ls_fieldcat-convexit = 'CUNIT'.
    WHEN: 'EKGRP1' .
      ls_fieldcat-convexit = 'EKGRP'.
    WHEN: 'ZXY_ID' OR 'ZHSQJ_ID' OR 'ZTK_ID'.
      ls_fieldcat-hotspot = 'X'.
    WHEN:'ZBPTYPE'.  ls_fieldcat-convexit = 'ZBPTP'.
    WHEN:'ZTKTYPE'.  ls_fieldcat-convexit  = 'ZTKTYPE'..

    WHEN:'ZFLLX'.
      ls_fieldcat-no_zero = ''.
    WHEN OTHERS.
*      LS_FIELDCAT-COL_OPT = 'X'.
  ENDCASE.


  APPEND ls_fieldcat TO pt_fieldcat.

ENDFORM.
**&---------------------------------------------------------------------*
**&      FORM  PROCESS_COMMAND
**&---------------------------------------------------------------------*
**       TEXT
**----------------------------------------------------------------------*
**      -->RT_COM       TEXT
**      -->RS_SELFIELD  TEXT
**----------------------------------------------------------------------*
FORM frm_user_command USING rt_com LIKE sy-ucomm
      rs_selfield TYPE slis_selfield.

  DATA  :lc_grid TYPE REF TO cl_gui_alv_grid.
  RANGES:s_zbudat FOR zret0026-zbudat.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR'
    IMPORTING
      e_grid = lc_grid.

  CALL METHOD lc_grid->check_changed_data.
  CASE rt_com.
    WHEN 'RWL'.
      PERFORM frm_get_data_rwl   .
    WHEN '&IC1'.
      READ TABLE gt_alv INTO gs_alv INDEX rs_selfield-tabindex.
      IF sy-subrc = 0.
        IF rs_selfield-fieldname = 'ZHSQJ_ID' .
          PERFORM frm_ic1_bill USING gs_alv  .
        ELSEIF rs_selfield-fieldname = 'ZXY_ID' .
*          SET PARAMETER ID : 'ZXYID' FIELD rs_selfield-value.
*          CALL TRANSACTION 'ZRED0001C' AND SKIP FIRST SCREEN.
*          SET PARAMETER ID 'ZTK_ID' FIELD ''.
        ELSEIF rs_selfield-fieldname = 'ZTK_ID'.
          SET PARAMETER ID 'ZTK_ID' FIELD rs_selfield-value.
          CALL TRANSACTION 'ZRED0041C' AND SKIP FIRST SCREEN.
          SET PARAMETER ID 'ZTK_ID' FIELD ''.
        ELSEIF rs_selfield-fieldname = 'ZPURJZSL_SUM' OR
                rs_selfield-fieldname = 'ZPURJZJE_SUM' .

          s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
          SUBMIT  zrer0003 WITH s_zxy_id-low = gs_alv-zxy_id
                           WITH s_zbsart-low = 'P'
                           WITH s_zbudat IN s_zbudat
                           AND RETURN.

        ELSEIF rs_selfield-fieldname = 'ZDISTJZSL_SUM' OR
                rs_selfield-fieldname = 'ZDISTJZJE_SUM' .

          s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
          SUBMIT  zrer0003 WITH s_zxy_id-low = gs_alv-zxy_id
                           WITH s_zbsart-low = 'D'
                           WITH s_zbudat IN s_zbudat
                           AND RETURN.
        ELSEIF rs_selfield-fieldname = 'ZSALEJZSL_SUM' OR
                rs_selfield-fieldname = 'ZSALEJZJE_SUM' .

          IF gs_alv-zfllx = 'RB06'.
            s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
            SUBMIT  zrer0003 WITH s_zxy_id-low = gs_alv-zxy_id
                             WITH s_zbsart-low = 'T'
                             WITH s_zbudat IN s_zbudat
                             AND RETURN.
          ELSE.
            s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
            SUBMIT  zrer0003 WITH s_zxy_id-low = gs_alv-zxy_id
                             WITH s_zbsart-low = 'S'
                             WITH s_zbudat IN s_zbudat
                             AND RETURN.
          ENDIF.
        ENDIF.
      ENDIF.
  ENDCASE.

  rs_selfield-refresh    = 'X'.
  rs_selfield-col_stable = 'X'.
  rs_selfield-row_stable = 'X'.
  SET TITLEBAR 'T1000'.
ENDFORM.                    "PROCESS_COMMAND
*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_status USING rt_extab TYPE slis_t_extab.
*
*  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH KEY zsplit = 'X'.
*  IF sy-subrc <> 0.
*    rt_extab = VALUE #( ( fcode =  'RWL' ) ).
*  ENDIF.
  SET PF-STATUS 'S1000' EXCLUDING rt_extab .

ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*& FORM FRM_IC1_BILL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> RS_SELFIELD
*&---------------------------------------------------------------------*
FORM frm_ic1_bill  USING  gs_alv STRUCTURE gs_alv .
  DATA: layout TYPE lvc_s_layo .
  DATA:lt_fieldcat TYPE lvc_t_fcat,
       ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_field_show.
    ls_fieldcat-fieldname = &1.   "字段名称
    ls_fieldcat-scrtext_l = &2.  "字段描述
    ls_fieldcat-no_zero = 'X'.  "字段描述
    APPEND ls_fieldcat TO lt_fieldcat.
  END-OF-DEFINITION.

  layout-cwidth_opt = 'X'.
  layout-zebra      = 'X'.
  layout-no_toolbar = 'X'.
  layout-detailtitl = 'X'.

  REFRESH:gt_zret0017.
  SELECT *
  INTO  TABLE  gt_zret0017
  FROM zret0017
  WHERE zxy_id = gs_alv-zxy_id
    AND zbudat >= gs_alv-zbegin
    AND zbudat <= gs_alv-zend .

  SORT gt_zret0017 BY zbudat.


  SELECT SINGLE zret0003~*
    INTO @DATA(ls_zret0003)
    FROM zret0003
    WHERE zhsjz = @gs_alv-zhsjz.



  REFRESH lt_fieldcat.
  add_field_show:
                  'ZXY_ID' '协议号码',
                  'ZBUDAT' '日期'.

  IF ls_zret0003-zpur = 'X' .
    add_field_show: 'ZPURJZSL' '当日基准采购数量' .
    add_field_show: 'ZPURJZJE' '当日基准采购总额' .
  ENDIF.
  IF ls_zret0003-zdist = 'X' .
    add_field_show: 'ZDISTJZSL' '当日基准调拨数量'.
    add_field_show: 'ZDISTJZJE' '当日基准调拨总额'.
  ENDIF.
  IF ls_zret0003-zsale = 'X' .
    add_field_show: 'ZSALEJZSL' '当日基准销售数量'.
    add_field_show: 'ZSALEJZJE' '当日基准销售总额'.
  ENDIF.
  IF ls_zret0003-zduepay = 'X' OR ls_zret0003-zactlpay = 'X'   .
    add_field_show: 'ZDUEPAYJE'  '应付金额'  .
    add_field_show: 'ZACTLPAYJE'  '实付金额'  .
  ENDIF.

  IF gs_alv-zfllx = 'RB06'.
    add_field_show: 'ZSALEJZSL' '当日基准销售数量'.
    add_field_show: 'ZSALEJZJE' '当日基准销售总额'.
  ENDIF.


  IF gs_alv-zflxs = 'Q' .

    IF ls_zret0003-zpur = 'X' .
      add_field_show: 'ZPURYSSL'  '当日采购应收数量'.
    ENDIF.
    IF ls_zret0003-zdist = 'X' .
      add_field_show: 'ZDISTYSSL'  '当日调拨应收数量'.
    ENDIF.
    IF ls_zret0003-zsale = 'X' .
      add_field_show: 'ZSALEYSSL'  '当日销售应收数量'.
    ENDIF.
    IF ls_zret0003-zduepay = 'X' OR ls_zret0003-zactlpay = 'X'   .
      add_field_show: 'ZDUERCV'  '应收金额(基于应付)'.
      add_field_show: 'ZACTLRCV'  '应收金额(基于实付)'.
    ENDIF.

  ELSE.
    IF ls_zret0003-zpur = 'X' .
      add_field_show: 'ZPURYSJE'  '当日采购应收金额'.
    ENDIF.
    IF ls_zret0003-zdist = 'X' .
      add_field_show: 'ZDISTYSJE'  '当日调拨应收金额'.
    ENDIF.
    IF ls_zret0003-zsale = 'X' .
      add_field_show: 'ZSALEYSJE'  '当日销售应收金额'.
    ENDIF.
    IF ls_zret0003-zduepay = 'X' OR ls_zret0003-zactlpay = 'X'   .
      add_field_show: 'ZDUERCV'  '应收金额(基于应付)'.
      add_field_show: 'ZACTLRCV'  '应收金额(基于实付)'.
    ENDIF.

    IF gs_alv-zfllx = 'RB06'. .
      add_field_show: 'ZSALEYSJE'  '当日销售应收金额'.
    ENDIF.

*    CASE 'X' .
*      WHEN GS_ZRET0003-ZPUR.
*        ADD_FIELD_SHOW: 'ZPURYSJE'  '当日采购应收金额'.
*      WHEN GS_ZRET0003-ZDIST .
*        ADD_FIELD_SHOW: 'ZDISTYSJE'  '当日调拨应收金额'.
*      WHEN GS_ZRET0003-ZSALE .
*        ADD_FIELD_SHOW: 'ZSALEYSJE'  '当日销售应收金额'.
*      WHEN GS_ZRET0003-ZDUEPAY OR GS_ZRET0003-ZACTLPAY.
*        ADD_FIELD_SHOW: 'ZDUERCV'  '应收金额(基于应付)'.
*        ADD_FIELD_SHOW: 'ZACTLRCV'  '应收金额(基于实付)'.
*      WHEN OTHERS.
*    ENDCASE.
  ENDIF.

  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = layout
      it_fieldcat_lvc          = lt_fieldcat
*     IS_VARIANT               = WA_VARIANT
      i_callback_pf_status_set = 'FRM_SET_ITEM_STATUS'
      i_callback_user_command  = 'FRM_USERCOMM_ITEM'
    TABLES
      t_outtab                 = gt_zret0017.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_USERCOMM_&IC1
*&---------------------------------------------------------------------*
*& 热点按钮
*&---------------------------------------------------------------------*
FORM frm_usercomm_item USING i_ucomm      TYPE sy-ucomm
                            re_selfield  TYPE slis_selfield..

  CASE i_ucomm.
    WHEN '&IC1'.
      READ TABLE gt_zret0017 INTO DATA(ls_zret0017) INDEX re_selfield-tabindex.
      IF sy-subrc = 0.
        IF re_selfield-fieldname = 'ZPURJZSL' OR
           re_selfield-fieldname = 'ZPURJZJE' OR
           re_selfield-fieldname = 'ZPURYSSL' OR
           re_selfield-fieldname = 'ZPURYSJE'  .
          SUBMIT  zrer0003 WITH s_zxy_id-low = ls_zret0017-zxy_id
                           WITH s_zbsart-low = 'P'
                           WITH s_zbudat-low =  ls_zret0017-zbudat
                           AND RETURN.
        ELSEIF re_selfield-fieldname = 'ZDISTJZSL' OR
                re_selfield-fieldname = 'ZDISTJZJE' OR
                re_selfield-fieldname = 'ZDISTYSSL' OR
                re_selfield-fieldname = 'ZDISTYSJE'   .
          SUBMIT  zrer0003 WITH s_zxy_id-low = ls_zret0017-zxy_id
                           WITH s_zbsart-low = 'D'
                           WITH s_zbudat-low =  ls_zret0017-zbudat
                           AND RETURN.
        ELSEIF re_selfield-fieldname = 'ZSALEJZSL' OR
                re_selfield-fieldname = 'ZSALEJZJE' OR
                re_selfield-fieldname = 'ZSALEYSSL' OR
                re_selfield-fieldname = 'ZSALEYSJE'  .
          SUBMIT  zrer0003 WITH s_zxy_id-low = ls_zret0017-zxy_id
                           WITH s_zbsart-low = 'S'
                           WITH s_zbudat-low =  ls_zret0017-zbudat
                           AND RETURN.
        ENDIF.
      ENDIF.


    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_item_status USING rt_extab TYPE slis_t_extab.

  SET PF-STATUS 'S0000' EXCLUDING rt_extab .

ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*& Form FRM_GET_RWLDATA
*&---------------------------------------------------------------------*
*& text 获取任务量数据
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data_rwl  .

  DATA(lt_alv) =  gt_alv[].
  SORT lt_alv BY zxy_id.
  DELETE ADJACENT DUPLICATES FROM lt_alv COMPARING zxy_id.
  IF lines( lt_alv ) <> 1 .
    lt_alv = gt_alv[].
    DELETE lt_alv WHERE sel = ''.
    SORT lt_alv BY zxy_id.
    DELETE ADJACENT DUPLICATES FROM lt_alv COMPARING zxy_id.
    IF lines( lt_alv ) <> 1.
      MESSAGE '请选择一个协议数据！' TYPE 'E'.
    ELSE.
      READ TABLE gt_alv WITH KEY sel = 'X' INTO gs_alv .
*      IF gs_alv-zsplit IS  INITIAL .
*        MESSAGE '不包含任务数据！' TYPE 'E'.
*      ENDIF.
    ENDIF.
  ELSE.
    READ TABLE lt_alv INDEX 1 INTO gs_alv .
*    IF gs_alv-zsplit IS  INITIAL .
*      MESSAGE '不包含任务数据！' TYPE 'E'.
*    ENDIF.
  ENDIF.

  DATA:gt_rwlalv LIKE TABLE OF gs_alv.
  DATA:ls_layout   TYPE lvc_s_layo.
  DATA:lt_fieldcat TYPE lvc_t_fcat.
  DATA:ls_fieldcat TYPE lvc_s_fcat.

  SELECT
         zret0006~zxy_id    ,
         zret0015~zhsqj_id  ,
         zret0015~zbegin    ,
         zret0015~zend      ,
      SUM( zret0040~zpurjzsl   ) AS zpurjzsl_sum  ,
      SUM( zret0040~zpurjzje   ) AS zpurjzje_sum  ,
      SUM( zret0040~zdistjzsl  ) AS zdistjzsl_sum ,
      SUM( zret0040~zdistjzje  ) AS zdistjzje_sum ,
      SUM( zret0040~zsalejzsl  ) AS zsalejzsl_sum ,
      SUM( zret0040~zsalejzje  ) AS zsalejzje_sum ,
      SUM( zret0040~zduepayje  ) AS zduepayje_sum ,
      SUM( zret0040~zactlpayje ) AS zactlpayje_sum
        INTO CORRESPONDING FIELDS OF  TABLE @gt_rwlalv
        FROM zret0006
        INNER JOIN  zret0015 ON zret0015~zxy_id   = zret0006~zxy_id
        LEFT  JOIN  zret0040 ON zret0040~zxy_id   = zret0006~zxy_id
                            AND zret0040~zbudat  >= zret0015~zbegin AND
                                zret0040~zbudat  <= zret0015~zend
        WHERE zret0006~zxy_id  =  @gs_alv-zxy_id
    GROUP BY  zret0006~zxy_id    ,
              zret0015~zhsqj_id  ,
              zret0015~zbegin    ,
              zret0015~zend
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .

  LOOP AT  gt_rwlalv ASSIGNING FIELD-SYMBOL(<lfs_rwlalv>) .
    <lfs_rwlalv>-ztk_id      = gs_alv-ztk_id.
    <lfs_rwlalv>-ztk_txt     = gs_alv-ztk_txt.
    <lfs_rwlalv>-zbptype     = gs_alv-zbptype.
    <lfs_rwlalv>-zbpcode     = gs_alv-zbpcode.
    <lfs_rwlalv>-zbpcode_txt = gs_alv-zbpcode_txt.
    <lfs_rwlalv>-zflsqf      = gs_alv-zflsqf.
    <lfs_rwlalv>-butxt       = gs_alv-butxt.
    <lfs_rwlalv>-zflzff      = gs_alv-zflzff.
    <lfs_rwlalv>-name1       = gs_alv-name1.
    <lfs_rwlalv>-zbukrs      = gs_alv-zbukrs.
    <lfs_rwlalv>-zbukrs_t    = gs_alv-zbukrs_t.
    <lfs_rwlalv>-ekgrp       = gs_alv-ekgrp.
    <lfs_rwlalv>-eknam      = gs_alv-eknam.
    <lfs_rwlalv>-zfllx       = gs_alv-zfllx.
    <lfs_rwlalv>-zfllx_txt   = gs_alv-zfllx_txt.
    <lfs_rwlalv>-zhtlx       = gs_alv-zhtlx.
    <lfs_rwlalv>-zhtlx_txt   = gs_alv-zhtlx_txt.
    <lfs_rwlalv>-zhsjz       = gs_alv-zhsjz.
    <lfs_rwlalv>-zhsjz_txt   = gs_alv-zhsjz_txt.
    <lfs_rwlalv>-zflxs       = gs_alv-zflxs.
    <lfs_rwlalv>-zflxs_txt   = gs_alv-zflxs_txt.
    CASE <lfs_rwlalv>-zflxs.
      WHEN 'M'.
        <lfs_rwlalv>-zflxs_txt = '金额'.
      WHEN 'Q'.
        <lfs_rwlalv>-zflxs_txt = '数量'.
      WHEN OTHERS.
    ENDCASE.

  ENDLOOP.

  SORT gt_rwlalv BY zxy_id zhsqj_id.


  ls_layout-zebra       = 'X'.
  ls_layout-zebra       = 'X'.
  ls_layout-cwidth_opt   = 'X'.
  ls_layout-sel_mode     = 'A'.
  SET TITLEBAR 'T1000_RWL'.



*内表字段
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZXY_ID'         '协议号码'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZTK_ID'        '条款ID'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZTK_TXT'        '条款描述'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLSQF'         '返利收取方'              '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'BUTXT'          '返利收取方名称'          '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLZFF'         '返利支付方'              '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'NAME1'          '返利支付方名称'          '10'.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZBUKRS'          '协议主体'          '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZBUKRS_T'          '协议主体名称'          '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'EKGRP'          '采购组'          '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'EKNAM'          '采购组名称'          '10'.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLLX'          '返利类型'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLLX_TXT'      '返利类型描述'            '10'.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZHTLX'          '合同类型'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZHTLX_TXT'      '合同类型描述'            '10'.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZHSJZ'          '核算基准'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZHSJZ_TXT'      '核算基准描述'            '10'.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLXS'          '返利形式'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZFLXS_TXT'      '返利形式描述'            '10'.


  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZHSQJ_ID'       '核算期间ID'              '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZBEGIN'         '开始日期'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZEND'           '结束日期'                '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZPURJZSL_SUM'   '期间采购数量'            '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZPURJZJE_SUM'   '期间采购总额'            '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZDISTJZSL_SUM'   '期间配送数量'           '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZDISTJZJE_SUM'   '期间配送总额'           '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZSALEJZSL_SUM'   '期间销售数量'           '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZSALEJZJE_SUM'   '期间销售总额'           '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZDUEPAYJE_SUM'   '总应付金额'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZACTLPAYJE_SUM'  '总实付金额'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZDUERCV_SUM'     '应收金额(应付)'         '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZACTLRCV_SUM'    '应收金额(实付)'         '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat  USING 'ZZJSBS'          '已结算'                 '10'.




  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = ls_layout
      it_fieldcat_lvc          = lt_fieldcat
      i_callback_pf_status_set = 'FRM_SET_STATUS_RWL'
      i_callback_user_command  = 'FRM_USER_COMMAND_RWL'
*     I_DEFAULT                = 'X'
      i_save                   = 'U'
    TABLES
      t_outtab                 = gt_rwlalv
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_status_rwl USING rt_extab TYPE slis_t_extab.

  SET PF-STATUS 'S1000B' EXCLUDING rt_extab .

ENDFORM.                    "PFSTATUS_FORM
**&---------------------------------------------------------------------*
**&      FORM  PROCESS_COMMAND
**&---------------------------------------------------------------------*
**       TEXT
**----------------------------------------------------------------------*
**      -->RT_COM       TEXT
**      -->RS_SELFIELD  TEXT
**----------------------------------------------------------------------*
FORM frm_user_command_rwl USING rt_com LIKE sy-ucomm
      rs_selfield TYPE slis_selfield.

  DATA  :lc_grid TYPE REF TO cl_gui_alv_grid.
  RANGES:s_zbudat FOR zret0026-zbudat.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR'
    IMPORTING
      e_grid = lc_grid.

  CALL METHOD lc_grid->check_changed_data.
  CASE rt_com.
    WHEN '&IC1'.
      READ TABLE gt_alv INTO gs_alv INDEX rs_selfield-tabindex.
      IF sy-subrc = 0.
        IF rs_selfield-fieldname = 'ZHSQJ_ID' .
          PERFORM frm_ic1_bill_rwl USING gs_alv  .
        ELSEIF rs_selfield-fieldname = 'ZXY_ID' .
          SET PARAMETER ID : 'ZXYID' FIELD rs_selfield-value.
          CALL TRANSACTION 'ZRED0001C' AND SKIP FIRST SCREEN.

        ELSEIF rs_selfield-fieldname = 'ZPURJZSL_SUM' OR
                rs_selfield-fieldname = 'ZPURJZJE_SUM' .

          s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
          SUBMIT  zrer0011 WITH s_zxy_id-low = gs_alv-zxy_id
                           WITH s_zbsart-low = 'P'
                           WITH s_zbudat IN s_zbudat
                           AND RETURN.

        ELSEIF rs_selfield-fieldname = 'ZDISTJZSL_SUM' OR
                rs_selfield-fieldname = 'ZDISTJZJE_SUM' .

          s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
          SUBMIT  zrer0011 WITH s_zxy_id-low = gs_alv-zxy_id
                           WITH s_zbsart-low = 'D'
                           WITH s_zbudat IN s_zbudat
                           AND RETURN.
        ELSEIF rs_selfield-fieldname = 'ZSALEJZSL_SUM' OR
                rs_selfield-fieldname = 'ZSALEJZJE_SUM' .
          s_zbudat[] = VALUE #(  ( sign = 'I' option = 'EQ'  low = gs_alv-zbegin   high = gs_alv-zend )   ).
          SUBMIT  zrer0011 WITH s_zxy_id-low = gs_alv-zxy_id
                           WITH s_zbsart-low = 'S'
                           WITH s_zbudat IN s_zbudat
                           AND RETURN.
        ENDIF.
      ENDIF.
  ENDCASE.

  rs_selfield-refresh    = 'X'.
  rs_selfield-col_stable = 'X'.
  rs_selfield-row_stable = 'X'.
ENDFORM.                    "PROCESS_COMMAND
*&---------------------------------------------------------------------*
*& FORM FRM_IC1_BILL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> RS_SELFIELD
*&---------------------------------------------------------------------*
FORM frm_ic1_bill_rwl  USING  gs_alv STRUCTURE gs_alv .
  DATA: layout TYPE lvc_s_layo .
  DATA:lt_fieldcat TYPE lvc_t_fcat,
       ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_field_show.
    ls_fieldcat-fieldname = &1.   "字段名称
    ls_fieldcat-scrtext_l = &2.  "字段描述
    ls_fieldcat-no_zero = 'X'.  "字段描述
    APPEND ls_fieldcat TO lt_fieldcat.
  END-OF-DEFINITION.

  layout-cwidth_opt = 'X'.
  layout-zebra      = 'X'.
  layout-no_toolbar = 'X'.
  layout-detailtitl = 'X'.

  REFRESH:gt_zret0040.
  SELECT *
  INTO  TABLE  gt_zret0040
  FROM zret0040
  WHERE zxy_id = gs_alv-zxy_id
    AND zbudat >= gs_alv-zbegin
    AND zbudat <= gs_alv-zend
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029. .

  SORT gt_zret0017 BY zbudat.


  SELECT SINGLE zret0003~*
    INTO @DATA(ls_zret0003)
    FROM zret0003 INNER JOIN zret0006 ON zret0003~zhsjz = zret0006~zhsjz
    WHERE zxy_id = @gs_alv-zxy_id
    %_HINTS HDB 'RESULT_LAG(''hana_sr'',600)' .             "ERP-16029..



  REFRESH lt_fieldcat.
  add_field_show:
                  'ZXY_ID' '协议号码',
                  'ZBUDAT' '日期'.

  IF ls_zret0003-zpur = 'X' .
    add_field_show: 'ZPURJZSL' '当日基准采购数量' .
    add_field_show: 'ZPURJZJE' '当日基准采购总额' .
  ENDIF.
  IF ls_zret0003-zdist = 'X' .
    add_field_show: 'ZDISTJZSL' '当日基准调拨数量'.
    add_field_show: 'ZDISTJZJE' '当日基准调拨总额'.
  ENDIF.
  IF ls_zret0003-zsale = 'X' .
    add_field_show: 'ZSALEJZSL' '当日基准销售数量'.
    add_field_show: 'ZSALEJZJE' '当日基准销售总额'.
  ENDIF.
  IF ls_zret0003-zduepay = 'X' OR ls_zret0003-zactlpay = 'X'   .
    add_field_show: 'ZDUEPAYJE'  '应付金额'  .
    add_field_show: 'ZACTLPAYJE'  '实付金额'  .
  ENDIF.

  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = layout
      it_fieldcat_lvc          = lt_fieldcat
*     IS_VARIANT               = WA_VARIANT
      i_callback_pf_status_set = 'FRM_SET_ITEM_STATUS_RWL'
      i_callback_user_command  = 'FRM_USERCOMM_ITEM_RWL'
    TABLES
      t_outtab                 = gt_zret0040.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_USERCOMM_&IC1
*&---------------------------------------------------------------------*
*& 热点按钮
*&---------------------------------------------------------------------*
FORM frm_usercomm_item_rwl USING i_ucomm      TYPE sy-ucomm
                            re_selfield  TYPE slis_selfield..

  CASE i_ucomm.
    WHEN '&IC1'.
      READ TABLE gt_zret0040 INTO DATA(ls_zret0040) INDEX re_selfield-tabindex.
      IF sy-subrc = 0.
        IF re_selfield-fieldname = 'ZPURJZSL' OR
           re_selfield-fieldname = 'ZPURJZJE'  .
          SUBMIT  zrer0011 WITH s_zxy_id-low = ls_zret0040-zxy_id
                           WITH s_zbsart-low = 'P'
                           WITH s_zbudat-low =  ls_zret0040-zbudat
                           AND RETURN.
        ELSEIF re_selfield-fieldname = 'ZDISTJZSL' OR
                re_selfield-fieldname = 'ZDISTJZJE' .
          SUBMIT  zrer0011 WITH s_zxy_id-low = ls_zret0040-zxy_id
                           WITH s_zbsart-low = 'D'
                           WITH s_zbudat-low =  ls_zret0040-zbudat
                           AND RETURN.
        ELSEIF re_selfield-fieldname = 'ZSALEJZSL' OR
                re_selfield-fieldname = 'ZSALEJZJE'.
          SUBMIT  zrer0011 WITH s_zxy_id-low = ls_zret0040-zxy_id
                           WITH s_zbsart-low = 'S'
                           WITH s_zbudat-low =  ls_zret0040-zbudat
                           AND RETURN.
        ENDIF.
      ENDIF.


    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_item_status_rwl USING rt_extab TYPE slis_t_extab.

  SET PF-STATUS 'S0000' EXCLUDING rt_extab .

ENDFORM.                    "PFSTATUS_FORM