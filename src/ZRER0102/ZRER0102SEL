*&---------------------------------------------------------------------*
*& 包含               ZRED0015SEL
*&---------------------------------------------------------------------*
TABLES:zret0015,zret0017,zret0006,zreta001,zreta002,t024,t001.
SELECTION-SCREEN BEGIN OF BLOCK bl1 WITH FRAME TITLE TEXT-001 .


SELECT-OPTIONS s_zbukht  FOR t001-bukrs  NO-DISPLAY .
SELECT-OPTIONS s_zbukxy  FOR t001-bukrs      OBLIGATORY  MEMORY ID buk.
SELECT-OPTIONS s_ekgrp   FOR t024-ekgrp      OBLIGATORY  MEMORY ID ekg.
SELECT-OPTIONS s_zhtlx   FOR zret0006-zhtlx  OBLIGATORY  MEMORY ID zhtlx.
SELECT-OPTIONS s_bptype  FOR zreta001-zbptype  NO-EXTENSION NO INTERVALS."伙伴类型
SELECT-OPTIONS s_bpcode  FOR zreta001-zbpcode  ."伙伴编码
SELECT-OPTIONS s_zht_id  FOR zreta001-zht_id  ."合同编码
SELECT-OPTIONS s_ztk_id  FOR zreta002-ztk_id MATCHCODE OBJECT zresh0018 ."条款编码
SELECT-OPTIONS s_zxy_id  FOR zret0015-zxy_id MATCHCODE OBJECT zresh0005 MEMORY ID  zxyid .

SELECT-OPTIONS s_hsqjid  FOR zret0015-zhsqj_id     .
SELECT-OPTIONS s_zbudat  FOR zret0017-zbudat    .
SELECT-OPTIONS s_zxyzt   FOR zret0006-zxyzt DEFAULT 'A'    .
PARAMETERS:p_incld      TYPE c AS CHECKBOX DEFAULT 'X'.
PARAMETERS p_job NO-DISPLAY.

SELECTION-SCREEN END OF BLOCK bl1.