*&---------------------------------------------------------------------*
*& REPORT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
REPORT zrer0013.

TABLES:
  zreta001,
  zreta002.
TYPE-POOLS icon.
TYPES:
  BEGIN OF ty_data,

    sel      TYPE char1,
    coin(10),
    message  TYPE text100,
    zht_id   TYPE zreta001-zht_id,
    zht_txt  TYPE zreta001-zht_txt,
    zhtlx    TYPE zreta001-zhtlx,
    zbukrs   TYPE zreta001-zbukrs,
    zbptype  TYPE zreta001-zbptype,
    zbpcode  TYPE zreta001-zbpcode,

    ztk_id   TYPE zreta002-ztk_id,
    ztk_txt  TYPE zreta002-ztk_txt,
    zfllx    TYPE zreta002-zfllx,
    ekgrp    TYPE zreta002-ekgrp,
    zxyzt    TYPE zreta002-zxyzt,
    frgsx    TYPE zreta002-frgsx,
    kolnr    TYPE zreta002-kolnr,
    zcjr     TYPE zreta002-zcjr,
    zxybstyp TYPE zreta002-zxybstyp,

    butxt    TYPE t001-butxt,
    zbpname  TYPE lfa1-name1,
    eknam    TYPE t024-eknam,
    frgc1    TYPE zretc007-frgc1,
    zfrgtx   TYPE zretc007-zfrgtx,
    zcnyg    TYPE zreta002-zcnyg,

  END OF ty_data,
  tt_data TYPE TABLE OF ty_data.

DATA: lv_title  TYPE lvc_title.
DATA:
      gt_data   TYPE  tt_data.
DATA:wa_data TYPE ty_data.
DATA:w_delete TYPE c.
DATA: gs_bdcdata TYPE bdcdata,
      gi_bdcdata TYPE TABLE OF bdcdata.
DATA: messtab    TYPE TABLE OF bdcmsgcoll,
      ls_messtab TYPE bdcmsgcoll,
      w_message  TYPE text100,
      ls_params  TYPE ctu_params,
      c_mode(1)  TYPE c VALUE 'N'.
DATA: BEGIN OF ls_msglist,
        bcset_id      TYPE scpr_id,       "BC-Set-ID
        category      TYPE scpr_ctgry,    "BC-Set-Kategorie
        activity      TYPE scprimgact,    "IMG-Aktivität
        objectname    TYPE scprcobjna,    "Customzing-Objekt
        objecttype    TYPE scprcobjty,    "Customzing-Objekt-Typ
        tablename     TYPE scpr_tabl,     "Tabellenname
        tabletype     TYPE scprcobjty,    "Tabellentyp
        fieldname     TYPE scpr_fld,      "Feldname
        recnumber     TYPE scpr_recnr,    "Datensatznummer
        add_text(220) TYPE c,             "additional info text
        msgid         TYPE sy-msgid,
        msgty         TYPE sy-msgty,
        msgno         TYPE sy-msgno,
        msgv1         TYPE sy-msgv1,
        msgv2         TYPE sy-msgv2,
        msgv3         TYPE sy-msgv3,
        msgv4         TYPE sy-msgv4,
      END OF ls_msglist,
      lt_msglist LIKE STANDARD TABLE OF ls_msglist.
SELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.

SELECT-OPTIONS:
    s_zht_id FOR zreta001-zht_id MATCHCODE OBJECT zresh0016 MEMORY ID zht_id ,
    s_zhtlx  FOR zreta001-zhtlx ,
    s_zbukrs FOR zreta001-zbukrs MATCHCODE OBJECT  c_t001,
    s_bptype FOR zreta001-zbptype NO-EXTENSION NO INTERVALS,
    s_bpcode FOR zreta001-zbpcode,
    s_ztk_id FOR zreta002-ztk_id MATCHCODE OBJECT zresh0018 MEMORY ID ztk_id,
    s_zfllx  FOR zreta002-zfllx MATCHCODE OBJECT zresh0011,
    s_zxybtp FOR zreta002-zxybstyp ,
    s_ekgrp  FOR zreta002-ekgrp MATCHCODE OBJECT h_t024.
*    s_zxyzt  FOR zreta002-zxyzt .
PARAMETERS:p_zxyzt LIKE zreta002-zxyzt OBLIGATORY.
PARAMETERS:p_frgsx TYPE zretc007-frgsx OBLIGATORY,
           p_frgc1 TYPE zretc007-frgc1 OBLIGATORY.


SELECTION-SCREEN END OF BLOCK b1.

INITIALIZATION.
  PERFORM frm_init.

AT SELECTION-SCREEN .

AT SELECTION-SCREEN ON VALUE-REQUEST FOR s_bpcode-low.
  PERFORM fm_zpbcode_help USING  'LOW'.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR s_bpcode-high.
  PERFORM fm_zpbcode_help USING  'HIGH'.



START-OF-SELECTION.
  PERFORM frm_main.

END-OF-SELECTION.
  PERFORM frm_alv_display.



*&---------------------------------------------------------------------*
*&      FORM  FRM_INIT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_init .

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  F_MAIN
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
FORM frm_main.

  PERFORM frm_aut_check.

  PERFORM frm_get_data              CHANGING gt_data.

  PERFORM frm_data_process          CHANGING gt_data.

  PERFORM frm_data_process_end      CHANGING gt_data.
ENDFORM.                    "F_MAIN

*&---------------------------------------------------------------------*
*&      FORM  GET_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->ITAB       TEXT
*----------------------------------------------------------------------*
FORM frm_get_data CHANGING pt_data TYPE tt_data.

  DATA:
        ls_data TYPE LINE OF tt_data.

  CLEAR:
        pt_data,
        pt_data[].

  SELECT
    *
    FROM zreta001
    WHERE zht_id IN @s_zht_id
    AND   zhtlx  IN @s_zhtlx
    AND   zbukrs IN @s_zbukrs
    AND   zbptype IN @s_bptype
    AND   zbpcode IN @s_bpcode
    INTO TABLE @DATA(lt_ta01).

  SELECT
    *
    FROM zreta002
    WHERE ztk_id IN @s_ztk_id
    AND   zfllx  IN @s_zfllx
    AND   zxybstyp  IN @s_zxybtp
    AND   ekgrp IN @s_ekgrp
*    AND   zxyzt IN @s_zxyzt
    AND zxyzt = @p_zxyzt
    AND frgsx = @p_frgsx
    INTO TABLE @DATA(lt_ta02).

  IF lt_ta02[] IS   INITIAL.
    MESSAGE s001(00) WITH '没有查询到数据'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  SORT lt_ta01 BY zht_id.
  LOOP AT lt_ta02 INTO DATA(ls_ta02).
    CLEAR:
          ls_data.
    READ TABLE lt_ta01 INTO DATA(ls_ta01) WITH KEY zht_id = ls_ta02-zht_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zht_id    = ls_ta01-zht_id.
      ls_data-zht_txt   = ls_ta01-zht_txt.
      ls_data-zhtlx     = ls_ta01-zhtlx.
      ls_data-zbukrs    = ls_ta01-zbukrs.
      ls_data-zbptype   = ls_ta01-zbptype.
      ls_data-zbpcode   = ls_ta01-zbpcode.

      ls_data-ztk_id    = ls_ta02-ztk_id.
      ls_data-ztk_txt   = ls_ta02-ztk_txt.
      ls_data-zfllx     = ls_ta02-zfllx.
      ls_data-ekgrp     = ls_ta02-ekgrp.
      ls_data-zxyzt     = ls_ta02-zxyzt.
      ls_data-frgsx     = ls_ta02-frgsx.
      ls_data-kolnr     = ls_ta02-kolnr.
      ls_data-zcjr      = ls_ta02-zcjr.
      ls_data-zcnyg     = ls_ta02-zcnyg.
      ls_data-zxybstyp  = ls_ta02-zxybstyp.


      SELECT SINGLE frgc1 zfrgtx INTO (ls_data-frgc1,ls_data-zfrgtx) FROM zretc007 WHERE frgsx = ls_data-frgsx AND kolnr = ls_data-kolnr .

      ls_data-coin = icon_yellow_light.
      APPEND ls_data TO pt_data.
    ENDIF.
  ENDLOOP.

  DELETE pt_data WHERE frgc1 <> p_frgc1.                    "ERP-11412

  LOOP AT pt_data INTO ls_data.

    AUTHORITY-CHECK OBJECT 'ZREAR006'
                        ID 'ZHTLX' FIELD ls_data-zhtlx
*                        ID 'ACTVT' FIELD '03'
                        ID 'ACTVT' FIELD p_frgc1.
    IF sy-subrc NE 0.
      DELETE pt_data .
      CONTINUE.
    ENDIF.

    AUTHORITY-CHECK OBJECT 'ZREAR008'
                        ID 'EKGRP' FIELD ls_data-ekgrp
*                        ID 'ACTVT' FIELD '03'.
                        ID 'ACTVT' FIELD p_frgc1.
    IF sy-subrc NE 0.
      DELETE pt_data .
      CONTINUE.
    ENDIF.

    AUTHORITY-CHECK OBJECT 'ZREAR007'
                        ID 'BUKRS' FIELD ls_data-zbukrs
*                        ID 'ACTVT' FIELD '03'.
                        ID 'ACTVT' FIELD p_frgc1.
    IF sy-subrc NE 0.
      DELETE pt_data .
      CONTINUE.
    ENDIF.



  ENDLOOP.


ENDFORM.                    "GET_DATA


FORM frm_data_process CHANGING pt_data TYPE tt_data.

  LOOP AT pt_data ASSIGNING FIELD-SYMBOL(<fs_data>).

    SELECT SINGLE butxt INTO <fs_data>-butxt FROM t001 WHERE bukrs = <fs_data>-zbukrs.
    IF <fs_data>-zbptype = 'S'.
      SELECT SINGLE name1 INTO <fs_data>-zbpname FROM lfa1 WHERE lifnr = <fs_data>-zbpcode.
    ELSEIF <fs_data>-zbptype = 'M'.
*      SELECT SINGLE b0105 INTO <fs_data>-zbpname FROM zmmt0345 WHERE codeitemid = <fs_data>-zbpcode.
      SELECT SINGLE  name_org1 INTO <fs_data>-zbpname  FROM but000 WHERE partner = <fs_data>-zbpcode.
    ENDIF.
    SELECT SINGLE eknam FROM t024 INTO <fs_data>-eknam WHERE ekgrp = <fs_data>-ekgrp .


  ENDLOOP.


  IF pt_data[] IS INITIAL.
    MESSAGE s001(00) WITH '没有查询到数据'.
    LEAVE LIST-PROCESSING.
  ENDIF.

ENDFORM.


FORM frm_data_process_end CHANGING pt_data TYPE tt_data.


  LOOP AT pt_data ASSIGNING FIELD-SYMBOL(<fs_data>).


  ENDLOOP.



ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  CHECK_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
FORM frm_aut_check.
*  AUTHORITY-CHECK OBJECT 'ZMM_RP_AU'
*                      ID 'BUKRS' FIELD P_BUKRS.
*  IF SY-SUBRC NE 0.
*    MESSAGE S888(SABAPDOCU) WITH '您没有公司' P_BUKRS '的操作权限' DISPLAY LIKE 'E'.
*    LEAVE LIST-PROCESSING.
*  ENDIF.

ENDFORM.                    "CHECK_FORM

*&---------------------------------------------------------------------*
*&      FORM  FRM_ALV_DISPLAY
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_alv_display .
  "ALV
  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  DATA: lv_title    TYPE lvc_title.

  PERFORM frm_set_catalog     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout      CHANGING  ls_layout.

  PERFORM frm_set_variant     CHANGING  ls_variant.



  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
*     I_INTERFACE_CHECK        = ''                     "
      i_grid_title             = lv_title
      i_callback_program       = sy-repid
      i_callback_pf_status_set = 'FRM_STATUS_ALV'
      i_callback_user_command  = 'FRM_USER_COMMAND'
      is_layout_lvc            = ls_layout
      it_fieldcat_lvc          = lt_fieldcat
      i_save                   = 'A'
    TABLES
      t_outtab                 = gt_data
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  SET_CATALOG
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_FIELDCAT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_catalog CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext = &5.                 "

    CASE &4.
      WHEN 'ZHTLX'  .
      ls_fieldcat-convexit = 'ZREHT'.
      WHEN 'ZFLLX'  .
      ls_fieldcat-convexit = 'ZFLLX'.
      WHEN 'ZXYZT'  .
      ls_fieldcat-convexit = 'ZXYZT'.
      WHEN 'ZBPTYPE'  .
      ls_fieldcat-convexit = 'ZBPTP'.
      WHEN 'ZXYBSTYP'  .
      ls_fieldcat-convexit = 'ZXYBS'.

      WHEN 'FRGSX'  .
      ls_fieldcat-convexit = 'FRGSX'.
      WHEN 'ZTK_ID'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'SEL'  .
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.

  add_fcat  '10' '' '' 'COIN'       '状态'  .
  add_fcat  '100' '' '' 'MESSAGE'    '消息'  .
  add_fcat  '10' '' '' 'ZHT_ID '    '合同号码'  .
  add_fcat  '10' '' '' 'ZHT_TXT'    '返利合同描述'  .
  add_fcat  '10' '' '' 'ZHTLX  '    '合同类型'  .
  add_fcat  '10' '' '' 'ZBUKRS '    '合同主体'  .
  add_fcat  '10' '' '' 'BUTXT  '    '合同主体描述'  .
  add_fcat  '10' '' '' 'ZBPTYPE'    '伙伴类型'  .
  add_fcat  '10' '' '' 'ZBPCODE'    '伙伴编码'  .
  add_fcat  '10' '' '' 'ZBPNAME'    '伙伴编码描述'  .
  add_fcat  '10' '' '' 'ZTK_ID '    '返利条款'  .
  add_fcat  '10' '' '' 'ZTK_TXT'    '返利条款描述'  .
  add_fcat  '10' '' '' 'ZFLLX  '    '返利类型'  .
  add_fcat  '10' '' '' 'ZXYBSTYP'   '协议类型'  .
  add_fcat  '10' '' '' 'EKGRP  '    '采购组'  .
  add_fcat  '10' '' '' 'EKNAM  '    '采购组描述'  .
  add_fcat  '10' '' '' 'ZXYZT  '    '返利条款状态'  .
  add_fcat  '10' '' '' 'FRGSX  '    '审批策略'  .
  add_fcat  '10' '' '' 'FRGC1  '    '审批代码'  .
  add_fcat  '10' '' '' 'ZFRGTX  '   '审批代码'  .
  add_fcat  '10' '' '' 'ZCJR   '    '创建人'  .
  add_fcat  '10' '' '' 'ZCNYG  '    '次年预估'  .



ENDFORM.                    "SET_CATALOG

*&---------------------------------------------------------------------*
*&      FORM  SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
*  PS_LAYOUT-BOX_FNAME = 'SEL_ALV'.
  ps_layout-sel_mode = 'D'.
  ps_layout-box_fname = 'SEL'.
ENDFORM.                    "SET_LAYOUT

*&---------------------------------------------------------------------*
*&      FORM  STATUS_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_status_alv USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.
  SET PF-STATUS 'S1000' EXCLUDING pt_extab.
ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*&      FORM  USER_COMMAND_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->R_UCOMM      TEXT
*      -->RS_SELFIELD  TEXT
*----------------------------------------------------------------------*
FORM frm_user_command USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_check_changed_data.

  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.
    WHEN '&ALL'.
      PERFORM frm_set_all CHANGING gt_data.
    WHEN '&SAL'.
      PERFORM frm_set_sal CHANGING gt_data.
    WHEN 'SAVE'.
      READ TABLE gt_data TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
      IF sy-subrc NE 0.
        MESSAGE s888(sabapdocu) WITH '请选择需要保存的记录！' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.
      PERFORM frm_save_data CHANGING gt_data
                                     lv_mtype
                                     lv_msg.
      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.
    WHEN '&IC1'.
      CLEAR rv_ucomm.
      CASE rs_selfield-fieldname.
        WHEN 'ZTK_ID' .
          READ TABLE gt_data INTO DATA(ls_data) INDEX rs_selfield-tabindex.
          IF sy-subrc EQ 0.
            CHECK ls_data-ztk_id IS NOT INITIAL.
            SET PARAMETER ID 'ZTK_ID' FIELD ls_data-ztk_id.
            CALL TRANSACTION 'ZRED0041D' AND SKIP FIRST SCREEN.
            SET PARAMETER ID 'ZTK_ID' FIELD ''.
            CLEAR w_delete.
            PERFORM frm_get_status USING ls_data
                                         w_delete.
            IF w_delete = 'Y'.
              DELETE gt_data WHERE ztk_id = ls_data-ztk_id.
            ENDIF.
          ENDIF.
        WHEN OTHERS.
      ENDCASE.
    WHEN 'ZAPP'.  "审批通过   ERP-11412
      PERFORM frm_call_zred0041d USING '=B_TK_R_OK'.
    WHEN 'ZREJ'. "审批拒绝 	ERP-11412
      PERFORM frm_call_zred0041d USING '=B_TK_R_CANCEL'.
    WHEN OTHERS.
  ENDCASE.

  PERFORM frm_refresh_alv.

ENDFORM.                    "USER_COMMAND_FORM
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant CHANGING ls_variant TYPE disvariant.
  ls_variant-report = sy-repid.
  ls_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT

*&---------------------------------------------------------------------*
*&      FORM  FRM_CHECK_CHANGED_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_check_changed_data .
  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.
  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.
  CALL METHOD lrf_alv->check_changed_data.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  FRM_REFRESH_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_refresh_alv .

  DATA:
        lrf_alv   TYPE REF TO cl_gui_alv_grid.

  DATA:
        ls_stable TYPE lvc_s_stbl.

  ls_stable-row = 'X'.
  ls_stable-col = 'X'.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.

  IF lrf_alv IS NOT INITIAL.
    CALL METHOD lrf_alv->refresh_table_display
      EXPORTING
        is_stable = ls_stable.
  ENDIF.
ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_SET_ALL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_set_all  CHANGING pt_data TYPE tt_data.
  DATA:
        ls_data TYPE LINE OF tt_data.
  ls_data-sel = 'X'.
  MODIFY pt_data FROM ls_data TRANSPORTING sel WHERE sel IS INITIAL.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SAL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_set_sal  CHANGING pt_data TYPE tt_data.
  DATA:
        ls_data TYPE LINE OF tt_data.
  ls_data-sel = ''.
  MODIFY pt_data FROM ls_data TRANSPORTING sel WHERE sel = 'X'.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_save_data  CHANGING pt_data  TYPE tt_data
                             pv_mtype TYPE bapi_mtype
                             pv_msg TYPE bapi_msg.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FM_ZPBCODE_HELP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&---------------------------------------------------------------------*
FORM fm_zpbcode_help  USING VALUE(p_flag).

  DATA:  lt_ret_tab TYPE TABLE OF ddshretval  WITH HEADER LINE.
  DATA: dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.

  DATA:
        lv_zbptype TYPE zreta001-zbptype.



  CLEAR: dynpfields, dynpfields[].
  dynpfields-fieldname = 'S_BPTYPE-LOW'.
  dynpfields-fieldvalue = s_bptype-low.
  APPEND dynpfields.

  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-repid
      dynumb               = sy-dynnr
      translate_to_upper   = 'X'
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      undefind_error       = 7
      OTHERS               = 8.
  IF sy-subrc = 0.
    READ TABLE dynpfields WITH KEY fieldname = 'S_BPTYPE-LOW'.
    lv_zbptype = dynpfields-fieldvalue.
  ENDIF.
  IF lv_zbptype IS INITIAL.
    RETURN.
  ENDIF.



  dynpfields-fieldname = 'GS_TA01-ZBPTYPE'.
  APPEND dynpfields.
  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-cprog
      dynumb               = sy-dynnr
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      invalid_parameter    = 7
      undefind_error       = 8
      double_conversion    = 9
      stepl_not_found      = 10
      OTHERS               = 11.
  IF sy-subrc EQ 0.
    READ TABLE dynpfields WITH KEY 'GS_TA01-ZBPTYPE'.
    IF sy-subrc = 0.
      lv_zbptype  = dynpfields-fieldvalue.
      TRANSLATE lv_zbptype TO UPPER CASE.
    ENDIF.
  ELSE.

  ENDIF.

  IF lv_zbptype = 'S'.

    DATA: ls_return TYPE           ddshretval,
          lt_return TYPE TABLE OF  ddshretval.
    DATA: ls_shlp   TYPE           shlp_descr,
          wa_selopt LIKE LINE OF   ls_shlp-selopt.



    CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
      EXPORTING
        tabname           = 'LFA1'
        fieldname         = 'LIFNR'
*       SEARCHHELP        = 'SH_AFASL_SPEC'
        callback_program  = sy-repid
*       CALLBACK_FORM     = 'SET_VALUES_FOR_F4_AFASL_304'
      TABLES
        return_tab        = lt_return
      EXCEPTIONS
        field_not_found   = 1
        no_help_for_field = 2
        inconsistent_help = 3
        no_values_found   = 4
        OTHERS            = 5.

    READ TABLE lt_return INDEX 1 INTO ls_return.
    CHECK sy-subrc = 0.
    IF p_flag = 'LOW'.
      s_bpcode-low = ls_return-fieldval.
    ELSE.
      s_bpcode-high = ls_return-fieldval.
    ENDIF.


    CLEAR sy-ucomm.


  ELSEIF lv_zbptype = 'M'.

    DATA:
          lv_dynprofield TYPE help_info-dynprofld.

    IF p_flag = 'LOW'.
      lv_dynprofield = 'S_BPCODE-LOW'.
    ELSE.
      lv_dynprofield = 'S_BPCODE-HIGH'.
    ENDIF.



*    SELECT
*       a~guidkey ,
*       a~codeitemid ,
*       a~codeitemdesc ,
*       a~b0105 ,
*       a~parentid ,
*       b~b0105 AS b0105_p
*      FROM zmmt0345 AS a LEFT JOIN zmmt0345 AS b
*                                ON a~parentid = b~codeitemid
*      WHERE a~status = '1'
*      INTO TABLE @DATA(lt_zmmt0345).

    SELECT partner, name_org1 INTO TABLE @DATA(lt_but000) FROM but000 WHERE bu_group = 'BP07'.


    CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
      EXPORTING
        retfield        = 'PARTNER'
        value_org       = 'S'
        dynpprog        = sy-repid
        dynpnr          = sy-dynnr
        dynprofield     = lv_dynprofield
*       CALLBACK_PROGRAM = SY-REPID
*       CALLBACK_FORM   = 'USER_FORM'
      TABLES
*       value_tab       = lt_zmmt0345[]
        value_tab       = lt_but000[]
        return_tab      = lt_ret_tab[]
      EXCEPTIONS
        parameter_error = 1
        no_values_found = 2
        OTHERS          = 3.
  ENDIF.

  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
    CHECK sy-subrc = 0.
  ENDIF.

ENDFORM.
FORM frm_call_zred0041d USING pl_code .
  IF pl_code = '=B_TK_R_OK'.
    LOOP AT gt_data INTO wa_data WHERE sel = 'X'
                                    AND zxybstyp <> 'F' AND zxybstyp <> 'P' .

    ENDLOOP.
    IF sy-subrc = 0.
      MESSAGE e398(00) WITH '批量审批通过仅对固定类/促销类协议'.
    ENDIF.
  ENDIF.

  LOOP AT gt_data INTO wa_data WHERE sel = 'X'.
    CLEAR: gs_bdcdata, gi_bdcdata,messtab.
    CLEAR: w_message.

    PERFORM bdc_dynpro USING 'ZRED0041' '1000'.
    PERFORM bdc_field USING 'BDC_OKCODE' '=ONLI'.
    PERFORM bdc_field USING 'P_ZTK_ID' wa_data-ztk_id.

    PERFORM bdc_dynpro USING 'ZRED0041' '2000'.
    PERFORM bdc_field USING 'BDC_OKCODE' pl_code.

    ls_params-dismode = 'N'.
    ls_params-updmode = 'S'.
    ls_params-racommit = 'X'.
    CALL TRANSACTION 'ZRED0041D'  USING gi_bdcdata
                               MESSAGES INTO messtab
                                OPTIONS FROM ls_params.
    LOOP AT messtab INTO ls_messtab WHERE msgtyp = 'E'.
      CALL FUNCTION 'MESSAGE_TEXT_BUILD'
        EXPORTING
          msgid               = ls_messtab-msgid
          msgnr               = ls_messtab-msgnr
          msgv1               = ls_messtab-msgv1
          msgv2               = ls_messtab-msgv2
          msgv3               = ls_messtab-msgv3
          msgv4               = ls_messtab-msgv4
        IMPORTING
          message_text_output = w_message.
    ENDLOOP.
    IF sy-subrc = 0.
      wa_data-coin = icon_red_light.
      wa_data-message = w_message.
      MODIFY gt_data FROM wa_data.
    ELSE.
      CLEAR w_delete.
      PERFORM frm_get_status USING wa_data
                                   w_delete.
      IF w_delete = 'Y'.
        DELETE gt_data WHERE ztk_id = wa_data-ztk_id.
      ENDIF.
    ENDIF.

    LOOP AT messtab INTO ls_messtab.
      MOVE-CORRESPONDING ls_messtab TO ls_msglist.
      ls_msglist-msgty = ls_messtab-msgtyp.
      ls_msglist-msgno = ls_messtab-msgnr.
      APPEND ls_msglist TO lt_msglist.
    ENDLOOP.
  ENDLOOP.
  IF sy-subrc <> 0.
    MESSAGE e398(00) WITH '请至少选择一条数据'.
  ENDIF.

  IF lt_msglist[] IS NOT INITIAL.

    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '审批结果显示'
        sort_by_level = ' '
        show_ids      = 'X'
        message_list  = lt_msglist[].
  ENDIF.
  CLEAR:lt_msglist[].

ENDFORM.
FORM frm_get_status USING plwa_data TYPE ty_data
                          pl_delete.
  SELECT SINGLE *
    INTO @DATA(lwa_zreta002)
      FROM zreta002
        WHERE ztk_id = @plwa_data-ztk_id.
  IF sy-subrc = 0.
    SELECT SINGLE *
      INTO @DATA(lwa_zretc007)
        FROM zretc007
          WHERE frgsx = @lwa_zreta002-frgsx
            AND kolnr = @lwa_zreta002-kolnr.
  ENDIF.

  IF lwa_zreta002-frgsx <> plwa_data-frgsx OR
     lwa_zretc007-frgc1 <> plwa_data-frgc1 .
    pl_delete = 'Y'.
  ENDIF.
ENDFORM.
FORM bdc_dynpro USING program dynpro.

  CLEAR: gs_bdcdata.
  gs_bdcdata-program = program.
  gs_bdcdata-dynpro = dynpro.
  gs_bdcdata-dynbegin = 'X'.
  APPEND gs_bdcdata TO gi_bdcdata.

ENDFORM.

FORM bdc_field USING fnam fval.

  CLEAR: gs_bdcdata.
  gs_bdcdata-fnam = fnam.
  gs_bdcdata-fval = fval.
  APPEND gs_bdcdata TO gi_bdcdata.

ENDFORM.