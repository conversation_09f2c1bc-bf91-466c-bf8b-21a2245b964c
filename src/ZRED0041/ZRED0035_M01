*&---------------------------------------------------------------------*
*& 包含               ZRED0035_M01
*&---------------------------------------------------------------------*


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_BUKRS' ITSELF
CONTROLS: tc_bukrs TYPE TABLEVIEW USING SCREEN 9001.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_BUKRS'
DATA:     g_tc_bukrs_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_bukrs_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_bukrs LINES tc_bukrs-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_bukrs_get_lines OUTPUT.
  g_tc_bukrs_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_bukrs_modify INPUT.
  MODIFY gt_bukrs
    FROM gs_bukrs
    INDEX tc_bukrs-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_bukrs_mark INPUT.
  DATA: g_tc_bukrs_wa2 LIKE LINE OF gt_bukrs.
  IF tc_bukrs-line_sel_mode = 1
  AND gs_bukrs-sel = 'X'.
    LOOP AT gt_bukrs INTO g_tc_bukrs_wa2
      WHERE sel = 'X'.
      g_tc_bukrs_wa2-sel = ''.
      MODIFY gt_bukrs
        FROM g_tc_bukrs_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_bukrs
    FROM gs_bukrs
    INDEX tc_bukrs-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_bukrs_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_BUKRS'
                              'GT_BUKRS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_DCWRK' ITSELF
CONTROLS: tc_dcwrk TYPE TABLEVIEW USING SCREEN 9002.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_DCWRK'
DATA:     g_tc_dcwrk_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_DCWRK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_dcwrk_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_dcwrk LINES tc_dcwrk-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_DCWRK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_dcwrk_get_lines OUTPUT.
  g_tc_dcwrk_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_DCWRK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_dcwrk_modify INPUT.
  MODIFY gt_dcwrk
    FROM gs_dcwrk
    INDEX tc_dcwrk-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_DCWRK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_dcwrk_mark INPUT.
  DATA: g_tc_dcwrk_wa2 LIKE LINE OF gt_dcwrk.
  IF tc_dcwrk-line_sel_mode = 1
  AND gs_dcwrk-sel = 'X'.
    LOOP AT gt_dcwrk INTO g_tc_dcwrk_wa2
      WHERE sel = 'X'.
      g_tc_dcwrk_wa2-sel = ''.
      MODIFY gt_dcwrk
        FROM g_tc_dcwrk_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_dcwrk
    FROM gs_dcwrk
    INDEX tc_dcwrk-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_DCWRK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_dcwrk_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_DCWRK'
                              'GT_DCWRK'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.




*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_WERKS' ITSELF
CONTROLS: tc_werks TYPE TABLEVIEW USING SCREEN 9003.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_WERKS'
DATA:     g_tc_werks_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_werks_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_werks LINES tc_werks-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_werks_get_lines OUTPUT.
  g_tc_werks_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_werks_modify INPUT.
  MODIFY gt_werks
    FROM gs_werks
    INDEX tc_werks-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_werks_mark INPUT.
  DATA: g_tc_werks_wa2 LIKE LINE OF gt_werks.
  IF tc_werks-line_sel_mode = 1
  AND gs_werks-sel = 'X'.
    LOOP AT gt_werks INTO g_tc_werks_wa2
      WHERE sel = 'X'.
      g_tc_werks_wa2-sel = ''.
      MODIFY gt_werks
        FROM g_tc_werks_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_werks
    FROM gs_werks
    INDEX tc_werks-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_WERKS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_werks_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_WERKS'
                              'GT_WERKS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.



*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_ekorg TYPE TABLEVIEW USING SCREEN 9004.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_ekorg_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ekorg_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ekorg LINES tc_ekorg-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ekorg_get_lines OUTPUT.
  g_tc_ekorg_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ekorg_modify INPUT.
  MODIFY gt_ekorg
    FROM gs_ekorg
    INDEX tc_ekorg-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ekorg_mark INPUT.
  DATA: g_tc_ekorg_wa2 LIKE LINE OF gt_ekorg.
  IF tc_ekorg-line_sel_mode = 1
  AND gs_ekorg-sel = 'X'.
    LOOP AT gt_ekorg INTO g_tc_ekorg_wa2
      WHERE sel = 'X'.
      g_tc_ekorg_wa2-sel = ''.
      MODIFY gt_ekorg
        FROM g_tc_ekorg_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ekorg
    FROM gs_ekorg
    INDEX tc_ekorg-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ekorg_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_EKORG'
                              'GT_EKORG'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_zzlbm TYPE TABLEVIEW USING SCREEN 9011.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_zzlbm_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zzlbm_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_zzlbm LINES tc_zzlbm-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zzlbm_get_lines OUTPUT.
  g_tc_zzlbm_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zzlbm_modify INPUT.
  MODIFY gt_zzlbm
    FROM gs_zzlbm
    INDEX tc_zzlbm-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zzlbm_mark INPUT.
  DATA: g_tc_zzlbm_wa2 LIKE LINE OF gt_zzlbm.
  IF tc_zzlbm-line_sel_mode = 1
  AND gs_zzlbm-sel = 'X'.
    LOOP AT gt_zzlbm INTO g_tc_zzlbm_wa2
      WHERE sel = 'X'.
      g_tc_zzlbm_wa2-sel = ''.
      MODIFY gt_zzlbm
        FROM g_tc_zzlbm_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_zzlbm
    FROM gs_zzlbm
    INDEX tc_zzlbm-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zzlbm_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZZLBM'
                              'GT_ZZLBM'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.

ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_zqdbm TYPE TABLEVIEW USING SCREEN 9012.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_zqdbm_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zqdbm_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_zqdbm LINES tc_zqdbm-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zqdbm_get_lines OUTPUT.
  g_tc_zqdbm_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zqdbm_modify INPUT.
  MODIFY gt_zqdbm
    FROM gs_zqdbm
    INDEX tc_zqdbm-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zqdbm_mark INPUT.
  DATA: g_tc_zqdbm_wa2 LIKE LINE OF gt_zqdbm.
  IF tc_zqdbm-line_sel_mode = 1
  AND gs_zqdbm-sel = 'X'.
    LOOP AT gt_zqdbm INTO g_tc_zqdbm_wa2
      WHERE sel = 'X'.
      g_tc_zqdbm_wa2-sel = ''.
      MODIFY gt_zqdbm
        FROM g_tc_zqdbm_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_zqdbm
    FROM gs_zqdbm
    INDEX tc_zqdbm-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zqdbm_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZQDBM'
                              'GT_ZQDBM'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_EKORG' ITSELF
CONTROLS: tc_zzgys TYPE TABLEVIEW USING SCREEN 9013.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_EKORG'
DATA:     g_tc_zzgys_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zzgys_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_zzgys LINES tc_zzgys-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zzgys_get_lines OUTPUT.
  g_tc_zzgys_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zzgys_modify INPUT.
  MODIFY gt_zzgys
    FROM gs_zzgys
    INDEX tc_zzgys-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zzgys_mark INPUT.
  DATA: g_tc_zzgys_wa2 LIKE LINE OF gt_zzgys.
  IF tc_zzgys-line_sel_mode = 1
  AND gs_zzgys-sel = 'X'.
    LOOP AT gt_zzgys INTO g_tc_zzgys_wa2
      WHERE sel = 'X'.
      g_tc_zzgys_wa2-sel = ''.
      MODIFY gt_zzgys
        FROM g_tc_zzgys_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_zzgys
    FROM gs_zzgys
    INDEX tc_zzgys-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_EKORG'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zzgys_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZZGYS'
                              'GT_ZZGYS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_BUKRS' ITSELF
CONTROLS: tc_matnr TYPE TABLEVIEW USING SCREEN 9007.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_BUKRS'
DATA:     g_tc_matnr_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_matnr_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_matnr LINES tc_matnr-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_matnr_get_lines OUTPUT.
  g_tc_matnr_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_matnr_modify INPUT.
  MODIFY gt_matnr
    FROM gs_matnr
    INDEX tc_matnr-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_matnr_mark INPUT.
  DATA: g_tc_matnr_wa2 LIKE LINE OF gt_matnr.
  IF tc_matnr-line_sel_mode = 1
  AND gs_matnr-sel = 'X'.
    LOOP AT gt_matnr INTO g_tc_matnr_wa2
      WHERE sel = 'X'.
      g_tc_matnr_wa2-sel = ''.
      MODIFY gt_matnr
        FROM g_tc_matnr_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_matnr
    FROM gs_matnr
    INDEX tc_matnr-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_BUKRS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_matnr_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_MATNR'
                              'GT_MATNR'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_ghf TYPE TABLEVIEW USING SCREEN 9005.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'
DATA:     g_tc_ghf_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ghf_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_tc04 LINES tc_ghf-lines.

ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ghf_get_lines OUTPUT.
  g_tc_ghf_lines = sy-loopc.


ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ghf_modify INPUT.
  MODIFY gt_tc04
    FROM gs_tc04
    INDEX tc_ghf-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ghf_mark INPUT.
  DATA: g_tc_ghf_wa2 LIKE LINE OF gt_tc04.
  IF tc_ghf-line_sel_mode = 1
  AND gs_tc04-sel = 'X'.
    LOOP AT gt_tc04 INTO g_tc_ghf_wa2
      WHERE sel = 'X'.
      g_tc_ghf_wa2-sel = ''.
      MODIFY gt_tc04
        FROM g_tc_ghf_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_tc04
    FROM gs_tc04
    INDEX tc_ghf-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ghf_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_GHF'
                              'GT_TC04'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_zff_sub TYPE TABLEVIEW USING SCREEN 9006.


*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'

DATA:     g_tc_zff_sub_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR


MODULE tc_zff_sub_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t44_sub LINES tc_zff_sub-lines.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL


MODULE tc_zff_sub_get_lines OUTPUT.
  g_tc_zff_sub_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE


MODULE tc_zff_sub_modify INPUT.
  MODIFY gt_t44_sub
    FROM gs_t44_sub
    INDEX tc_zff_sub-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE


MODULE tc_zff_sub_mark INPUT.
  DATA: g_tc_zff_sub_wa2 LIKE LINE OF gt_t44_sub.
  IF tc_zff_sub-line_sel_mode = 1
  AND gs_t44_sub-sel = 'X'.
    LOOP AT gt_t44_sub INTO g_tc_zff_sub_wa2
      WHERE sel = 'X'.
      g_tc_zff_sub_wa2-sel = ''.
      MODIFY gt_t44_sub
        FROM g_tc_zff_sub_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t44_sub
    FROM gs_t44_sub
    INDEX tc_zff_sub-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND



MODULE tc_zff_sub_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZFF_SUB'
                              'GT_T44_SUB'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

**********************************************************************
*  公共部分
**********************************************************************
*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ITEM' ITSELF
CONTROLS: tc_item TYPE TABLEVIEW USING SCREEN 2200.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ITEM'
DATA:     g_tc_item_lines  LIKE sy-loopc.
*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR

MODULE tc_item_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_tc02 LINES tc_item-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_item_get_lines OUTPUT.
  g_tc_item_lines = sy-loopc.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_item_modify INPUT.
  MODIFY gt_tc02
    FROM gs_tc02
    INDEX tc_item-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_item_mark INPUT.
  DATA: g_tc_item_wa2 LIKE LINE OF gt_tc02.
  IF tc_item-line_sel_mode = 1
  AND gs_tc02-sel = 'X'.
    LOOP AT gt_tc02 INTO g_tc_item_wa2
      WHERE sel = 'X'.
      g_tc_item_wa2-sel = ''.
      MODIFY gt_tc02
        FROM g_tc_item_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_tc02
    FROM gs_tc02
    INDEX tc_item-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ITEM'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_item_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ITEM'
                              'GT_TC02'
                              'SEL'
                     CHANGING ok_code.

  sy-ucomm = ok_code.
ENDMODULE.

FORM frm_set_screen_9001 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9002 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.
FORM frm_set_screen_9003 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9004 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9005 .

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9007 .

*  根据字段配置控制字段是否隐藏
  LOOP AT tc_matnr-cols INTO DATA(ls_cols).

    PERFORM frm_set_screen_attr_col USING gt_data_scn
                                      ls_cols-screen-name.
    IF sy-subrc EQ 0.
      ls_cols-invisible = 1.
      MODIFY tc_matnr-cols FROM ls_cols.
    ENDIF.

  ENDLOOP.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9008 .

*  根据字段配置控制字段是否隐藏
  LOOP AT tc_matnr-cols INTO DATA(ls_cols).

    PERFORM frm_set_screen_attr_col USING gt_data_scn
                                      ls_cols-screen-name.
    IF sy-subrc EQ 0.
      ls_cols-invisible = 1.
      MODIFY tc_matnr-cols FROM ls_cols.
    ENDIF.

  ENDLOOP.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9011 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9012 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9013 .
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.


FORM frm_set_screen_9300 .

*  根据字段配置控制字段是否隐藏
  LOOP AT tc_matnr-cols INTO DATA(ls_cols).

    PERFORM frm_set_screen_attr_col USING gt_data_scn
                                      ls_cols-screen-name.
    IF sy-subrc EQ 0.
      ls_cols-invisible = 1.
      MODIFY tc_matnr-cols FROM ls_cols.
    ENDIF.

  ENDLOOP.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDFORM.

FORM frm_set_screen_9200 .
*  PERFORM FRM_SET_SCREEN_PUB.
  PERFORM frm_set_screen_pub_mass.

ENDFORM.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9001 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9001 OUTPUT.
  SET PF-STATUS 'S9000'.

  SET TITLEBAR 'T2000' WITH gv_title_mass01 gv_title_mass02 .
  PERFORM frm_set_screen_9001.
ENDMODULE.

MODULE status_9002 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9002.
ENDMODULE.

MODULE status_9003 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9003.
ENDMODULE.

MODULE status_9004 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9004.
ENDMODULE.

MODULE status_9005 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9005.
ENDMODULE.

MODULE status_9006 OUTPUT.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDMODULE.


MODULE status_9007 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9007.
ENDMODULE.

MODULE status_9008 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9008.
ENDMODULE.

MODULE status_9011 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9011.
ENDMODULE.

MODULE status_9012 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9012.
ENDMODULE.

MODULE status_9013 OUTPUT.
  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_9013.
ENDMODULE.

MODULE status_9200 OUTPUT.
  SET PF-STATUS 'S9000'.
  SET TITLEBAR 'T2000' WITH gv_title_mass01 gv_title_mass02 .
  PERFORM frm_set_screen_9200.
ENDMODULE.


MODULE mdl_set_screen_tc_bukrs OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_bukrs[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_werks OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_werks[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_dcwrk OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_dcwrk[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_ekorg OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_ekorg[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_zzlbm OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_zzlbm[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_zqdbm OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_zqdbm[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_zzgys OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_zzgys[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_matnr OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_matnr[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_ta04 OUTPUT.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_matnr[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_ghf OUTPUT.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_tc04[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_DATA_TC_WERKS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_werks OUTPUT.
  PERFORM frm_set_data_tc_werks CHANGING gs_werks.
ENDMODULE.
MODULE mdl_set_data_tc_dcwrk OUTPUT.
  PERFORM frm_set_data_tc_dcwrk CHANGING gs_dcwrk.
ENDMODULE.
MODULE mdl_set_data_tc_bukrs OUTPUT.
  PERFORM frm_set_data_tc_bukrs CHANGING gs_bukrs.
ENDMODULE.
MODULE mdl_set_data_tc_matnr OUTPUT.
  PERFORM frm_set_data_tc_matnr CHANGING gs_matnr.
ENDMODULE.

MODULE mdl_set_data_tc_ta04 OUTPUT.

ENDMODULE.

MODULE mdl_check_bukrs INPUT.
  PERFORM frm_check_bukrs CHANGING gs_bukrs-bukrs.
ENDMODULE.
MODULE mdl_check_dcwrk INPUT.

  PERFORM frm_check_dcwrk CHANGING gs_dcwrk-dcwrk.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_WERKS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_werks INPUT.

  PERFORM frm_check_dcwrk CHANGING gs_werks-werks.
ENDMODULE.

MODULE mdl_check_ekorg INPUT.
  PERFORM frm_check_ekorg USING gs_ekorg-ekorg.
ENDMODULE.

MODULE mdl_check_zzlbm INPUT.
  PERFORM frm_screen_check_zzlbm USING gs_zzlbm-zzlbm.
ENDMODULE.

MODULE mdl_check_zqdbm INPUT.
  PERFORM frm_screen_check_zqdbm USING gs_zqdbm-zqdbm.
ENDMODULE.

MODULE mdl_check_zzgys INPUT.
  PERFORM frm_screen_check_zzgys USING gs_zzgys-lifnr.
ENDMODULE.

MODULE mdl_check_zghf INPUT.
*  PERFORM FRM_ALPHA_IN_LIFNR CHANGING GS_TC04-ZGHF.
*  PERFORM FRM_CHECK_ZGHF CHANGING GS_TC04-ZGHF.

  PERFORM frm_set_screen_zghf CHANGING gs_tc04-zghf gs_tc04-name1.

ENDMODULE.

MODULE mdl_check_zghf_35 INPUT.
  PERFORM frm_alpha_in_lifnr CHANGING gs_tc04-zghf.
  PERFORM frm_check_zghf_35 CHANGING gs_tc04-zghf.

ENDMODULE.


FORM frm_set_data_tc_bukrs CHANGING ps_bukrs TYPE LINE OF tt_bukrs.
  PERFORM frm_get_butxt USING ps_bukrs-bukrs CHANGING ps_bukrs-butxt.
ENDFORM.
FORM frm_set_data_tc_werks CHANGING ps_werks TYPE LINE OF tt_werks.
  PERFORM frm_get_werks_t USING ps_werks-werks CHANGING ps_werks-name1.
ENDFORM.
FORM frm_set_data_tc_dcwrk CHANGING ps_dcwrk TYPE LINE OF tt_dcwrk.
  PERFORM frm_get_werks_t USING ps_dcwrk-dcwrk CHANGING ps_dcwrk-name1.
ENDFORM.
FORM frm_set_data_tc_ekorg CHANGING ps_ekorg TYPE LINE OF tt_ekorg.
  PERFORM frm_get_ekotx USING ps_ekorg-ekorg CHANGING ps_ekorg-ekotx.
ENDFORM.
FORM frm_set_data_tc_zzlbm CHANGING ps_zzlbm TYPE LINE OF tt_t81.
  PERFORM frm_get_zzlms USING ps_zzlbm-zzlbm CHANGING ps_zzlbm-zzlms.
ENDFORM.
FORM frm_set_data_tc_zqdbm CHANGING ps_zqdbm TYPE LINE OF tt_t82.
  PERFORM frm_get_zqdms USING ps_zqdbm-zqdbm CHANGING ps_zqdbm-zqdms.
ENDFORM.
FORM frm_set_data_tc_zzgys CHANGING ps_zzgsy TYPE LINE OF tt_t84.
  PERFORM frm_get_gysms USING ps_zzgsy-lifnr CHANGING ps_zzgsy-name1.
ENDFORM.
FORM frm_set_data_tc_matnr CHANGING ps_matnr TYPE LINE OF tt_matnr.
  PERFORM frm_get_maktx(zbcs0001) USING ps_matnr-matnr CHANGING ps_matnr-maktx.

ENDFORM.

FORM frm_set_data_tc_ghf CHANGING ps_tc04 TYPE LINE OF tt_tc04.
  PERFORM frm_get_zghf_t USING ps_tc04-zghf CHANGING ps_tc04-name1.
ENDFORM.

MODULE mdl_set_data_tc_ekorg OUTPUT.
  PERFORM frm_set_data_tc_ekorg CHANGING gs_ekorg.
ENDMODULE.
MODULE mdl_set_data_tc_zzlbm OUTPUT.
  PERFORM frm_set_data_tc_zzlbm CHANGING gs_zzlbm.
ENDMODULE.
MODULE mdl_set_data_tc_zqdbm OUTPUT.
  PERFORM frm_set_data_tc_zqdbm CHANGING gs_zqdbm.
ENDMODULE.
MODULE mdl_set_data_tc_zzgsy OUTPUT.
  PERFORM frm_set_data_tc_zzgys CHANGING gs_zzgys.
ENDMODULE.
MODULE mdl_set_data_tc_ghf OUTPUT.
  PERFORM frm_set_data_tc_ghf CHANGING gs_tc04.
ENDMODULE.


MODULE status_2200 OUTPUT.
  PERFORM frm_set_screen_2200.
ENDMODULE.

FORM frm_set_screen_2200 .
  PERFORM frm_set_screen_pub.
ENDFORM.

MODULE mdl_set_style_tc_item OUTPUT.

  PERFORM frm_set_style_tc_item .

  PERFORM frm_set_screen_pub.

  IF gv_flg_comm NE 'SAVE'.
    IF gv_flg_rb = '05'.
      LOOP AT SCREEN.
        IF screen-group1 = '101' .
          IF screen-name(7) = 'TC_ITEM'.
            screen-input = 1.
          ELSE.
            screen-input = 0.
          ENDIF.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.

    ENDIF.
  ENDIF.
ENDMODULE.

MODULE mdl_set_data_2200 OUTPUT.

  IF gs_data_base-zflg_type = 'GD'..
    PERFORM frm_set_data_2200_mwskz USING gs_ta02 CHANGING gt_tc02 .
  ENDIF.
ENDMODULE.

FORM frm_set_style_tc_item .

  DATA: ls_cols TYPE cx_tableview_column.

  IF  gs_data_base-zflg_cprog = 'ZRED0035'.
    LOOP AT tc_item-cols INTO ls_cols.
*      IF GV_FLG_RB = '01'.
*        IF LS_COLS-SCREEN-NAME = 'GS_TC02-ZSTATUS_DEL'.
*          LS_COLS-INVISIBLE = 1.
*        ENDIF.
*      ENDIF.
      IF ls_cols-screen-group2 = '202'.
        ls_cols-invisible = 1.
      ENDIF.

*      IF LS_COLS-SCREEN-GROUP3 = '301'.
*        LS_COLS-SCREEN-INPUT = 0.
*      ENDIF.



      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.

  ELSEIF   gs_data_base-zflg_cprog = 'ZRED0041'.

    LOOP AT tc_item-cols INTO ls_cols.
      IF ls_cols-screen-group2 = '201'.
        ls_cols-invisible = 1.
      ENDIF.


      IF ls_cols-screen-name = 'GS_TC02-ZXYZT_06'  OR
         ls_cols-screen-name = 'GS_TC02-FRGSX'     OR
         ls_cols-screen-name = 'GS_TC02-KOLNR'.
        IF NOT ( gs_data_base-zlrsi = 'X' AND
                  ( gv_flg_rb = '03' OR gv_flg_rb = '04' OR gv_flg_rb = '05' )
                ).
          ls_cols-invisible = 1.
        ELSE.
          ls_cols-invisible = 0.
        ENDIF.
      ENDIF.
      IF ls_cols-screen-name = 'B_TK_R_OK_S' OR
          ls_cols-screen-name = 'B_TK_R_OK_SS' OR
          ls_cols-screen-name = 'B_TK_R_CANCEL_SS' OR
          ls_cols-screen-name = 'B_TK_R_CANCEL_S'.

        IF gs_data_base-zlrsi = '' OR gv_flg_rb NE '04'  .
          ls_cols-invisible = 1.
        ELSE.
          ls_cols-invisible = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDIF.


      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.

  ENDIF.


*  固定类控制
  IF gs_data_base-zflg_type = 'GD'.

    LOOP AT tc_item-cols INTO ls_cols.
      IF ls_cols-screen-group4 = '401'
          OR ls_cols-screen-group4 = '404'.
        ls_cols-invisible = 1.
      ENDIF.
      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.

*  计算类
  ELSEIF gs_data_base-zflg_type = 'JS'.
    LOOP AT tc_item-cols INTO ls_cols.
      IF ls_cols-screen-group4 = '402'.
        ls_cols-invisible = 1.
      ENDIF.
      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.
*  促销类
  ELSEIF gs_data_base-zflg_type = 'CX'.
    LOOP AT tc_item-cols INTO ls_cols.
      IF ls_cols-screen-group4 = '403'
          OR ls_cols-screen-group4 = '404' .
        ls_cols-invisible = 1.
      ENDIF.
      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.
  ENDIF.




  IF   gs_data_base-zflg_cprog = 'ZRED0041'.
    LOOP AT tc_item-cols INTO ls_cols.

*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 15:46:25
*    <NOTES> #####
*    <OLD CODES>
*      CASE GS_DATA_BASE-ZFLLX.
*    </OLD CODES>
*    <NEW CODES>
      CASE gs_data_base-zxybstyp.
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*
        WHEN 'T' . "'RB06'. "T 促销类

          IF ls_cols-screen-name = 'GS_TC02-ZJE'  OR
              ls_cols-screen-name = 'GS_TC02-ZMWSKZ'  OR
              ls_cols-screen-name = 'GV_ICON_JT_SUB'  OR
              ls_cols-screen-name = 'GV_ICON_ZGHF'  OR
              ls_cols-screen-name = 'GS_TC02-ZITSPLR'  OR
              ls_cols-screen-name = 'GS_TC02-ZETSPLR'  OR
              ls_cols-screen-name = 'GV_ICON_EKORG'  OR
              ls_cols-screen-name = 'GV_ICON_XY_SUB' OR
              ls_cols-screen-name = 'GS_TC02-ZSL'   OR
*              ls_cols-screen-name = 'GS_TC02-MATNR'  OR        "根据配置表控制
*              ls_cols-screen-name = 'GS_TC02-MAKTX'   OR       "根据配置表控制
*              LS_COLS-SCREEN-NAME = 'GS_TC02-ZITZFF'   OR
              ls_cols-screen-name = 'GS_TC02-MEINS'   OR
              ls_cols-screen-name = 'GS_TC02-ZSPZ_ID'  OR
              ls_cols-screen-name = 'GS_TC02-ZSPZID_TXT'

            .


            ls_cols-invisible = 1.
          ENDIF.
        WHEN 'Q' ."'RB04'. "Q 固定数量类

          IF ls_cols-screen-name = 'GS_TC02-ZJE'  OR
              ls_cols-screen-name = 'GS_TC02-ZITZFF'   OR
              ls_cols-screen-name = 'GS_TC02-ZMWSKZ'  OR
              ls_cols-screen-name = 'GS_TC02-ZSPZ_ID'  OR
              ls_cols-screen-name = 'GS_TC02-ZSPZID_TXT' .

            ls_cols-invisible = 1.
          ENDIF.

        WHEN 'F'. " 'RB03' OR 'RB02' . "F 固定金额类

          IF
              ls_cols-screen-name = 'GS_TC02-ZSL'   OR
*              ls_cols-screen-name = 'GS_TC02-MATNR'  OR      "根据配置表控制
*              ls_cols-screen-name = 'GS_TC02-MAKTX'   OR     "根据配置表控制
              ls_cols-screen-name = 'GS_TC02-MEINS' OR
              ls_cols-screen-name = 'GS_TC02-ZSL'   OR
              ls_cols-screen-name = 'GS_TC02-ZSPZ_ID'  OR
              ls_cols-screen-name = 'GS_TC02-ZSPZID_TXT' .

            ls_cols-invisible = 1.
          ENDIF.
        WHEN 'A'. "'RB05'.  "A 付款类

          IF
              ls_cols-screen-name = 'GS_TC02-ZSL'   OR
*              ls_cols-screen-name = 'GS_TC02-MATNR'  OR        "根据配置表控制
*              ls_cols-screen-name = 'GS_TC02-MAKTX'   OR      "根据配置表控制
              ls_cols-screen-name = 'GS_TC02-MEINS' OR
              ls_cols-screen-name = 'GS_TC02-ZSPZ_ID'  OR
              ls_cols-screen-name = 'GS_TC02-ZSPZID_TXT' OR

              ls_cols-screen-name = 'GV_ICON_WERKS'
*              LS_COLS-SCREEN-NAME = 'GS_TC02-ZETSPLR' OR
*              LS_COLS-SCREEN-NAME = 'GS_TC02-ZITSPLR' OR
*              LS_COLS-SCREEN-NAME = 'GV_ICON_ZGHF' OR
*              LS_COLS-SCREEN-NAME = 'GV_ICON_ZFLZFF'
             .

            ls_cols-invisible = 1.
          ENDIF.
        WHEN OTHERS.

      ENDCASE.

      MODIFY tc_item-cols FROM ls_cols.
    ENDLOOP.
  ENDIF.



  LOOP AT tc_item-cols INTO ls_cols.
    IF gv_flg_rb = '01'.
      IF ls_cols-screen-name = 'GS_TC02-ZSTATUS_DEL'.
        ls_cols-invisible = 1.
        MODIFY tc_item-cols FROM ls_cols.
      ENDIF.
    ENDIF.
  ENDLOOP.


  IF   gs_data_base-zflg_cprog = 'ZRED0041'.
    LOOP AT tc_item-cols INTO ls_cols.
      IF ls_cols-screen-name = 'GS_TC02-ZSTATUS_DEL'.
        ls_cols-invisible = 1.
        MODIFY tc_item-cols FROM ls_cols.
      ENDIF.

    ENDLOOP.

  ENDIF.


*  根据字段配置控制字段是否隐藏
  LOOP AT tc_item-cols INTO ls_cols.

    PERFORM frm_set_screen_attr_col USING gt_data_scn
                                      ls_cols-screen-name.
    IF sy-subrc EQ 0.
      ls_cols-invisible = 1.
      MODIFY tc_item-cols FROM ls_cols.
    ENDIF.

  ENDLOOP.

*  根据列是否隐藏控制相关MASS按钮是否显示
  LOOP AT SCREEN.
    IF screen-name = 'TC_ITEM_MASS_ZJT' OR
        screen-name = 'TC_ITEM_MASS_ZGHF' OR
        screen-name = 'TC_ITEM_MASS_ZGHF1' OR
        screen-name = 'TC_ITEM_MASS_ZFLZFF' .

      PERFORM frm_check_screen_invisible USING screen-name.
      IF sy-subrc NE 0.
        screen-invisible = 1.
        MODIFY SCREEN.
      ENDIF.


      IF (  screen-name = 'TC_ITEM_MASS_ZGHF' OR
          screen-name = 'TC_ITEM_MASS_ZGHF1'   ) AND   screen-invisible NE 1 .
        IF gs_data_base-zghf_t = '出票方' AND screen-name = 'TC_ITEM_MASS_ZGHF'.
          screen-invisible = 1.
          MODIFY SCREEN.
        ELSEIF gs_data_base-zghf_t = '供货方' AND screen-name = 'TC_ITEM_MASS_ZGHF1'.
          screen-invisible = 1.
          MODIFY SCREEN.
        ENDIF.

      ENDIF.
      IF  gs_data_base-zflg_cprog = 'ZRED0035'.
        screen-invisible = 1.
        MODIFY SCREEN.
      ENDIF.


    ENDIF.
  ENDLOOP.


  LOOP AT tc_item-cols INTO ls_cols.
* 商品明细按钮暂时隐藏
*    IF ls_cols-screen-name = 'GV_ICON_MATNR'.
*      ls_cols-invisible = 1.
*    ENDIF.

    IF ls_cols-screen-name = 'GS_TC02-MATNR' OR ls_cols-screen-name = 'GS_TC02-MAKTX'.
      ls_cols-invisible = 1.
    ENDIF.

    MODIFY tc_item-cols FROM ls_cols.
  ENDLOOP.



ENDFORM.

MODULE mdl_set_data_tc_item OUTPUT.
  PERFORM frm_set_data_tc_item CHANGING gs_tc02.

  PERFORM frm_set_icon USING  gs_tc02
                              gt_tc04_set
                              gt_zzlbm_set
                              gt_zqdbm_set
                              gt_zzgys_set
                              gt_bukrs_set
                              gt_dcwrk_set
                              gt_werks_set
                              gt_ekorg_set
                              gt_t11_all
                              gt_t44_all
                              gt_matnr_set
                              gt_ta04

                      CHANGING
                               gv_icon_zflzff
                               gv_icon_xy_sub
                               gv_icon_jt_sub
                               gv_icon_zghf
                               gv_icon_zzlbm
                               gv_icon_zqdbm
                               gv_icon_lifnr
                               gv_icon_bukrs
                               gv_icon_dcwrk
                               gv_icon_werks
                               gv_icon_ekorg
                               gv_icon_matnr

                                .

ENDMODULE.

FORM frm_set_data_tc_item  CHANGING ps_tc02 TYPE LINE OF tt_tc02.


ENDFORM.

FORM frm_set_icon  USING      ps_tc02     TYPE LINE OF tt_tc02
                              pt_tc04     TYPE tt_tc04
                              pt_zzlbm    TYPE tt_t81
                              pt_zqdbm    TYPE tt_t82
                              pt_zzgys    TYPE tt_t84
                              pt_bukrs    TYPE tt_bukrs
                              pt_dcwrk    TYPE tt_dcwrk
                              pt_werks    TYPE tt_werks
                              pt_ekorg    TYPE tt_ekorg
                              pt_t11_all    TYPE tt_t11
                              pt_t44_all    TYPE tt_t44
                              pt_matnr    TYPE tt_matnr
                              pt_ta04    TYPE tt_ta04

                   CHANGING

                              pv_icon_zflzff  TYPE icon-id
                              pv_icon_xy_sub  TYPE icon-id
                              pv_icon_jt_sub  TYPE icon-id
                              pv_icon_zghf    TYPE icon-id
                              pv_icon_zzlbm   TYPE icon-id
                              pv_icon_zqdbm   TYPE icon-id
                              pv_icon_zzgys   TYPE icon-id
                              pv_icon_bukrs   TYPE icon-id
                              pv_icon_dcwrk   TYPE icon-id
                              pv_icon_werks   TYPE icon-id
                              pv_icon_ekorg   TYPE icon-id
                              pv_icon_matnr   TYPE icon-id
                              .

  DATA(lt_tc04)    = pt_tc04[]  .
  DATA(lt_zzlbm)   = pt_zzlbm[]  .
  DATA(lt_zqdbm)   = pt_zqdbm[]  .
  DATA(lt_zzgys)   = pt_zzgys[]  .
  DATA(lt_bukrs)   = pt_bukrs[]  .
  DATA(lt_dcwrk)   = pt_dcwrk[]  .
  DATA(lt_werks)   = pt_werks[]  .
  DATA(lt_ekorg)   = pt_ekorg[]  .
  DATA(lt_t11_all) = pt_t11_all[]  .
  DATA(lt_t44_all) = pt_t44_all[]  .
  DATA(lt_matnr)   = pt_matnr[]  .
  DATA(lt_ta04)    = pt_ta04[]  .

  DELETE lt_tc04    WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_zzlbm   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_zqdbm   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_zzgys   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_bukrs   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_dcwrk   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_werks   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_ekorg   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_t11_all WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_t44_all WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_matnr   WHERE zitems_key NE ps_tc02-zitems_key          .
  DELETE lt_ta04    WHERE zitems_key NE ps_tc02-zitems_key          .

  DELETE lt_tc04    WHERE zghf   IS INITIAL.
  DELETE lt_zzlbm   WHERE zzlbm  IS INITIAL.
  DELETE lt_zqdbm   WHERE zqdbm  IS INITIAL.
  DELETE lt_zzgys   WHERE lifnr  IS INITIAL.
  DELETE lt_bukrs   WHERE bukrs  IS INITIAL.
  DELETE lt_dcwrk   WHERE dcwrk  IS INITIAL.
  DELETE lt_werks   WHERE werks  IS INITIAL.
  DELETE lt_ekorg   WHERE ekorg  IS INITIAL.
  DELETE lt_t44_all WHERE zflzff IS INITIAL.
  DELETE lt_matnr   WHERE matnr  IS INITIAL.
*  DELETE lt_ta04  WHERE zrlid IS INITIAL.

  PERFORM frm_set_icon_exe USING lt_tc04  CHANGING pv_icon_zghf .
  PERFORM frm_set_icon_exe USING lt_zzlbm CHANGING pv_icon_zzlbm.
  PERFORM frm_set_icon_exe USING lt_zqdbm CHANGING pv_icon_zqdbm.
  PERFORM frm_set_icon_exe USING lt_zzgys CHANGING pv_icon_zzgys.
  PERFORM frm_set_icon_exe USING lt_bukrs CHANGING pv_icon_bukrs.
  PERFORM frm_set_icon_exe USING lt_dcwrk CHANGING pv_icon_dcwrk.
  PERFORM frm_set_icon_exe USING lt_werks CHANGING pv_icon_werks.
  PERFORM frm_set_icon_exe USING lt_ekorg CHANGING pv_icon_ekorg.
  PERFORM frm_set_icon_exe USING lt_t11_all CHANGING pv_icon_jt_sub.
  PERFORM frm_set_icon_exe USING lt_t44_all CHANGING pv_icon_zflzff.
  PERFORM frm_set_icon_exe USING lt_matnr CHANGING pv_icon_matnr.
  PERFORM frm_set_icon_exe USING lt_ta04 CHANGING pv_icon_xy_sub.



ENDFORM.

FORM frm_set_icon_exe USING pt_data   TYPE STANDARD TABLE
                      CHANGING pv_icon TYPE icon-id.

  IF pt_data[] IS INITIAL.
    pv_icon = '@1F@'.
  ELSE.
    pv_icon = '@1E@'.
  ENDIF.

ENDFORM.

MODULE mdl_set_zitemtxt INPUT.

  PERFORM frm_check_bukrs CHANGING gs_tc02-zbukrs.

  PERFORM frm_set_zitemtxt USING gs_tc02-zbukrs
                           CHANGING gs_tc02-zitemtxt.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_ZFLSQF  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_zflsqf INPUT.
*& ADD BY zsfsx = '2'
*  PERFORM frm_check_bukrs CHANGING gs_tc02-zflsqf.
  PERFORM frm_check_zflsqf1 CHANGING gs_tc02-zflsqf.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_ZFLZFF  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_zflzff INPUT.


  PERFORM frm_alpha_in_lifnr CHANGING gs_tc02-zflzff.

  PERFORM frm_check_zflzff CHANGING gs_tc02-zflzff.

  PERFORM frm_set_zpaytp USING gs_tc02-zflzff CHANGING gs_tc02-zpaytp.


ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_ZRTBUKS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_zrtbuks INPUT.

  PERFORM frm_check_bukrs CHANGING gs_tc02-zrtbuks.
ENDMODULE.

FORM frm_set_zitemtxt  USING    pv_bukrs TYPE t001-bukrs
                       CHANGING pv_butxt .

  CHECK pv_bukrs IS NOT INITIAL.
  SELECT SINGLE butxt INTO pv_butxt
    FROM t001 WHERE bukrs = pv_bukrs.
ENDFORM.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_SCREEN_TC_ITEM OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_item OUTPUT.
*  IF GS_DATA_BASE-ZFLG_CPROG = 'ZRED0035'.
*
*    PERFORM FRM_SET_SCREEN_PUB.
*  ELSEIF GS_DATA_BASE-ZFLG_CPROG = 'ZRED0041'.
*    PERFORM FRM_SET_SCREEN_PUB.
*  ENDIF.

  DATA:
        gv_zdffs TYPE zreta002-zdffs.



  CLEAR gv_zdffs.
  IF gs_tc02-zdffs IS  INITIAL.
    gv_zdffs = gs_ta02-zdffs.
  ELSE.
    gv_zdffs = gs_tc02-zdffs.
  ENDIF.



  IF gs_data_base-zflg_type = 'GD'.
    IF gv_zdffs = 'A' OR gv_zdffs = 'C'.
      gs_tc02-zmwskz = 'X3'.

*      及时更新内表
      MODIFY gt_tc02 FROM gs_tc02  INDEX g_tc_item_lines TRANSPORTING zmwskz.

      LOOP AT SCREEN.
        IF screen-name = 'GS_TC02-ZMWSKZ'.
          screen-input = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    ENDIF.
  ENDIF.

  IF gs_data_base-zflg_cprog = 'ZRED0035'..
    LOOP AT SCREEN.
      IF screen-group3 = '301'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF gs_data_base-zflg_cprog = 'ZRED0041'..
    IF gs_tc02-zflg_exist IS NOT INITIAL.
      LOOP AT SCREEN.
        IF screen-group3 = '301'.
          screen-input = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.

    ENDIF.

  ENDIF.


  IF gv_flg_rb = '01'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TC02-ZITEMS'.
        screen-input = 0.
      ENDIF.
*
*      IF SCREEN-NAME = 'GS_TC02-ZSTATUS_DEL'.
*        SCREEN-ACTIVE = 0.
*      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.

*  IF GS_TC02-ZXY_ID IS NOT INITIAL.
*    LOOP AT SCREEN.
*      IF SCREEN-NAME = 'GS_TC02-ZITEMS'
*       OR  SCREEN-NAME = 'GS_TC02-SEL_MAN' .
*        SCREEN-INPUT = 0.
*      ENDIF.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.

*  行项目审批控制
  IF gs_data_base-zflg_cprog = 'ZRED0041'..
    IF gv_flg_rb = '04' AND gs_data_base-zlrsi = 'X'.

*
*    PERFORM FRM_AUTHOR_CHECK_ITEM_FRGC1 USING  GS_TA01
*                                               GS_TA02
*                                               GS_TC02
*                                               ''
*                                   CHANGING LV_MTYPE_AUTH
*                                            LV_MSG_AUTH.
*
*    IF LV_MTYPE_AUTH = 'E' .
*
*      LOOP AT TC_ITEM-COLS INTO DATA(LS_COLS).
*        IF LS_COLS-SCREEN-NAME = 'B_TK_R_OK_S' OR
*            LS_COLS-SCREEN-NAME = 'B_TK_R_OK_SS' OR
*            LS_COLS-SCREEN-NAME = 'B_TK_R_CANCEL_SS' OR
*            LS_COLS-SCREEN-NAME = 'B_TK_R_CANCEL_S'.
*
*          LS_COLS-INVISIBLE = 1.
*        ENDIF.
*        MODIFY TC_ITEM-COLS FROM LS_COLS.
*      ENDLOOP.
*    ENDIF.
    ENDIF.
  ENDIF.



*  若存在附加条款则行项目  选择 组织级别、协议主体、所属平台 置灰
  READ TABLE gt_ta04 TRANSPORTING NO FIELDS WITH KEY zitems_key = gs_tc02-zitems_key .
  IF sy-subrc EQ 0.
    LOOP AT SCREEN.
      IF
*        screen-name = 'GS_TC02-SEL_MAN'       OR
        screen-name = 'GS_TC02-ZCTGR'
       OR  screen-name = 'GS_TC02-ZBUKRS'
       OR  screen-name = 'GS_TC02-ZRTBUKS' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02.

  IF  gt_tc02[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.


*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_get_org_data_ztmpid  USING  pv_ztmpid TYPE zretc001-ztmpid
                            CHANGING
                                      ps_tc01 TYPE LINE OF tt_tc01
                                      pt_tc02 TYPE tt_tc02
                                      pt_tc03 TYPE tt_tc03
                                      pt_tc04 TYPE tt_tc04
                                      pt_t44_all TYPE tt_t44
                                      pt_matnr TYPE tt_matnr
                                      pt_zzgys TYPE tt_t84.

  IF pv_ztmpid IS NOT INITIAL.

    CLEAR ps_tc01.

    SELECT
      *
      INTO  TABLE @DATA(lt_tc01)
      FROM zretc001
      WHERE ztmpid = @pv_ztmpid.

    IF lt_tc01 IS  INITIAL.
      MESSAGE s888(sabapdocu) WITH '没有对应的数据！' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ELSE.
      READ TABLE lt_tc01 INTO DATA(ls_tc01) INDEX 1.
      MOVE-CORRESPONDING ls_tc01 TO ps_tc01.
    ENDIF.
  ENDIF.

  SELECT
    a~*
    FROM @lt_tc01 AS i JOIN zretc002 AS a
                         ON i~ztmpid = a~ztmpid
*    WHERE A~ZSTATUS NE 'D'
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc02.


  SELECT
    a~*
    FROM @lt_tc01 AS i JOIN zretc003 AS a
                         ON i~ztmpid = a~ztmpid
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc03.



  SELECT
    a~*
    FROM @lt_tc01 AS i JOIN zretc004 AS a
                         ON i~ztmpid = a~ztmpid
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc04.

  SELECT
    a~*
    FROM @lt_tc01 AS i JOIN zretc018 AS a
                         ON i~ztmpid = a~ztmpid
    INTO CORRESPONDING FIELDS OF TABLE @pt_t44_all.

  SELECT
    a~*
    FROM @lt_tc01 AS i JOIN zretc020 AS a
                         ON i~ztmpid = a~ztmpid
    INTO CORRESPONDING FIELDS OF TABLE @pt_zzgys.

*  获取商品明细 （模板中暂时缺失，若需要在此处添加）


  PERFORM frm_pro_data_org CHANGING
                                  ps_tc01
                                  pt_tc02
                                  pt_tc03
                                  pt_tc04
                                  pt_t44_all
                                  pt_matnr
                                  pt_zzgys.
ENDFORM.



*FORM FRM_PRO_ZTMPID_DATA  CHANGING
*                            PS_TC01 TYPE LINE OF TT_TC01
*                            PT_TC02 TYPE TT_TC02
*                            PT_TC03 TYPE TT_TC03
*                            PT_TC04 TYPE TT_TC04
*                            PT_BUKRS TYPE TT_BUKRS
*                            PT_DCWRK TYPE TT_DCWRK
*                            PT_WERKS TYPE TT_WERKS
*                            PT_EKORG TYPE TT_EKORG.
*
*
*  IF PT_TC03[] IS NOT INITIAL.
*
*  ENDIF.
*
*
*ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_ZZZLX_UNFOLD
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_TC03
*&      <-- PT_BUKRS
*&      <-- PT_DCWRK
*&      <-- PT_WERKS
*&      <-- PT_EKORG
*&---------------------------------------------------------------------*
FORM frm_zzzlx_unfold  USING    pt_tc03 TYPE tt_tc03
                       CHANGING pt_bukrs TYPE tt_bukrs
                                pt_dcwrk TYPE tt_dcwrk
                                pt_werks TYPE tt_werks
                                pt_ekorg TYPE tt_ekorg.

  DATA:
    ls_bukrs TYPE LINE OF tt_bukrs,
    ls_dcwrk TYPE LINE OF tt_dcwrk,
    ls_werks TYPE LINE OF tt_werks,
    ls_ekorg TYPE LINE OF tt_ekorg.


*  CLEAR:
*        PT_BUKRS ,
*        PT_DCWRK ,
*        PT_WERKS ,
*        PT_EKORG .

  LOOP AT pt_tc03 INTO DATA(ls_tc03).
    CLEAR:
          ls_bukrs  ,
          ls_dcwrk  ,
          ls_werks  ,
          ls_ekorg  .

    ls_bukrs-exclude = ls_tc03-zzzpc.
    ls_dcwrk-exclude = ls_tc03-zzzpc.
    ls_werks-exclude = ls_tc03-zzzpc.
    ls_ekorg-exclude = ls_tc03-zzzpc.

    ls_bukrs-zitems_key  = ls_tc03-zitems_key     .
    ls_dcwrk-zitems_key  = ls_tc03-zitems_key     .
    ls_werks-zitems_key  = ls_tc03-zitems_key     .
    ls_ekorg-zitems_key  = ls_tc03-zitems_key     .


    CASE ls_tc03-zzzlx.
      WHEN 'A'.
        ls_bukrs-bukrs = ls_tc03-zzzid.
        ls_bukrs-zmdsx = ls_tc03-zmdsx.
        APPEND ls_bukrs TO pt_bukrs.
      WHEN 'P'.
        ls_ekorg-ekorg = ls_tc03-zzzid.
        APPEND ls_ekorg TO pt_ekorg.
      WHEN 'C'.
        ls_dcwrk-dcwrk = ls_tc03-zzzid.
        APPEND ls_dcwrk TO pt_dcwrk.
      WHEN 'S'.
        ls_werks-werks = ls_tc03-zzzid.
        APPEND ls_werks TO pt_werks.
      WHEN OTHERS.
    ENDCASE.

  ENDLOOP.



ENDFORM.

FORM frm_set_zitems  USING pv_ztmpid  TYPE zretc002-ztmpid
                           pv_ztk_id  TYPE zreta002-ztk_id
                           pv_flg     TYPE char1
                    CHANGING pt_tc02 TYPE tt_tc02.

  IF pv_flg = 'A'.
    SELECT SINGLE MAX( i~zitems )  FROM zretc002 AS i WHERE ztmpid = @pv_ztmpid INTO @DATA(lv_zitems).
  ELSEIF pv_flg = 'B'.
    SELECT SINGLE MAX( i~zitems )  FROM zret0006 AS i WHERE ztk_id = @pv_ztk_id AND zitems >= 200 INTO @lv_zitems.
    IF lv_zitems IS INITIAL.
      SELECT  MAX( i~zitems )  FROM @pt_tc02 AS i  INTO @lv_zitems.
      IF lv_zitems IS INITIAL.
        lv_zitems = 199.
      ENDIF.
    ENDIF.
  ENDIF.

  LOOP AT pt_tc02 INTO DATA(ls_tc02) WHERE zitems IS INITIAL OR zitems = ''.
    IF pv_flg = 'B' AND ls_tc02-sel_man = ''.
      CONTINUE.
    ENDIF.
    lv_zitems = lv_zitems + 1.
    ls_tc02-zitems = lv_zitems.
    MODIFY pt_tc02 FROM ls_tc02.
  ENDLOOP.
ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_CALL_DTL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_CODE
*&      --> GV_CURSOR_LINE
*&---------------------------------------------------------------------*
FORM frm_dtl_perform  USING    pv_code TYPE sy-ucomm
                            pv_cursor_line TYPE i
                            pt_tc02 TYPE tt_tc02
                      CHANGING ps_tc02_sub TYPE LINE OF tt_tc02.



  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems,
    lv_code       TYPE sy-ucomm.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.
*  获取行项目KEY
  PERFORM frm_dtl_get_zitems USING lv_index_line
                                   'GT_TC02[]'
                             CHANGING lv_zitems_key.

  PERFORM frm_dtl_pro_data USING pv_code 'PBO'  lv_zitems_key.

*  获取行项目数据
  CLEAR ps_tc02_sub.
  READ TABLE pt_tc02 INTO DATA(ls_tc02) INDEX lv_index_line.
  IF sy-subrc EQ 0.
    ps_tc02_sub = ls_tc02.
  ENDIF.

*  屏幕跳转前记录 OK_CODE
  CLEAR lv_code.
  lv_code = pv_code.
  PERFORM frm_dtl_call_screen USING pv_code.

  PERFORM frm_dtl_pro_data USING lv_code 'PAI'  lv_zitems_key.

ENDFORM.

FORM frm_get_line_data_tc_02  USING    pv_code TYPE sy-ucomm
                            pv_cursor_line TYPE i
                            pt_tc02 TYPE tt_tc02
                      CHANGING ps_tc02_sub TYPE LINE OF tt_tc02.



  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems,
    lv_code       TYPE sy-ucomm.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.


*  获取行项目数据
  CLEAR ps_tc02_sub.
  READ TABLE pt_tc02 INTO DATA(ls_tc02) INDEX lv_index_line.
  IF sy-subrc EQ 0.
    ps_tc02_sub = ls_tc02.
  ENDIF.


ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_GET_INDEX_LINE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      --> PV_CURSOR_LINE
*&      <-- LV_INDEX_LINE
*&---------------------------------------------------------------------*
FORM frm_get_index_line  USING    pv_code TYPE sy-ucomm
                                  pv_cursor_line  TYPE i
                         CHANGING pv_index_line   TYPE i.


  DATA:
    lv_field_name TYPE char30,
    lv_tc_name    TYPE dynfnam.
  FIELD-SYMBOLS:
                 <fs_any> TYPE any.

*  获取TABLE CONTROL 的表名
  PERFORM frm_get_tc_name USING pv_code
                          CHANGING lv_tc_name.

  CLEAR pv_index_line.

  lv_field_name = lv_tc_name && '-TOP_LINE'.

  ASSIGN (lv_field_name) TO <fs_any>.
  IF <fs_any> IS ASSIGNED.
    pv_index_line = <fs_any> + pv_cursor_line - 1.
  ENDIF.


ENDFORM.

FORM frm_get_index_line_new  USING    pv_code TYPE sy-ucomm
                                  pv_cursor_line  TYPE i
                                  pv_tc_name      TYPE dynfnam
                         CHANGING pv_index_line   TYPE i.


  DATA:
    lv_field_name TYPE char30,
    lv_tc_name    TYPE dynfnam.
  FIELD-SYMBOLS:
                 <fs_any> TYPE any.

  IF pv_tc_name IS INITIAL.
*  获取TABLE CONTROL 的表名
    PERFORM frm_get_tc_name USING pv_code
                            CHANGING lv_tc_name.
  ELSE.
    lv_tc_name = pv_tc_name.
  ENDIF.

  CLEAR pv_index_line.

  lv_field_name = lv_tc_name && '-TOP_LINE'.

  ASSIGN (lv_field_name) TO <fs_any>.
  IF <fs_any> IS ASSIGNED.
    pv_index_line = <fs_any> + pv_cursor_line - 1.
  ENDIF.




ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_TC_NAME
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      <-- LV_TC_NAME
*&---------------------------------------------------------------------*
FORM frm_get_tc_name  USING    pv_code  TYPE sy-ucomm
                      CHANGING pv_tc_name TYPE dynfnam.

*  CASE PV_CODE.
*    WHEN 'BT_BUKRS'. PV_TC_NAME = 'TC_BUKRS'.
*    WHEN 'BT_DCWRK'. PV_TC_NAME = 'TC_DCWRK'.
*    WHEN 'BT_WERKS'. PV_TC_NAME = 'TC_WERKS'.
*    WHEN 'BT_EKORG'. PV_TC_NAME = 'TC_EKORG'.
*    WHEN 'BT_ZGHF'.  PV_TC_NAME = 'TC_GHF'.
*    WHEN 'B_TK_JT_SUB'.  PV_TC_NAME = 'TC_ZJT_SUB'.
*    WHEN OTHERS.
*  ENDCASE.

  CASE pv_code.
    WHEN 'BT_BUKRS' OR
         'BT_ZZLBM' OR
         'BT_ZQDBM' OR
         'BT_ZZGYS' OR
         'BT_DCWRK' OR
         'BT_WERKS' OR
         'BT_EKORG' OR
         'BT_MATNR' OR
         'BT_ZGHF'  OR
         'BT_ZFLZFF'  OR
         'B_TK_R_OK_S'  OR
         'B_TK_R_CANCEL_S'  OR
         'B_TK_JT_SUB' OR
         'BT_XY_SUB' .
      pv_tc_name = 'TC_ITEM'.
    WHEN 'B_ZMZMX'.
      pv_tc_name = 'TC_ZCXJT'.
    WHEN OTHERS.
  ENDCASE.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_DTL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      --> LV_INDEX_LINE
*&---------------------------------------------------------------------*

FORM frm_dtl_get_zitems  USING   pv_index_line TYPE i
                                 pv_tabname TYPE tabname
                         CHANGING pv_zitems TYPE zretc002-zitems.
*  DATA:  lv_tabname TYPE tabname VALUE 'GT_TC02[]'.

  FIELD-SYMBOLS:
    <fs_tab_tc02> TYPE STANDARD TABLE,
    <fs_zitems> ,
    <fs_wa_tc02> .

  CLEAR pv_zitems.

  ASSIGN (pv_tabname) TO <fs_tab_tc02>.
  UNASSIGN <fs_wa_tc02>.
  READ TABLE <fs_tab_tc02> ASSIGNING <fs_wa_tc02>  INDEX pv_index_line.

  UNASSIGN <fs_zitems>.
  ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_wa_tc02> TO <fs_zitems>.
  IF <fs_zitems> IS NOT ASSIGNED.
    RETURN.
  ELSE.
    pv_zitems = <fs_zitems>.
  ENDIF.


ENDFORM.
FORM frm_dtl_pro_data  USING
                                pv_code TYPE sy-ucomm
                                pv_flg TYPE char4
                                pv_zitems TYPE zretc002-zitems.

  DATA:
    lv_table_name_set TYPE tabname,
    lv_tab_name_sub   TYPE tabname.

  FIELD-SYMBOLS:
    <fs_tab_set> TYPE STANDARD TABLE,
    <fs_tab_sub> TYPE STANDARD TABLE,
    <fs_zitems>  ,
    <fs_wa_set>  ,
    <fs_wa_sub>  .


  PERFORM frm_get_tab_name USING pv_code
                           CHANGING lv_table_name_set           "主表
                                    lv_tab_name_sub.            "行项目表

  UNASSIGN: <fs_tab_set>, <fs_tab_sub>.
  ASSIGN (lv_table_name_set) TO <fs_tab_set>.
  ASSIGN (lv_tab_name_sub) TO <fs_tab_sub>.


  IF pv_flg = 'PBO'.
    CLEAR <fs_tab_sub>.
    UNASSIGN <fs_wa_set>.
    LOOP AT <fs_tab_set> ASSIGNING <fs_wa_set>.
      UNASSIGN <fs_zitems>.
      ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_wa_set> TO <fs_zitems>.
      IF <fs_zitems> IS ASSIGNED.
        IF <fs_zitems> EQ pv_zitems.
          APPEND <fs_wa_set> TO <fs_tab_sub>.
          DELETE <fs_tab_set>.
          CONTINUE.
        ENDIF.
      ENDIF.
    ENDLOOP.


  ELSEIF pv_flg = 'PAI'.
    UNASSIGN <fs_wa_sub>.
    LOOP AT <fs_tab_sub> ASSIGNING <fs_wa_sub>.
      IF <fs_wa_sub> IS ASSIGNED AND <fs_wa_set> IS ASSIGNED.
        IF <fs_wa_sub> IS NOT INITIAL.
          UNASSIGN <fs_zitems>.
          ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_wa_sub> TO <fs_zitems>.
          IF <fs_zitems> IS ASSIGNED.
            <fs_zitems> = pv_zitems.
          ENDIF.
          APPEND <fs_wa_sub> TO <fs_tab_set>.
        ENDIF.
      ENDIF.
    ENDLOOP.
    CLEAR <fs_tab_sub>.
  ENDIF.



ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_TAB_NAME
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      <-- LV_TABLE_NAME
*&---------------------------------------------------------------------*
FORM frm_get_tab_name  USING    pv_code TYPE sy-ucomm
                       CHANGING pv_table_name_set TYPE tabname
                                pv_table_name_sub TYPE tabname.

  CASE pv_code.
    WHEN 'BT_BUKRS'. pv_table_name_set = 'GT_BUKRS_SET'. pv_table_name_sub = 'GT_BUKRS'.
    WHEN 'BT_DCWRK'. pv_table_name_set = 'GT_DCWRK_SET'. pv_table_name_sub = 'GT_DCWRK'.
    WHEN 'BT_WERKS'. pv_table_name_set = 'GT_WERKS_SET'. pv_table_name_sub = 'GT_WERKS'.
    WHEN 'BT_EKORG'. pv_table_name_set = 'GT_EKORG_SET'. pv_table_name_sub = 'GT_EKORG'.
    WHEN 'BT_MATNR'. pv_table_name_set = 'GT_MATNR_SET'. pv_table_name_sub = 'GT_MATNR'.
    WHEN 'BT_ZGHF '. pv_table_name_set = 'GT_TC04_SET' . pv_table_name_sub = 'GT_TC04'.
    WHEN 'BT_ZFLZFF '. pv_table_name_set = 'GT_T44_ALL' . pv_table_name_sub = 'GT_T44_SUB'.
    WHEN 'BT_XY_SUB '. pv_table_name_set = 'GT_TA04' . pv_table_name_sub = 'GT_TA04_SUB'.
    WHEN 'B_ZMZMX '. pv_table_name_set = 'GT_T58_SET' . pv_table_name_sub = 'GT_T58_SUB'.
    WHEN 'BT_ZZLBM '. pv_table_name_set = 'GT_ZZLBM_SET' . pv_table_name_sub = 'GT_ZZLBM'.
    WHEN 'BT_ZQDBM '. pv_table_name_set = 'GT_ZQDBM_SET' . pv_table_name_sub = 'GT_ZQDBM'.
    WHEN 'BT_ZZGYS '. pv_table_name_set = 'GT_ZZGYS_SET' . pv_table_name_sub = 'GT_ZZGYS'.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_DTL_CALL_SCREEN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&---------------------------------------------------------------------*
FORM frm_dtl_call_screen  USING    pv_code TYPE sy-ucomm.
  CASE pv_code.
    WHEN 'BT_BUKRS'. CALL SCREEN 9001 .
    WHEN 'BT_DCWRK'. CALL SCREEN 9002 .
    WHEN 'BT_WERKS'. CALL SCREEN 9003 .
    WHEN 'BT_EKORG'. CALL SCREEN 9004 .
    WHEN 'BT_ZGHF'. CALL SCREEN 9005 .
    WHEN 'BT_ZFLZFF'. CALL SCREEN 9006 .
    WHEN 'BT_MATNR'. CALL SCREEN 9007 .
    WHEN 'BT_XY_SUB'. CALL SCREEN 9008 .
    WHEN 'B_ZMZMX'. CALL SCREEN 9010 .
    WHEN 'BT_ZZLBM'. CALL SCREEN 9011 .
    WHEN 'BT_ZQDBM'. CALL SCREEN 9012 .
    WHEN 'BT_ZZGYS'. CALL SCREEN 9013 .
    WHEN OTHERS.
  ENDCASE.
ENDFORM.


FORM frm_zzzlx_fold  USING     pt_bukrs TYPE tt_bukrs
                               pt_dcwrk TYPE tt_dcwrk
                               pt_werks TYPE tt_werks
                               pt_ekorg TYPE tt_ekorg
                      CHANGING pt_tc03   TYPE tt_tc03.

  DATA:
        ls_tc03 TYPE LINE OF tt_tc03.

  CLEAR pt_tc03.
  CLEAR ls_tc03.


  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    CLEAR:
          ls_tc03.
    ls_tc03-zzzlx = 'P'.
    ls_tc03-zzzid = ls_ekorg-ekorg.
    ls_tc03-zzzpc = ls_ekorg-exclude.
    ls_tc03-zitems_key = ls_ekorg-zitems_key.
    APPEND ls_tc03 TO pt_tc03.
  ENDLOOP.

  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    CLEAR:
          ls_tc03.
    ls_tc03-zzzlx = 'A'.
    ls_tc03-zzzid = ls_bukrs-bukrs.
    ls_tc03-zmdsx = ls_bukrs-zmdsx.
    ls_tc03-zzzpc = ls_bukrs-exclude.
    ls_tc03-zitems_key = ls_bukrs-zitems_key.
    APPEND ls_tc03 TO pt_tc03.
  ENDLOOP.

  LOOP AT pt_dcwrk INTO DATA(ls_dcwrk).
    CLEAR:
          ls_tc03.
    ls_tc03-zzzlx = 'C'.
    ls_tc03-zzzid = ls_dcwrk-dcwrk.
    ls_tc03-zzzpc = ls_dcwrk-exclude.
    ls_tc03-zitems_key = ls_dcwrk-zitems_key.
    APPEND ls_tc03 TO pt_tc03.
  ENDLOOP.


  LOOP AT pt_werks INTO DATA(ls_werks).
    CLEAR:
          ls_tc03.
    ls_tc03-zzzlx = 'S'.
    ls_tc03-zzzid = ls_werks-werks.
    ls_tc03-zzzpc = ls_werks-exclude.
    ls_tc03-zitems_key = ls_werks-zitems_key.
    APPEND ls_tc03 TO pt_tc03.
  ENDLOOP.



ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_2200
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_set_data_2200  CHANGING pt_tc02 TYPE tt_tc02.
  PERFORM frm_set_seq TABLES pt_tc02 USING 'SEQ'.
ENDFORM.

FORM frm_set_data_2200_mwskz USING ps_ta02 TYPE LINE OF tt_ta02 CHANGING pt_tc02 TYPE tt_tc02.



  DATA:
        lv_zdffs TYPE zreta002-zdffs.


  LOOP AT pt_tc02 INTO DATA(ls_tc02).

    CLEAR lv_zdffs.
    IF ls_tc02-zdffs IS  INITIAL.
      lv_zdffs = ps_ta02-zdffs.
    ELSE.
      lv_zdffs = ls_tc02-zdffs.
    ENDIF.

    IF lv_zdffs = 'A' OR lv_zdffs = 'C'.
      ls_tc02-zmwskz = 'X3'.

      MODIFY pt_tc02 FROM ls_tc02   TRANSPORTING zmwskz.

    ENDIF.


  ENDLOOP.




  IF gs_data_base-zflg_type = 'GD'.
    IF gv_zdffs = 'A' OR gv_zdffs = 'C'.
      gs_tc02-zmwskz = 'X3'.

*      及时更新内表
      MODIFY gt_tc02 FROM gs_tc02  INDEX g_tc_item_lines TRANSPORTING zmwskz.

      LOOP AT SCREEN.
        IF screen-name = 'GS_TC02-ZMWSKZ'.
          screen-input = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_PUBLIC
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_public  USING    pt_tc02 TYPE  tt_tc02
                                 pt_tc04  TYPE tt_tc04
                                 pt_dcwrk TYPE tt_dcwrk
                                 pt_werks TYPE tt_werks
                       CHANGING pt_msglist TYPE scp1_general_errors.


  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.

  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    CLEAR lv_msgv1. lv_msgv1 =  '行项目ID:' && ls_tc02-zitems.
    PERFORM frm_check_inital_02 USING ls_tc02-zitemtxt    '行描述' lv_msgv1 CHANGING pt_msglist .
*      PERFORM FRM_CHECK_INITAL USING LS_TC02-ZCTGR    '主体类别' CHANGING LT_MSGLIST .
    PERFORM frm_check_inital_02 USING ls_tc02-zbukrs    '协议主体' lv_msgv1 CHANGING pt_msglist .
    PERFORM frm_check_inital_02 USING ls_tc02-zflsqf    '收款方' lv_msgv1 CHANGING pt_msglist .

    IF ls_tc02-zctgr = 'C'..
      IF ls_tc02-zrtbuks IS INITIAL.
        CLEAR lv_msgv1. lv_msgv1 = '若组织级别为“项目公司”，则平台公司代码必输' &&  '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SELECT COUNT(*) FROM @pt_tc02 AS i WHERE zctgr = 'A' INTO @DATA(lv_count).
  IF lv_count > 1.
    CLEAR lv_msgv1. lv_msgv1 = '每个模板里组织级别”全国量“最多只能有一条。'.
    PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
  ENDIF.


  LOOP AT pt_tc02 INTO ls_tc02.

    DATA(lt_tc04) = pt_tc04[].
    DATA(lt_dcwrk) = pt_dcwrk[].
    DATA(lt_werks) = pt_werks[].

    DELETE lt_tc04 WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_dcwrk WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_werks WHERE zitems_key NE ls_tc02-zitems_key.

    PERFORM frm_check_data_zzzpc USING
                                        lt_tc04
                                        lt_dcwrk
                                        lt_werks
                                        ls_tc02
                                  CHANGING pt_msglist.


  ENDLOOP.

ENDFORM.

FORM frm_check_data_zzzpc USING
                                 pt_tc04  TYPE tt_tc04
                                 pt_dcwrk TYPE tt_dcwrk
                                 pt_werks TYPE tt_werks
                                 ps_tc02  TYPE LINE OF tt_tc02
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.


  LOOP AT pt_tc04 INTO DATA(ls_tc04).
    LOOP AT pt_tc04 TRANSPORTING NO FIELDS WHERE zghf = ls_tc04-zghf AND zzzpc NE ls_tc04-zzzpc.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      CLEAR lv_msgv1. lv_msgv1 = '供货方不能既排除，又不排除' && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

    ENDIF.
  ENDLOOP.

  LOOP AT pt_dcwrk INTO DATA(ls_dcwrk).
    LOOP AT pt_dcwrk TRANSPORTING NO FIELDS WHERE dcwrk = ls_dcwrk-dcwrk AND exclude NE ls_dcwrk-exclude.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      CLEAR lv_msgv1. lv_msgv1 = 'DC/门店不能既排除，又不排除' && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

    ENDIF.
  ENDLOOP.

  LOOP AT pt_werks INTO DATA(ls_werks).
    LOOP AT pt_werks TRANSPORTING NO FIELDS WHERE werks = ls_werks-werks AND exclude NE ls_werks-exclude.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.

      CLEAR lv_msgv1. lv_msgv1 = 'DC/门店不能既排除，又不排除' && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.
  ENDLOOP.

ENDFORM.

FORM frm_check_public_tc02  USING  pv_flg TYPE char1
                                  pt_tc02 TYPE  tt_tc02
                                  pt_tc04 TYPE  tt_tc04
                                  pt_t44_all         TYPE tt_t44
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_flg TYPE char1.

  DATA:lv_msgv1 TYPE scp1_general_error-msgv1.


  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    DATA(lt_tmp) = pt_tc04[].
    DELETE lt_tmp WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_tmp WHERE zghf IS INITIAL.

    IF pv_flg = 'A'.


      LOOP AT lt_tmp INTO DATA(ls_tmp).

        IF ls_tmp-zghf IS NOT INITIAL.
          PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
          IF lv_flg EQ 'E'.

*            READ TABLE LT_TC04_TMP TRANSPORTING NO FIELDS WITH KEY ZGHF = 'ALL'.
*            IF SY-SUBRC EQ 0.
*              IF LS_TC04_TMP-ZGHF NE 'ALL' .
*                IF LS_TC04_TMP-ZZZPC = ''.
*                  PERFORM FRM_ADD_MSG USING '当外部供货方存在ALL值，则其他外部供货方只能排除'  CHANGING PT_MSGLIST.
*                ENDIF.
*              ENDIF.
*            ELSE.
*              IF LS_TC04_TMP-ZGHF NE 'ALL' .
*                IF LS_TC04_TMP-ZZZPC = 'X'.
*                  PERFORM FRM_ADD_MSG USING '当外部供货方存在ALL值，则其他外部供货方不能排除'  CHANGING PT_MSGLIST.
*                ENDIF.
*              ENDIF.
*            ENDIF.

          ELSEIF  lv_flg = 'I'.

            IF ls_tc02-zitsplr = 'X'.
              IF ls_tmp-zzzpc = ''.
                CLEAR lv_msgv1. lv_msgv1 = '若勾选了内部供货方，则维护的内部供货方只能排除' && '行项目ID:' && ls_tc02-zitems.
                PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

              ENDIF.
            ELSE.
              IF ls_tmp-zzzpc = 'X'.
                CLEAR lv_msgv1. lv_msgv1 = '若没有勾选内部供货方，则维护的内部供货方不能排除' && '行项目ID:' && ls_tc02-zitems.
                PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

              ENDIF.
            ENDIF.

          ENDIF.
        ENDIF.

      ENDLOOP.


*      IF LS_TC02-ZITSPLR = ''.
*        IF LT_TMP[] IS INITIAL.
*          PERFORM FRM_ADD_MSG USING '行项目供货方不能为空'  CHANGING PT_MSGLIST.
*        ENDIF.
*      ENDIF.








*******      IF LS_TC02-ZITSPLR = '' AND LS_TC02-ZETSPLR = ''..
*******        LOOP AT LT_TMP INTO DATA(LS_TMP) .
*******
*******          IF LS_TMP-ZGHF IS NOT INITIAL.
*******            PERFORM FRM_GET_ZGHF_ATTR USING LS_TMP-ZGHF CHANGING LV_FLG.
*******            IF LV_FLG NE 'I'.
*******              PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
*******            ENDIF.
*******          ENDIF.
*******
*******
********          DATA(LV_ZGHF) = |{ LS_TMP-ZGHF ALPHA = OUT }|.
********
********
********
********          IF STRLEN( LV_ZGHF ) > 4.
********            PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
********          ELSE.
********            SELECT COUNT(*) FROM T001 WHERE BUKRS = LV_ZGHF.
********            IF SY-SUBRC NE 0.
********              SELECT COUNT(*) FROM T001W WHERE WERKS = LV_ZGHF.
********              IF SY-SUBRC NE 0.
********                PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
********              ENDIF.
********            ENDIF.
********          ENDIF.
*******
*******        ENDLOOP.
*******      ELSEIF LS_TC02-ZITSPLR = '' AND LS_TC02-ZETSPLR = 'X'.
*******
*******
*******
*******        LOOP AT LT_TMP INTO LS_TMP.
*******
*******          IF LS_TMP-ZGHF IS NOT INITIAL.
*******            PERFORM FRM_GET_ZGHF_ATTR USING LS_TMP-ZGHF CHANGING LV_FLG.
*******            IF LV_FLG NE 'I'.
*******              PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
*******            ENDIF.
*******          ENDIF.
*******
*******
********          LV_ZGHF = |{ LS_TMP-ZGHF ALPHA = OUT }|.
********          IF STRLEN( LV_ZGHF ) > 4.
********            PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
********          ELSE.
********            SELECT COUNT(*) FROM T001 WHERE BUKRS = @LV_ZGHF.
********            IF SY-SUBRC NE 0.
********              SELECT COUNT(*) FROM T001W WHERE WERKS = LV_ZGHF.
********              IF SY-SUBRC NE 0.
********                PERFORM FRM_ADD_MSG USING '只允许维护内部供货方'  CHANGING PT_MSGLIST.
********              ENDIF.
********            ENDIF.
********          ENDIF.
*******
*******        ENDLOOP.
*******      ELSEIF LS_TC02-ZITSPLR = 'X' AND LS_TC02-ZETSPLR = ''.
*******        IF LT_TMP[] IS NOT INITIAL.
*******          PERFORM FRM_ADD_MSG USING '不允许维护供货方'  CHANGING PT_MSGLIST.
*******        ENDIF.
*******      ELSEIF LS_TC02-ZITSPLR = 'X' AND LS_TC02-ZETSPLR = 'X'.
*******        IF LT_TMP[] IS NOT INITIAL.
*******          PERFORM FRM_ADD_MSG USING '不允许维护供货方'  CHANGING PT_MSGLIST.
*******        ENDIF.
*******      ENDIF.

*      IF ls_tc02-zflzff IS NOT INITIAL.
*        PERFORM frm_get_zghf_attr USING ls_tc02-zflzff CHANGING lv_flg.
*        IF lv_flg NE 'I'.
*
*          CLEAR lv_msgv1. lv_msgv1 = '，付款方必须为内部付款方' && '行项目ID:' && ls_tc02-zitems.
*          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*
*        ENDIF.
*      ENDIF.

    ELSEIF pv_flg = 'B'.
    ENDIF.

  ENDLOOP.


  LOOP AT pt_t44_all   INTO  DATA(ls_t44_all).

    IF ls_t44_all-zflzff IS NOT INITIAL.
      PERFORM frm_get_zghf_attr USING ls_t44_all-zflzff CHANGING lv_flg.
      IF lv_flg NE 'I'.

        CLEAR lv_msgv1. lv_msgv1 = '，付款方必须为内部付款方' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

      ENDIF.
    ENDIF.

  ENDLOOP.


ENDFORM.

FORM frm_check_data_org USING pt_tc02 TYPE  tt_tc02
                                  pt_bukrs TYPE  tt_bukrs
                                  pt_dcwrk TYPE  tt_dcwrk
                                  pt_werks TYPE  tt_werks
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:lv_msgv1 TYPE scp1_general_error-msgv1.

  LOOP AT pt_tc02 INTO DATA(ls_tc02).

    DATA(lt_bukrs) = pt_bukrs[].
    DATA(lt_dcwrk) = pt_dcwrk[].
    DATA(lt_werks) = pt_werks[].

    DELETE lt_bukrs WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_dcwrk WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_werks WHERE zitems_key NE ls_tc02-zitems_key.

*  检查组织结构的层级关系
*________________________________________________________________________________________
*\       \ 排除                            \    \  新增                                  \
*\_______\_________________________________\____\________________________________________\
*\ 上级  \ 上级不存在、上级存在状态是排除  \    \ 上级存在（上级存在但是状态是排除允许） \
*\_______\_________________________________\____\________________________________________\
*\ 下级  \ 下级不允许排除                  \    \ 下级不允许新增                         \
*\_______\_________________________________\____\________________________________________\


*    PERFORM frm_check_zzz USING     lt_bukrs
*                                    lt_dcwrk
*                                    lt_werks
*                                    ls_tc02
*                           CHANGING pt_msglist.


    PERFORM frm_check_zzz_new USING     lt_bukrs
                                        lt_werks
                                        ls_tc02
                               CHANGING pt_msglist.


  ENDLOOP.


ENDFORM.

*FORM frm_check_zzz  USING    pt_bukrs      TYPE tt_bukrs
*                              pt_dcwrk      TYPE tt_dcwrk
*                              pt_werks      TYPE tt_werks
*                              ps_tc02       TYPE LINE OF tt_tc02
*                     CHANGING pt_msglist    TYPE scp1_general_errors.
*
*  DATA:
*    lt_dc_buk   TYPE tt_wrk_check,
*    lt_wrk_dc   TYPE tt_wrk_check,
*    ls_data_wrk TYPE ty_wrk_check,
*    ls_dcwrk    TYPE ty_dcwrk.
*  DATA:
*        ls_msglist TYPE scp1_general_error.
*
*  DATA(lt_bukrs)   = pt_bukrs[].
*  DATA(lt_exclude) = pt_bukrs[].
*  DATA(lt_dcwrk)   = pt_dcwrk[].
*  DATA(lt_werks)   = pt_werks[].
*
*  DELETE lt_bukrs WHERE bukrs IS INITIAL.
*  DELETE lt_dcwrk WHERE dcwrk IS INITIAL.
*  DELETE lt_werks WHERE werks IS INITIAL.
*  SORT lt_exclude BY exclude.
*  DELETE ADJACENT DUPLICATES FROM lt_exclude COMPARING exclude.
*
*  ls_msglist-msgty = 'E'.
*  ls_msglist-msgid = '00'.
*  ls_msglist-msgno = '001'.
*
*
**  检查DC层级（与公司代码层级比较）
*
**********  11111 先取出公司代码层级对应的DC清单
*********  READ TABLE LT_BUKRS TRANSPORTING NO FIELDS WITH KEY BUKRS = GC_ALL .
**********  IF SY-SUBRC EQ 0 OR LT_BUKRS[] IS INITIAL.
**********  若存在 ALL 的条目则无限制取数
*********  IF SY-SUBRC EQ 0 .
*********    SELECT
*********      A~WERKS,
*********      ' ' AS EXCLUDE
*********      FROM T001W AS A  JOIN T001K AS B
*********                       ON A~WERKS = B~BWKEY
*********                       AND A~VLFKZ = 'B'
*********      INTO CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
*********
*********  ELSE.
*********
**********    取未排除的数据
*********    SELECT
*********      A~WERKS,
*********      ' ' AS EXCLUDE
*********      FROM T001W AS A  JOIN T001K AS B
*********                       ON A~WERKS = B~BWKEY
*********                       AND A~VLFKZ = 'B'
*********                       JOIN @LT_BUKRS AS M
*********                       ON B~BUKRS = M~BUKRS
*********                       AND  M~EXCLUDE = ''
*********      INTO CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
*********
**********    取排除的数据
*********    SELECT
*********      A~WERKS,
*********      'X' AS EXCLUDE
*********      FROM T001W AS A  JOIN T001K AS B
*********                       ON A~WERKS = B~BWKEY
*********                       AND A~VLFKZ = 'B'
*********                       JOIN @LT_BUKRS AS M
*********                       ON B~BUKRS = M~BUKRS
*********                       AND  M~EXCLUDE = 'X'
*********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
*********
*********  ENDIF.
*********
**********  22222  根据DC层级的DC清单 与 上一步公司代码层级对应的DC清单进行比较检查
*********  SORT LT_DC_BUK BY EXCLUDE WERKS .
*********  LOOP AT LT_DCWRK INTO LS_DCWRK .
*********
**********    检查是否允许排除
*********    IF LS_DCWRK-EXCLUDE = 'X'.
*********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
*********                                                              EXCLUDE = ''
*********                                                              WERKS = LS_DCWRK-DCWRK
*********                                                              BINARY SEARCH.
*********      IF SY-SUBRC NE 0.
**********        PV_FLG_ERR = 'E'.
*********        LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!'.
*********        APPEND LS_MSGLIST TO PT_MSGLIST.
*********      ENDIF.
*********
*********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
*********                                                              EXCLUDE = 'X'
*********                                                              WERKS = LS_DCWRK-DCWRK
*********                                                              BINARY SEARCH.
*********      IF SY-SUBRC EQ 0.
**********        PV_FLG_ERR = 'E'.
*********        LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!'.
*********        APPEND LS_MSGLIST TO PT_MSGLIST.
*********      ENDIF.
*********
**********    检查是否允许新增
*********    ELSE.
*********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
*********                                                              EXCLUDE = 'X'
*********                                                              WERKS = LS_DCWRK-DCWRK
*********                                                              BINARY SEARCH.
*********      IF SY-SUBRC EQ 0.
*********      ELSE.
*********        READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
*********                                                                EXCLUDE = ''
*********                                                                WERKS = LS_DCWRK-DCWRK
*********                                                                BINARY SEARCH.
*********        IF SY-SUBRC EQ 0.
**********          PV_FLG_ERR = 'E'.
*********          LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：存在上一级的情况下，下一级可以排除但不能被增加!'.
*********          APPEND LS_MSGLIST TO PT_MSGLIST.
*********        ENDIF.
*********      ENDIF.
*********    ENDIF.
*********  ENDLOOP.
*
*********
**********  检查门店层级（与DC代码层级比较）
*********
*********
**********  首先将公司代码级对应的DC清单 并入到DC级
*********  LOOP AT LT_DC_BUK INTO LS_DATA_WRK.
*********    CLEAR LS_DCWRK.
*********    LS_DCWRK-DCWRK = LS_DATA_WRK-WERKS.
*********    LS_DCWRK-EXCLUDE = LS_DATA_WRK-EXCLUDE.
*********    APPEND LS_DCWRK TO LT_DCWRK.
*********  ENDLOOP.
*********
**********  11111 先取出DC层级对应的门店清单
*********  READ TABLE LT_DCWRK TRANSPORTING NO FIELDS WITH KEY DCWRK = GC_ALL .
**********  IF SY-SUBRC EQ 0 OR LT_DCWRK[] IS INITIAL.
**********  若存在 ALL 的条目则无限制取数
*********  IF SY-SUBRC EQ 0 .
*********    SELECT
*********      A~WERKS,
*********      ' ' AS EXCLUDE
*********      FROM T001W AS A
*********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
*********
*********  ELSE.
*********
**********    取未排除的数据
**********    SELECT
**********      A~WERKS,
**********      ' ' AS EXCLUDE
**********      FROM T001W AS A JOIN ZSDT0014 AS B
**********                      ON   A~WERKS = B~LOCNR
**********                      JOIN @LT_DCWRK AS M
**********                      ON   B~ZSJBM = M~DCWRK
**********                      AND  M~EXCLUDE = ''
**********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
*********
*********    SELECT
*********      A~WERKS,
*********      ' ' AS EXCLUDE
*********      FROM T001W AS A JOIN WRF3 AS B
*********                      ON   RIGHT( B~LOCNR,4 ) = A~WERKS
*********                      JOIN @LT_DCWRK AS M
*********                      ON   B~LOCLB = M~DCWRK
*********                      AND  M~EXCLUDE = ''
*********                      AND  A~VLFKZ = 'A'
*********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
*********
**********    取排除的数据
*********    SELECT
*********      A~WERKS,
*********      'X' AS EXCLUDE
*********      FROM T001W AS A JOIN WRF3 AS B
*********                      ON   RIGHT( B~LOCNR,4 ) = A~WERKS
*********                      JOIN @LT_DCWRK AS M
*********                      ON   B~LOCLB = M~DCWRK
*********                      AND  M~EXCLUDE = 'X'
*********                      AND  A~VLFKZ = 'A'
*********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
*********
*********  ENDIF.
*
*
***********************************************************************
*
**  11111 先取出公司代码层级对应的地点清单
*  READ TABLE lt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = gc_all .
**  IF SY-SUBRC EQ 0 OR LT_BUKRS[] IS INITIAL.
**  若存在 ALL 的条目则无限制取数
*  IF sy-subrc EQ 0 .
*    SELECT
*      a~werks,
*      ' ' AS exclude
*      FROM t001w AS a  JOIN t001k AS b
*                       ON a~werks = b~bwkey
**                       AND A~VLFKZ = 'B'
*      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
*
*  ELSE.
*
**    取未排除的数据
*    SELECT
*      a~werks,
*      ' ' AS exclude
*      FROM t001w AS a  JOIN t001k AS b
*                       ON a~werks = b~bwkey
**                       AND A~VLFKZ = 'B'
*                       JOIN @lt_bukrs AS m
*                       ON b~bukrs = m~bukrs
*                       AND  m~exclude = ''
*      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
*
***********************************************************************
*    SELECT
*      a~werks,
*      ' ' AS exclude
*       FROM t001w     AS a JOIN t001k     AS b
*                           ON a~werks = b~bwkey AND bukrs = '9901'
*                           JOIN zsdt0014  AS d
*                           ON a~werks = d~locnr
*                           JOIN @lt_bukrs AS m
*                           ON d~bukrs = m~bukrs AND  m~exclude = ''
*      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
***********************************************************************
*
**    取排除的数据
*    SELECT
*      a~werks,
*      'X' AS exclude
*      FROM t001w AS a  JOIN t001k AS b
*                       ON a~werks = b~bwkey
**                       AND A~VLFKZ = 'B'
*                       JOIN @lt_bukrs AS m
*                       ON b~bukrs = m~bukrs
*                       AND  m~exclude = 'X'
*      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
*
***********************************************************************
*    SELECT
*      a~werks,
*      'X' AS exclude
*      FROM t001w AS a  JOIN t001k AS b
*                       ON a~werks = b~bwkey AND bukrs = '9901'
*                       JOIN zsdt0014  AS d
*                       ON a~werks = d~locnr
*                       JOIN @lt_bukrs AS m
*                       ON d~bukrs = m~bukrs
*                       AND  m~exclude = 'X'
*      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
***********************************************************************
*  ENDIF.
*
*  CLEAR:lt_wrk_dc,lt_wrk_dc[].
*
*  lt_wrk_dc[] = lt_dc_buk[].
*
***********************************************************************
*  IF lines( lt_exclude ) = 2.
*    ls_msglist-msgv1 = '【公司代码】不容许排除和包含同时存在！' && '行项目ID:' && ps_tc02-zitems.
*    APPEND ls_msglist TO pt_msglist.
*  ENDIF.
*
**  22222  根据门店层级的门店清单 与 上一步公司代码层级对应的门店清单进行比较检查
*
*  SORT lt_wrk_dc BY exclude werks .
*  LOOP AT lt_werks INTO DATA(ls_werks) .
**    检查是否允许排除
*    IF ls_werks-exclude = 'X'.
*      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
*                                                              exclude = ''
*                                                              werks = ls_werks-werks
*                                                              BINARY SEARCH.
*      IF sy-subrc NE 0.
**        PV_FLG_ERR = 'E'.
*        ls_msglist-msgv1 = ls_werks-werks && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!' && '行项目号：' && ps_tc02-zitems.
*        APPEND ls_msglist TO pt_msglist.
*      ENDIF.
*
*      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
*                                                              exclude = 'X'
*                                                              werks = ls_werks-werks
*                                                              BINARY SEARCH.
*      IF sy-subrc EQ 0.
**        PV_FLG_ERR = 'E'.
*        ls_msglist-msgv1 = ls_werks-werks && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!' && '行项目号：' && ps_tc02-zitems.
*        APPEND ls_msglist TO pt_msglist.
*      ENDIF.
*
**    检查是否允许新增
*    ELSE.
*      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
*                                                              exclude = 'X'
*                                                              werks = ls_werks-werks
*                                                              BINARY SEARCH.
*      IF sy-subrc EQ 0.
*      ELSE.
*        READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
*                                                                exclude = ''
*                                                                werks = ls_werks-werks
*                                                                BINARY SEARCH.
*        IF sy-subrc EQ 0.
**          PV_FLG_ERR = 'E'.
*          ls_msglist-msgv1 = ls_werks-werks && '：存在上一级的情况下，下一级可以排除但不能被增加!' && '行项目号：' && ps_tc02-zitems.
*          APPEND ls_msglist TO pt_msglist.
*        ENDIF.
*      ENDIF.
*    ENDIF.
*  ENDLOOP.
*
*
*  READ TABLE lt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = gc_all .
*  IF sy-subrc EQ 0.
*    LOOP AT lt_werks INTO ls_werks WHERE exclude = ''.
*      ls_msglist-msgv1 = ls_werks-werks && '公司代码为ALL时，DC/门店只能排除!' && '行项目号：' && ps_tc02-zitems.
*      APPEND ls_msglist TO pt_msglist.
*      EXIT.
*    ENDLOOP.
*  ENDIF.
*
*
*  IF lt_bukrs[] IS INITIAL AND lt_werks[]  IS INITIAL.
*    ls_msglist-msgv1 = '【公司代码】和【门店/DC】不能同时为空' && '行项目ID:' && ps_tc02-zitems.
*    APPEND ls_msglist TO pt_msglist.
*  ENDIF.
*
*
*ENDFORM.


FORM frm_check_zzz_new  USING    pt_bukrs      TYPE tt_bukrs
                                 pt_werks      TYPE tt_werks
                                 ps_tc02       TYPE LINE OF tt_tc02
                        CHANGING pt_msglist    TYPE scp1_general_errors.

  DATA:it_bukrs TYPE zrei0080.
  DATA:it_werks TYPE zrei0081.
  DATA:ot_msglist TYPE  scp1_general_errors.

  MOVE-CORRESPONDING pt_bukrs TO it_bukrs.
  MOVE-CORRESPONDING pt_werks TO it_werks.

  CALL FUNCTION 'ZREFM0038'
    EXPORTING
      it_bukrs   = it_bukrs
      it_werks   = it_werks
    IMPORTING
      ot_msglist = ot_msglist.

  LOOP AT ot_msglist INTO DATA(ls_msglist) .
    ls_msglist-msgv1 = ls_msglist-msgv1 && '行项目ID:' && ps_tc02-zitems.
    APPEND ls_msglist TO pt_msglist.
  ENDLOOP.

ENDFORM.

FORM frm_check_zzz_new_pd  USING   pt_bukrs      TYPE tt_bukrs
                                   pt_werks      TYPE tt_werks
                                   pv_zitems
                          CHANGING pt_msglist    TYPE scp1_general_errors.

  DATA:it_bukrs TYPE zrei0080.
  DATA:it_werks TYPE zrei0081.
  DATA:ot_msglist TYPE  scp1_general_errors.

  MOVE-CORRESPONDING pt_bukrs TO it_bukrs.
  MOVE-CORRESPONDING pt_werks TO it_werks.

  CALL FUNCTION 'ZREFM0038'
    EXPORTING
      it_bukrs   = it_bukrs
      it_werks   = it_werks
    IMPORTING
      ot_msglist = ot_msglist.

  LOOP AT ot_msglist INTO DATA(ls_msglist) .
    ls_msglist-msgv2 = ls_msglist-msgv1 && '行项目ID:' && pv_zitems.
    ls_msglist-msgv1 = pv_zitems.
    APPEND ls_msglist TO pt_msglist.
  ENDLOOP.

ENDFORM.



*&---------------------------------------------------------------------*
*&      MODULE  MDL_USER_COMMAND_2200  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_user_command_2200_a INPUT.

  IF sy-ucomm =  'TC_ITEM_DELE'.
*    先删除附加信息， 然后再物理删除，因此需要放在物理删除之前进行 否则丢失行项目KEY 无法再删除其他信息
    PERFORM frm_pro_data_delete CHANGING
                                       ok_code
                                       gt_tc02
                                       gt_tc04_set
                                       gt_t11_all
                                       gt_bukrs_set
                                       gt_dcwrk_set
                                       gt_werks_set
                                       gt_ekorg_set.
    sy-ucomm = ok_code.

  ENDIF.

  CLEAR gs_data_base-zflg_ztmpid_changed.
ENDMODULE.

MODULE mdl_user_command_2200_b INPUT.

**  IF SY-UCOMM = 'TC_ITEM_COPY'.
**    PERFORM FRM_PRO_DATA_COPY CHANGING GT_TC02
**                                       GT_TC04_SET
**                                       GT_T11_ALL
**                                       GT_BUKRS_SET
**                                       GT_DCWRK_SET
**                                       GT_WERKS_SET
**                                       GT_EKORG_SET .
**
**    CLEAR OK_CODE.
**    SY-UCOMM = OK_CODE.
**
**  ENDIF.

  CASE sy-ucomm.
    WHEN 'TC_ITEM_INSR'.
      PERFORM frm_set_zzlqd_default USING gt_tc02
                                          gt_zzlbm_set
                                          gt_zqdbm_set.
    WHEN 'TC_ITEM_COPY'.

      PERFORM frm_pro_data_copy CHANGING gt_tc02
                                         gt_t44_all
                                         gt_tc04_set
                                         gt_t11_all
                                         gt_zzlbm_set
                                         gt_zqdbm_set
                                         gt_bukrs_set
                                         gt_dcwrk_set
                                         gt_werks_set
                                         gt_ekorg_set .
      CLEAR ok_code.
      sy-ucomm = ok_code.
    WHEN 'TC_ITEM_TKGYS_ALL'.
      PERFORM frm_set_tkgys_all CHANGING gt_tc02.
    WHEN 'TC_ITEM_TKGYS_SEL'.
      PERFORM frm_set_tkgys_sel CHANGING gt_tc02.
    WHEN 'TC_ITEM_MASS_ZJT' OR 'TC_ITEM_MASS_ZGHF' OR 'TC_ITEM_MASS_ZFLZFF'.

      gs_data_base-zflg_ok_code = sy-ucomm.

      PERFORM frm_check_pre_mass_data USING gt_tc02.
      CHECK sy-subrc EQ 0.


      PERFORM frm_pro_pre_mass_data.

      CALL SCREEN '9200'.
      CLEAR ok_code.
      sy-ucomm = ok_code.
    WHEN OTHERS.
  ENDCASE.


  CLEAR gs_data_base-zflg_ztmpid_changed.
  CLEAR gs_data_base-zflg_ok_code.

ENDMODULE.

FORM frm_set_zzlqd_default  CHANGING pt_tc02  TYPE tt_tc02
                                     pt_zzlbm  TYPE tt_t81
                                     pt_zqdbm  TYPE tt_t82.

  DATA:
    ls_zzlbm TYPE ty_t81,
    ls_zqdbm TYPE ty_t82.

  LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<lfs_tc02>) WHERE zitems_insr = 'X'.

    ls_zzlbm-zitems_key = <lfs_tc02>-zitems_key.
    ls_zzlbm-zzlbm      = '999'.
    APPEND ls_zzlbm TO gt_zzlbm_set.

    ls_zqdbm-zitems_key = <lfs_tc02>-zitems_key.
    ls_zqdbm-zqdbm      = '9999'.
    APPEND ls_zqdbm TO gt_zqdbm_set.

    <lfs_tc02>-zitems_insr = ''.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_COPY
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_TC02
*&      <-- GT_TC04
*&      <-- GT_T11_ALL
*&      <-- PT_BUKRS
*&      <-- PT_DCWRK
*&      <-- PT_WERKS
*&      <-- PT_EKORG
*&---------------------------------------------------------------------*
FORM frm_pro_data_copy  CHANGING pt_tc02  TYPE tt_tc02
                                 pt_t44_all   TYPE tt_t44
                                 pt_tc04   TYPE tt_tc04
                                 pt_t11_all TYPE tt_t11
                                 pt_zzlbm  TYPE tt_t81
                                 pt_zqdbm  TYPE tt_t82
                                 pt_bukrs  TYPE tt_bukrs
                                 pt_dcwrk  TYPE tt_dcwrk
                                 pt_werks  TYPE tt_werks
                                 pt_ekorg  TYPE tt_ekorg.

  DATA(lt_tc04) = pt_tc04[].
  DATA(lt_t44_all) = pt_t44_all[].
  DATA(lt_t11_all) = pt_t11_all[].
  DATA(lt_zzlbm) = pt_zzlbm[].
  DATA(lt_zqdbm) = pt_zqdbm[].
  DATA(lt_bukrs) = pt_bukrs[].
  DATA(lt_dcwrk) = pt_dcwrk[].
  DATA(lt_werks) = pt_werks[].
  DATA(lt_ekorg) = pt_ekorg[].


  LOOP AT pt_tc02 INTO DATA(ls_tc02) WHERE zitems_copy IS NOT INITIAL .
    LOOP AT lt_t11_all INTO DATA(ls_t11_all) WHERE zitems_key = ls_tc02-zitems_copy.
      IF ls_tc02-zjt_id IS INITIAL.
        PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING ls_tc02-zjt_id.
      ENDIF.
      ls_t11_all-zjt_id     = ls_tc02-zjt_id.
      ls_t11_all-zitems_key = ls_tc02-zitems_key.
      APPEND ls_t11_all TO pt_t11_all.
    ENDLOOP.

    LOOP AT lt_t44_all INTO DATA(ls_t44_all) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_t44_all-zitems_key = ls_tc02-zitems_key.
      APPEND ls_t44_all TO pt_t44_all.
    ENDLOOP.

    LOOP AT lt_tc04 INTO DATA(ls_tc04) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_tc04-zitems_key = ls_tc02-zitems_key.
      APPEND ls_tc04 TO pt_tc04.
    ENDLOOP.

    LOOP AT lt_zzlbm INTO DATA(ls_zzlbm) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_zzlbm-zitems_key = ls_tc02-zitems_key.
      APPEND ls_zzlbm TO pt_zzlbm.
    ENDLOOP.

    LOOP AT lt_zqdbm INTO DATA(ls_zqdbm) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_zqdbm-zitems_key = ls_tc02-zitems_key.
      APPEND ls_zqdbm TO pt_zqdbm.
    ENDLOOP.

    LOOP AT lt_bukrs INTO DATA(ls_bukrs) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_bukrs-zitems_key = ls_tc02-zitems_key.
      APPEND ls_bukrs TO pt_bukrs.
    ENDLOOP.

    LOOP AT lt_dcwrk INTO DATA(ls_dcwrk) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_dcwrk-zitems_key = ls_tc02-zitems_key.
      APPEND ls_dcwrk TO pt_dcwrk.
    ENDLOOP.

    LOOP AT lt_werks INTO DATA(ls_werks) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_werks-zitems_key = ls_tc02-zitems_key.
      APPEND ls_werks TO pt_werks.
    ENDLOOP.

    LOOP AT lt_ekorg INTO DATA(ls_ekorg) WHERE zitems_key = ls_tc02-zitems_copy.
      ls_ekorg-zitems_key = ls_tc02-zitems_key.
      APPEND ls_ekorg TO pt_ekorg.
    ENDLOOP.

    ls_tc02-zitems_copy = ''.
    MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING zjt_id zitems_copy.
  ENDLOOP.


ENDFORM.

FORM frm_pro_data_delete  CHANGING
                                 pv_code TYPE sy-ucomm
                                 pt_tc02  TYPE tt_tc02
                                 pt_tc04   TYPE tt_tc04
                                 pt_t11_all TYPE tt_t11
                                 pt_bukrs  TYPE tt_bukrs
                                 pt_dcwrk  TYPE tt_dcwrk
                                 pt_werks  TYPE tt_werks
                                 pt_ekorg  TYPE tt_ekorg.




  LOOP AT pt_tc02 INTO DATA(ls_tc02) WHERE sel = 'X'.

    IF ls_tc02-zitems IS NOT INITIAL.
*      命令终止
      CLEAR pv_code.
      MESSAGE s888(sabapdocu) WITH '已经生成行项目ID，不允许删除！' DISPLAY LIKE 'E'.
      RETURN.
    ENDIF.

    DELETE pt_tc04 WHERE zitems_key = ls_tc02-zitems_key.
    DELETE pt_t11_all WHERE zitems_key = ls_tc02-zitems_key.
    DELETE pt_bukrs WHERE zitems_key = ls_tc02-zitems_key.
    DELETE pt_dcwrk WHERE zitems_key = ls_tc02-zitems_key.
    DELETE pt_werks WHERE zitems_key = ls_tc02-zitems_key.
    DELETE pt_ekorg WHERE zitems_key = ls_tc02-zitems_key.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_ZITEMS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_zitems INPUT.


  DATA:
    gv_tmp_index_line TYPE i.


  PERFORM frm_get_index_line USING      'BT_BUKRS'
                                        gv_cursor_line
                             CHANGING   gv_tmp_index_line.


  IF gs_data_base-zflg_cprog = 'ZRED0041'.
    IF gs_data_base-zflg_ztmpid_changed = ''.   "模板更改有单独的整体处理逻辑，此处只处理新输入行项目逻辑
      IF gs_tc02-zitems NE 0 AND gs_tc02-zflg_exist = ''.
        IF gs_tc02-zitems < 200.
          SELECT SINGLE * INTO @DATA(ls_tc02_tmp) FROM zretc002 WHERE ztmpid = @gs_tc01-ztmpid AND zitems = @gs_tc02-zitems.
          IF sy-subrc NE 0.
            MESSAGE e888(sabapdocu) WITH '该行在组织模板中不存在，请检查' DISPLAY LIKE 'E'.
          ELSE.
            IF ls_tc02_tmp-zstatus = 'D'.
              MESSAGE e888(sabapdocu) WITH '该行在组织模板中已删除，请检查' DISPLAY LIKE 'E'.
            ENDIF.
          ENDIF.
        ENDIF.


        SELECT COUNT(*) FROM @gt_tc02 AS i WHERE  zitems = @gs_tc02-zitems INTO @DATA(lv_count).
        IF lv_count > 0.
          CLEAR lv_count.
          MESSAGE e888(sabapdocu) WITH '该行项目已经添加，不能重复添加' DISPLAY LIKE 'E'.
        ENDIF.

        IF gs_tc02-zitems >= 200 .
          MESSAGE e888(sabapdocu) WITH '行项目不是组织模板行项目号，若新增请点击新增按钮' DISPLAY LIKE 'E'.
        ENDIF.

        PERFORM frm_add_data_org      USING     gv_tmp_index_line
                                      CHANGING
                                                gs_tc01
                                                gs_tc02
                                                gt_tc02
                                                gt_tc03
                                                gt_tc04_set
                                                gt_t44_all
                                                gt_bukrs_set
                                                gt_dcwrk_set
                                                gt_werks_set
                                                gt_ekorg_set
                                                gt_matnr_set
                                                .


      ENDIF.
    ENDIF.

  ENDIF.

  CLEAR ls_tc02_tmp.
ENDMODULE.

MODULE mdl_check_sel_man INPUT.
  IF gs_ta02-zleib = 'R'.
    SELECT SINGLE zxyzt INTO @DATA(lv_zxyzt)
      FROM zret0006
     INNER JOIN zreta007 ON zret0006~zxy_id = zreta007~zdjbm
      WHERE zdybm = @gs_tc02-zxy_id.
    IF sy-subrc = 0 .
      IF gs_tc02-sel_man = 'X' .
        IF lv_zxyzt = 'D' .
          MESSAGE e888(sabapdocu) WITH '原协议状态为删除，所以对应申请协议不可设置为有效' DISPLAY LIKE 'E'.
        ENDIF.
      ELSE.
        IF lv_zxyzt = 'A'  .
          MESSAGE e888(sabapdocu) WITH '原协议状态为有效，所以对应申请协议不可设置为无效' DISPLAY LIKE 'E'.

        ENDIF.
      ENDIF.
    ENDIF.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_F4_ZITEMS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_f4_zitems INPUT.
  PERFORM frm_f4_zitems USING gs_tc01  .
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_F4_ZITEMS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TC02
*&---------------------------------------------------------------------*
FORM frm_f4_zitems  USING   ps_tc01 TYPE LINE OF tt_tc01.

  DATA dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.
  DATA:lt_ret_tab TYPE TABLE OF ddshretval WITH HEADER LINE.

  DATA:
        lv_dis TYPE ddbool_d .

  LOOP AT SCREEN.
    IF screen-name = 'GS_TC02-ZITEMS' AND screen-input EQ 0.
      lv_dis = 'X'.
    ENDIF.
  ENDLOOP.

  SELECT
    ztmpid,
    zitems
    FROM zretc002
    WHERE ztmpid = @ps_tc01-ztmpid
    INTO TABLE @DATA(lt_tc02).

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'ZITEMS'
      value_org       = 'S'
      dynpprog        = sy-repid
      dynpnr          = sy-dynnr
      dynprofield     = 'GS_TC02-ZITEMS'
      display         = lv_dis
*     CALLBACK_PROGRAM = SY-REPID
*     CALLBACK_FORM   = 'USER_FORM'
    TABLES
      value_tab       = lt_tc02[]
      return_tab      = lt_ret_tab[]
    EXCEPTIONS
      parameter_error = 1
      no_values_found = 2
      OTHERS          = 3.


  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_TYPE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE
*&---------------------------------------------------------------------*
FORM frm_set_screen_type  USING    ps_data_base TYPE ty_data_base.

*  固定类控制
  IF gs_data_base-zflg_type = 'GD'.

    LOOP AT SCREEN.
      IF     screen-group4 = '401'
          OR screen-group4 = '404' .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
*  计算类
  ELSEIF gs_data_base-zflg_type = 'JS'.
    LOOP AT SCREEN.
      IF screen-group4 = '402' .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
*  促销类
  ELSEIF gs_data_base-zflg_type = 'CX'.
    LOOP AT SCREEN.
      IF    screen-group4 = '403'
          OR screen-group4 = '404' .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.


FORM frm_add_data_org    USING    pv_index TYPE i
                          CHANGING
                            ps_tc01 TYPE LINE OF tt_tc01
                            ps_tc02 TYPE LINE OF tt_tc02
                            pt_tc02 TYPE tt_tc02
                            pt_tc03 TYPE tt_tc03
                            pt_tc04 TYPE tt_tc04
                            pt_t44_all  TYPE tt_t44
                            pt_bukrs TYPE tt_bukrs
                            pt_dcwrk TYPE tt_dcwrk
                            pt_werks TYPE tt_werks
                            pt_ekorg TYPE tt_ekorg
                            pt_matnr TYPE tt_matnr
  .

  DATA:
    ls_tc01  TYPE LINE OF tt_tc01,
    lt_tc02  TYPE tt_tc02,
    lt_tc03  TYPE tt_tc03,
    lt_tc04  TYPE tt_tc04,
    lt_t44   TYPE tt_t44,
    lt_matnr TYPE tt_matnr,
    lt_zzgys TYPE tt_t84.

  PERFORM frm_get_org_data_ztmpid USING ps_tc01-ztmpid
                        CHANGING
                                 ls_tc01
                                 lt_tc02
                                 lt_tc03
                                 lt_tc04
                                 lt_t44
                                 lt_matnr
                                 lt_zzgys
                                 .
*  排除无效数据
  DELETE lt_tc02 WHERE zitems NE ps_tc02-zitems.
  DELETE lt_tc03 WHERE zitems NE ps_tc02-zitems.
  DELETE lt_tc04 WHERE zitems NE ps_tc02-zitems.
  DELETE lt_t44 WHERE zitems NE ps_tc02-zitems.
  DELETE lt_matnr WHERE zitems NE ps_tc02-zitems.
  DELETE lt_zzgys WHERE zitems NE ps_tc02-zitems.

*  更新行项目数据
  READ TABLE lt_tc02 INTO DATA(ls_tc02) WITH KEY zitems = ps_tc02-zitems.
  MOVE-CORRESPONDING ls_tc02 TO ps_tc02.

*  此处增加KEY 无意义，如果增加KEY，那么其他表（组织结构，支付方，供货方等）都需要同步更新KEY
*****  IF ps_tc02-zitems_key IS NOT INITIAL.
*****    PERFORM frm_get_zitems_key CHANGING ps_tc02-zitems_key gv_zitem_key.
******    PS_TC02-ZITEMS_KEY = PS_TC02-ZITEMS. %%%%%
*****  ENDIF.

*  获取操作的行
  READ TABLE pt_tc02 INTO DATA(ls_tc02_tmp) INDEX pv_index.

*  删除掉旧数据
  DELETE pt_tc03 WHERE zitems = ls_tc02_tmp-zitems.
  DELETE pt_tc04 WHERE zitems = ls_tc02_tmp-zitems.
  DELETE pt_t44_all WHERE zitems = ls_tc02_tmp-zitems.
  DELETE pt_matnr WHERE zitems = ls_tc02_tmp-zitems.

*  添加新数据
  APPEND LINES OF lt_tc03 TO pt_tc03.
  APPEND LINES OF lt_tc04 TO pt_tc04.
  APPEND LINES OF lt_t44 TO pt_t44_all.
  APPEND LINES OF lt_matnr TO pt_matnr.


  PERFORM frm_zzzlx_unfold                USING       lt_tc03
                                          CHANGING    pt_bukrs
                                                      pt_dcwrk
                                                      pt_werks
                                                      pt_ekorg.



ENDFORM.

FORM frm_pro_data_double        CHANGING
                                  pt_t44        TYPE tt_t44
                                  pt_tc04        TYPE tt_tc04
                                  pt_zzlbm       TYPE tt_t81
                                  pt_zqdbm       TYPE tt_t82
                                  pt_zzgys       TYPE tt_t84
                                  pt_bukrs       TYPE tt_bukrs
                                  pt_dcwrk       TYPE tt_dcwrk
                                  pt_werks       TYPE tt_werks
                                  pt_ekorg       TYPE tt_ekorg
                                  pt_matnr       TYPE tt_matnr
  .


  PERFORM frm_set_seq TABLES pt_t44 USING 'SEQ'.

  SORT pt_t44 BY zitems_key zflzff  .
  SORT pt_tc04 BY zitems_key zghf zzzpc .
  SORT pt_zzlbm BY zitems_key zzlbm .
  SORT pt_zqdbm BY zitems_key zqdbm .
  SORT pt_zzgys BY zitems_key lifnr .
  SORT pt_bukrs BY zitems_key bukrs .
  SORT pt_dcwrk BY zitems_key dcwrk exclude.
  SORT pt_werks BY zitems_key werks exclude.
  SORT pt_ekorg BY zitems_key ekorg.
  SORT pt_matnr BY zitems_key matnr.

  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING zitems_key zflzff.
  DELETE ADJACENT DUPLICATES FROM pt_tc04 COMPARING zitems_key zghf zzzpc.
  DELETE ADJACENT DUPLICATES FROM pt_zzlbm COMPARING zitems_key zzlbm .
  DELETE ADJACENT DUPLICATES FROM pt_zqdbm COMPARING zitems_key zqdbm .
  DELETE ADJACENT DUPLICATES FROM pt_zzgys COMPARING zitems_key lifnr .
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING zitems_key bukrs .
  DELETE ADJACENT DUPLICATES FROM pt_dcwrk COMPARING zitems_key dcwrk exclude.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING zitems_key werks exclude.
  DELETE ADJACENT DUPLICATES FROM pt_ekorg COMPARING zitems_key ekorg .
  DELETE ADJACENT DUPLICATES FROM pt_matnr COMPARING zitems_key matnr .

  SORT pt_t44 BY seq .

ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_SCREEN_MATNR  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_screen_tc_item_zspz_id INPUT.
  PERFORM frm_set_screen_zspz_id USING gs_tc02-zspz_id
                               CHANGING gs_tc02-zspzid_txt.

ENDMODULE.

MODULE mdl_set_screen_tc_item_matnr INPUT.

  PERFORM frm_set_screen_matnr USING gs_tc02-matnr
                               CHANGING gs_tc02-meins
                                        gs_tc02-maktx.
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZDFFS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_TA02
*&      --> PT_TC02
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_zdffs  USING    ps_ta02  TYPE LINE OF tt_ta02
                               pt_tc02  TYPE tt_tc02
                      CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.

*  IF PS_TA02-ZFLLX = 'RB02' OR
*     PS_TA02-ZFLLX = 'RB03' OR
*     PS_TA02-ZFLLX = 'RB05' OR
*     PS_TA02-ZFLLX = 'RB06' .
*    IF PS_TA02-ZDFFS = 'M'.
*      PERFORM FRM_ADD_MSG USING '兑付方式不能是M-货返'  CHANGING PT_MSGLIST.
*    ENDIF.
*    LOOP AT PT_TC02 INTO DATA(LS_TC02) WHERE ZDFFS = 'M'.
*      CLEAR LV_MSGV1. LV_MSGV1 = '兑付方式不能是M-货返' && '行项目ID:' && LS_TC02-ZITEMS.
*      PERFORM FRM_ADD_MSG USING LV_MSGV1  CHANGING PT_MSGLIST.
*    ENDLOOP.
*  ELSEIF PS_TA02-ZFLLX = 'RB04' .
*    IF PS_TA02-ZDFFS IS NOT INITIAL AND  PS_TA02-ZDFFS NE 'M'.
*      PERFORM FRM_ADD_MSG USING '兑付方式只能是M-货返'  CHANGING PT_MSGLIST.
*    ENDIF.
*    LOOP AT PT_TC02 INTO LS_TC02 WHERE ZDFFS IS NOT INITIAL AND ZDFFS NE 'M'.
*      CLEAR LV_MSGV1. LV_MSGV1 = '兑付方式只能是M-货返' && '行项目ID:' && LS_TC02-ZITEMS.
*      PERFORM FRM_ADD_MSG USING LV_MSGV1  CHANGING PT_MSGLIST.
*    ENDLOOP.
*
*  ENDIF.


  IF ps_ta02-zxybstyp NE 'Q'.
    IF ps_ta02-zdffs = 'M'.
      PERFORM frm_add_msg USING '兑付方式不能是M-货返'  CHANGING pt_msglist.
    ENDIF.
    LOOP AT pt_tc02 INTO DATA(ls_tc02) WHERE zdffs = 'M'.
      CLEAR lv_msgv1. lv_msgv1 = '兑付方式不能是M-货返' && '行项目ID:' && ls_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDLOOP.
  ELSE.
    IF ps_ta02-zdffs IS NOT INITIAL AND  ps_ta02-zdffs NE 'M'.
      PERFORM frm_add_msg USING '兑付方式只能是M-货返'  CHANGING pt_msglist.
    ENDIF.
    LOOP AT pt_tc02 INTO ls_tc02 WHERE zdffs IS NOT INITIAL AND zdffs NE 'M'.
      CLEAR lv_msgv1. lv_msgv1 = '兑付方式只能是M-货返' && '行项目ID:' && ls_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDLOOP.

  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_F4_TC_ITEM_ZSPZ_ID  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_f4_tc_item_zspz_id INPUT.

  PERFORM frm_f4_zspz_id USING  'A'
                                gt_t09
                                gs_ta02-zxybstyp
                                gs_ta02-zht_id.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_F4_TC_ITEM_ZSPZ_ID  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_f4_tc_item_zflsqf INPUT.
*& ADD BY zsfsx = '2'
  PERFORM frm_f4_zflsqf.
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_PAI_ZFLZFF
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_CURSOR_LINE
*&      --> GT_TC02
*&      <-- GT_T44_ALL
*&---------------------------------------------------------------------*
FORM frm_pro_pbo_zflzff  USING
                                  pv_code TYPE sy-ucomm
                                  pv_cursor_line TYPE i
                                  pt_tc02 TYPE  tt_tc02
                         CHANGING pt_t44_all TYPE tt_t44.


  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.
*  获取行项目KEY
  PERFORM frm_dtl_get_zitems USING lv_index_line
                                   'GT_TC02[]'
                             CHANGING lv_zitems_key.

  READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems_key = lv_zitems_key.

  PERFORM frm_add_zflzff_t44 USING ls_tc02
                             CHANGING pt_t44_all.


ENDFORM.
FORM frm_pro_pai_zflzff  USING
                                  pv_code TYPE sy-ucomm
                                  pv_cursor_line TYPE i
                                  pt_tc02 TYPE  tt_tc02
                         CHANGING pt_t44_all TYPE tt_t44.


  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.
*  获取行项目KEY
  PERFORM frm_dtl_get_zitems USING lv_index_line
                                   'GT_TC02[]'
                             CHANGING lv_zitems_key.

  READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems_key = lv_zitems_key.

  READ TABLE pt_t44_all INTO DATA(ls_t44_all) WITH KEY zitems_key = lv_zitems_key.
*  IF SY-SUBRC EQ 0.
*  ENDIF.
  ls_tc02-zflzff = ls_t44_all-zflzff.
  ls_tc02-zpaytp = ls_t44_all-zpaytp.
  MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING zflzff zpaytp WHERE zitems_key = lv_zitems_key.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_ADD_ZFLZFF_T44
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      <-- PT_T44_ALL
*&---------------------------------------------------------------------*
*  自动将行项目返利支付方添加到返利支付方列表中第一行
FORM frm_add_zflzff_t44  USING    ps_tc02 TYPE LINE OF tt_tc02
                         CHANGING pt_t44_all  TYPE tt_t44.

  DATA:

    ls_t44        TYPE LINE OF tt_t44.

  CLEAR ls_t44.
  ls_t44-zflzff = ps_tc02-zflzff.
  ls_t44-zitems_key = ps_tc02-zitems_key.
  ls_t44-zpaytp = ps_tc02-zpaytp.

  READ TABLE pt_t44_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ps_tc02-zitems_key
                                                        zflzff   = ps_tc02-zflzff.
  IF sy-subrc NE 0.
    APPEND ls_t44 TO pt_t44_all.
  ELSE.
    MODIFY pt_t44_all FROM ls_t44 INDEX sy-tabix TRANSPORTING zpaytp .
  ENDIF.

ENDFORM.

*  自动将行项目返利支付方添加到返利支付方列表中第一行
FORM frm_add_zflzff_t44_all  USING    pt_tc02 TYPE  tt_tc02
                         CHANGING pt_t44_all  TYPE tt_t44.

  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    PERFORM frm_add_zflzff_t44 USING ls_tc02
                               CHANGING pt_t44_all.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_CHECK_TC_BUKRS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_tc_bukrs INPUT.
  READ TABLE gt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = 'ALL'.
  IF sy-subrc EQ 0.
    LOOP AT gt_bukrs TRANSPORTING NO FIELDS WHERE bukrs NE 'ALL' .
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      MESSAGE e888(sabapdocu) WITH '公司代码ALL与公司代码值 不能同时存在'.
    ENDIF.
  ENDIF.

  DATA(lt_buk1) = gt_bukrs.
  DATA(lt_buk2) = gt_bukrs.
  DELETE lt_buk1 WHERE bukrs = '' .
  DELETE lt_buk2 WHERE bukrs = ''.
  SORT lt_buk1 BY bukrs.
  DELETE ADJACENT DUPLICATES FROM lt_buk1 COMPARING bukrs.
  IF lines( lt_buk1 ) <>  lines( lt_buk2 ) .
    MESSAGE e888(sabapdocu) WITH '公司代码有重复值 请检查！'.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_DATA_ZDFFS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_data_zdffs INPUT.


  DATA:
        gv_zdffs_tmp TYPE zreta002-zdffs.

  CLEAR gv_zdffs_tmp.
  IF gs_tc02-zdffs IS  INITIAL.
    gv_zdffs_tmp = gs_ta02-zdffs.
  ELSE.
    gv_zdffs_tmp = gs_tc02-zdffs.
  ENDIF.



  IF gs_data_base-zflg_type = 'GD'.

    IF gv_zdffs_tmp = 'A' OR gv_zdffs_tmp = 'C'.
      gs_tc02-zmwskz = 'X3'.
    ENDIF.
  ENDIF.

ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_TEXT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_DATA_BASE
*&---------------------------------------------------------------------*
FORM frm_set_text  CHANGING ps_data_base TYPE ty_data_base.

  IF   ps_data_base-zflg_cprog = 'ZRED0035'..
    ps_data_base-zghf_t = '供货方'.
    ps_data_base-zitsplr_t = '内部供货'.
    ps_data_base-zetsplr_t = '条款供货方'.

  ELSEIF ps_data_base-zflg_cprog = 'ZRED0041'..

    IF ps_data_base-zxybstyp = 'A'.
      ps_data_base-zghf_t = '出票方'.
      ps_data_base-zitsplr_t = '内部出票方'.
      ps_data_base-zetsplr_t = '条款出票方'.
    ELSE.
      ps_data_base-zghf_t = '供货方'.
      ps_data_base-zitsplr_t = '内部供货'.
      ps_data_base-zetsplr_t = '条款供货方'.
    ENDIF.

    IF ps_data_base-zxybstyp = 'A'.
      ps_data_base-zghf_tt = '外部出票方'.
    ELSE.
      ps_data_base-zghf_tt = '外部供货方'.
    ENDIF.

    IF ps_data_base-zxybstyp = 'P'.
      ps_data_base-zspz_id_t = '商品组'.
    ELSE.
      ps_data_base-zspz_id_t = '任务商品'.
    ENDIF.

  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM frm_set_text2
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- gs_ta02
*&      <-- gs_screen_lab
*&---------------------------------------------------------------------*
FORM frm_set_text2           CHANGING ps_ta02      TYPE LINE OF tt_ta02
                                     ps_screen_lab TYPE ty_screen_lab
                                     pv_title_01.

  IF ps_ta02-zleib = 'R' .
    ps_screen_lab-zbm = '申请编码'.
    ps_screen_lab-zms = '申请描述'.
    pv_title_01       = '条款申请'.

    SELECT SINGLE zdjbm INTO ps_ta02-zdytk FROM  zreta007 WHERE zdybm = ps_ta02-ztk_id.

  ELSE.
    ps_screen_lab-zbm = '条款编码'.
    ps_screen_lab-zms = '条款描述'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_ZLRSI
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_FLG_COMM
*&      --> GV_FLG_RB
*&      --> GS_TC02
*&---------------------------------------------------------------------*
FORM frm_set_screen_zlrsi  USING    pv_flg_comm TYPE char10
                                    pv_flg_rb TYPE char2
                                    ps_tc02 TYPE LINE OF tt_tc02.

*  行项目级别编辑控制

  IF pv_flg_comm NE 'SAVE'.
    IF pv_flg_rb = '05' AND
        ( ps_tc02-zxyzt_06 = 'D' OR ps_tc02-zxyzt_06 = 'N' OR ps_tc02-zxyzt_06 = 'R' OR ps_tc02-zxyzt_06 = '' ).
      LOOP AT SCREEN.
        IF screen-group1 = '101' .
*          IF SCREEN-NAME(8) = 'GS_TC02-'.
*            SCREEN-INPUT = 1.
*          ELSE.
*            SCREEN-INPUT = 0.
*          ENDIF.
          screen-input = 1.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.

    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_SCREEN_INVISIBLE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_check_screen_invisible  USING pv_name TYPE screen-name.

  DATA: ls_cols TYPE cx_tableview_column.

  DATA:
    lv_name    TYPE screen-name,
    lv_flg_tmp TYPE char1.

  CASE pv_name.
    WHEN 'TC_ITEM_MASS_ZJT' . lv_name = 'GV_ICON_JT_SUB'.
    WHEN 'TC_ITEM_MASS_ZGHF' . lv_name = 'GV_ICON_ZGHF'.
    WHEN 'TC_ITEM_MASS_ZGHF1' . lv_name = 'GV_ICON_ZGHF'.
    WHEN 'TC_ITEM_MASS_ZFLZFF' . lv_name = 'GV_ICON_ZFLZFF'.
    WHEN OTHERS.
  ENDCASE.


  LOOP AT tc_item-cols INTO ls_cols .
    IF ls_cols-screen-name = lv_name AND ls_cols-invisible NE '1'.
      lv_flg_tmp = 'X'.
      EXIT.
    ENDIF.
  ENDLOOP.

  IF lv_flg_tmp = ''.
    sy-subrc = 4.
  ELSE.
    sy-subrc = 0.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_PRE_MASS_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_pro_pre_mass_data .

  IF gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZJT'.
    gs_data_base-subscreen_mass = '9100'.  "阶梯抬头
  ELSE.
    gs_data_base-subscreen_mass = '9201'.  "MASS 明细
  ENDIF.

  CASE gs_data_base-zflg_ok_code.
    WHEN 'TC_ITEM_MASS_ZJT'. gv_title_mass01 = '阶梯'.
    WHEN 'TC_ITEM_MASS_ZGHF'. gv_title_mass01 = '供货方'.
    WHEN 'TC_ITEM_MASS_ZFLZFF'. gv_title_mass01 = '支付方'.
    WHEN OTHERS.
  ENDCASE.

  CASE gv_flg_rb.
    WHEN '01' OR '02'.  gv_title_mass02 = '批量编辑'.
    WHEN OTHERS.        gv_title_mass02 = '批量查询'.
  ENDCASE.

  PERFORM frm_get_data_mass USING
                                  gs_data_base-zflg_ok_code
                                  gt_tc02
                                  gt_tc04_set
                                  gt_t44_all
                                  gt_t11_all
                            CHANGING gt_mass.

  PERFORM frm_sort_data USING gs_data_base-zflg_ok_code
                            CHANGING gt_mass.


ENDFORM.


FORM frm_check_pre_mass_data USING pt_tc02 TYPE tt_tc02.
  DATA:
        lv_msg TYPE string.

  CLEAR sy-subrc.
  READ TABLE pt_tc02 TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' zitems = '000'.
  IF sy-subrc EQ 0.
    sy-subrc = 4.
    MESSAGE s888(sabapdocu) WITH '新建行项目未保存，请先保存！' DISPLAY LIKE 'E'.
  ELSE.
    CLEAR sy-subrc.

*    READ TABLE PT_TC02 INTO DATA(LS_TC02) WITH KEY SEL_MAN = '' .
*    IF SY-SUBRC EQ 0.
*      CLEAR LV_MSG. LV_MSG = '行项目' && LS_TC02-ZITEMS && '未生效，请确认是否维护？'.
*      PERFORM FRM_ARE_YOU_SURE(ZBCS0001) USING LV_MSG SY-TITLE.
*    ENDIF.

  ENDIF.
ENDFORM.

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_MASS' ITSELF
CONTROLS: tc_mass TYPE TABLEVIEW USING SCREEN 9202.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_MASS'
DATA:     g_tc_mass_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_MASS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_mass_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_mass LINES tc_mass-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_MASS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_mass_get_lines OUTPUT.
  g_tc_mass_lines = sy-loopc.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_MASS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_mass_modify INPUT.
  MODIFY gt_mass
    FROM gs_mass
    INDEX tc_mass-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_MASS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_mass_mark INPUT.
  DATA: g_tc_mass_wa2 LIKE LINE OF gt_mass.
  IF tc_mass-line_sel_mode = 1
  AND gs_mass-sel = 'X'.
    LOOP AT gt_mass INTO g_tc_mass_wa2
      WHERE sel = 'X'.
      g_tc_mass_wa2-sel = ''.
      MODIFY gt_mass
        FROM g_tc_mass_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_mass
    FROM gs_mass
    INDEX tc_mass-current_line
    TRANSPORTING sel.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_SCREEN_TC_MASS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_mass OUTPUT.

  IF gs_mass-zitems IS NOT INITIAL.
    LOOP AT SCREEN.
      IF screen-name = 'GS_MASS-ZITEMS' OR
         screen-name = 'GS_MASS-ZBUKRS'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF gs_mass-zflg_1st = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_MASS-ZJT_FROM'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


  IF  gt_mass[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_pub_mass.

ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_DATA_TC_MASS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_mass OUTPUT.
* SET PF-STATUS 'XXXXXXXX'.
* SET TITLEBAR 'XXX'.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_MASS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_mass_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_MASS'
                              'GT_MASS'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9200  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9200 INPUT.

  DATA:
    gv_mtype_mass TYPE bapi_mtype,
    gv_msg_mass   TYPE bapi_msg.



  CLEAR:
        gv_mtype_mass,gv_msg_mass.

  PERFORM frm_code_pro CHANGING   ok_code
                                  gv_code.

  CASE gv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15' OR 'B_MASS_CANCEL'.
      IF sy-dynnr = '2000' AND ( gv_flg_rb = '01' OR gv_flg_rb = '02' ).
        PERFORM frm_are_you_sure(zbcs0001) USING '未保存的数据将丢失，是否继续？' sy-title.
        IF sy-subrc EQ 0.
          CLEAR gs_data_base-subscreen_mass.
          LEAVE TO SCREEN 0.
        ELSE.
          RETURN.
        ENDIF.
      ELSE.
        CLEAR gs_data_base-subscreen_mass.
        LEAVE TO SCREEN 0.

      ENDIF.

    WHEN 'B_MASS_DOWN'.
      PERFORM frm_mass_download USING gs_data_base-zflg_ok_code.
    WHEN 'B_MASS_PASTE'.
      PERFORM frm_mass_paste USING gs_data_base-zflg_ok_code
                                   gt_tc02
                                CHANGING gt_mass.
    WHEN 'B_MASS_SAVE'.
      PERFORM frm_check_data_mass   USING gt_mass
                                          gt_tc02
                                    CHANGING
                                            gv_mtype_mass
                                            gv_msg_mass.
      IF gv_mtype_mass = 'S'.
        PERFORM frm_save_data_mass USING    gs_data_base-zflg_ok_code
                                  CHANGING  gt_mass
                                            gt_tc04_set
                                            gt_t44_all
                                            gt_t11_all
                                            gt_tc02
                                            .
        gv_msg_mass = '保存成功！'.
      ENDIF.
      MESSAGE s888(sabapdocu) WITH gv_msg_mass DISPLAY LIKE gv_mtype_mass.

    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_STYLE_TC_MASS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_style_tc_mass OUTPUT.

  PERFORM frm_set_style_tc_mass .

  PERFORM frm_set_screen_pub_mass.

ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_STYLE_TC_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_style_tc_mass .


  DATA: ls_cols TYPE cx_tableview_column.

  LOOP AT tc_mass-cols INTO ls_cols.
    IF ls_cols-screen-group4 IS NOT INITIAL.
      IF gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZJT'..
        IF ls_cols-screen-group4 EQ '411'.
          ls_cols-invisible = 0.
        ELSE.
          ls_cols-invisible = 1.
        ENDIF.
      ENDIF.

      IF gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZGHF'..
        IF ls_cols-screen-group4 EQ '412'.
          ls_cols-invisible = 0.
        ELSE.
          ls_cols-invisible = 1.
        ENDIF.
      ENDIF.

      IF gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZFLZFF'..
        IF ls_cols-screen-group4 EQ '413'.
          ls_cols-invisible = 0.
        ELSE.
          ls_cols-invisible = 1.
        ENDIF.
      ENDIF.

      MODIFY tc_mass-cols FROM ls_cols.

    ENDIF.

  ENDLOOP.




ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_PUB_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_pub_mass .


  PERFORM frm_set_screen_type USING gs_data_base.

  LOOP AT SCREEN.
    IF screen-group1 = '101'   OR screen-group1 = '102' OR screen-group1 = '103'.

      PERFORM frm_set_screen_attr USING gt_data_scn
                                  CHANGING screen.
      IF sy-subrc = 0.
        LOOP AT tc_item-cols INTO DATA(ls_cols) WHERE screen-name = screen-name .
          ls_cols-invisible = screen-invisible.
          MODIFY tc_item-cols  FROM ls_cols.
        ENDLOOP.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.


  IF gv_flg_rb = '03' OR gv_flg_rb = '04' OR gv_flg_rb = '05'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' OR screen-group1 = '102'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  IF gv_flg_comm = 'SAVE'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> SY_UCOMM
*&      --> GT_TC02
*&      <-- GT_MASS
*&---------------------------------------------------------------------*
*FORM FRM_GET_DATA_MASS  USING    PV_UCOMM TYPE SY-UCOMM
*                                 PT_TC02 TYPE TT_TC02
*                                 PT_T11  TYPE TT_T11
*                                 PT_TC04  TYPE TT_TC04
*                                 PT_T44  TYPE TT_T44
*                        CHANGING PT_MASS  TYPE TT_MASS.
*
*  CLEAR PT_MASS.
*
*
*  CASE PV_UCOMM.
*    WHEN 'TC_ITEM_MASS_ZJT'.  PERFORM FRM_GET_DATA_MASS USING PT_TC02 PT_T11 PT_TC04 PT_T44  CHANGING PT_MASS.
*
*    WHEN 'TC_ITEM_MASS_ZGHF'.  PERFORM FRM_GET_DATA_MASS USING PT_TC02 PT_T11 PT_TC04 PT_T44  CHANGING PT_MASS.
*
*    WHEN 'TC_ITEM_MASS_ZFLZFF'.  PERFORM FRM_GET_DATA_MASS USING PT_TC02 PT_T11 PT_TC04 PT_T44  CHANGING PT_MASS.
*
*    WHEN OTHERS.
*  ENDCASE.
*
*
*ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_MASS_ZJT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T11
*&      <-- PT_MASS
*&---------------------------------------------------------------------*
FORM frm_get_data_mass  USING       pv_code TYPE sy-ucomm
                                    pt_tc02 TYPE tt_tc02
                                    pt_tc04 TYPE tt_tc04
                                    pt_t44 TYPE  tt_t44
                                    pt_t11 TYPE tt_t11
                            CHANGING pt_mass  TYPE tt_mass.

  DATA:
    ls_mass TYPE LINE OF tt_mass,
    ls_tc02 TYPE LINE OF tt_tc02,
    ls_tc04 TYPE LINE OF tt_tc04,
    ls_t44  TYPE LINE OF tt_t44,
    ls_t11  TYPE LINE OF tt_t11.

  CLEAR pt_mass.

  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZJT'.
      LOOP AT pt_t11 INTO ls_t11.

        CLEAR ls_mass.

        PERFORM frm_read_data_tc02 USING ls_t11-zitems_key
                                            pt_tc02
                                   CHANGING ls_tc02.

        PERFORM frm_data_conver_mass USING 'A'
                                     CHANGING
                                              ls_mass
                                              ls_tc02
                                              ls_tc04
                                              ls_t44
                                              ls_t11
                                              .
        APPEND ls_mass TO pt_mass.
      ENDLOOP.

    WHEN 'TC_ITEM_MASS_ZGHF'.

      LOOP AT pt_tc04 INTO ls_tc04 WHERE zghf IS NOT INITIAL .

        CLEAR ls_mass.

        PERFORM frm_read_data_tc02 USING ls_tc04-zitems_key
                                            pt_tc02
                                   CHANGING ls_tc02.

        PERFORM frm_data_conver_mass USING 'A'
                                     CHANGING
                                              ls_mass
                                              ls_tc02
                                              ls_tc04
                                              ls_t44
                                              ls_t11.
        APPEND ls_mass TO pt_mass.
      ENDLOOP.

    WHEN 'TC_ITEM_MASS_ZFLZFF'.

      PERFORM frm_add_zflzff_t44_all USING pt_tc02
                                      CHANGING pt_t44.

      LOOP AT pt_t44 INTO ls_t44 WHERE zflzff IS NOT INITIAL .

        CLEAR ls_mass.

        PERFORM frm_read_data_tc02 USING ls_t44-zitems_key
                                            pt_tc02
                                   CHANGING ls_tc02.


        PERFORM frm_data_conver_mass USING 'A'
                                     CHANGING
                                              ls_mass
                                              ls_tc02
                                              ls_tc04
                                              ls_t44
                                              ls_t11.
        APPEND ls_mass TO pt_mass.
      ENDLOOP.

    WHEN OTHERS.
  ENDCASE.

*  排除掉未勾选的行项目
  LOOP AT pt_mass INTO ls_mass.
    READ TABLE pt_tc02 TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' zitems_key = ls_mass-zitems_key.
    IF sy-subrc NE 0.
      DELETE pt_mass.
      CONTINUE.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_DATA_CONVER_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_TC02_ZITEMS
*&      --> LS_TC02_ZBUKRS
*&      <-- LS_MASS_ZBUKRS
*&      <-- LS_MASS_ZBUKRS
*&---------------------------------------------------------------------*
FORM frm_data_conver_mass  USING    pv_flg TYPE char1     "A 行项目----> MASS  B MASS ----> 行项目
                           CHANGING ps_mass TYPE LINE OF tt_mass
                                    ps_tc02 TYPE LINE OF  tt_tc02
                                    ps_tc04 TYPE LINE OF  tt_tc04
                                    ps_t44 TYPE LINE OF   tt_t44
                                    ps_t11 TYPE LINE OF   tt_t11  .
  FIELD-SYMBOLS:
    <fv_item> TYPE any,
    <fv_mass> TYPE any.


  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE ps_tc02 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZITEMS' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZITEMS' OF STRUCTURE ps_tc02 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZBUKRS' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZBUKRS' OF STRUCTURE ps_tc02 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE ps_tc02 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZFLG_1ST' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZFLG_1ST' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZJT_ID' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZJT_ID' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZJT_FROM' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZJT_FROM' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.


  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZJT_BY' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZJT_BY' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZJT_FZ' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZJT_FZ' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZJT_FM' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZJT_FM' OF STRUCTURE ps_t11 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.


  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZGHF' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZGHF' OF STRUCTURE ps_tc04 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.


  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZZZPC' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZZZPC' OF STRUCTURE ps_tc04 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

  UNASSIGN <fv_mass>.
  ASSIGN COMPONENT 'ZFLZFF' OF STRUCTURE ps_mass TO <fv_mass>.
  UNASSIGN <fv_item>.
  ASSIGN COMPONENT 'ZFLZFF' OF STRUCTURE ps_t44 TO <fv_item>.
  IF <fv_mass> IS ASSIGNED AND <fv_item> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fv_mass> = <fv_item>.
    ELSE.
      <fv_item> = <fv_mass>.
    ENDIF.
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_DATA_TC_MASS_PAI  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_data_tc_mass_pai INPUT.

  PERFORM frm_set_data_tc_mass USING gt_tc02
                                   CHANGING gt_mass.
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_MASS_SET_ZBUKRS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_MASS_ZITEMS
*&      --> GT_TC02
*&      <-- GS_MASS_ZBUKRS
*&---------------------------------------------------------------------*
FORM frm_set_data_tc_mass  USING    pt_tc02    TYPE tt_tc02
                                CHANGING pt_mass  TYPE  tt_mass.

  LOOP AT pt_mass INTO DATA(ls_mass) WHERE zitems IS INITIAL.
    DATA(lt_tc02_tmp) = pt_tc02.
    DELETE lt_tc02_tmp WHERE zbukrs NE ls_mass-zbukrs.
    DELETE lt_tc02_tmp WHERE sel_man NE 'X'.

    IF lines( lt_tc02_tmp ) = 1.
      READ TABLE lt_tc02_tmp INTO DATA(ls_tc02) INDEX 1.
      IF sy-subrc EQ 0.
        ls_mass-zitems = ls_tc02-zitems.
        MODIFY pt_mass FROM ls_mass.
      ENDIF.

    ELSEIF lines( lt_tc02_tmp ) > 1..
      MESSAGE s888(sabapdocu) WITH '该协议主体存在多个行项目，请手工指定行项目号！' DISPLAY LIKE 'E'.
    ENDIF.


  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_ZBUKRS_MASS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_zbukrs_mass INPUT.

  PERFORM frm_set_zbukrs_mass USING gt_tc02
                                    gs_mass-zitems
                                   CHANGING gs_mass-zbukrs.
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZBUKRS_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_TC02
*&      --> GS_MASS_ZITEMS
*&      根据行项目ID获取协议主体
*&---------------------------------------------------------------------*
FORM frm_set_zbukrs_mass  USING    pt_tc02  TYPE tt_tc02
                                   pv_zitems  TYPE zretc002-zitems
                          CHANGING pv_zbukrs  TYPE zreta001-zbukrs.
  CLEAR pv_zbukrs.
  READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems = pv_zitems.
  IF sy-subrc EQ 0.
    pv_zbukrs = ls_tc02-zbukrs.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_DATA_TC_MASS_PAI_ZJT  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_data_tc_mass_pai_zjt INPUT.
  IF  gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZJT'.

    PERFORM frm_pro_mass_data_pai_zjt USING gs_tc05
                                  CHANGING gt_mass.

  ENDIF.
ENDMODULE.



FORM frm_check_zjt_from_new  USING
                                      ps_tc05  TYPE ty_tc05
                                      pt_t11 TYPE tt_t11.




  DATA(lt_t11) = pt_t11[].

  DELETE lt_t11 WHERE zjt_from IS INITIAL.


  DATA(lv_lines1) = lines( lt_t11 ).
  SORT lt_t11 BY zjt_from .
  DELETE ADJACENT DUPLICATES FROM lt_t11 COMPARING zjt_from.
  DATA(lv_lines2) = lines( lt_t11 ).
  IF lv_lines1 <> lv_lines2.
    MESSAGE e888(sabapdocu) WITH '起始值存在相同的记录！'.
  ENDIF.

ENDFORM.

FORM frm_check_zjt_from_mass  USING
                                      ps_tc05  TYPE ty_tc05
                                      pt_mass TYPE tt_mass.



  DATA(lt_t11) = pt_mass[].

  DELETE lt_t11 WHERE zjt_from IS INITIAL.


  DATA(lv_lines1) = lines( lt_t11 ).
  SORT lt_t11 BY zjt_from .
  DELETE ADJACENT DUPLICATES FROM lt_t11 COMPARING zjt_from.
  DATA(lv_lines2) = lines( lt_t11 ).
  IF lv_lines1 <> lv_lines2.
    MESSAGE e888(sabapdocu) WITH '起始值存在相同的记录！'.
  ENDIF.

ENDFORM.
FORM frm_set_style_zjt  USING    ps_tc05       TYPE ty_tc05
                                 pv_fname     TYPE char20
                                 pv_tc        TYPE dynfnam
                        CHANGING
*                                 PT_T11       TYPE TT_T11
                                 pv_zjt_fz_t  TYPE char10
                                 pv_zjt_fm_t  TYPE char10.

  DATA: lth_cols TYPE cx_tableview_column.
  DATA:
        ls_t11 TYPE LINE OF tt_t11.
  DATA:
    lv_fname  TYPE char50,
    lv_fname2 TYPE char50,
    lv_fname3 TYPE char50.

  DATA:lv_table  LIKE feld-name,
       lv_fname1 TYPE char20.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <wa_value>.


  lv_fname = pv_fname && '-ZJT_FM'.
  CONDENSE lv_fname.
  lv_fname1 = 'INVISIBLE'.

  lv_table = pv_tc && '-COLS'.
  ASSIGN (lv_table) TO <lt_table>.

  CLEAR:
        pv_zjt_fz_t,
        pv_zjt_fm_t.

*  先统一全部展示分子分母
***  LOOP AT TC_ZJT-COLS INTO LTH_COLS.
****    IF LTH_COLS-SCREEN-NAME = 'GS_T11-ZJT_FM'.
***    IF LTH_COLS-SCREEN-NAME = LV_FNAME.
***      LTH_COLS-INVISIBLE = 0.
***      MODIFY TC_ZJT-COLS FROM LTH_COLS.
***    ENDIF.
***  ENDLOOP.

  LOOP AT <lt_table> ASSIGNING <wa>.
    CLEAR:
          lth_cols.
    lth_cols = <wa>.

    IF lth_cols-screen-name = lv_fname.
      UNASSIGN <wa_value>.
      ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
      <wa_value> = 0.
    ENDIF.

  ENDLOOP.

  IF ps_tc05-zjsff NE 'T'.


    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    IF pv_fname = 'GS_T11' .
      lv_fname3 = 'BT_DTL_JS'.
    ELSEIF pv_fname = 'GS_T11_HS_SUB'.
      lv_fname3 = 'BT_DTL_JSH'.
    ENDIF.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = lv_fname3.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

      IF lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 0.
      ENDIF.

    ENDLOOP.
  ENDIF.




  IF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'F'.
***    LOOP AT TC_ZJT-COLS INTO LTH_COLS.
****      IF LTH_COLS-SCREEN-NAME = 'GS_T11-ZJT_FM'.
***      IF LTH_COLS-SCREEN-NAME = LV_FNAME.
***        LTH_COLS-INVISIBLE = 1.
***        MODIFY TC_ZJT-COLS FROM LTH_COLS.
***      ENDIF.
***    ENDLOOP.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.

      IF lth_cols-screen-name = lv_fname.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.
    ENDLOOP.

    gv_zjt_fz_t = '固定金额'.

*    LOOP AT PT_T11 INTO LS_T11.
*      CLEAR LS_T11-ZJT_FM.
*      MODIFY PT_T11 FROM LS_T11.
*    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'S'.
    pv_zjt_fz_t = '单价'.
    pv_zjt_fm_t = '价格数量'.
*    LOOP AT PT_T11 INTO LS_T11.
*      LS_T11-ZJT_FM = 1.
*      MODIFY PT_T11 FROM LS_T11.
*    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'P'.
    pv_zjt_fz_t = '比例'.
    pv_zjt_fm_t = '%'.
*    LOOP AT PT_T11 INTO LS_T11.
*      LS_T11-ZJT_FM = 100.
*      MODIFY PT_T11 FROM LS_T11.
*    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'Q' AND ps_tc05-zjsff = 'F'.
    pv_zjt_fz_t = '固定数量'.

**    LOOP AT TC_ZJT-COLS INTO LTH_COLS.
***      IF LTH_COLS-SCREEN-NAME = 'GS_T11-ZJT_FM'.
**      IF LTH_COLS-SCREEN-NAME = LV_FNAME.
**        LTH_COLS-INVISIBLE = 1.
**        MODIFY TC_ZJT-COLS FROM LTH_COLS.
**      ENDIF.
**    ENDLOOP.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.

      IF lth_cols-screen-name = lv_fname.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

    ENDLOOP.
*    LOOP AT PT_T11 INTO LS_T11.
*      CLEAR LS_T11-ZJT_FM.
*      MODIFY PT_T11 FROM LS_T11.
*    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'Q' AND ps_tc05-zjsff = 'P'.
    pv_zjt_fz_t = '返量'.
    pv_zjt_fm_t = '每'.
  ENDIF.

*  阶梯含税变灰
*  IF PS_TC05-ZJGWD = 'A'.
*    PS_TC05-ZJTHS = 'X'.
*    LOOP AT SCREEN.
*      IF SCREEN-NAME = 'GS_T10-ZJTHS'.
*        SCREEN-INPUT = '0'.
*        MODIFY SCREEN.
*      ENDIF.
*    ENDLOOP.
*  ENDIF.
*
*  IF PS_TC05-ZFLJGWD = 'A'.
*    PS_TC05-ZFLHS = 'X'.
*    LOOP AT SCREEN.
*      IF SCREEN-NAME = 'GS_T10-ZFLHS'.
*        SCREEN-INPUT = '0'.
*        MODIFY SCREEN.
*      ENDIF.
*    ENDLOOP.
*  ENDIF.

*  IF PV_FNAME = 'GS_T11' AND CB_SPLIT = '' .
*    LOOP AT TC_ZJT-COLS INTO LTH_COLS.
**      IF LTH_COLS-SCREEN-NAME = 'GS_T11-ZJT_FM'.
*      IF LTH_COLS-SCREEN-NAME = 'GV_ICON' OR LTH_COLS-SCREEN-NAME = 'BT_DTL_JS'.
**        OR LTH_COLS-SCREEN-NAME =  'GS_T11-ZJT_FM'.
*        LTH_COLS-INVISIBLE = 1.
*        MODIFY TC_ZJT-COLS FROM LTH_COLS.
*      ENDIF.
*    ENDLOOP.
*  ENDIF.
*
*  IF PV_FNAME = 'GS_T11_HS_SUB' AND CB_SPLIT = ''.
*    LOOP AT TC_ZJT-COLS INTO LTH_COLS.
**      IF LTH_COLS-SCREEN-NAME = 'GS_T11-ZJT_FM'.
*      IF LTH_COLS-SCREEN-NAME = 'GV_ICON' OR LTH_COLS-SCREEN-NAME = 'BT_DTL_JSH'.
**        OR LTH_COLS-SCREEN-NAME =  'GS_T11-ZJT_FM'.
*        LTH_COLS-INVISIBLE = 1.
*        MODIFY TC_ZJT-COLS FROM LTH_COLS.
*      ENDIF.
*    ENDLOOP.
*  ENDIF.

  IF ps_tc05-zjsff = 'T'.

    lv_fname = pv_fname && '-ZJT_FM'.
    CONDENSE lv_fname.

    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    IF pv_fname = 'GS_T11' .
      lv_fname3 = 'BT_DTL_JS'.
    ELSEIF pv_fname = 'GS_T11_HS_SUB'.
      lv_fname3 = 'BT_DTL_JSH'.
    ENDIF.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = lv_fname OR lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

      IF lth_cols-screen-name = 'GV_ICON' OR lth_cols-screen-name = lv_fname3.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 0.
      ENDIF.


    ENDLOOP.

  ENDIF.

  IF ps_tc05-zjsff = 'R'.

    lv_fname = pv_fname && '-ZJT_FM'.
    CONDENSE lv_fname.

    lv_fname2 = pv_fname && '-ZJT_FZ'.
    CONDENSE lv_fname2.

    LOOP AT <lt_table> ASSIGNING <wa>.
      CLEAR:
            lth_cols.
      lth_cols = <wa>.
      IF lth_cols-screen-name = lv_fname OR lth_cols-screen-name = lv_fname2.
        UNASSIGN <wa_value>.
        ASSIGN COMPONENT lv_fname1 OF STRUCTURE <wa> TO <wa_value>.
        <wa_value> = 1.
      ENDIF.

    ENDLOOP.

  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_ZJT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_T11
*&---------------------------------------------------------------------*
FORM frm_set_data_zjt     USING   ps_tc05  TYPE ty_tc05
                          CHANGING pt_t11 TYPE tt_t11.
  DATA:
        lv_zjt_by TYPE zret0011-zjt_by.
  CONSTANTS:
  lc_max_dec TYPE  zret0011-zjt_by VALUE '999999999999999'.

  IF pt_t11[] IS INITIAL.
    RETURN.
  ENDIF.

*  倒序排列
  SORT pt_t11 BY zjt_from DESCENDING.

*    首次时设置为最大值
  lv_zjt_by = lc_max_dec.
  LOOP AT pt_t11 INTO DATA(ls_t11).
*    每行记录的【至】 = 上次行的【从】 - 1.
    ls_t11-zjt_by = lv_zjt_by.
*    记录【从】 - 1 的值
    lv_zjt_by = ls_t11-zjt_from - 1.
    IF lv_zjt_by < 0.
      lv_zjt_by = 0.
    ENDIF.
    ls_t11-zflg_1st = ''.
*    IF LS_T11-ZJT_FROM IS INITIAL AND LS_T11-ZJT_BY IS NOT INITIAL.
*      LS_T11-ZFLG_DEL = 'X'.
*    ENDIF.
    MODIFY pt_t11 FROM ls_t11.
  ENDLOOP.
  SORT pt_t11 BY zjt_from .

*  更新首行标志
  IF pt_t11[] IS NOT INITIAL.
    ls_t11-zflg_1st = 'X'.
    ls_t11-zjt_from = 0.
    MODIFY pt_t11 FROM ls_t11 INDEX 1 TRANSPORTING zflg_1st zjt_from.
  ENDIF.

*  按照类型更新分子和分母
  IF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'F'.
    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'S'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 1.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'P'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 100.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'Q' AND ps_tc05-zjsff = 'F'.

    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ENDIF.

*  DELETE PT_T11 WHERE ZFLG_DEL = 'X' AND ZFLG_1ST = ''.
ENDFORM.

FORM frm_set_data_zjt_mass     USING   ps_tc05  TYPE ty_tc05
                          CHANGING pt_t11 TYPE tt_mass.
  DATA:
        lv_zjt_by TYPE zret0011-zjt_by.
  CONSTANTS:
  lc_max_dec TYPE  zret0011-zjt_by VALUE '999999999999999'.

  IF pt_t11[] IS INITIAL.
    RETURN.
  ENDIF.

*  倒序排列
  SORT pt_t11 BY zjt_from DESCENDING.

*    首次时设置为最大值
  lv_zjt_by = lc_max_dec.
  LOOP AT pt_t11 INTO DATA(ls_t11).
*    每行记录的【至】 = 上次行的【从】 - 1.
    ls_t11-zjt_by = lv_zjt_by.
*    记录【从】 - 1 的值
    lv_zjt_by = ls_t11-zjt_from - 1.
    IF lv_zjt_by < 0.
      lv_zjt_by = 0.
    ENDIF.
    ls_t11-zflg_1st = ''.
*    IF LS_T11-ZJT_FROM IS INITIAL AND LS_T11-ZJT_BY IS NOT INITIAL.
*      LS_T11-ZFLG_DEL = 'X'.
*    ENDIF.
    MODIFY pt_t11 FROM ls_t11.
  ENDLOOP.
  SORT pt_t11 BY zjt_from .

*  更新首行标志
  IF pt_t11[] IS NOT INITIAL.
    ls_t11-zflg_1st = 'X'.
    ls_t11-zjt_from = 0.
    MODIFY pt_t11 FROM ls_t11 INDEX 1 TRANSPORTING zflg_1st zjt_from.
  ENDIF.

*  按照类型更新分子和分母
  IF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'F'.
    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'S'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 1.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'M' AND ps_tc05-zjsff = 'P'.

    LOOP AT pt_t11 INTO ls_t11.
      ls_t11-zjt_fm = 100.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ELSEIF ps_tc05-zflxs = 'Q' AND ps_tc05-zjsff = 'F'.

    LOOP AT pt_t11 INTO ls_t11.
      CLEAR ls_t11-zjt_fm.
      MODIFY pt_t11 FROM ls_t11.
    ENDLOOP.
  ENDIF.

*  DELETE PT_T11 WHERE ZFLG_DEL = 'X' AND ZFLG_1ST = ''.
ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_PRO_MASS_DATA_ZJT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TC05
*&      <-- GT_MASS
*&---------------------------------------------------------------------*
FORM frm_pro_mass_data_pai_zjt  USING    ps_tc05  TYPE LINE OF tt_tc05
                                CHANGING pt_mass TYPE tt_mass.
  SELECT
    DISTINCT
    i~zitems
    FROM @pt_mass AS i
    INTO TABLE @DATA(lt_zitems).
  DELETE lt_zitems WHERE zitems IS INITIAL.

  LOOP AT lt_zitems INTO DATA(ls_zitems).
    DATA(lt_mass_tmp) = pt_mass[].
    DELETE lt_mass_tmp WHERE zitems NE ls_zitems-zitems.

    PERFORM frm_check_zjt_from_mass USING
                                         ps_tc05
                                         lt_mass_tmp.

  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& MODULE MDL_PBO_TC_MASS_ZJT OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_pbo_tc_mass_zjt OUTPUT.
  IF  gs_data_base-zflg_ok_code = 'TC_ITEM_MASS_ZJT'.

    PERFORM frm_set_style_zjt USING gs_tc05
                                    'GS_MASS'
                                    'TC_MASS'
                              CHANGING
                                       gv_zjt_fz_t
                                       gv_zjt_fm_t.


    PERFORM frm_pro_mass_data_pbo_zjt USING gs_tc05
                                  CHANGING gt_mass.

    PERFORM frm_sort_data USING gs_data_base-zflg_ok_code
                          CHANGING gt_mass.

  ENDIF.
ENDMODULE.

FORM frm_pro_mass_data_pbo_zjt  USING    ps_tc05  TYPE LINE OF tt_tc05
                                CHANGING pt_mass TYPE tt_mass.

  DELETE pt_mass WHERE zjt_from IS INITIAL AND zjt_by IS NOT INITIAL AND zflg_1st NE 'X'.

  SELECT
    DISTINCT
    i~zitems
    FROM @pt_mass AS i
    INTO TABLE @DATA(lt_zitems).
  DELETE lt_zitems WHERE zitems IS INITIAL.

*  逐行处理每个行项目中的阶梯数据
  LOOP AT lt_zitems INTO DATA(ls_zitems).
    DATA(lt_mass_tmp) = pt_mass[].
    DELETE lt_mass_tmp WHERE zitems NE ls_zitems-zitems.
    DELETE pt_mass WHERE zitems EQ ls_zitems-zitems.

    PERFORM frm_set_data_zjt_mass USING
                                         ps_tc05
                                         lt_mass_tmp.
    APPEND LINES OF lt_mass_tmp TO pt_mass.

  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SORT_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZFLG_OK_CODE
*&      <-- GT_MASS
*&---------------------------------------------------------------------*
FORM frm_sort_data  USING    pv_code TYPE sy-ucomm
                    CHANGING pt_mass  TYPE tt_mass.
  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZJT'.    SORT pt_mass BY zitems ASCENDING zbukrs ASCENDING zjt_from ASCENDING.
    WHEN 'TC_ITEM_MASS_ZGHF'.    SORT pt_mass BY zitems ASCENDING zbukrs ASCENDING zghf ASCENDING.
    WHEN 'TC_ITEM_MASS_ZFLZFF'.    SORT pt_mass BY zitems ASCENDING zbukrs ASCENDING zflzff ASCENDING.
    WHEN OTHERS.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_MASS_DOWNLOAD
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZFLG_OK_CODE
*&---------------------------------------------------------------------*
FORM frm_mass_download  USING    pv_code  TYPE sy-ucomm.
  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZJT'. PERFORM frm_download_template(zbcs0001) USING 'ZRED0001' '阶梯模板'.
    WHEN 'TC_ITEM_MASS_ZGHF'. PERFORM frm_download_template(zbcs0001) USING 'ZRED0002' '供货方模板'.
    WHEN 'TC_ITEM_MASS_ZFLZFF'. PERFORM frm_download_template(zbcs0001) USING 'ZRED0003' '支付方模板'.
    WHEN OTHERS.
  ENDCASE.
ENDFORM.

FORM frm_mass_paste  USING    pv_code  TYPE sy-ucomm
                              pt_tc02 TYPE tt_tc02
                     CHANGING pt_mass TYPE tt_mass.

  DATA:

    lt_mass       TYPE  tt_mass.

  PERFORM frm_get_paste_data USING pv_code CHANGING lt_mass.

  CHECK lt_mass[] IS NOT INITIAL.


  PERFORM frm_pro_paste_data_mass USING pv_code pt_tc02 CHANGING lt_mass pt_mass.

  APPEND LINES OF lt_mass TO pt_mass.

  PERFORM frm_sort_data USING pv_code
                        CHANGING pt_mass.


ENDFORM.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_ZGHF_MASS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_zghf_mass INPUT.

  PERFORM frm_set_screen_zghf CHANGING gs_mass-zghf gs_mass-name1_zghf.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_ZFLZFF_MASS  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_set_zflzff_mass INPUT.


  PERFORM frm_set_screen_zflzff_sub CHANGING gs_mass-zflzff gs_mass-name1_zflzff.

ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLZFF_SUB
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_set_screen_zflzff_sub CHANGING pv_zflzff TYPE lfa1-lifnr
                                        pv_name TYPE lfa1-name1.

  PERFORM frm_alpha_in_lifnr CHANGING pv_zflzff.

  PERFORM frm_get_lifnr_name1(zbcs0001) USING pv_zflzff CHANGING pv_name.
  IF sy-subrc NE 0.
    CHECK pv_zflzff IS NOT INITIAL.
    MESSAGE e888(sabapdocu) WITH '支付方不存在！'.
  ENDIF.

  PERFORM frm_check_status_zflzff USING pv_zflzff.
  IF sy-subrc NE 0.
    CHECK pv_zflzff IS NOT INITIAL.
    MESSAGE e888(sabapdocu) WITH '该支付方' && pv_zflzff && '已被冻结或删除，请检查主数据!'.
  ENDIF.

  PERFORM frm_check_zflzff_bukrs USING pv_zflzff.
  IF sy-subrc NE 0.
    CHECK pv_zflzff IS NOT INITIAL.
    MESSAGE e888(sabapdocu) WITH '内部支付方' && pv_zflzff && '必须是公司代码!'.
  ENDIF.

ENDFORM.

*FORM FRM_SET_SCREEN_ZGHF .
*
*  PERFORM FRM_ALPHA_IN_LIFNR CHANGING GS_TC04-ZGHF.
*
*
*  PERFORM FRM_GET_LIFNR_NAME1(ZBCS0001) USING GS_TC04-ZGHF CHANGING GS_TC04-NAME1_ZGHF.
*  IF SY-SUBRC NE 0.
*    CHECK GS_TC04-ZGHF IS NOT INITIAL.
*    MESSAGE E888(SABAPDOCU) WITH '供货方不存在！'.
*  ENDIF.
*
*
*ENDFORM.


FORM frm_set_screen_zghf  CHANGING pv_zghf TYPE lfa1-lifnr
                                        pv_name1 TYPE lfa1-name1.

  PERFORM frm_alpha_in_lifnr CHANGING pv_zghf.

  PERFORM frm_get_lifnr_name1(zbcs0001) USING pv_zghf CHANGING pv_name1.
  IF sy-subrc NE 0.
    CHECK pv_zghf IS NOT INITIAL.
    MESSAGE e888(sabapdocu) WITH '供货方不存在！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& MODULE MDL_PBO_TC_MASS_PUB OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_pbo_tc_mass_pub OUTPUT.
  PERFORM frm_pbo_tc_mass_pub USING gs_data_base-zflg_ok_code
                                    gt_tc02
                              CHANGING gt_mass.
ENDMODULE.
*&---------------------------------------------------------------------*
*& FORM FRM_PBO_TC_MASS_PUB
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZFLG_OK_CODE
*&      <-- GT_MASS
*&---------------------------------------------------------------------*
FORM frm_pbo_tc_mass_pub  USING    pv_code TYPE sy-ucomm
                                   pt_tc02 TYPE tt_tc02
                          CHANGING pt_mass TYPE tt_mass.
  LOOP AT pt_mass INTO DATA(ls_mass).

    PERFORM frm_alpha_in_lifnr CHANGING ls_mass-zflzff.
    PERFORM frm_alpha_in_lifnr CHANGING ls_mass-zghf.

    PERFORM frm_get_lifnr_name1(zbcs0001) USING ls_mass-zflzff CHANGING ls_mass-name1_zflzff.

    PERFORM frm_get_lifnr_name1(zbcs0001) USING ls_mass-zghf CHANGING ls_mass-name1_zghf.

    IF ls_mass-zitems IS NOT INITIAL.

      PERFORM frm_set_zbukrs_mass USING pt_tc02
                                        ls_mass-zitems
                                       CHANGING ls_mass-zbukrs.
    ENDIF.

    MODIFY pt_mass FROM ls_mass.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SPLIT_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      --> LS_DATA
*&      <-- LV_MTYPE
*&      <-- LV_ERROR_TEXT
*&      <-- LS_MASS
*&---------------------------------------------------------------------*
FORM frm_split_data  USING    pv_code TYPE sy-ucomm
                              ps_data TYPE tdline
                     CHANGING pv_mtype  TYPE bapi_mtype
                              pv_error_text TYPE string
                              ps_mass TYPE LINE OF tt_mass.

  DATA:
    lv_str1 TYPE string,
    lv_str2 TYPE string,
    lv_str3 TYPE string,
    lv_str4 TYPE string,
    lv_str5 TYPE string,
    lv_str6 TYPE string,
    lv_str7 TYPE string.


  CLEAR: ps_mass,pv_mtype,pv_error_text.

  SPLIT ps_data AT cl_abap_char_utilities=>horizontal_tab INTO lv_str1 lv_str2 lv_str3 lv_str4 lv_str5 lv_str6 lv_str7 .

  TRY .
      MOVE:lv_str1 TO ps_mass-zitems.
    CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
*        PV_ERROR_TEXT = LV_ERROR->GET_TEXT( ).
      pv_error_text       = |条款行项目必须为数字|  .
      pv_mtype = 'E'.
      EXIT.
    CLEANUP.
  ENDTRY.

  TRY .
      MOVE:lv_str2 TO ps_mass-zbukrs.
    CATCH cx_sy_conversion_no_number INTO lv_error.
      pv_error_text = lv_error->get_text( ).
      pv_mtype = 'E'.
      EXIT.
    CLEANUP.
  ENDTRY.

  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZJT'.
      TRY .
          MOVE:lv_str3 TO ps_mass-zjt_from.
        CATCH cx_sy_conversion_no_number INTO lv_error.
*        PV_ERROR_TEXT = LV_ERROR->GET_TEXT( ).
          pv_error_text       = |从必须为数字|  .
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

      TRY .
          MOVE:lv_str4 TO ps_mass-zjt_fz.
        CATCH cx_sy_conversion_no_number INTO lv_error.
*        PV_ERROR_TEXT = LV_ERROR->GET_TEXT( ).
          pv_error_text       = |分子必须为数字|  .
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

      TRY .
          MOVE:lv_str5 TO ps_mass-zjt_fm.
        CATCH cx_sy_conversion_no_number INTO lv_error.
*        PV_ERROR_TEXT = LV_ERROR->GET_TEXT( ).
          pv_error_text       = |分母必须为数字|  .
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

    WHEN 'TC_ITEM_MASS_ZGHF'.

      TRY .
          MOVE:lv_str3 TO ps_mass-zghf.
        CATCH cx_sy_conversion_no_number INTO lv_error.
          pv_error_text = lv_error->get_text( ).
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

      TRY .
          MOVE:lv_str4 TO ps_mass-zzzpc.
        CATCH cx_sy_conversion_no_number INTO lv_error.
          pv_error_text = lv_error->get_text( ).
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

    WHEN 'TC_ITEM_MASS_ZFLZFF'.

      TRY .
          MOVE:lv_str3 TO ps_mass-zflzff.
        CATCH cx_sy_conversion_no_number INTO lv_error.
          pv_error_text = lv_error->get_text( ).
          pv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.
    WHEN OTHERS.
  ENDCASE.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZFLG_OK_CODE
*&      <-- GT_MASS
*&      <-- GT_TC04_SET
*&      <-- GT_T44_ALL
*&      <-- GT_T11_ALL
*&---------------------------------------------------------------------*
FORM frm_save_data_mass  USING    pv_code  TYPE sy-ucomm
                          CHANGING pt_mass TYPE tt_mass
                                    pt_tc04 TYPE tt_tc04
                                    pt_t44  TYPE tt_t44
                                    pt_t11  TYPE tt_t11
                                    pt_tc02  TYPE tt_tc02.

  DATA:
    lt_t11_tmp  TYPE  tt_t11,
    lt_t44_tmp  TYPE  tt_t44,
    lt_tc04_tmp TYPE  tt_tc04.

  PERFORM frm_save_data_mass_pre USING pv_code
                                          pt_tc02
                                 CHANGING pt_mass.


  PERFORM frm_save_data_mass_conver USING    pv_code
                                          pt_tc02
                                CHANGING  pt_mass
                                      lt_tc04_tmp
                                      lt_t44_tmp
                                      lt_t11_tmp
                                      .

  PERFORM frm_save_data_mass_exe USING    pv_code
                                CHANGING
                                      pt_tc04
                                      pt_t44
                                      pt_t11
                                      lt_tc04_tmp
                                      lt_t44_tmp
                                      lt_t11_tmp
                                      pt_tc02
                                       .

ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_MASS_PRE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      <-- PT_MASS
*&      <-- LT_T11_TMP
*&      <-- LT_T44_TMP
*&      <-- LT_TC04_TMP
*&---------------------------------------------------------------------*
FORM frm_save_data_mass_conver  USING    pv_code  TYPE sy-ucomm
                                      pt_tc02 TYPE tt_tc02
                          CHANGING pt_mass TYPE tt_mass
                                    pt_tc04_tmp TYPE tt_tc04
                                    pt_t44_tmp TYPE tt_t44
                                    pt_t11_tmp TYPE tt_t11.
  DATA:
    ls_tc02 TYPE LINE OF tt_tc02,
    ls_t11  TYPE LINE OF tt_t11,
    ls_t44  TYPE LINE OF tt_t44,
    ls_tc04 TYPE LINE OF tt_tc04.

  CLEAR:
  pt_tc04_tmp  ,
  pt_t44_tmp   ,
  pt_t11_tmp   .

  LOOP AT pt_mass INTO DATA(ls_mass).
    CLEAR:      ls_t11 ,      ls_t44 ,      ls_tc04.

*    READ TABLE PT_TC02 INTO LS_TC02 WITH KEY ZITEMS = LS_MASS-ZITEMS.
*    IF SY-SUBRC EQ 0.
*      LS_T11-ZITEMS_KEY   = LS_TC02-ZITEMS_KEY.
*      LS_T44-ZITEMS_KEY   = LS_TC02-ZITEMS_KEY.
*      LS_TC04-ZITEMS_KEY  = LS_TC02-ZITEMS_KEY.
*    ENDIF.

    ls_t11-zitems_key   = ls_mass-zitems_key.
    ls_t44-zitems_key   = ls_mass-zitems_key.
    ls_tc04-zitems_key  = ls_mass-zitems_key.

    CLEAR ls_tc02.
    PERFORM frm_data_conver_mass USING 'B'
                                 CHANGING
                                          ls_mass
                                          ls_tc02
                                          ls_tc04
                                          ls_t44
                                          ls_t11.

    APPEND ls_t11 TO  pt_t11_tmp.
    APPEND ls_t44 TO  pt_t44_tmp.
    APPEND ls_tc04 TO pt_tc04_tmp.


  ENDLOOP.

ENDFORM.

FORM frm_save_data_mass_exe  USING    pv_code  TYPE sy-ucomm
                          CHANGING
                                    pt_tc04 TYPE tt_tc04
                                    pt_t44  TYPE tt_t44
                                    pt_t11  TYPE tt_t11
                                    pt_tc04_tmp TYPE tt_tc04
                                    pt_t44_tmp TYPE tt_t44
                                    pt_t11_tmp TYPE tt_t11
                                    pt_tc02  TYPE tt_tc02.


  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZGHF'.
      CLEAR pt_tc04.
*      LOOP AT PT_TC04_TMP INTO DATA(LS_TC04_TMP).
*        DELETE PT_TC04 WHERE ZITEMS_KEY = LS_TC04_TMP-ZITEMS_KEY.
*      ENDLOOP.
      APPEND LINES OF pt_tc04_tmp TO pt_tc04.

    WHEN 'TC_ITEM_MASS_ZFLZFF'.

      CLEAR pt_t44.
*      LOOP AT PT_T44_TMP INTO DATA(LS_T44_TMP).
*        DELETE PT_T44 WHERE ZITEMS_KEY = LS_T44_TMP-ZITEMS_KEY.
*      ENDLOOP.
      APPEND LINES OF pt_t44_tmp TO pt_t44.

*        自动将返利支付方列表中第一行添加到行项目返利支付方
      PERFORM frm_pro_zflzff_pai USING pt_t44
                                 CHANGING pt_tc02.

    WHEN 'TC_ITEM_MASS_ZJT'.

      CLEAR pt_t11.
*      LOOP AT PT_T11_TMP INTO DATA(LS_T11_TMP).
*        DELETE PT_T11 WHERE ZITEMS_KEY = LS_T11_TMP-ZITEMS_KEY.
*      ENDLOOP.
      APPEND LINES OF pt_t11_tmp TO pt_t11.
*      更新行项目阶梯ID到行项目表中
      PERFORM frm_update_zjt_id_into_tc02 USING pt_t11_tmp
                                          CHANGING pt_tc02.
    WHEN OTHERS.
  ENDCASE.




ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_DATA_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GV_MTYPE_MASS
*&      <-- GV_MSG_MASS
*&---------------------------------------------------------------------*
FORM frm_check_data_mass  USING pt_mass TYPE tt_mass
                                pt_tc02 TYPE tt_tc02
                          CHANGING pv_mtype_mass
                                   pv_msg_mass.

  DATA:
    lt_msglist    TYPE scp1_general_errors,
    lt_msglist_02 TYPE scp1_general_errors,
    ls_msglist    TYPE scp1_general_error.

  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.

  LOOP AT pt_mass INTO DATA(ls_mass).
    PERFORM frm_check_inital USING ls_mass-zitems    '条款行项目' CHANGING lt_msglist .
    PERFORM frm_check_inital USING ls_mass-zbukrs    '协议主体' CHANGING lt_msglist .

    READ TABLE pt_tc02 TRANSPORTING NO FIELDS WITH KEY zitems = ls_mass-zitems zbukrs = ls_mass-zbukrs.
    IF sy-subrc NE 0.
      CLEAR lv_msgv1. lv_msgv1 = '条款行项目' && ls_mass-zitems && '与协议主体' && ls_mass-zbukrs && '不一致' .
      PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.

    ENDIF.

    READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY sel_man = '' zitems = ls_mass-zitems.
    IF sy-subrc EQ 0.
      CLEAR lv_msgv1. lv_msgv1 = '行项目' && ls_tc02-zitems && '未生效，请确认是否维护？'.
      PERFORM frm_add_msg_w USING lv_msgv1  CHANGING lt_msglist_02.

    ENDIF.

  ENDLOOP.


  IF lt_msglist[] IS NOT INITIAL.

    SORT lt_msglist.
    DELETE ADJACENT DUPLICATES FROM lt_msglist.

    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].

    pv_mtype_mass = 'E'.
    pv_msg_mass = '数据检查未通过，操作已终止!'.
  ELSE.
    pv_mtype_mass = 'S'.

  ENDIF.

  IF pv_mtype_mass = 'S'.


    IF lt_msglist_02[] IS NOT INITIAL.

      SORT lt_msglist_02.
      DELETE ADJACENT DUPLICATES FROM lt_msglist_02.

      CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
        EXPORTING
          title_text    = '消息提示'
          sort_by_level = ' '
          show_ids      = ''
          message_list  = lt_msglist_02[].


      IF sy-ucomm = 'RW' .
        pv_mtype_mass = 'E'.
        pv_msg_mass = '操作已终止!'.
      ENDIF.

    ENDIF.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_ZFLZFF_PAI
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T44
*&      <-- PT_TC02
*&---------------------------------------------------------------------*
FORM frm_pro_zflzff_pai  USING    pt_t44  TYPE tt_t44
                         CHANGING pt_tc02 TYPE tt_tc02.

  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    READ TABLE pt_t44 INTO DATA(ls_t44) WITH KEY zitems_key = ls_tc02-zitems_key.
    IF sy-subrc EQ 0.
      ls_tc02-zflzff = ls_t44-zflzff.
      MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING zflzff .
    ELSE.
      ls_tc02-zflzff = ''.
      MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING zflzff .
    ENDIF.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_READ_DATA_TC02
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_T11_ZITEMS_KEY
*&      --> PT_TC02
*&      <-- LS_TC02
*&---------------------------------------------------------------------*
FORM frm_read_data_tc02  USING    pv_zitems_key TYPE zretc002-zitems
                                  pt_tc02 TYPE tt_tc02
                         CHANGING ps_tc02 TYPE LINE OF tt_tc02.
  CLEAR ps_tc02.
  READ TABLE pt_tc02  INTO ps_tc02 WITH KEY zitems_key = pv_zitems_key.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SAVE_DATA_MASS_PRE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      <-- PT_MASS
*&---------------------------------------------------------------------*
FORM frm_save_data_mass_pre  USING    pv_code TYPE sy-ucomm
                                       pt_tc02 TYPE tt_tc02
                             CHANGING pt_mass TYPE tt_mass.

  LOOP AT pt_mass INTO DATA(ls_mass).


    READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems = ls_mass-zitems.
    IF sy-subrc EQ 0.
      ls_mass-zitems_key   = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_mass FROM ls_mass.
  ENDLOOP.

  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZGHF'.

    WHEN 'TC_ITEM_MASS_ZFLZFF'.


    WHEN 'TC_ITEM_MASS_ZJT'.
*      没有阶梯ID的 生成新的阶梯ID
      PERFORM frm_get_new_zjt_id_mass CHANGING pt_mass.

    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_NEW_ZJT_ID_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- PT_MASS
*&---------------------------------------------------------------------*
FORM frm_get_new_zjt_id_mass  CHANGING pt_mass  TYPE tt_mass.

  DATA(lt_mass_tmp) = pt_mass[].
  SORT lt_mass_tmp BY zitems.
  DELETE ADJACENT DUPLICATES FROM lt_mass_tmp COMPARING zitems.
  DELETE lt_mass_tmp WHERE zjt_id IS NOT INITIAL.
  LOOP AT lt_mass_tmp INTO DATA(ls_mass_tmp).
    PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING ls_mass_tmp-zjt_id.
    MODIFY lt_mass_tmp FROM ls_mass_tmp.
  ENDLOOP.
  SORT lt_mass_tmp BY zitems.
  LOOP AT pt_mass INTO DATA(ls_mass).
    READ TABLE lt_mass_tmp INTO ls_mass_tmp WITH KEY zitems = ls_mass-zitems BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_mass-zjt_id = ls_mass_tmp-zjt_id.
      MODIFY pt_mass FROM ls_mass.
    ENDIF.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_UPDATE_ZJT_ID_INTO_TC02
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T11_TMP
*&      <-- PT_TC02
*&---------------------------------------------------------------------*
FORM frm_update_zjt_id_into_tc02  USING    pt_t11 TYPE tt_t11
                                  CHANGING pt_tc02 TYPE tt_tc02.




  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    READ TABLE pt_t11 INTO DATA(ls_t11) WITH KEY zitems_key = ls_tc02-zitems_key.
    IF sy-subrc EQ 0.
      ls_tc02-zjt_id = ls_t11-zjt_id.
    ELSE.
      CLEAR ls_tc02-zjt_id .
    ENDIF.
    MODIFY pt_tc02 FROM ls_tc02 .
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_PASTE_DATA_MASS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_CODE
*&      <-- LT_MASS
*&---------------------------------------------------------------------*
FORM frm_pro_paste_data_mass  USING    pv_code  TYPE sy-ucomm
                                       pt_tc02 TYPE tt_tc02
                              CHANGING lt_mass  TYPE tt_mass
                                       pt_mass  TYPE tt_mass.

  CASE pv_code.
    WHEN 'TC_ITEM_MASS_ZGHF'.

    WHEN 'TC_ITEM_MASS_ZFLZFF'.


    WHEN 'TC_ITEM_MASS_ZJT'.
*      阶梯自动补零行
      PERFORM frm_pro_paste_data_mass_zjt USING pt_tc02 CHANGING lt_mass pt_mass.

    WHEN OTHERS.
  ENDCASE.
ENDFORM.

FORM frm_pro_paste_data_mass_zjt USING pt_tc02 TYPE tt_tc02 CHANGING lt_mass  TYPE tt_mass
                                                                      pt_mass  TYPE tt_mass.
  DATA:
    ls_mass_add TYPE LINE OF tt_mass,
    lt_mass_add TYPE tt_mass.

  DATA(lt_mass_tmp) = lt_mass[].
  SORT lt_mass_tmp BY zitems zjt_from ASCENDING.

  LOOP AT lt_mass INTO DATA(ls_mass).
    PERFORM frm_set_zbukrs_mass USING pt_tc02
                                      ls_mass-zitems
                                     CHANGING ls_mass-zbukrs.

  ENDLOOP.

  PERFORM frm_set_data_tc_mass USING pt_tc02
                                   CHANGING lt_mass.

*  组织抬头
  DATA(lt_mass_head) = lt_mass[].
  SORT lt_mass_head BY zitems.
  DELETE ADJACENT DUPLICATES FROM lt_mass_head COMPARING zitems.
  SORT lt_mass BY zitems zjt_from.
  LOOP AT lt_mass_head INTO DATA(ls_mass_head).
    READ TABLE lt_mass INTO ls_mass WITH KEY zitems = ls_mass_head-zitems
                                                   zjt_from = 0.
*    判断当前粘贴的数据中是否包含0行
    IF sy-subrc NE 0.

*    判断原数据中是否包含0行
      READ TABLE pt_mass INTO DATA(ps_mass) WITH KEY zitems = ls_mass_head-zitems
                                                    zjt_from = 0.
      IF sy-subrc NE 0.
*        增加从为0 的行数据
        CLEAR ls_mass_add.
        CLEAR ls_mass.
        ls_mass_add-zitems = ls_mass_head-zitems.
        ls_mass_add-zbukrs = ls_mass_head-zbukrs.
        ls_mass_add-zflg_1st = 'X'.
        ls_mass_add-zjt_from = 0.
        READ TABLE lt_mass INTO ls_mass WITH KEY zitems = ls_mass_head-zitems.
        IF sy-subrc EQ 0.
          ls_mass_add-zjt_by = ls_mass-zjt_from - 1.
          APPEND ls_mass_add TO lt_mass_add.
        ENDIF.
      ELSE.

*        修改原先为0的行的【至】值

*        记录索引行
        DATA(lv_index) = sy-tabix.

*        首先获取本次粘贴数据的最小【从】值
        READ TABLE lt_mass_tmp INTO DATA(ls_mass_tmp) WITH KEY zitems = ls_mass_head-zitems.
        IF sy-subrc EQ 0.
*          修改原数据的【至】值
          ps_mass-zjt_by  = ls_mass_tmp-zjt_from - 1.
          MODIFY pt_mass FROM ls_mass INDEX lv_index TRANSPORTING zjt_by .
          CLEAR lv_index.
        ENDIF.

      ENDIF.

    ENDIF.
  ENDLOOP.


*  若粘贴前就存在为0的行，则将数据取过去，删除原数据为0的行，统一处理
*  LOOP AT LT_MASS_ADD INTO LS_MASS_ADD WHERE ZFLG_1ST = 'X'.
*    READ TABLE PT_MASS INTO DATA(LS_MASS) WITH KEY ZITEMS = LS_MASS_ADD-ZITEMS ZFLG_1ST = 'X' ZJT_FROM = 0.
*    IF SY-SUBRC EQ 0.
*      LS_MASS_ADD
*    ENDIF.
*    ENDLOOP.



  IF lt_mass_add[] IS NOT INITIAL.
    APPEND LINES OF lt_mass_add TO lt_mass.
  ENDIF.



ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_PASTE_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LT_MASS
*&---------------------------------------------------------------------*
FORM frm_get_paste_data USING    pv_code  TYPE sy-ucomm
                       CHANGING pt_mass TYPE tt_mass.


  DATA:
         lt_data TYPE TABLE OF tdline.
  DATA:
    ls_mass       TYPE LINE OF tt_mass,
    lv_mtype      TYPE bapi_mtype,
    lv_error_text TYPE string.

  CLEAR pt_mass.

  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     LENGTH               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.
  ELSE.
    RETURN.
  ENDIF.

  "拆分
  LOOP AT lt_data INTO DATA(ls_data).

    PERFORM frm_split_data USING pv_code ls_data CHANGING lv_mtype lv_error_text ls_mass.
    IF lv_mtype = 'E'.
      EXIT.
    ELSE.
      APPEND ls_mass TO pt_mass.
    ENDIF.
  ENDLOOP.

  IF lv_mtype = 'E'..
    MESSAGE:lv_error_text TYPE 'S' DISPLAY LIKE 'E'.
    CLEAR: pt_mass.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_MATNR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_matnr INPUT.
  PERFORM frm_check_matnr(zbcs0001) USING gs_matnr-matnr.
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '商品编码不存在！'.
  ENDIF.
  PERFORM frm_get_maktx(zbcs0001)   USING gs_matnr-matnr CHANGING gs_matnr-maktx.


ENDMODULE.


MODULE mdl_set_screen_tc_zff_sub OUTPUT.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_t44_sub[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.


MODULE mdl_set_data_tc_zff_sub OUTPUT.

  PERFORM frm_set_data_tc_zff CHANGING gs_t44_sub.

ENDMODULE.


MODULE mdl_check_zflzff_sub INPUT.


  PERFORM frm_set_screen_zflzff_sub CHANGING gs_t44_sub-zflzff gs_t44_sub-name1.


  PERFORM frm_set_zpaytp USING gs_t44_sub-zflzff CHANGING gs_t44_sub-zpaytp.

*  PERFORM frm_alpha_in_lifnr CHANGING gs_t44_sub-zflzff.
*
*  PERFORM frm_set_screen_zflzff USING gs_t44_sub-zflzff CHANGING gs_t44_sub-name1.
*
*  PERFORM frm_check_status_zflzff USING gs_t44_sub-zflzff.
*  IF sy-subrc NE 0.
*    MESSAGE e888(sabapdocu) WITH '该支付方' && gs_t44_sub-zflzff && '已被冻结或删除，请检查主数据!'.
*  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_9003  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_9003 INPUT.


  CASE ok_code.

    WHEN 'B_WERKS_PASTE'.
      CLEAR:ok_code.
      PERFORM frm_paste_data_werks  CHANGING gt_werks.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_PASTE_DATA_WERKS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_WERKS
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_paste_data_werks  CHANGING    pt_werks TYPE tt_werks.
  DATA:
    lv_str1    TYPE string,
    lv_str2    TYPE string,
    lv_str3    TYPE string,
    lv_werks   TYPE t001w-werks,
    lv_name1   TYPE t001w-name1,
    lv_exclude TYPE char1.
  DATA:
    lt_data  TYPE TABLE OF tdline,
    ls_werks TYPE ty_werks,
    lt_werks TYPE tt_werks.

  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     LENGTH               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.
  ELSE.
    RETURN.
  ENDIF.

  LOOP AT lt_data INTO DATA(ls_data).
    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO lv_str1  lv_str2 lv_str3 .


    MOVE lv_str1 TO lv_werks .
    MOVE lv_str3 TO lv_exclude.

    IF lv_werks IS INITIAL.
      MESSAGE |粘贴的门店为空,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_werks.
      EXIT.
    ENDIF.

*    SELECT SINGLE name1 INTO lv_name1 FROM t001w WHERE werks = lv_werks. "ERP-17212  未上线批发加盟店
    SELECT SINGLE name1 INTO lv_name1 FROM zrev010_wrk WHERE werks = lv_werks.
    IF sy-subrc <> 0.
      MESSAGE |门店:{ lv_werks }不存在,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_werks.
      EXIT.
    ENDIF.

    IF lv_exclude <> '' AND lv_exclude <> 'X'.
      MESSAGE |门店:{ lv_werks }排除标识异常,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_werks.
      EXIT.
    ENDIF.

    ls_werks-werks = lv_werks.
    ls_werks-exclude = lv_exclude.
    APPEND ls_werks TO  lt_werks.
  ENDLOOP.

  IF lt_werks[]  IS NOT INITIAL.
    APPEND LINES OF lt_werks TO pt_werks.
  ENDIF.

  SORT pt_werks BY werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING werks.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_9003  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_9013 INPUT.


  CASE ok_code.

    WHEN 'B_LIFNR_PASTE'.
      CLEAR:ok_code.
      PERFORM frm_paste_data_lifnr  CHANGING gt_zzgys.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_PASTE_DATA_WERKS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_WERKS
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_paste_data_lifnr  CHANGING    pt_zzgys TYPE tt_t84.
  DATA:
    lv_str1    TYPE string,
    lv_str2    TYPE string,
    lv_lifnr   TYPE lfa1-lifnr,
    lv_name1   TYPE lfa1-name1,
    lv_exclude TYPE char1.
  DATA:
    lt_data  TYPE TABLE OF tdline,
    lt_zzgys TYPE tt_t84,
    ls_zzgys TYPE ty_t84.

  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     LENGTH               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.
  ELSE.
    RETURN.
  ENDIF.

  LOOP AT lt_data INTO DATA(ls_data).
    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO lv_str1 lv_str2  .

    MOVE lv_str1 TO lv_lifnr .

    IF lv_lifnr IS INITIAL.
      MESSAGE |粘贴的供应商为空,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_zzgys.
      EXIT.
    ENDIF.

    lv_lifnr = |{ lv_lifnr ALPHA = IN }|.
    SELECT SINGLE name1 INTO lv_name1 FROM lfa1 WHERE lifnr = lv_lifnr.
    IF sy-subrc <> 0.
      MESSAGE |供应商:{ lv_lifnr }不存在,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_zzgys.
      EXIT.
    ENDIF.


    ls_zzgys-lifnr = lv_lifnr.
    ls_zzgys-name1 = lv_name1.
    APPEND ls_zzgys TO  lt_zzgys.
  ENDLOOP.

  IF lt_zzgys[]  IS NOT INITIAL.
    APPEND LINES OF lt_zzgys TO pt_zzgys.
  ENDIF.

  SORT pt_zzgys BY lifnr.
  DELETE ADJACENT DUPLICATES FROM pt_zzgys COMPARING lifnr.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_TKGYS_ALL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_tkgys_all CHANGING pt_tc02  TYPE tt_tc02 .
  LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<LFS_tc02>).
    <LFS_tc02>-ztkgys = 'X'.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_TKGYS_SEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_tkgys_sel CHANGING pt_tc02  TYPE tt_tc02 .

  LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<LFS_tc02>).
    <LFS_tc02>-ztkgys = ''.
  ENDLOOP.

ENDFORM.