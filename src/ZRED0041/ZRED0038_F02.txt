*&---------------------------------------------------------------------*
*& 包含               ZRED0038_F02
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_TC05
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_ZRETC005
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_tc05  USING    ps_zretc005 TYPE zretc005
                     CHANGING pt_msglist  TYPE scp1_general_errors.


  PERFORM frm_check_inital USING ps_zretc005-zclrtxt    '规则描述' CHANGING pt_msglist .
  PERFORM frm_check_inital USING ps_zretc005-zjtlx    '阶梯类型' CHANGING pt_msglist .
  IF ps_zretc005-zxybstyp = 'V' OR ps_zretc005-zxybstyp = 'A'.
    IF ps_zretc005-zxybstyp = 'V'.
*      PERFORM frm_check_inital USING ps_tc05-zflhsjz    '返利计算核算基准' CHANGING lt_msglist .
    ENDIF.
    PERFORM frm_check_inital USING ps_zretc005-zhsjz    ' 任务计算核算基准' CHANGING pt_msglist .
    PERFORM frm_check_inital USING ps_zretc005-zflxs    '返利形式' CHANGING pt_msglist .
    PERFORM frm_check_inital USING ps_zretc005-zjsff    '计算方法' CHANGING pt_msglist .

  ENDIF.



  IF ps_zretc005-zflxs = 'Q'.
    IF ps_zretc005-zjsff EQ 'S'.
      PERFORM frm_add_msg USING '返利形式为数量时，计算方法不能为单价'  CHANGING pt_msglist.
    ENDIF.
  ENDIF.

  IF ps_zretc005-zlhwd = 'Q' AND
     ps_zretc005-zflxs = 'M' AND
     ps_zretc005-zjtlx = 'S' .
    IF ps_zretc005-zjsff EQ 'P'.
      PERFORM frm_add_msg USING '量化维度为数量，返利形式为金额，阶梯类型为增量时计算方法不能是比例'  CHANGING pt_msglist.

    ENDIF.
  ENDIF.

  IF ps_zretc005-zlhwd = 'M' AND
     ps_zretc005-zflxs = 'Q' AND
     ps_zretc005-zjtlx = 'S' .
    IF ps_zretc005-zjsff NE 'F'.
      PERFORM frm_add_msg USING '量化维度为金额，返利形式为数量，阶梯类型为增量时计算方法只能是固定'  CHANGING pt_msglist.

    ENDIF.
  ENDIF.

  IF ps_zretc005-zlhwd = 'M' AND
     ps_zretc005-zflxs = 'M' AND
     ps_zretc005-zjtlx = 'S' .
    IF ps_zretc005-zjsff EQ 'S'.
      PERFORM frm_add_msg USING '量化维度为金额，返利形式为金额，阶梯类型为增量时计算方法不能是单价'  CHANGING pt_msglist.

    ENDIF.
  ENDIF.
  IF ps_zretc005-zjsff = 'R' .
    IF ps_zretc005-zjgwd =  'A'.
      PERFORM frm_add_msg USING '计算方法为补差时，任务价格维度不能为核算价！'  CHANGING pt_msglist.

    ENDIF.

    IF ps_zretc005-zfljgwd NE   'A'.
      PERFORM frm_add_msg USING '计算方法为补差时,返利价格维度只能为核算价！'  CHANGING pt_msglist.

    ENDIF.
  ENDIF.

  IF ps_zretc005-zjtlx = 'S' AND ( ps_zretc005-zjsff = 'P' OR ps_zretc005-zjsff = 'R' ).
    IF ps_zretc005-zjgwd NE ps_zretc005-zfljgwd.
      PERFORM frm_add_msg USING '当阶梯类型=“S-增量”且计算方法=“P-比例”或“R-补差”时，返利价格维度和价格维度必须一致'  CHANGING pt_msglist.

    ENDIF.
  ENDIF.

  IF ps_zretc005-zxybstyp = 'V' OR ps_zretc005-zxybstyp = 'T'.
    IF ps_zretc005-zflxs = 'M' OR ps_zretc005-zlhwd = 'M'.
      IF ps_zretc005-zjgwd = ''.
        PERFORM frm_add_msg USING '当量化纬度或返利形式为金额时，价格纬度不能为空'  CHANGING pt_msglist.

      ENDIF.
    ENDIF.

  ENDIF.




ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_2000
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9100 USING ps_tc05 TYPE zretc005.

  IF ps_tc05-zjgwd = 'A'.
    ps_tc05-zjths = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TC05-ZJTHS'.
        screen-input = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF ps_tc05-zfljgwd = 'A'.
    ps_tc05-zflhs = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TC05-ZFLHS'.
        screen-input = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.


  IF ps_tc05-zxybstyp = 'A'.
    LOOP AT SCREEN.
      IF screen-group2 = '203'.
        screen-active = '0'.
        MODIFY SCREEN.
      ELSEIF screen-group3 = '302'.
        screen-input = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_ZHSJZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TC05_ZXYBSTYP
*&      <-- GT_VLS_ZHSJZ
*&---------------------------------------------------------------------*
FORM frm_pro_zhsjz  USING    pv_zxybstyp TYPE zretc005-zxybstyp
                    CHANGING pt_vls_zhsjz TYPE vrm_values.

  CASE pv_zxybstyp.
    WHEN 'A'.
      DELETE pt_vls_zhsjz WHERE key NE '1006'
                            AND key NE '1007'.
    WHEN 'V'.
      DELETE pt_vls_zhsjz WHERE key NE '1001'
                            AND key NE '1002'
                            AND key NE '1003'
                            AND key NE '1004'
                            AND key NE '1005'
                            AND key NE '1008'
                            AND key NE '1009'
                            AND key NE '1010'
                            .
      SORT pt_vls_zhsjz BY key.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.