*&---------------------------------------------------------------------*
*& 包含               ZRED0041_C01
*&---------------------------------------------------------------------*

DATA:
  grf_container TYPE REF TO cl_gui_docking_container,
  grf_alv       TYPE REF TO cl_gui_alv_grid.


CLASS sec_lcl_event_receiver DEFINITION DEFERRED.
*----------------------------------------------------------------------*
*       CLASS SEC_LCL_EVENT_RECEIVER DEFINITION
*----------------------------------------------------------------------*
*
*----------------------------------------------------------------------*
CLASS sec_lcl_event_receiver DEFINITION.
  PUBLIC SECTION.
    DATA:
      objid    TYPE char10.

    METHODS:
      constructor
        IMPORTING
          i_objid TYPE char10 OPTIONAL,
      sec_handle_toolbar
                    FOR EVENT toolbar OF cl_gui_alv_grid
        IMPORTING e_object e_interactive,
      sec_handle_bef_user_command
                    FOR EVENT before_user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_user_command
                    FOR EVENT user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_hotspot_click
                    FOR EVENT hotspot_click OF cl_gui_alv_grid
        IMPORTING e_row_id e_column_id es_row_no,
      sec_handle_double_click
                    FOR EVENT double_click OF cl_gui_alv_grid
        IMPORTING e_row e_column es_row_no,
      sec_handle_data_changed
                    FOR EVENT data_changed OF cl_gui_alv_grid
        IMPORTING er_data_changed e_onf4 e_onf4_before e_onf4_after,
      sec_handle_data_changed_fin
                    FOR EVENT data_changed_finished OF cl_gui_alv_grid
        IMPORTING e_modified et_good_cells.
ENDCLASS.                    "LCL_EVENT_RECEIVER DEFINITION

*----------------------------------------------------------------------*
*       CLASS SEC_LCL_EVENT_RECEIVER IMPLEMENTATION
*----------------------------------------------------------------------*
*
*----------------------------------------------------------------------*
CLASS sec_lcl_event_receiver IMPLEMENTATION.

  METHOD constructor.
    objid = i_objid.
  ENDMETHOD.                    "CONSTRUCTOR
  METHOD sec_handle_toolbar.
    PERFORM handle_toolbar USING e_object e_interactive objid.
  ENDMETHOD.                    "HANDLE_TOOLBAR
  METHOD sec_handle_bef_user_command.
    PERFORM handle_bef_user_command USING e_ucomm.
  ENDMETHOD.                "BEFORE_USER_COMMAND
  METHOD sec_handle_user_command.
    PERFORM handle_user_commmand CHANGING e_ucomm objid.
  ENDMETHOD.                   "HANDLE USER COMMAND
  METHOD sec_handle_hotspot_click.
    PERFORM handle_hotspot_click USING e_row_id e_column_id es_row_no objid.
  ENDMETHOD.                   "HANDLE_HOTSPOT_CLICK
  METHOD sec_handle_double_click.
    PERFORM handle_double_click USING e_row e_column es_row_no objid.
  ENDMETHOD.                  "HANDLE_DOUBLE_CLICK
  METHOD sec_handle_data_changed.
    PERFORM handle_data_changed USING er_data_changed objid.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED
  METHOD sec_handle_data_changed_fin.
    PERFORM handle_data_changed_fin USING e_modified et_good_cells objid.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED_FINISHED
ENDCLASS.               "LCL_EVENT_RECEIVER


FORM handle_data_changed USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               pv_objid TYPE char10.

  DATA:
  ls_mod_cell TYPE lvc_s_modi.
*  DATA:
*  ls_data TYPE LINE OF tt_data.



**  LOOP AT prf_data_changed->mt_mod_cells INTO     ls_mod_cell
**                                          WHERE   fieldname = 'MATNR'
**                                          OR      fieldname = 'BUKRS'.
**
**    CLEAR ls_data.
**
**    CASE ls_mod_cell-fieldname.
**      WHEN 'MATNR'.
**        ls_mod_cell-value = |{ ls_mod_cell-value ALPHA = IN WIDTH = 18 }|.
**        SELECT SINGLE maktx INTO ls_data-maktx FROM makt WHERE matnr = ls_mod_cell-value.
**        IF sy-subrc NE 0.
**          ls_data-zmsg =  '商品编码不存在'.
**          ls_data-status = '1'.
**        ELSE.
**          CALL METHOD prf_data_changed->modify_cell
**            EXPORTING
**              i_row_id    = ls_mod_cell-row_id
**              i_fieldname = 'MAKTX'
**              i_value     = ls_data-maktx.
**        ENDIF.
**      WHEN OTHERS.
**    ENDCASE.
**
**    IF ls_data-status IS NOT INITIAL.
**      CALL METHOD prf_data_changed->modify_cell
**        EXPORTING
**          i_row_id    = ls_mod_cell-row_id
**          i_fieldname = 'STATUS'
**          i_value     = ls_data-status.
**
**      CALL METHOD prf_data_changed->modify_cell
**        EXPORTING
**          i_row_id    = ls_mod_cell-row_id
**          i_fieldname = 'ZMSG'
**          i_value     = ls_data-zmsg.
**
**    ENDIF.
**
**  ENDLOOP.


ENDFORM.                    " HANDLE_DATA_CHANGED
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_DATA_CHANGED_FINISHED
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->E_MODIFIED TEXT
*----------------------------------------------------------------------*
FORM handle_data_changed_fin USING pv_modified TYPE char01
                                    pt_good_cells  TYPE lvc_t_modi
                                    pv_objid TYPE char10 .

*  PERFORM FRM_REFRESH_ALV USING GRF_ALV.
ENDFORM.                    "HANDLE_DATA_CHANGED_FINISHED
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_TOOLBAR
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_OBJECT       TEXT
*      -->P_E_INTERACTIVE  TEXT
*----------------------------------------------------------------------*
FORM handle_toolbar USING prf_object TYPE REF TO cl_alv_event_toolbar_set
                          pv_interactive
                          pv_objid TYPE char10.
  DATA: lw_toolbar TYPE stb_button.

  DEFINE add_toolbar.
    lw_toolbar-butn_type  = &1.
    lw_toolbar-function   = &2.
    lw_toolbar-icon       = &3.
    lw_toolbar-quickinfo  = &4.
    lw_toolbar-text       = &4.
    lw_toolbar-disabled   = &5.
    APPEND lw_toolbar TO prf_object->mt_toolbar .
    CLEAR lw_toolbar.
  END-OF-DEFINITION.

*删除其他按钮
  DELETE prf_object->mt_toolbar WHERE
                                    function = '&GRAPH'
                                 OR function = '&INFO'
                                 OR function = '&DETAIL'
                                 OR function = '&REFRESH'
                                 OR function = '&CHECK'
                                 OR function = '&MB_VIEW'
                                 OR function = '&PRINT_BACK'
                                 OR function = '&LOCAL&CUT'
                                 OR function = '&LOCAL&COPY'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&PASTE'
                                 OR function = '&LOCAL&UNDO'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&INSERT_ROW'
                                 OR function = '&LOCAL&DELETE_ROW'
                                 OR function = '&LOCAL&COPY_ROW'
                                 .

  add_toolbar:
  '0' 'ALL'   '@4B@' '全选' '' ,
  '0' 'DALL'  '@4D@' '取消' ''  ,
*  '0' 'INS'   '@17@' '新增数据' ''  ,
*  '0' 'DEL'   '@11@' '删除数据' ''  ,
*  '0' 'DDEL'  '@07@' '取消删除' ''  ,
  '0' 'IMPT'  '@48@' '导入' ''  .


ENDFORM.                    " HANDLE_TOOLBAR
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_BEFORE_USER_COMMAND
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_UCOMM  TEXT
*----------------------------------------------------------------------*
FORM handle_bef_user_command  USING pv_ucomm.

ENDFORM.                    " HANDLE_BEFORE_USER_COMMAND
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_USER_COMMMAND
*&---------------------------------------------------------------------*
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM handle_user_commmand CHANGING pv_ucomm
                          pv_objid TYPE char10.

  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  DATA:lt_rows TYPE lvc_t_row,
       lw_rows TYPE lvc_s_row.

*  CALL METHOD grf_alv->get_selected_rows
*    IMPORTING
*      et_index_rows = lt_rows.
*
*  PERFORM frm_set_data_sel USING lt_rows
*                           CHANGING gt_data.

  CASE pv_ucomm.
    WHEN 'ALL'.
      PERFORM frm_set_all(zbcs0001) TABLES gt_excel USING 'SEL_MAN' .
    WHEN 'DALL'.
      PERFORM frm_set_sal(zbcs0001) TABLES gt_excel USING 'SEL_MAN' .
    WHEN 'IMPT'.
      PERFORM frm_check_bf_impt USING gt_excel CHANGING lv_mtype lv_msg.
      IF lv_mtype = 'E'.
        MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

      PERFORM frm_data_impt_main USING  gv_flg_impt
                                        GV_PASS
                                        gt_bukrs_excel
                                        gt_WERKS_excel
                                        gt_matnr_excel
                                        gt_zzlbm_excel
                                        gt_zqdbm_excel
                               CHANGING gt_excel
                                        lv_mtype
                                        lv_msg.

      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.


  ENDCASE.

  PERFORM frm_refresh_alv_excel USING grf_alv.
ENDFORM.                    " HANDLE_USER_COMMMAND
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_HOTSPOT_CLICK
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_ROW_ID  TEXT
*      -->P_E_COLUMN_ID  TEXT
*      -->P_ES_ROW_NO  TEXT
*----------------------------------------------------------------------*
FORM handle_hotspot_click  USING   ps_row_id TYPE lvc_s_row
                                    ps_column_id TYPE lvc_s_col
                                    ps_row_no TYPE lvc_s_roid
                                    pv_objid TYPE char10.

ENDFORM.                    " HANDLE_HOTSPOT_CLICK
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_DOUBLE_CLICK
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_ROW_ID  TEXT
*      -->P_E_COLUMN_ID  TEXT
*      -->P_ES_ROW_NO  TEXT
*----------------------------------------------------------------------*
FORM handle_double_click  USING   ps_row_id TYPE lvc_s_row
                                   ps_column_id TYPE lvc_s_col
                                   ps_row_no TYPE lvc_s_roid
                                   pv_objid TYPE char10.
ENDFORM.                    " HANDLE_DOUBLE_CLICK