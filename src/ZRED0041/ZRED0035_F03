*&---------------------------------------------------------------------*
*& 包含               ZRED0035_F03
*&---------------------------------------------------------------------*

FORM frm_pro_data_org CHANGING
                            ps_tc01 TYPE LINE OF tt_tc01
                            pt_tc02 TYPE tt_tc02
                            pt_tc03 TYPE tt_tc03
                            pt_tc04 TYPE tt_tc04
                            pt_t44_all TYPE tt_t44
                            pt_matnr TYPE tt_matnr
                            pt_zzgys TYPE tt_t84
                            .

  IF ps_tc01-ztktype = 'P'.
    ps_tc01-zfjtk = 'X'.
  ENDIF.


  LOOP AT pt_tc02 INTO DATA(ls_tc02).
*    获取KEY值
    PERFORM frm_get_zitems_key CHANGING ls_tc02-zitems_key gv_zitem_key.

*    IF LS_TC02-ZITEMS_KEY IS INITIAL.  %%%%%
*      LS_TC02-ZITEMS_KEY = LS_TC02-ZITEMS.
*    ENDIF.

    IF ls_tc02-zitemtxt IS  INITIAL.
      PERFORM frm_get_butxt USING ls_tc02-zbukrs CHANGING ls_tc02-zitemtxt.
    ENDIF.

    IF ls_tc02-zstatus = 'D'.
      ls_tc02-zstatus_del = 'X'.
    ELSE.
      ls_tc02-zstatus_del = ''.
    ENDIF.

    IF ls_tc02-ztmpid IS NOT INITIAL OR ls_tc02-zxy_id IS NOT INITIAL.
      IF ls_tc02-zxy_id IS NOT INITIAL.
        IF ls_tc02-zxyzt_06 = 'D'.
          ls_tc02-sel_man = ''.
        ELSE.
          ls_tc02-sel_man = 'X'.
        ENDIF.

      ELSE.
        IF ls_tc02-ztmpid IS NOT INITIAL.
          ls_tc02-sel_man = 'X'.
        ENDIF.

      ENDIF.
    ENDIF.

    IF ls_tc02-zfklx IS INITIAL  .
      ls_tc02-zfklx = '3'.
    ENDIF.

    MODIFY pt_tc02 FROM ls_tc02.
  ENDLOOP.

  LOOP AT pt_tc03 INTO DATA(ls_tc03).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = ls_tc03-zitems .
    IF sy-subrc EQ 0.
      ls_tc03-zitems_key = ls_tc02-zitems_key.
    ENDIF.
*    IF LS_TC03-ZITEMS_KEY IS INITIAL.      %%%%%
*      LS_TC03-ZITEMS_KEY = LS_TC03-ZITEMS.
*    ENDIF.
    MODIFY pt_tc03 FROM ls_tc03.
  ENDLOOP.

  LOOP AT pt_tc04 INTO DATA(ls_tc04).

    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = ls_tc04-zitems .
    IF sy-subrc EQ 0.
      ls_tc04-zitems_key = ls_tc02-zitems_key.
    ENDIF.

*    IF LS_TC04-ZITEMS_KEY IS INITIAL.    %%%%%
*      LS_TC04-ZITEMS_KEY = LS_TC04-ZITEMS.
*    ENDIF.
    MODIFY pt_tc04 FROM ls_tc04.
  ENDLOOP.

  LOOP AT pt_t44_all INTO DATA(ls_t44_all).

    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = ls_t44_all-zitems .
    IF sy-subrc EQ 0.
      ls_t44_all-zitems_key = ls_tc02-zitems_key.
    ENDIF.

*    IF LS_TC04-ZITEMS_KEY IS INITIAL.    %%%%%
*      LS_TC04-ZITEMS_KEY = LS_TC04-ZITEMS.
*    ENDIF.
    MODIFY pt_t44_all FROM ls_t44_all.
  ENDLOOP.

  LOOP AT pt_matnr INTO DATA(ls_matnr).

    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = ls_matnr-zitems .
    IF sy-subrc EQ 0.
      ls_matnr-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    ls_matnr-matnr = |{ ls_matnr-matnr ALPHA = IN WIDTH = 18 }|.
    MODIFY pt_matnr FROM ls_matnr.
  ENDLOOP.

  LOOP AT pt_zzgys INTO DATA(ls_zzgys).

    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = ls_zzgys-zitems .
    IF sy-subrc EQ 0.
      ls_zzgys-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zzgys FROM ls_zzgys.
  ENDLOOP.



  SORT pt_tc02 BY zitems.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC_ZFF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_T44
*&---------------------------------------------------------------------*
FORM frm_set_data_tc_zff  CHANGING ps_t44 TYPE LINE OF tt_t44.
  PERFORM frm_get_zghf_t USING ps_t44-zflzff CHANGING ps_t44-name1.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_ZRETA007
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_update_zreta007 TABLES pt_zreta007 STRUCTURE zreta007
                          USING  ps_ta02    TYPE LINE OF tt_ta02.

  CHECK ps_ta02-zleib = 'R'.

  SELECT SINGLE zdjbm INTO @DATA(lv_ztk_id) FROM zreta007
   WHERE zdybm = @ps_ta02-ztk_id AND zdele = ''.

*  返利申请单据2
  SELECT zreta007~*
APPENDING TABLE @pt_zreta007
    FROM zreta007
   WHERE zdjbm = @lv_ztk_id
     AND zdele = ''.
  SELECT zreta007~*
APPENDING TABLE @pt_zreta007
    FROM zreta007
   INNER JOIN zret0006 ON zreta007~zdjbm = zret0006~zxy_id
   WHERE zret0006~ztk_id = @lv_ztk_id
     AND zreta007~zdele = ''..

  LOOP AT pt_zreta007 ASSIGNING FIELD-SYMBOL(<lfs_zreta007>) .
    <lfs_zreta007>-zdele = 'X'.
  ENDLOOP.
ENDFORM.

*& ADD BY zsfsx = '2'
FORM frm_check_zflsqf1 CHANGING pv_zflsqf TYPE t001-bukrs.

  DATA:lv_sql_partner TYPE but000-partner.
  CHECK pv_zflsqf IS NOT INITIAL.

  SELECT SINGLE zsfsx INTO @DATA(ls_zsfsx) FROM zretcm13 WHERE zhtlx = @gs_tc01-zhtlx.

  IF ls_zsfsx = '2' .
    lv_sql_partner = |{ pv_zflsqf ALPHA = IN }|.
    SELECT SINGLE  partner  FROM but000  WHERE partner = @lv_sql_partner
      INTO @DATA(lv_partner).
    IF sy-subrc NE 0.
      MESSAGE e888(sabapdocu) WITH '收款方不存在！'.
    ENDIF.
  ELSE.
    IF pv_zflsqf NE 'ALL'.
      SELECT SINGLE bukrs    FROM t001    WHERE bukrs = @pv_zflsqf
        INTO @DATA(lv_bukrs).
      IF sy-subrc NE 0.
        MESSAGE e888(sabapdocu) WITH '收款方不存在！'.
      ENDIF.
    ENDIF.
  ENDIF.

ENDFORM.