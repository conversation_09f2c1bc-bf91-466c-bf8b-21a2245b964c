*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F05
*&---------------------------------------------------------------------*

FORM frm_excel_load TABLES pt_tab TYPE STANDARD TABLE
                      USING u_filename TYPE rlgrap-filename
                            pv_begin_row TYPE i.

  DATA: lv_type TYPE char1.

  DATA:lt_intern TYPE kcde_cells OCCURS 0 WITH HEADER LINE.

  CALL FUNCTION 'KCD_EXCEL_OLE_TO_INT_CONVERT'
    EXPORTING
      filename                = u_filename
      i_begin_col             = 1
      i_begin_row             = pv_begin_row
*     i_begin_row             = 4
      i_end_col               = 50
      i_end_row               = 9999
    TABLES
      intern                  = lt_intern
    EXCEPTIONS
      inconsistent_parameters = 1
      upload_ole              = 2
      OTHERS                  = 3.

  IF sy-subrc <> 0.
*
  ENDIF.

  IF lt_intern[] IS INITIAL.
    MESSAGE '文档中不包含数据，请检查文档！' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  "----------------------------------------------------------
  SORT lt_intern BY row col.

  LOOP AT lt_intern.

    ASSIGN COMPONENT lt_intern-col OF STRUCTURE  pt_tab TO FIELD-SYMBOL(<fs>).

    TRY .
        MOVE:lt_intern-value TO <fs>.
      CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
        DATA(lv_error_text) = lv_error->get_text( ).
        lv_error_text       = |模板中数据有误{ lt_intern-row }行{ lt_intern-col }列[{ lt_intern-value }]值:{ lv_error_text }|  .
        sy-subrc            = 1.
      CLEANUP.
    ENDTRY.

    IF sy-subrc = 1.
      MESSAGE:lv_error_text TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
      RETURN.
    ENDIF.

    CLEAR lv_type.
    DESCRIBE FIELD <fs> TYPE lv_type.
    IF lv_type = 'C'.
      TRANSLATE <fs> TO UPPER CASE.
    ENDIF.


    AT END OF row.
      APPEND pt_tab .
      CLEAR pt_tab.
    ENDAT.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_DATA_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_EXCEL_I
*&      --> GT_EXCEL_U
*&      <-- GT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_get_data_excel  USING    pt_excel_i  TYPE tt_excel_i
                                  pt_excel_u  TYPE tt_excel_u
                                  pt_excel_t  TYPE tt_excel_t
                         CHANGING pt_excel    TYPE tt_excel
                                  pt_bukrs_excel TYPE tt_bukrs
                                  pt_werks_excel TYPE tt_werks
                                  pt_matnr_excel TYPE tt_matnr
                                  pt_zzlbm_excel TYPE tt_t81
                                  pt_zqdbm_excel TYPE tt_t82
  .
  DATA:
        ls_excel TYPE LINE OF tt_excel.


  LOOP AT pt_excel_i INTO DATA(ls_excel_i).
    CLEAR ls_excel.
*    ls_excel_i-matnr = |{ ls_excel_i-matnr  ALPHA  = IN WIDTH = 18   }|.
    MOVE-CORRESPONDING ls_excel_i TO ls_excel.

    IF ls_excel-bukrs_x IS INITIAL.
      SELECT * FROM zretcm05
        WHERE zbukrs = @ls_excel-zbukrs
        AND   zctgr  = @ls_excel-zctgr
        AND   zxybstyp = @ls_excel-zxybstyp
        INTO TABLE @DATA(lt_zretcm05).

      PERFORM frm_get_bukrs_x TABLES lt_zretcm05 CHANGING ls_excel-bukrs_x.
      ls_excel_i-bukrs_x = ls_excel-bukrs_x.
    ENDIF.

    PERFORM frm_conv_data_org_i   USING ls_excel_i
                               CHANGING
                                        pt_bukrs_excel
                                        pt_werks_excel
                                        pt_matnr_excel
                                        pt_zzlbm_excel
                                        pt_zqdbm_excel.
    APPEND ls_excel TO pt_excel.
  ENDLOOP.

  LOOP AT pt_excel_u INTO DATA(ls_excel_u).
    CLEAR ls_excel.
*    MOVE-CORRESPONDING LS_EXCEL_U TO LS_EXCEL.
    ls_excel-seq = ls_excel_u-seq.
*    ls_excel-matnr_x = ls_excel_u-matnr_x.
*    ls_excel-bukrs_x = ls_excel_u-bukrs_x.
*    ls_excel-zzlbm_x = ls_excel_u-zzlbm_x.
*    ls_excel-zqdbm_x = ls_excel_u-zqdbm_x.
*    ls_excel-zjzzd   = ls_excel_u-zjzzd.

    PERFORM frm_conver_excel_u USING 'A'
                               CHANGING ls_excel_u
                                        ls_excel.

    IF ls_excel-bukrs_x IS INITIAL.
      SELECT SINGLE zxybstyp FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id INTO @ls_excel-zxybstyp.

      CLEAR lt_zretcm05.
      SELECT * FROM zretcm05
        WHERE zbukrs = @ls_excel-zbukrs
        AND   zctgr  = @ls_excel-zctgr
        AND   zxybstyp = @ls_excel-zxybstyp
        INTO TABLE @lt_zretcm05.
      PERFORM frm_get_bukrs_x TABLES lt_zretcm05 CHANGING ls_excel-bukrs_x.
      ls_excel_u-bukrs_x = ls_excel-bukrs_x.
    ENDIF.

*    ls_excel-matnr = |{ ls_excel-matnr  ALPHA  = IN WIDTH = 18   }|.
    PERFORM frm_conv_data_org_u USING ls_excel_u  CHANGING
                                                           pt_bukrs_excel
                                                           pt_werks_excel
                                                           pt_matnr_excel
                                                           pt_zzlbm_excel
                                                           pt_zqdbm_excel
                                                           .
    APPEND ls_excel TO pt_excel.
  ENDLOOP.


  LOOP AT pt_excel_t INTO DATA(ls_excel_t).
    CLEAR ls_excel.
    MOVE-CORRESPONDING ls_excel_t TO ls_excel.
    ls_excel-seq = ls_excel_u-seq.

    APPEND ls_excel TO pt_excel.
  ENDLOOP.

*  PERFORM frm_set_seq(zbcs0001) TABLES pt_excel USING 'SEQ'.

ENDFORM.

FORM frm_get_data_excel_c   USING pt_excel_c     TYPE tt_excel_c
                         CHANGING pt_excel       TYPE tt_excel
                                  pt_bukrs_excel TYPE tt_bukrs
                                  pt_werks_excel TYPE tt_werks
                                  pt_matnr_excel TYPE tt_matnr
                                  pt_zzlbm_excel TYPE tt_t81
                                  pt_zqdbm_excel TYPE tt_t82.
  DATA:
    ls_excel    TYPE LINE OF tt_excel,
    lt_zret0014 TYPE TABLE OF zret0014.

  LOOP AT pt_excel_c INTO DATA(ls_excel_c).
    CLEAR ls_excel.
    MOVE-CORRESPONDING ls_excel_c TO ls_excel.

    ls_excel-zxy_id = ls_excel_c-zxy_id.

*    外码
    ls_excel-zje_n      = ls_excel_c-zje   .
    ls_excel-zdate_n    = ls_excel_c-zdate .
    ls_excel-zmwskz_n   = ls_excel_c-zmwskz.
    ls_excel-zflbz_n    = ls_excel_c-zflbz .
    ls_excel-zfljs_n    = ls_excel_c-zfljs .
    ls_excel-zdffs_n    = ls_excel_c-zdffs .
    ls_excel-zpaytp_n   = ls_excel_c-zpaytp .
    ls_excel-zflzff_n   = ls_excel_c-zflzff .
    ls_excel-zzsbs_n    = ls_excel_c-zzsbs .
    ls_excel-zctgr_n    = ls_excel_c-zctgr .
    ls_excel-matnr_x_n  = ls_excel_c-matnr_x .
    ls_excel-bukrs_x_n  = ls_excel_c-bukrs_x .
    ls_excel-buklx_x_n  = ls_excel_c-buklx_x .
    ls_excel-werks_x_n  = ls_excel_c-werks_x .
    ls_excel-werpc_x_n  = ls_excel_c-werpc_x .
    ls_excel-zzlbm_x_n  = ls_excel_c-zzlbm_x .
    ls_excel-zqdbm_x_n  = ls_excel_c-zqdbm_x .
    ls_excel-zjzzd_n    = ls_excel_c-zjzzd .
    ls_excel-zsqbm_xy_n = ls_excel_c-zsqbm_xy .

    IF ls_excel_c-bukrs_x = 'Q' .
      CLEAR:ls_excel_c-bukrs_x.
      CLEAR:ls_excel_c-buklx_x.
    ENDIF.

    IF ls_excel_c-werks_x = 'Q' .
      CLEAR:ls_excel_c-werks_x.
      CLEAR:ls_excel_c-werpc_x.
    ENDIF.

*@锚点XY
    PERFORM frm_conv_data_org_c    USING ls_excel_c
                                CHANGING
                                         pt_bukrs_excel
                                         pt_werks_excel
                                         pt_matnr_excel
                                         pt_zzlbm_excel
                                         pt_zqdbm_excel.

*    原值
    SELECT SINGLE * FROM zret0008 WHERE zxy_id = @ls_excel-zxy_id    INTO @DATA(ls_zret0008).
    SELECT SINGLE * FROM zret0006 WHERE zxy_id = @ls_excel-zxy_id    INTO @DATA(ls_zret0006).
    SELECT SINGLE * FROM zret0044 WHERE zxy_id = @ls_excel-zxy_id    INTO @DATA(ls_zret0044).
    SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_zret0006-ztk_id INTO @DATA(ls_zreta002).
    SELECT        * FROM zret0071 WHERE zxy_id = @ls_excel-zxy_id    INTO TABLE @DATA(lt_zret0071).

    LOOP AT lt_zret0071 INTO DATA(ls_zret0071).
      IF ls_excel-matnr_x_o IS NOT INITIAL.
        ls_excel-matnr_x_o = ls_zret0071-matnr && '/' && ls_excel-matnr_x_o.
      ELSE.
        ls_excel-matnr_x_o = ls_zret0071-matnr .
      ENDIF.
    ENDLOOP.

    CLEAR:lt_zret0014.
    SELECT * FROM zret0014 WHERE zxy_id = @ls_excel-zxy_id AND zzzlx = 'A' AND zzzpc = '' INTO TABLE @lt_zret0014.
    LOOP AT lt_zret0014 INTO DATA(ls_zret0014).
      IF ls_excel-bukrs_x_o IS NOT INITIAL.
        ls_excel-bukrs_x_o = ls_zret0014-zzzid && '/' && ls_excel-bukrs_x_o.
      ELSE.
        ls_excel-bukrs_x_o = ls_zret0014-zzzid .
      ENDIF.

      IF ls_excel-buklx_x_o IS NOT INITIAL.
        ls_excel-buklx_x_o = ls_zret0014-zmdsx && '/' && ls_excel-buklx_x_o.
      ELSE.
        ls_excel-buklx_x_o = ls_zret0014-zmdsx .
      ENDIF.
    ENDLOOP.

    CLEAR:lt_zret0014.
    SELECT * FROM zret0014 WHERE zxy_id = @ls_excel-zxy_id AND zzzlx = 'S' AND zzzpc = '' INTO TABLE @lt_zret0014.
    LOOP AT lt_zret0014 INTO ls_zret0014.
      IF ls_excel-werks_x_o IS NOT INITIAL.
        ls_excel-werks_x_o = ls_zret0014-zzzid && '/' && ls_excel-werks_x_o.
      ELSE.
        ls_excel-werks_x_o = ls_zret0014-zzzid .
      ENDIF.

      IF ls_excel-werpc_x_o IS NOT INITIAL.
        ls_excel-werpc_x_o = ls_zret0014-zzzpc && '/' && ls_excel-werpc_x_o.
      ELSE.
        ls_excel-werpc_x_o = ls_zret0014-zzzpc .
      ENDIF.

    ENDLOOP.

    SELECT * FROM zret0081 WHERE zxy_id = @ls_excel-zxy_id INTO TABLE @DATA(lt_zret0081).
    LOOP AT lt_zret0081 INTO DATA(ls_zret0081).
      IF ls_excel-zzlbm_x_o IS NOT INITIAL.
        ls_excel-zzlbm_x_o = ls_zret0081-zzlbm && '/' && ls_excel-zzlbm_x_o.
      ELSE.
        ls_excel-zzlbm_x_o = ls_zret0081-zzlbm .
      ENDIF.
    ENDLOOP.

    SELECT * FROM zret0082 WHERE zxy_id = @ls_excel-zxy_id INTO TABLE @DATA(lt_zret0082).
    LOOP AT lt_zret0082 INTO DATA(ls_zret0082).
      IF ls_excel-zqdbm_x_o IS NOT INITIAL.
        ls_excel-zqdbm_x_o = ls_zret0082-zqdbm && '/' && ls_excel-zqdbm_x_o.
      ELSE.
        ls_excel-zqdbm_x_o = ls_zret0082-zqdbm .
      ENDIF.
    ENDLOOP.

    ls_excel-zht_id     = ls_zreta002-zht_id.
    ls_excel-ztk_id     = ls_zreta002-ztk_id.
    ls_excel-zje_o      = ls_zret0008-zje   .
    ls_excel-zdate_o    = ls_zret0008-zdate .
    ls_excel-zmwskz_o   = ls_zret0008-zmwskz.
    ls_excel-zflbz_o    = ls_zret0008-zflbz .
    ls_excel-zfljs_o    = ls_zret0008-zfljs .
    ls_excel-zjzzd_o    = ls_zret0008-zjzzd .
    ls_excel-zsqbm_xy_o = ls_zret0008-zsqbm_xy .
    IF ls_zret0006-zdffs IS NOT INITIAL.
      ls_excel-zdffs_o  = ls_zret0006-zdffs .
    ELSE.
      ls_excel-zdffs_o  = ls_zreta002-zdffs .
    ENDIF.
    ls_excel-zpaytp_o   = ls_zret0044-zpaytp .
    ls_excel-zflzff_o   = ls_zret0044-zflzff .
    ls_excel-zzsbs_o    = ls_zret0006-zzsbs .
    ls_excel-zctgr_o    = ls_zret0006-zctgr .

*    内码
    ls_excel-zje      = ls_excel_c-zje   .

    IF ls_excel-zdate_n    IS INITIAL. ls_excel-zdate    = ls_excel-zdate_o .   ELSE. ls_excel-zdate    = ls_excel-zdate_n.    ENDIF.
    IF ls_excel-zmwskz_n   IS INITIAL. ls_excel-zmwskz   = ls_excel-zmwskz_o .  ELSE. ls_excel-zmwskz   = ls_excel-zmwskz_n.   ENDIF.
    IF ls_excel-zflbz_n    IS INITIAL. ls_excel-zflbz    = ls_excel-zflbz_o .   ELSE. ls_excel-zflbz    = ls_excel-zflbz_n.    ENDIF.
    IF ls_excel-zfljs_n    IS INITIAL. ls_excel-zfljs    = ls_excel-zfljs_o .   ELSE. ls_excel-zfljs    = ls_excel-zfljs_n.    ENDIF.
    IF ls_excel-zzsbs_n    IS INITIAL. ls_excel-zzsbs    = ls_excel-zzsbs_o .   ELSE. ls_excel-zzsbs    = ls_excel-zzsbs_n.    ENDIF.
    IF ls_excel-zctgr_n    IS INITIAL. ls_excel-zctgr    = ls_excel-zctgr_o .   ELSE. ls_excel-zctgr    = ls_excel-zctgr_n.    ENDIF.
    IF ls_excel-bukrs_x_n  IS INITIAL. ls_excel-bukrs_x  = ls_excel-bukrs_x_o.  ELSE. ls_excel-bukrs_x  = ls_excel-bukrs_x_n.  ENDIF. "数据已经处理 ls_excel-bukrs_x 无用处
    IF ls_excel-bukrs_x_n  IS INITIAL. ls_excel-buklx_x  = ls_excel-buklx_x_o.  ELSE. ls_excel-bukrs_x  = ls_excel-buklx_x_n.  ENDIF. "数据已经处理 ls_excel-bukrs_x 无用处
    IF ls_excel-werks_x_n  IS INITIAL. ls_excel-werks_x  = ls_excel-werks_x_o.  ELSE. ls_excel-werks_x  = ls_excel-werks_x_n.  ENDIF. "数据已经处理 ls_excel-bukrs_x 无用处
    IF ls_excel-werks_x_n  IS INITIAL. ls_excel-werpc_x  = ls_excel-werpc_x_o.  ELSE. ls_excel-werks_x  = ls_excel-werpc_x_n.  ENDIF. "数据已经处理 ls_excel-bukrs_x 无用处
    IF ls_excel-matnr_x_n  IS INITIAL. ls_excel-matnr_x  = ls_excel-matnr_x_o.  ELSE. ls_excel-matnr_x  = ls_excel-matnr_x_n.  ENDIF. "数据已经处理 ls_excel-bukrs_x 无用处
    IF ls_excel-zdffs_n    IS INITIAL. ls_excel-zdffs_i  = ls_excel-zdffs_o .   ELSE. ls_excel-zdffs_i  = ls_excel-zdffs_n.    ENDIF.
    IF ls_excel-zjzzd_n    IS INITIAL. ls_excel-zjzzd    = ls_excel-zjzzd_o .   ELSE. ls_excel-zjzzd    = ls_excel-zdffs_n.    ENDIF.
    IF ls_excel-zsqbm_xy_n IS INITIAL. ls_excel-zjzzd    = ls_excel-zsqbm_xy_o. ELSE. ls_excel-zjzzd    = ls_excel-zsqbm_xy_n. ENDIF.

    IF ls_excel-zpaytp_n IS INITIAL AND ls_excel-zflzff_n IS INITIAL.
      ls_excel-zpaytp    = ls_excel-zpaytp_o .
      ls_excel-zflzff    = ls_excel-zflzff_o .
    ELSE.
      ls_excel-zpaytp    = ls_excel-zpaytp_n.
      ls_excel-zflzff    = ls_excel-zflzff_n.
    ENDIF.
    IF ls_excel-zjzzd_n    IS INITIAL.ls_excel-zjzzd     = ls_excel-zjzzd_o .     ELSE. ls_excel-zjzzd  = ls_excel-zjzzd_n.        ENDIF.
    IF ls_excel-zsqbm_xy_n IS INITIAL.ls_excel-zsqbm_xy  = ls_excel-zsqbm_xy_o .  ELSE. ls_excel-zsqbm_xy  = ls_excel-zsqbm_xy_n.  ENDIF.


    CLEAR ls_zret0008.
    IF ls_excel-zdate_n     IS INITIAL. CLEAR:ls_excel-zdate_o.    ENDIF.
    IF ls_excel-zmwskz_n    IS INITIAL. CLEAR:ls_excel-zmwskz_o.   ENDIF.
    IF ls_excel-zflbz_n     IS INITIAL. CLEAR:ls_excel-zflbz_o.    ENDIF.
    IF ls_excel-zfljs_n     IS INITIAL. CLEAR:ls_excel-zfljs_o.    ENDIF.
    IF ls_excel-zzsbs_n     IS INITIAL. CLEAR:ls_excel-zzsbs_o.    ENDIF.
    IF ls_excel-zctgr_n     IS INITIAL. CLEAR:ls_excel-zctgr_o.    ENDIF.
    IF ls_excel-bukrs_x_n   IS INITIAL. CLEAR:ls_excel-bukrs_x_o.  ENDIF.
    IF ls_excel-bukrs_x_n   IS INITIAL. CLEAR:ls_excel-buklx_x_o.  ENDIF.
    IF ls_excel-werks_x_n   IS INITIAL. CLEAR:ls_excel-werks_x_o.  ENDIF.
    IF ls_excel-werks_x_n   IS INITIAL. CLEAR:ls_excel-werpc_x_o.  ENDIF.
    IF ls_excel-matnr_x_n   IS INITIAL. CLEAR:ls_excel-matnr_x_o.  ENDIF.
    IF ls_excel-zdffs_n     IS INITIAL. CLEAR:ls_excel-zdffs_o.    ENDIF.
    IF ls_excel-zjzzd_n     IS INITIAL. CLEAR:ls_excel-zjzzd_o.    ENDIF.
    IF ls_excel-zsqbm_xy_n  IS INITIAL. CLEAR:ls_excel-zsqbm_xy_o. ENDIF.
    IF ls_excel-zpaytp_n    IS INITIAL. CLEAR:ls_excel-zpaytp_o.   ENDIF.
    IF ls_excel-zflzff_n    IS INITIAL. CLEAR:ls_excel-zflzff_o.   ENDIF.
    IF ls_excel-zzlbm_x_n   IS INITIAL. CLEAR:ls_excel-zzlbm_x_o.  ENDIF.
    IF ls_excel-zqdbm_x_n   IS INITIAL. CLEAR:ls_excel-zqdbm_x_o.  ENDIF.
    IF ls_excel-zsqbm_xy_n  IS INITIAL. CLEAR:ls_excel-zsqbm_xy_o. ENDIF.


    APPEND ls_excel TO pt_excel.
  ENDLOOP.

*  PERFORM frm_set_seq(zbcs0001) TABLES pt_excel USING 'SEQ'.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_FLG_IMPT
*&      <-- GT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_pro_data_excel  USING    pv_flg_impt TYPE char2
                         CHANGING pt_excel  TYPE tt_excel.
  DATA:
    ls_zret0008 TYPE zret0008,
    ls_zret0006 TYPE zret0006,
    ls_zreta001 TYPE zreta001,
    ls_zreta002 TYPE zreta002,
    ls_excel_u  TYPE LINE OF tt_excel_u.

  IF pv_flg_impt = '01'.
    LOOP AT pt_excel INTO DATA(ls_excel).

      ls_excel-zflzff = |{ ls_excel-zflzff ALPHA = IN }|.

*        获取合同主数据
      CLEAR ls_zreta001.
      SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_excel-zht_id INTO @ls_zreta001.

      IF ls_excel-ekgrp IS INITIAL.
        ls_excel-ekgrp = ls_zreta001-ekgrp.
      ENDIF.
      IF ls_excel-zdffs_h IS INITIAL.
        ls_excel-zdffs_h = ls_zreta001-zdffs.
      ENDIF.
      IF ls_excel-zpayday IS INITIAL.
        ls_excel-zpayday = ls_zreta001-zpayday.
      ENDIF.


      IF ls_excel-zbegin IS INITIAL.
        ls_excel-zbegin = ls_zreta001-zbegin.
      ENDIF.

      IF ls_excel-zend IS INITIAL.
        ls_excel-zend = ls_zreta001-zend.
      ENDIF.


      ls_excel-zhtlx = ls_zreta001-zhtlx.
*      ls_excel-zbegin = ls_zreta001-zbegin.
*      ls_excel-zend = ls_zreta001-zend.
      ls_excel-ztmpid = ls_zreta001-ztmpid.
      ls_excel-zbukrs_ht = ls_zreta001-zbukrs.

      MODIFY pt_excel FROM ls_excel.
      CLEAR:ls_zreta001.
    ENDLOOP.

  ELSEIF pv_flg_impt = '02'.

    LOOP AT pt_excel INTO ls_excel.

      ls_excel-zflzff = |{ ls_excel-zflzff ALPHA = IN }|.

*      1先将EXCEL数据转移到临时工作区，待条款主数据完善后，2再将EXCEL数据回填
      CLEAR ls_excel_u.
      PERFORM frm_conver_excel_u USING 'B'
                                 CHANGING ls_excel_u
                                          ls_excel.
*        获取条款主数据
      CLEAR ls_zreta002.
      SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id INTO @ls_zreta002.
      MOVE-CORRESPONDING ls_zreta002 TO ls_excel.
      ls_excel-zdffs_h = ls_zreta002-zdffs.


*      2再将EXCEL数据回填
      PERFORM frm_conver_excel_u USING 'A'
                                 CHANGING ls_excel_u
                                          ls_excel.

*        获取合同主数据
      CLEAR ls_zreta001.
      SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_excel-zht_id INTO @ls_zreta001.
      ls_excel-zbukrs_ht = ls_zreta001-zbukrs.
      ls_excel-zhtlx = ls_zreta001-zhtlx.

      MODIFY pt_excel FROM ls_excel.
      CLEAR:ls_zreta002.
    ENDLOOP.


  ELSEIF pv_flg_impt = '03'.


    LOOP AT pt_excel INTO ls_excel.


*        获取条款主数据
      CLEAR ls_zreta002.
      SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id INTO @ls_zreta002.

*        获取合同主数据
      CLEAR ls_zreta001.
      SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_excel-zht_id INTO @ls_zreta001.

*        获取协议主数据
      CLEAR ls_zret0006.
      SELECT SINGLE * FROM zret0006 WHERE zxy_id = @ls_excel-zxy_id INTO @ls_zret0006.

      CLEAR ls_zret0008.
      SELECT SINGLE * FROM zret0008 WHERE zxy_id = @ls_excel-zxy_id INTO @ls_zret0008.

      ls_excel-zxybstyp = ls_zret0006-zxybstyp.

      ls_excel-zbukrs = ls_zret0006-zbukrs.

      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.

  ELSEIF pv_flg_impt = '04'.

    LOOP AT pt_excel INTO ls_excel.

*        获取条款主数据
      CLEAR ls_zreta002.
      SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id INTO @ls_zreta002.
      ls_excel-zht_id = ls_excel-zht_id.
      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_DATA_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_FLG_IMPT
*&      <-- GT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_check_data_excel  USING    pv_flg_impt  TYPE char2
                                    pv_pass      TYPE char1
                                  pt_bukrs_excel TYPE tt_bukrs
                                  pt_werks_excel TYPE tt_werks
                                  pt_matnr_excel TYPE tt_matnr
                                  pt_zzlbm_excel TYPE tt_t81
                                  pt_zqdbm_excel TYPE tt_t82
                         CHANGING pt_excel       TYPE tt_excel.
  DATA:
        lt_msglist TYPE scp1_general_errors.

  IF pv_flg_impt = '01'.
    PERFORM frm_check_data_excel_i   USING pt_bukrs_excel
                                           pt_werks_excel
                                           pt_matnr_excel
                                           pt_zzlbm_excel
                                           pt_zqdbm_excel
                                  CHANGING pt_excel
                                           lt_msglist.
  ELSEIF pv_flg_impt = '02'.
    PERFORM frm_check_data_excel_u   USING pv_pass
                                           pt_bukrs_excel
                                           pt_werks_excel
                                           pt_matnr_excel
                                           pt_zzlbm_excel
                                           pt_zqdbm_excel
                                  CHANGING pt_excel lt_msglist            .
  ELSEIF pv_flg_impt = '03'.
    PERFORM frm_check_data_excel_c   USING pt_bukrs_excel
                                           pt_werks_excel
                                           pt_matnr_excel
                                           pt_zzlbm_excel
                                           pt_zqdbm_excel
                                  CHANGING pt_excel
                                           lt_msglist.          .
  ELSEIF pv_flg_impt = '04'.
    PERFORM frm_check_data_excel_t CHANGING pt_excel lt_msglist            .
  ENDIF.

  IF pv_flg_impt = '01' OR pv_flg_impt = '02'.
*  检查字段属性信息
    PERFORM frm_check_data_attr_excel USING    pv_flg_impt CHANGING pt_excel lt_msglist.

  ENDIF.


  PERFORM frm_conver_msglist_2_msg USING lt_msglist CHANGING pt_excel.

ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_EXCEL_END
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GV_FLG_IMPT
*&      <-- GT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_pro_data_excel_end  USING    pv_flg_impt TYPE char2
                             CHANGING pt_excel  TYPE tt_excel.
  LOOP AT pt_excel INTO DATA(ls_excel).
    PERFORM frm_set_status  CHANGING ls_excel.
    ls_excel-zflzff = |{ ls_excel-zflzff ALPHA  = IN }|.
    MODIFY pt_excel FROM ls_excel.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_DATA_EXCEL_I
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_check_data_excel_i USING
                                      pt_bukrs   TYPE tt_bukrs
                                      pt_werks   TYPE tt_werks
                                      pt_matnr   TYPE tt_matnr
                                      pt_t81     TYPE tt_t81
                                      pt_t82     TYPE tt_t82
                             CHANGING pt_excel   TYPE tt_excel
                                      lt_msglist TYPE scp1_general_errors.

  DATA:lv_zsfsx_ht   TYPE zretcm13-zsfsx.
  DATA:lv_zsfsx_fllx TYPE zretcm13-zsfsx.
  DATA:lv_msgv1      TYPE scp1_general_error-msgv1.
  DATA:lv_bukrs      TYPE t001-bukrs.
  DATA:lt_split_table   TYPE TABLE OF string,
       lt_split_table_x TYPE TABLE OF string.


  LOOP AT pt_excel INTO DATA(ls_excel).

    CLEAR:lv_zsfsx_ht,lv_zsfsx_fllx.
    SELECT SINGLE zsfsx INTO lv_zsfsx_ht FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx
     WHERE zht_id = ls_excel-zht_id.

    SELECT  SINGLE zsfsx INTO  lv_zsfsx_fllx FROM zretcm17  WHERE zfllx = ls_excel-zfllx.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZHT_ID' CHANGING lt_msglist.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLLX' CHANGING lt_msglist.

    PERFORM frm_check_pub_excel  USING ls_excel  'EKGRP' CHANGING lt_msglist.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZCTGR' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZBUKRS' CHANGING lt_msglist.
*  PERFORM frm_check_pub_excel  USING ls_excel  'ZFLSQF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel_t  USING ls_excel lv_zsfsx_ht  'ZFLSQF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLZFF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZITEMTXT' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'MATNR' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZJE' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZDFFS' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZMWSKZ' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZPAYTP' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZDATE' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZBEGIN' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZEND' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLDFSJ' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZZSBS' CHANGING lt_msglist.
*    PERFORM frm_check_pub_excel  USING ls_excel  'BUKRS_X' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZZLBM_X' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZQDBM_X' CHANGING lt_msglist.

    IF ls_excel-zhscj IS NOT INITIAL.
      PERFORM frm_check_pub_excel  USING ls_excel  'ZHSCJ' CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zaccer IS NOT INITIAL.
      PERFORM frm_check_pub_excel  USING ls_excel  'ZACCER' CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zbegin > ls_excel-zend.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '结束日期必须大于开始日期'  CHANGING lt_msglist.
    ENDIF.

    PERFORM frm_check_zsfsx_fllx USING ls_excel lv_zsfsx_ht lv_zsfsx_fllx    CHANGING lt_msglist.

    IF ls_excel-bukrs_x IS INITIAL AND ls_excel-werks_x IS INITIAL  .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '组织数据公司和门店不能同时为空'  CHANGING lt_msglist.
    ENDIF.

    PERFORM frm_check_bukrs_x    USING ls_excel   CHANGING lt_msglist .

  ENDLOOP.

  SORT pt_bukrs.
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING ALL FIELDS.
  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    IF ls_bukrs-bukrs IS NOT INITIAL .
      PERFORM frm_check_bukrs(zbcs0001) USING ls_bukrs-bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_bukrs-zmdsx IS NOT INITIAL.
      IF ls_bukrs-zmdsx <> '0' AND ls_bukrs-zmdsx <> '1' AND ls_bukrs-zmdsx <> '2' .
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司属性值只能维护为0/1/2'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING ALL FIELDS.
  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-werks IS NOT INITIAL .
      PERFORM frm_check_werks2(zbcs0001) USING ls_werks-werks CHANGING lv_bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_werks-seq '门店不存在'  CHANGING lt_msglist.
      ELSE.
        READ TABLE pt_excel TRANSPORTING NO FIELDS WITH  KEY  seq = ls_werks-seq zbukrs = lv_bukrs.
        IF sy-subrc <> 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_werks-seq '门店所属公司和协议主体不一致!'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.

    IF ls_werks-exclude IS NOT INITIAL AND ls_werks-exclude <> 'X' .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_werks-seq '门店排除标识只能维护为X'  CHANGING lt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_matnr.
  DELETE ADJACENT DUPLICATES FROM pt_matnr COMPARING ALL FIELDS.
  LOOP AT pt_matnr INTO DATA(ls_matnr).
    IF ls_matnr-matnr IS NOT INITIAL .
      PERFORM frm_check_matnr(zbcs0001) USING ls_matnr-matnr  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_matnr-seq '物料编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    IF ls_t81-zzlbm IS NOT INITIAL .
      PERFORM frm_check_zzlbm(zbcs0001) USING ls_t81-zzlbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    IF ls_t82-zqdbm IS NOT INITIAL .
      PERFORM frm_check_zqdbm(zbcs0001) USING ls_t82-zqdbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  PERFORM frm_check_data_org_pd USING pt_excel
                                      pt_bukrs
                                      pt_werks
                             CHANGING lt_msglist.

ENDFORM.

FORM frm_check_data_org_pd USING pt_data TYPE  tt_excel
                                  pt_bukrs TYPE  tt_bukrs
                                  pt_werks TYPE  tt_werks
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
    lt_bukrs TYPE tt_bukrs,
    lt_werks TYPE tt_werks,
    ls_bukrs TYPE ty_bukrs,
    ls_werks TYPE ty_werks.

  LOOP AT pt_data INTO DATA(ls_data).

    CLEAR:
    lt_bukrs ,
    lt_werks .

    DATA(lt_bukrs_tmp) = pt_bukrs[].
    DATA(lt_werks_tmp) = pt_werks[].

    DELETE lt_bukrs_tmp WHERE seq NE ls_data-seq.
    DELETE lt_werks_tmp WHERE seq NE ls_data-seq.

    MOVE-CORRESPONDING lt_bukrs_tmp TO lt_bukrs.
    MOVE-CORRESPONDING lt_werks_tmp TO lt_werks.

    PERFORM frm_check_zzz_new_pd USING   lt_bukrs
                                         lt_werks
                                         ls_data-seq
                              CHANGING   pt_msglist[].

  ENDLOOP.

ENDFORM.

FORM frm_check_data_org_pd_c USING pt_data TYPE  tt_excel
                                  pt_bukrs TYPE  tt_bukrs
                                  pt_werks TYPE  tt_werks
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
    lt_bukrs TYPE tt_bukrs,
    lt_werks TYPE tt_werks,
    ls_bukrs TYPE ty_bukrs,
    ls_werks TYPE ty_werks.

  LOOP AT pt_data INTO DATA(ls_data).

    CLEAR:
    lt_bukrs ,
    lt_werks .

    DATA(lt_bukrs_tmp) = pt_bukrs[].
    DATA(lt_werks_tmp) = pt_werks[].

    DELETE lt_bukrs_tmp WHERE seq NE ls_data-seq.
    DELETE lt_werks_tmp WHERE seq NE ls_data-seq.



    MOVE-CORRESPONDING lt_bukrs_tmp TO lt_bukrs.
    MOVE-CORRESPONDING lt_werks_tmp TO lt_werks.

    "公司门店检查的时候,
    "只调整公司,需要把原始门店获取出来一并检查,
    "只调整门店,需要把原始公司获取出来一并检查
    IF ls_data-zxy_id IS NOT INITIAL.
      IF  lt_bukrs[] IS INITIAL AND ls_data-bukrs_x_n <> 'Q'  AND  lt_werks[] IS NOT INITIAL .

        SELECT zzzid AS bukrs
               zmdsx AS zmdsx
               zzzpc AS exclude
          INTO CORRESPONDING FIELDS OF TABLE lt_bukrs
          FROM zret0014
         WHERE zxy_id = ls_data-zxy_id AND zzzlx = 'A'.
      ENDIF.
      IF  lt_bukrs[] IS NOT INITIAL  AND  lt_werks[] IS  INITIAL AND  ls_data-bukrs_x_n <> 'Q' .

        SELECT zzzid AS werks
               zzzpc AS exclude
          INTO CORRESPONDING FIELDS OF TABLE lt_werks
          FROM zret0014
         WHERE zxy_id = ls_data-zxy_id AND zzzlx = 'S'.
      ENDIF.
    ENDIF.

    IF lines( lt_bukrs ) > 1.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_data-seq '公司代码列维护时,需和协议主体保持一致!'  CHANGING pt_msglist.
    ELSEIF lines( lt_bukrs ) = 1 AND lt_bukrs[ 1 ]-bukrs  <> ls_data-zbukrs.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_data-seq '公司代码列维护时,需和协议主体保持一致!'  CHANGING pt_msglist.
    ELSE.
      SELECT SINGLE zrev010_wrk_ddl~bukrs
        FROM zrev010_wrk_ddl
       INNER JOIN @lt_werks AS wrk ON zrev010_wrk_ddl~werks = wrk~werks
       WHERE zrev010_wrk_ddl~bukrs <> @ls_data-zbukrs
        INTO @DATA(lv_bukrs).
      IF sy-subrc = 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_data-seq '门店所属公司和协议主体不一致!'  CHANGING pt_msglist.
      ENDIF.
    ENDIF.

    PERFORM frm_check_zzz_new_pd USING   lt_bukrs
                                         lt_werks
                                         ls_data-seq
                              CHANGING   pt_msglist[].

  ENDLOOP.

ENDFORM.



FORM frm_check_data_excel_u USING pv_pass     TYPE char1
                                   pt_bukrs   TYPE tt_bukrs
                                   pt_werks   TYPE tt_werks
                                   pt_matnr   TYPE tt_matnr
                                   pt_t81     TYPE tt_t81
                                   pt_t82     TYPE tt_t82
                          CHANGING pt_excel   TYPE tt_excel
                                   lt_msglist TYPE scp1_general_errors.

  DATA:lv_zsfsx TYPE zretcm13-zsfsx.
  DATA:lv_zlock      TYPE c.
  DATA:lv_sylsd      TYPE datum.
  DATA:lv_bukrs      TYPE t001-bukrs.

  PERFORM frm_get_zlock(zre0001) CHANGING lv_zlock IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊
  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊

  LOOP AT pt_excel INTO DATA(ls_excel).

    CLEAR:lv_zsfsx.
    SELECT SINGLE zsfsx INTO lv_zsfsx FROM zretcm13
     INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx
     WHERE zht_id = ls_excel-zht_id.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZTK_ID' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZCTGR' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZBUKRS' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLSQF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel_t  USING ls_excel lv_zsfsx  'ZFLSQF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLZFF' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZITEMTXT' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'MATNR' CHANGING lt_msglist.
    IF pv_pass = ''.
      PERFORM frm_check_pub_excel  USING ls_excel  'ZJE' CHANGING lt_msglist.
    ENDIF.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZDFFS' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZMWSKZ' CHANGING lt_msglist.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZPAYTP' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZDATE' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZBEGIN' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZEND'  CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLDFSJ' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZZSBS'  CHANGING lt_msglist.
*    PERFORM frm_check_pub_excel  USING ls_excel  'BUKRS_X' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZZLBM_X' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZQDBM_X' CHANGING lt_msglist.

    "ERP-17167-月结锁定期间---新月份返利的计算与分摊
    IF lv_zlock = 'Y' AND ls_excel-zbegin < lv_sylsd .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '月结锁定期不可导入月结期间条款或协议'  CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zbegin > ls_excel-zend.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '结束日期必须大于开始日期'  CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-bukrs_x IS INITIAL AND ls_excel-werks_x IS INITIAL  .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '组织数据公司和门店不能同时为空'  CHANGING lt_msglist.
    ENDIF.

    PERFORM frm_check_bukrs_x    USING ls_excel   CHANGING lt_msglist .

  ENDLOOP.

  SORT pt_bukrs.
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING ALL FIELDS.
  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    IF ls_bukrs-bukrs IS NOT INITIAL .
      PERFORM frm_check_bukrs(zbcs0001) USING ls_bukrs-bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_bukrs-zmdsx IS NOT INITIAL.
      IF ls_bukrs-zmdsx <> '0' AND ls_bukrs-zmdsx <> '1' AND ls_bukrs-zmdsx <> '2' .
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司属性值只能维护为0/1/2'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING ALL FIELDS.
  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-werks IS NOT INITIAL .
      PERFORM frm_check_werks2(zbcs0001) USING ls_werks-werks CHANGING lv_bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_coll_msg_3(zbcs0001) USING  ls_werks-seq '门店不存在'  CHANGING lt_msglist.
      ELSE.
        READ TABLE pt_excel TRANSPORTING NO FIELDS WITH  KEY  seq = ls_werks-seq zbukrs = lv_bukrs.
        IF sy-subrc <> 0.
          PERFORM frm_coll_msg_3(zbcs0001) USING  ls_werks-seq '门店所属公司和协议主体不一致!'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.

    IF ls_werks-exclude IS NOT INITIAL AND ls_werks-exclude <> 'X' .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_werks-seq '门店排除标识只能维护为X'  CHANGING lt_msglist.
    ENDIF.
  ENDLOOP.

  SORT pt_matnr.
  DELETE ADJACENT DUPLICATES FROM pt_matnr COMPARING ALL FIELDS.
  LOOP AT pt_matnr INTO DATA(ls_matnr).
    IF ls_matnr-matnr IS NOT INITIAL .
      PERFORM frm_check_matnr(zbcs0001) USING ls_matnr-matnr  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_matnr-seq '物料编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    IF ls_t81-zzlbm IS NOT INITIAL .
      PERFORM frm_check_zzlbm(zbcs0001) USING ls_t81-zzlbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    IF ls_t82-zqdbm IS NOT INITIAL .
      PERFORM frm_check_zqdbm(zbcs0001) USING ls_t82-zqdbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  PERFORM frm_check_data_org_pd USING pt_excel
                                      pt_bukrs
                                      pt_werks
                             CHANGING lt_msglist.

ENDFORM.

FORM frm_check_data_excel_c  USING
                                      pt_bukrs   TYPE tt_bukrs
                                      pt_werks   TYPE tt_werks
                                      pt_matnr   TYPE tt_matnr
                                      pt_t81     TYPE tt_t81
                                      pt_t82     TYPE tt_t82
                             CHANGING pt_excel   TYPE tt_excel
                                      lt_msglist TYPE scp1_general_errors.

  DATA:lv_zlock      TYPE c.
  DATA:lv_sylsd      TYPE datum.

  PERFORM frm_get_zlock(zre0001) CHANGING lv_zlock IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊
  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊

  SELECT
    i~zxy_id,
    COUNT(*)  AS num
    FROM @pt_excel AS i
    GROUP BY i~zxy_id
    INTO TABLE @DATA(lt_excel_tmp).
  DELETE lt_excel_tmp WHERE num <= 1.

  SORT lt_excel_tmp BY zxy_id.
  LOOP AT pt_excel INTO DATA(ls_excel).
    READ TABLE lt_excel_tmp TRANSPORTING NO FIELDS WITH KEY zxy_id = ls_excel-zxy_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '同一个协议号码，模板里只能有一条数据'  CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zxybstyp NE 'F' .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '协议类型必须为F类型'  CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zje <= 0.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '金额必须大于0'  CHANGING lt_msglist.
    ENDIF.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZXY_ID' CHANGING lt_msglist.

    "ERP-17167-月结锁定期间---新月份返利的计算与分摊
    IF lv_zlock = 'Y'.
      SELECT SINGLE zbegin INTO @DATA(lv_zbegin) FROM zret0006 WHERE zxy_id = @ls_excel-zxy_id.
      IF sy-subrc = 0 AND lv_zbegin < lv_sylsd.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '月结锁定期不可调整月结期间条款或协议'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZDATE' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'MATNR' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZMWSKZ' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZPAYTP' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZPAYTP_LOGIC' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZFLZFF' CHANGING lt_msglist.

    IF ls_excel-zzsbs <> 'Q' ."Q 修改时，Q表示全部，不校验域值，存储落表转为空
      PERFORM frm_check_pub_excel  USING ls_excel  'ZZSBS' CHANGING lt_msglist.
    ENDIF.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZCTGR' CHANGING lt_msglist.

    IF ls_excel-zdffs_i NE '' .
      IF ls_excel-zdffs_i NE 'A' AND ls_excel-zdffs_i NE 'O' AND ls_excel-zdffs_i NE 'C'.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '兑付方式只能填写 A,O,C'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_excel-zdffs_i EQ 'A' OR ls_excel-zdffs_i EQ 'C' .
      IF ls_excel-zmwskz NE 'X3'.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '兑付方式为A和C时，税码必须为X3'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_excel-zdffs_i EQ 'O' .
      IF ls_excel-zmwskz(1) NE 'J'.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '兑付方式为O时，只能选择进项税码'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_bukrs.
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING ALL FIELDS.
  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    IF ls_bukrs-bukrs IS NOT INITIAL .
      PERFORM frm_check_bukrs(zbcs0001) USING ls_bukrs-bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_bukrs-zmdsx IS NOT INITIAL.
      IF ls_bukrs-zmdsx <> '0' AND ls_bukrs-zmdsx <> '1' AND ls_bukrs-zmdsx <> '2' .
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '公司属性值只能维护为0/1/2'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING ALL FIELDS.
  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-werks IS NOT INITIAL .
      PERFORM frm_check_werks(zbcs0001) USING ls_werks-werks  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_bukrs-seq '门店不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_werks-exclude IS NOT INITIAL AND ls_werks-exclude <> 'X' .
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_werks-seq '门店排除标识只能维护为X'  CHANGING lt_msglist.
    ENDIF.

  ENDLOOP.



  SORT pt_matnr.
  DELETE ADJACENT DUPLICATES FROM pt_matnr COMPARING ALL FIELDS.
  LOOP AT pt_matnr INTO DATA(ls_matnr).
    IF ls_matnr-matnr IS NOT INITIAL .
      PERFORM frm_check_matnr(zbcs0001) USING ls_matnr-matnr  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_matnr-seq '物料编码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    IF ls_t81-zzlbm IS NOT INITIAL .
      PERFORM frm_check_zzlbm(zbcs0001) USING ls_t81-zzlbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
*        PERFORM frm_write_msg(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    IF ls_t82-zqdbm IS NOT INITIAL .
      PERFORM frm_check_zqdbm(zbcs0001) USING ls_t82-zqdbm  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.
*      PERFORM frm_write_msg(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.

      ENDIF.
    ENDIF.
  ENDLOOP.

  PERFORM frm_check_data_org_pd_c   USING pt_excel
                                        pt_bukrs
                                        pt_werks
                               CHANGING lt_msglist.

ENDFORM.


FORM frm_check_data_excel_t  CHANGING pt_excel  TYPE tt_excel
                                      lt_msglist TYPE scp1_general_errors.

  DATA:lv_zlock      TYPE c.
  DATA:lv_sylsd      TYPE datum.

  PERFORM frm_get_zlock(zre0001) CHANGING lv_zlock IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊
  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND."ERP-17167-月结锁定期间---新月份返利的计算与分摊

  LOOP AT pt_excel INTO DATA(ls_excel).
*    PERFORM frm_check_pub_excel  USING ls_excel  'ZTK_ID' CHANGING lt_msglist.

    PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-ztk_id    '条款编码' CHANGING lt_msglist .


    PERFORM frm_check_ztk_id(zbcs0001) USING ls_excel-ztk_id.
    IF sy-subrc NE 0.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '条款编码不存在'  CHANGING lt_msglist.
    ENDIF.

*    SELECT SINGLE b~zxybstyp FROM zreta002 AS a JOIN zret0002 AS b
*                                                  ON a~zfllx = b~zfllx
*                                                WHERE a~ztk_id = @ls_excel-ztk_id
*                                              INTO @DATA(lv_zxybstyp).
*    IF lv_zxybstyp NE 'F' AND lv_zxybstyp NE 'Q'.
*      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '只能更改固定类条款'  CHANGING lt_msglist.
*    ENDIF.
*    CLEAR lv_zxybstyp.
*
*    IF ls_excel-zfllx IS NOT INITIAL.
*      SELECT SINGLE b~zxybstyp FROM zret0002 AS b
*                                                  WHERE b~zfllx = @ls_excel-zfllx
*                                                INTO @lv_zxybstyp.
*      IF lv_zxybstyp NE 'F' AND lv_zxybstyp NE 'Q'.
*        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '只能更改固定类条款'  CHANGING lt_msglist.
*      ENDIF.
*      CLEAR lv_zxybstyp.
*
*    ENDIF.
    IF ls_excel-zfllx IS NOT INITIAL.
      PERFORM frm_check_zfllx USING ls_excel-zfllx.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '返利类型不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    PERFORM frm_check_pub_excel  USING ls_excel  'EKGRP' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZDFFS' CHANGING lt_msglist.

    PERFORM frm_check_pub_excel  USING ls_excel  'ZBEGIN' CHANGING lt_msglist.
    PERFORM frm_check_pub_excel  USING ls_excel  'ZEND' CHANGING lt_msglist.

    IF ls_excel-zhscj IS NOT INITIAL  .
      PERFORM frm_check_pub_excel  USING ls_excel  'ZHSCJ' CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zaccer IS NOT INITIAL.
      PERFORM frm_check_pub_excel  USING ls_excel  'ZACCER' CHANGING lt_msglist.
    ENDIF.

    IF ls_excel-zfldfsj IS NOT INITIAL.
      PERFORM frm_check_pub_excel  USING ls_excel  'ZFLDFSJ' CHANGING lt_msglist.
    ENDIF.

    "ERP-17167-月结锁定期间---新月份返利的计算与分摊
    IF lv_zlock = 'Y'.
      SELECT SINGLE zbegin INTO @DATA(lv_zbegin) FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id.
      IF sy-subrc = 0  AND  lv_zbegin < lv_sylsd.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '月结锁定期不可修改月结期间条款或协议'  CHANGING lt_msglist.
      ENDIF.

      IF  ls_excel-zbegin > lv_sylsd .
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '月结锁定期不可调整条款开始结束日期到月结期间内'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_excel-zbegin > ls_excel-zend.
      PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '结束日期必须大于开始日期'  CHANGING lt_msglist.
    ENDIF.
  ENDLOOP.
ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_PUB_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL
*&      --> P_
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_pub_excel  USING    ls_excel  TYPE LINE OF tt_excel
                                   pv_flg     TYPE char20
                             CHANGING lt_msglist  TYPE scp1_general_errors.
  DATA:
    lv_flg(1).

  CASE pv_flg.
    WHEN 'ZHT_ID'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zht_id    '合同编码' CHANGING lt_msglist .

      PERFORM frm_check_zht_id USING ls_excel-zht_id.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '合同编码不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZXY_ID'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zxy_id    '协议编码' CHANGING lt_msglist .

      PERFORM frm_check_zxy_id USING ls_excel-zxy_id.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '协议编码不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZTK_ID'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-ztk_id    '条款编码' CHANGING lt_msglist .

      PERFORM frm_check_ztk_id_impt USING ls_excel-ztk_id.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '条款编码不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZFLLX'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zfllx    '返利类型' CHANGING lt_msglist .
      PERFORM frm_check_zfllx USING ls_excel-zfllx.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '返利类型不存在'  CHANGING lt_msglist.
      ENDIF.

      IF ls_excel-zxybstyp NE 'F'.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '返利类型不等于 F固定金额 '  CHANGING lt_msglist.
      ENDIF.
      PERFORM frm_check_zfllx_zxybstyp USING ls_excel-zfllx ls_excel-zxybstyp.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '返利类型与协议类型不匹配'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'EKGRP'.

      IF ls_excel-ekgrp IS NOT INITIAL.
        PERFORM frm_check_ekgrp(zbcs0001) USING ls_excel-ekgrp .
        IF sy-subrc NE 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '采购组不存在'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.

    WHEN 'ZCTGR'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zctgr    '组织级别' CHANGING lt_msglist .
      PERFORM frm_check_domname(zbcs0001) USING ls_excel-zctgr 'ZRED_CTGR' .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '组织级别不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZBUKRS'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zbukrs    '协议主体' CHANGING lt_msglist .
      PERFORM frm_check_bukrs(zbcs0001) USING ls_excel-zbukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '协议主体不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZFLSQF'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zflsqf    '收款方' CHANGING lt_msglist .
      PERFORM frm_check_bukrs(zbcs0001) USING ls_excel-zflsqf  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '收款方不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZFLZFF'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zflzff    '付款方' CHANGING lt_msglist .
      PERFORM frm_check_lifnr(zbcs0001) USING ls_excel-zflzff  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '付款方不存在'  CHANGING lt_msglist.
      ENDIF.

      PERFORM frm_check_status_zflzff USING ls_excel-zflzff .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '外部支付方已被冻结或删除，请检查主数据。'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZITEMTXT'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zitemtxt    '协议行描述' CHANGING lt_msglist .

    WHEN 'MATNR'.
      IF ls_excel-matnr IS NOT INITIAL.
        PERFORM frm_check_matnr(zbcs0001) USING ls_excel-matnr .
        IF sy-subrc NE 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '商品不存在'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.

    WHEN 'ZJE'.
*      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zje    '金额' CHANGING lt_msglist .

    WHEN 'ZDFFS'.
      IF ls_excel-zdffs_h IS NOT INITIAL.

        PERFORM frm_check_domname(zbcs0001) USING ls_excel-zdffs_h 'ZREM_ZDFFS' .
        IF sy-subrc NE 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '兑付方式不存在'  CHANGING lt_msglist.
        ENDIF.

      ENDIF.

      IF ls_excel-zdffs_i IS NOT INITIAL.
        PERFORM frm_check_domname(zbcs0001) USING ls_excel-zdffs_i 'ZREM_ZDFFS' .
        IF sy-subrc NE 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '兑付方式不存在'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    WHEN 'ZMWSKZ'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zmwskz    '税码' CHANGING lt_msglist .
      PERFORM frm_check_mwskz(zbcs0001) USING ls_excel-zmwskz .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '税码不存在'  CHANGING lt_msglist.
      ENDIF.


    WHEN 'ZPAYTP'.
      PERFORM frm_check_domname(zbcs0001) USING ls_excel-zpaytp 'ZRED_ZPAYTP' .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '付款方级别不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZPAYTP_LOGIC'.

      IF ls_excel-zflzff IS NOT INITIAL.
        PERFORM frm_get_zghf_attr USING ls_excel-zflzff CHANGING lv_flg.
        IF lv_flg EQ 'I'.
          IF ls_excel-zpaytp IS INITIAL.
            PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '内部支付方的支付方级别不可是空'  CHANGING lt_msglist.
          ENDIF.
        ELSEIF lv_flg EQ 'E'.
          IF ls_excel-zpaytp EQ 'A' OR ls_excel-zpaytp EQ 'B'.
            PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '外部支付方，则支付方级别不可以是A或B'  CHANGING lt_msglist.
          ENDIF.
        ENDIF.
      ENDIF.

    WHEN 'ZDATE'.
      PERFORM frm_check_date(zbcs0001) USING ls_excel-zdate  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '归属日期填写错误'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZBEGIN'.
      PERFORM frm_check_date(zbcs0001) USING ls_excel-zbegin  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '开始日期填写错误'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZEND'.
      PERFORM frm_check_date(zbcs0001) USING ls_excel-zend  .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '结束日期填写错误'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZFLDFSJ'.

      PERFORM frm_check_zfldfsj USING ls_excel-zfldfsj.
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '返利兑付时间不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN 'ZZSBS'.
      PERFORM frm_check_domname(zbcs0001) USING ls_excel-zzsbs 'ZREM_ZZSBS' .
      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '专属标识不存在'  CHANGING lt_msglist.
      ENDIF.
    WHEN 'BUKRS_X'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-bukrs_x    '公司代码' CHANGING lt_msglist .
    WHEN 'ZZLBM_X'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zzlbm_x    '子类编码' CHANGING lt_msglist .
    WHEN 'ZQDBM_X'.

      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zzlbm_x    '渠道编码' CHANGING lt_msglist .
    WHEN 'ZHSCJ'.
      IF ls_excel-zhscj = '1000004716' .
        IF ls_excel-zaccer IS INITIAL .
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '核算厂家为1000004716 ,核算厂家描述必输！'  CHANGING lt_msglist.
        ENDIF.
      ELSE.
        PERFORM frm_check_zhscj(zbcs0001) USING ls_excel-zhscj .
        IF sy-subrc NE 0.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '核算厂家不存在'  CHANGING lt_msglist.
        ENDIF.

      ENDIF.
    WHEN 'ZACCER'.
      IF ls_excel-zhscj <> '1000004716' .
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '输入核算厂家描述，核算厂家编码必须为 1000004716 ！'  CHANGING lt_msglist.
      ENDIF.
    WHEN OTHERS.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_PUB_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL
*&      --> P_
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_pub_excel_t    USING    ls_excel  TYPE LINE OF tt_excel
                                        pv_zsfsx
                                        pv_flg    TYPE char20
                              CHANGING lt_msglist TYPE scp1_general_errors.

  DATA: lv_flg(1).
  DATA:lv_sql_partner TYPE but000-partner.

  CASE pv_flg.
    WHEN 'ZFLSQF'.
      PERFORM frm_check_inital_2(zbcs0001) USING  ls_excel-seq ls_excel-zflsqf    '收款方' CHANGING lt_msglist .

      IF pv_zsfsx = '1'.
        PERFORM frm_check_bukrs(zbcs0001) USING ls_excel-zflsqf  .
      ELSE.
        CLEAR sy-subrc.
        lv_sql_partner =  ls_excel-zflsqf .
        lv_sql_partner = |{ lv_sql_partner ALPHA = IN }|.
        SELECT SINGLE  partner  FROM but000  WHERE partner = @lv_sql_partner
          INTO @DATA(lv_partner).
      ENDIF.

      IF sy-subrc NE 0.
        PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '收款方不存在'  CHANGING lt_msglist.
      ENDIF.

    WHEN OTHERS.
  ENDCASE.
ENDFORM.
FORM frm_check_zzlbm USING    pv_zzlbm . "TYPE t024-ekgrp.
  DATA:
        lv_zzlbm TYPE zretcm10-zzlbm.
  CLEAR sy-subrc.
  SELECT SINGLE zzlbm  INTO lv_zzlbm FROM zretcm10 WHERE zzlbm EQ pv_zzlbm .
ENDFORM.

FORM frm_check_zqdbm USING    pv_qdlbm . "TYPE t024-ekgrp.
  DATA:
        lv_zqdbm TYPE zretcm11-zqdbm.
  CLEAR sy-subrc.
  SELECT SINGLE zqdbm  INTO lv_zqdbm FROM zretcm11 WHERE zqdbm EQ pv_qdlbm .
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CONVER_MSGLIST_2_MSG
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LT_MSGLIST
*&      <-- PT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_conver_msglist_2_msg  USING    pt_msglist   TYPE scp1_general_errors
                               CHANGING pt_excel  TYPE tt_excel.

  DATA:
    lv_msg   TYPE char255,
    ls_excel TYPE LINE OF tt_excel.

  DATA(lt_msglist) = pt_msglist[].
  SORT lt_msglist BY msgv1.

  LOOP AT lt_msglist INTO DATA(ls_msglist).
    AT NEW msgv1.
      DATA(lv_flg_new) = 'X'.
    ENDAT.
    AT END OF msgv1.
      DATA(lv_flg_end) = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_msg.
    ENDIF.
    lv_msg = lv_msg && '/' && ls_msglist-msgv2.

    IF lv_flg_end = 'X'.
      CLEAR ls_excel.
      ls_excel-ztype_excel = 'E'.
      ls_excel-zmsg_excel = lv_msg.
      MODIFY pt_excel FROM ls_excel TRANSPORTING ztype_excel zmsg_excel WHERE seq = ls_msglist-msgv1.
    ENDIF.
    CLEAR:lv_flg_new,lv_flg_end.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_STATUS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_set_status  CHANGING ls_excel  TYPE LINE OF tt_excel.
  IF ls_excel-ztype_excel = 'E' OR ls_excel-ztype_impt = 'E'.
    ls_excel-status = '1'.
  ELSEIF ls_excel-ztype_excel = '' AND ls_excel-ztype_impt = ''.
    ls_excel-status = '2'.
  ELSEIF  ls_excel-ztype_impt = 'S'.
    ls_excel-status = '3'.
  ENDIF.
ENDFORM.

FORM frm_refresh_alv USING prf_alv TYPE REF TO cl_gui_alv_grid.
  DATA:
    ls_stable TYPE lvc_s_stbl,
    ls_layout TYPE lvc_s_layo.


  PERFORM frm_set_layout CHANGING ls_layout.
  CALL METHOD prf_alv->set_frontend_layout
    EXPORTING
      is_layout = ls_layout.

  ls_stable-row = ls_stable-col = 'X'.
  CALL METHOD prf_alv->refresh_table_display
    EXPORTING
      is_stable = ls_stable
*     I_SOFT_REFRESH = ''
    EXCEPTIONS
      finished  = 1
      OTHERS    = 2.
  IF sy-subrc <> 0.

  ENDIF.
ENDFORM.

FORM frm_refresh_alv_excel USING prf_alv TYPE REF TO cl_gui_alv_grid.
  DATA:
    ls_stable TYPE lvc_s_stbl,
    ls_layout TYPE lvc_s_layo.


  PERFORM frm_set_layout_excel CHANGING ls_layout.
  CALL METHOD prf_alv->set_frontend_layout
    EXPORTING
      is_layout = ls_layout.

  ls_stable-row = ls_stable-col = 'X'.
  CALL METHOD prf_alv->refresh_table_display
    EXPORTING
      is_stable = ls_stable
*     I_SOFT_REFRESH = ''
    EXCEPTIONS
      finished  = 1
      OTHERS    = 2.
  IF sy-subrc <> 0.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  FRM_DISPLAY
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_GO_ALV  TEXT
*----------------------------------------------------------------------*
FORM frm_alv_display_excel  USING  prf_alv TYPE REF TO cl_gui_alv_grid.

  DATA: lt_fieldcat TYPE TABLE OF lvc_s_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_layout   TYPE TABLE OF lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  PERFORM frm_set_catalog_excel CHANGING lt_fieldcat.

  PERFORM frm_set_layout_excel CHANGING ls_layout.

  PERFORM frm_set_ex_fcode_excel TABLES lt_ex_fcode.

  PERFORM frm_set_variant_excel CHANGING  ls_variant.

*ALV显示

  PERFORM frm_set_alv USING
                            'GT_EXCEL'
                            ls_variant
                            ls_layout
                            lt_ex_fcode
                      CHANGING
                            lt_fieldcat
                            prf_alv.

*  事件注册
  DATA:
  lv_objid TYPE char10 VALUE 'MAIN'.
  DATA lrf_event TYPE REF TO sec_lcl_event_receiver.


  CREATE OBJECT lrf_event
    EXPORTING
      i_objid = lv_objid.


  PERFORM frm_set_event_handler USING lv_objid
                                CHANGING lrf_event
                                  prf_alv.
ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LS_VARIANT  TEXT
*      -->P_LS_LAYOUT  TEXT
*      -->P_LT_EX_FCODE  TEXT
*      <--P_LT_FIELDCAT  TEXT
*      <--P_'GT_DATA_T'  TEXT
*----------------------------------------------------------------------*
FORM frm_set_alv  USING    pv_tname TYPE any
                           ps_variant TYPE disvariant
                           ps_layout TYPE lvc_s_layo
                           pt_ex_fcode TYPE ui_functions
                  CHANGING pt_fieldcat TYPE lvc_t_fcat
                           prf_alv_grid TYPE REF TO cl_gui_alv_grid.


  DATA lv_table  LIKE feld-name.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.

  CONCATENATE pv_tname '[]' INTO lv_table.
  ASSIGN (lv_table) TO <lt_table>.

  CALL METHOD prf_alv_grid->set_table_for_first_display
    EXPORTING
*     I_BUFFER_ACTIVE               =
*     I_BYPASSING_BUFFER            =
*     I_CONSISTENCY_CHECK           =
*     I_STRUCTURE_NAME              = 'IT_ITAB'
      is_variant                    = ps_variant
      i_save                        = 'A'
*     I_DEFAULT                     = 'X'
      is_layout                     = ps_layout
*     IS_PRINT                      =
*     IT_SPECIAL_GROUPS             =
      it_toolbar_excluding          = pt_ex_fcode
*     IT_HYPERLINK                  =
*     IT_ALV_GRAPHICS               =
*     IT_EXCEPT_QINFO               =
*     IR_SALV_ADAPTER               =
    CHANGING
      it_outtab                     = <lt_table>
      it_fieldcatalog               = pt_fieldcat
*     IT_SORT                       =
*     IT_FILTER                     =
    EXCEPTIONS
      invalid_parameter_combination = 1
      program_error                 = 2
      too_many_lines                = 3
      OTHERS                        = 4.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
               WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.

FORM frm_set_catalog_excel  CHANGING   pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.
  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-fieldname = &2.                 "
    ls_fieldcat-coltext = &3.                 "

    CASE &2.

      WHEN 'MATNR'  .
        ls_fieldcat-ref_field = 'MATNR'.
        ls_fieldcat-ref_table = 'MARA'.
        WHEN 'SEL_MAN'.
          ls_fieldcat-edit = 'X'.
          ls_fieldcat-checkbox = 'X'.
      WHEN OTHERS.
    ENDCASE.

    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.

  add_fcat  '10' 'SEL_MAN'     '选择'  .
  add_fcat  '10' 'ZMSG_EXCEL'  '消息'  .
  IF gv_flg_impt = '01'.
    add_fcat  '10' 'ZHT_ID'    '合同编码'  .
    add_fcat  '10' 'ZTK_ID'    '条款编码'  .
    add_fcat  '10' 'ZFLLX'     '返利类型'  .
    add_fcat  '10' 'ZXYBSTYP'  '协议类型'  .
    add_fcat  '10' 'EKGRP'     '采购组'  .
    add_fcat  '10' 'ZDFFS_H'   '兑付方式'  .
    add_fcat  '10' 'ZPAYDAY'   '付款期间'  .
    add_fcat  '10' 'ZTK_TXT'   '条款描述'  .
    add_fcat  '10' 'ZLCBH '    'OA流程编码'.
    add_fcat  '10' 'ZPFFL '    '批发返利'  .
    add_fcat  '10' 'ZSQBM '    '收取部门'  .
    add_fcat  '10' 'ZPTBM '    '平台编码'  .
    add_fcat  '10' 'ZZJZR '    '收款截止日'  .
    add_fcat  '10' 'ZBEGIN'    '开始时间'  .
    add_fcat  '10' 'ZEND'      '结束时间'  .
    add_fcat  '10' 'ZHSCJ'     '核算厂家'  .
    add_fcat  '10' 'ZACCER'    '核算厂家描述'  .
    add_fcat  '10' 'ZCGJL'     '采购经理'  .
    add_fcat  '10' 'ZCGZJ'     '采购总监'  .
    add_fcat  '10' 'ZFLDFSJ'   '返利兑付时间'  .

    add_fcat  '10' 'ZCTGR'     '组织级别'  .
    add_fcat  '10' 'ZBUKRS'    '协议主体'  .
    add_fcat  '10' 'ZFLSQF'    '收款方'  .
    add_fcat  '10' 'ZFLZFF'    '付款方'  .
    add_fcat  '10' 'ZPAYTP'    '付款方级别'  .
    add_fcat  '10' 'ZDFFS_I'   '兑付方式'  .
    add_fcat  '10' 'ZITEMTXT'  '协议行描述'  .
    add_fcat  '10' 'ZDATE'     '归属日期'  .
    add_fcat  '10' 'MATNR_X'   '商品'  .
    add_fcat  '10' 'ZFLBZ'     '返利标准'  .
    add_fcat  '10' 'ZFLJS'     '返利基数'  .
    add_fcat  '10' 'ZJE'       '金额'  .
    add_fcat  '10' 'ZMWSKZ'    '税码'  .
    add_fcat  '10' 'ZXY_ID'    '协议号码'  .
    add_fcat  '10' 'BUKRS_X'   '公司代码'  .
    add_fcat  '10' 'BUKLX_X'   '公司属性'  .
    add_fcat  '10' 'WERKS_X'   '门店编码'  .
    add_fcat  '10' 'WERPC_X'   '门店排除'  .
    add_fcat  '10' 'ZZSBS'     '专属标识'  .
    add_fcat  '10' 'ZZLBM_X'   '子类编码'  .
    add_fcat  '10' 'ZQDBM_X'   '渠道编码'  .
    add_fcat  '10' 'ZJZZD'     '基准字段'  .
    add_fcat  '10' 'ZSQBM_XY'  '收取部门(协议级)'  .
  ELSEIF gv_flg_impt = '02'.
    add_fcat  '10' 'ZTK_ID'    '条款编码'  .
    add_fcat  '10' 'ZBEGIN'    '开始时间'  .
    add_fcat  '10' 'ZEND'      '结束时间'  .
    add_fcat  '10' 'ZCTGR'     '组织级别'  .
    add_fcat  '10' 'ZBUKRS'    '协议主体'  .
    add_fcat  '10' 'ZFLSQF'    '收款方'  .
    add_fcat  '10' 'ZFLZFF'    '付款方'  .
    add_fcat  '10' 'ZPAYTP'    '付款方级别'  .
    add_fcat  '10' 'ZDFFS_I'   '兑付方式'  .
    add_fcat  '10' 'ZITEMTXT'  '协议行描述'  .
    add_fcat  '10' 'ZDATE'     '归属日期'  .
    add_fcat  '10' 'MATNR_X'   '商品'  .
    add_fcat  '10' 'ZFLBZ'     '返利标准'  .
    add_fcat  '10' 'ZFLJS'     '返利基数'  .
*    add_fcat  '10' 'MATNR'    '商品'  .
    add_fcat  '10' 'ZJE'       '金额'  .
    add_fcat  '10' 'ZMWSKZ'    '税码'  .
    add_fcat  '10' 'ZXY_ID'    '协议号码'  .
    add_fcat  '10' 'BUKRS_X'   '公司代码'  .
    add_fcat  '10' 'BUKLX_X'   '公司属性'  .
    add_fcat  '10' 'WERKS_X'   '门店编码'  .
    add_fcat  '10' 'WERPC_X'   '门店排除'  .
    add_fcat  '10' 'ZZSBS'     '专属标识'  .
    add_fcat  '10' 'ZZLBM_X'   '子类编码'  .
    add_fcat  '10' 'ZQDBM_X'   '渠道编码'  .
    add_fcat  '10' 'ZJZZD'     '基准字段'  .
    add_fcat  '10' 'ZSQBM_XY'     '收取部门(协议级)'  .
  ELSEIF gv_flg_impt = '03'.
    add_fcat  '10' 'ZXY_ID'      '协议号码'  .
    add_fcat  '10' 'ZJE_O'       '金额（旧）'  .
    add_fcat  '10' 'ZJE_N'       '金额（新）'  .
    add_fcat  '10' 'ZPAYTP_O'    '付款方级别（旧）'  .
    add_fcat  '10' 'ZPAYTP_N'    '付款方级别（新）'  .
    add_fcat  '10' 'ZFLZFF_O'    '付款方（旧）'  .
    add_fcat  '10' 'ZFLZFF_N'    '付款方（新）'  .
    add_fcat  '10' 'ZDATE_O'     '归属日期（旧）'  .
    add_fcat  '10' 'ZDATE_N'     '归属日期（新）'  .
    add_fcat  '10' 'MATNR_X_O'   '商品（旧）'  .
    add_fcat  '10' 'MATNR_X_N'   '商品（新）'  .
    add_fcat  '10' 'ZMWSKZ_O'    '税码（旧）'  .
    add_fcat  '10' 'ZMWSKZ_N'    '税码（新）'  .
    add_fcat  '10' 'ZFLBZ_O'     '返利标准（旧）'  .
    add_fcat  '10' 'ZFLBZ_N'     '返利标准（新）'  .
    add_fcat  '10' 'ZFLJS_O'     '返利基数（旧）'  .
    add_fcat  '10' 'ZFLJS_N'     '返利基数（新）'  .
    add_fcat  '10' 'ZDFFS_O'     '兑付方式（旧）'  .
    add_fcat  '10' 'ZDFFS_N'     '兑付方式（新）'  .
    add_fcat  '10' 'BUKRS_X_O'   '公司代码（旧）'  .
    add_fcat  '10' 'BUKRS_X_N'   '公司代码（新）'  .
    add_fcat  '10' 'BUKLX_X_O'   '公司属性（旧）'  .
    add_fcat  '10' 'BUKLX_X_N'   '公司属性（新）'  .
    add_fcat  '10' 'WERKS_X_O'   '门店编码（旧）'  .
    add_fcat  '10' 'WERKS_X_N'   '门店编码（新）'  .
    add_fcat  '10' 'WERPC_X_O'   '门店排除（旧）'  .
    add_fcat  '10' 'WERPC_X_N'   '门店排除（新）'  .
    add_fcat  '10' 'ZZSBS_O'     '专属标识（旧）'  .
    add_fcat  '10' 'ZZSBS_N'     '专属标识（新）'  .
    add_fcat  '10' 'ZZLBM_X_O'   '子类编码（旧）'  .
    add_fcat  '10' 'ZZLBM_X_N'   '子类编码（新）'  .
    add_fcat  '10' 'ZQDBM_X_O'   '渠道编码（旧）'  .
    add_fcat  '10' 'ZQDBM_X_N'   '渠道编码（新）'  .
    add_fcat  '10' 'ZCTGR_O'     '组织级别（旧）'  .
    add_fcat  '10' 'ZCTGR_N'     '组织级别（新）'  .
    add_fcat  '10' 'ZJZZD_O'     '基准字段（旧）'  .
    add_fcat  '10' 'ZJZZD_N'     '基准字段（新）'  .
    add_fcat  '10' 'ZSQBM_XY_O'  '收取部门-协议级（旧）'  .
    add_fcat  '10' 'ZSQBM_XY_N'  '收取部门-协议级（新）'  .
  ELSEIF gv_flg_impt = '04'.

    add_fcat  '10' 'ZTK_ID'   '条款编码'  .
    add_fcat  '10' 'ZFLLX'    '返利类型'  .
    add_fcat  '10' 'EKGRP'    '采购组'  .
    add_fcat  '10' 'ZDFFS_H'  '兑付方式'  .
    add_fcat  '10' 'ZPAYDAY'  '付款期间'  .
    add_fcat  '10' 'ZBEGIN'   '开始时间'  .
    add_fcat  '10' 'ZEND'     '结束时间'  .
    add_fcat  '10' 'ZHSCJ'    '核算厂家'  .
    add_fcat  '10' 'ZACCER'   '核算厂家描述'  .
    add_fcat  '10' 'ZCGJL'    '采购经理'  .
    add_fcat  '10' 'ZCGZJ'    '采购总监'  .
    add_fcat  '10' 'ZFLDFSJ'  '返利兑付时间'  .
    add_fcat  '10' 'ZTK_TXT'  '条款描述'  .
    add_fcat  '10' 'ZLCBH '   'OA流程编码'  .
    add_fcat  '10' 'ZPFFL '   '批发返利'  .
    add_fcat  '10' 'ZSQBM '   '收取部门'  .
    add_fcat  '10' 'ZPTBM '   '平台编码'  .
    add_fcat  '10' 'ZZJZR '   '收款截止日'  .

  ENDIF..


ENDFORM.                    " FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout_excel CHANGING ps_layout TYPE lvc_s_layo.
  ps_layout-sel_mode = 'D'.
  ps_layout-zebra     = 'X'.
  ps_layout-cwidth_opt  = 'X'.
*  IF GV_FLG = '02'.
  ps_layout-excp_fname = 'STATUS'.
*  ENDIF.
*  PS_LAYOUT-CTAB_FNAME = 'CELLCOLOR'.
  ps_layout-box_fname = 'SEL'.


ENDFORM.                    "FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EVENT_HANDLER
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      <--P_LO_EVENT  TEXT
*      <--P_GO_ALV_TOP  TEXT
*----------------------------------------------------------------------*
FORM frm_set_event_handler USING pv_objid TYPE char10
                            CHANGING prf_event TYPE REF TO sec_lcl_event_receiver
                                     prf_grid  TYPE REF TO cl_gui_alv_grid.


  CALL METHOD prf_grid->set_ready_for_input
    EXPORTING
      i_ready_for_input = 1.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_enter.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_modified.

*  CREATE OBJECT PRF_EVENT.
  SET HANDLER prf_event->sec_handle_bef_user_command         FOR prf_grid.
  SET HANDLER prf_event->sec_handle_user_command             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_toolbar                  FOR prf_grid.
  SET HANDLER prf_event->sec_handle_hotspot_click            FOR prf_grid.
  SET HANDLER prf_event->sec_handle_double_click             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed_fin         FOR prf_grid.


  CALL METHOD prf_grid->set_toolbar_interactive.

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_EXCLUDE TEXT
*----------------------------------------------------------------------*
FORM frm_set_ex_fcode_excel TABLES pt_exclude.
*  DATA LS_EXCLUDE TYPE UI_FUNC.
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_INSERT_ROW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_DELETE_ROW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_REFRESH.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_DETAIL.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_MB_PASTE.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_UNDO.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_CUT.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_COPY.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_COPY_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_APPEND_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_MOVE_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_PRINT .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_PRINT_PREV .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_GRAPH .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_CHECK .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_MB_VIEW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_HELP .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_INFO .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.

ENDFORM.                    "FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant_excel CHANGING ps_variant TYPE disvariant.
  ps_variant-report = sy-repid.
  ps_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT

FORM frm_check_changed_data_excel USING prf_alv TYPE REF TO cl_gui_alv_grid..
  IF prf_alv IS NOT INITIAL.
    CALL METHOD prf_alv->check_changed_data.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_DATA_ATTR_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_FLG_IMPT
*&      <-- PT_EXCEL
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_data_attr_excel  USING    pv_flg_impt  TYPE char2
                                CHANGING pt_excel TYPE tt_excel
                                         lt_msglist TYPE scp1_general_errors.
  DATA:
    ls_zres0056 TYPE zres0056,
    lt_zrei0024 TYPE zrei0024.

  LOOP AT pt_excel INTO DATA(ls_excel).
    CLEAR: ls_zres0056,lt_zrei0024.

    ls_zres0056-zxybstyp = ls_excel-zxybstyp.
    ls_zres0056-ztktype = ''.
    ls_zres0056-zfllx = ls_excel-zfllx.
    IF pv_flg_impt = '01'.
      ls_zres0056-ztktype = 'N'.
    ELSEIF pv_flg_impt = '02'.
      ls_zres0056-ztktype = 'U'.
    ENDIF.

    PERFORM frm_get_data_screen_attr_s USING ls_zres0056
                                             'GS_TC02-MATNR'
                                       CHANGING lt_zrei0024.

    READ TABLE lt_zrei0024 INTO DATA(ls_zrei0024) INDEX 1.
    IF sy-subrc EQ 0.
      IF ls_zrei0024-zfdatb = '4'.
        IF ls_excel-matnr IS NOT INITIAL.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '商品编码必须为空'  CHANGING lt_msglist.
        ENDIF.
      ELSEIF ls_zrei0024-zfdatb = '2'.
        IF ls_excel-matnr IS  INITIAL.
          PERFORM frm_add_msg_2(zbcs0001) USING  ls_excel-seq '商品编码不能为空'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.

  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BF_IMPT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GT_EXCEL
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_check_bf_impt  USING    pt_excel TYPE tt_excel
                        CHANGING pv_mtype TYPE bapi_mtype
                                 pv_msg TYPE bapi_msg.

  READ TABLE pt_excel TRANSPORTING NO FIELDS WITH KEY sel_man = 'X'.
  IF sy-subrc NE 0.
    pv_mtype = 'E'.
    pv_msg = '请选择需要导入的数据！'.
  ENDIF.

  READ TABLE pt_excel TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' status = '1'.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '选择的数据存在错误，无法导入'.
  ENDIF.

  READ TABLE pt_excel TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' status = '3'.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '选择的数据已经导入，不能重复导入'.
  ENDIF.

ENDFORM.


FORM frm_data_impt_main  USING  pv_flg_impt TYPE char2
                                pv_pass TYPE char1
                                         pt_bukrs_excel TYPE tt_bukrs
                                         pt_werks_excel TYPE tt_werks
                                         pt_matnr_excel TYPE tt_matnr
                                         pt_zzlbm_excel TYPE tt_t81
                                         pt_zqdbm_excel TYPE tt_t82
                        CHANGING
                                 pt_excel TYPE tt_excel
                                 lv_mtype TYPE bapi_mtype
                                 lv_msg TYPE bapi_msg.

  DATA(lt_excel) = pt_excel[].
  DELETE lt_excel WHERE sel_man = ''.

  IF pv_flg_impt = '01' OR pv_flg_impt = '02'.
    PERFORM frm_data_impt_01_02  USING pv_flg_impt
                                       pv_pass
                                       pt_bukrs_excel
                                       pt_werks_excel
                                       pt_matnr_excel
                                       pt_zzlbm_excel
                                       pt_zqdbm_excel
                              CHANGING pt_excel
                                       lt_excel
                                       lv_mtype
                                       lv_msg.

    PERFORM frm_updata_data_alv USING lv_mtype lt_excel  CHANGING pt_excel.
  ELSEIF pv_flg_impt = '03'.
    PERFORM frm_data_impt_03     USING pv_flg_impt
                                       pt_bukrs_excel
                                       pt_werks_excel
                                       pt_matnr_excel
                                       pt_zzlbm_excel
                                       pt_zqdbm_excel
                              CHANGING pt_excel
                                       lt_excel
                                       lv_mtype
                                       lv_msg.
*    PERFORM frm_updata_data_alv USING lv_mtype lt_excel  CHANGING pt_excel.
  ELSEIF pv_flg_impt  = '04'.
    PERFORM frm_data_impt_04 USING pv_flg_impt
                              CHANGING pt_excel lt_excel lv_mtype lv_msg.
  ENDIF.




ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SEG_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_FLG_IMPT
*&      <-- PT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_set_zkey_excel  USING    pv_flg_impt  TYPE char2
                        CHANGING pt_excel TYPE tt_excel.

  DATA:
        lv_int TYPE i.

  IF pv_flg_impt = '01'.
    SORT pt_excel BY
                      zht_id
                      zfllx
                      zxybstyp
                      ekgrp
                      zdffs_h
                      zpayday
                      zbegin
                      zend
                      zhscj
                      zcgjl
                      zcgzj
                      zfldfsj
                      ztk_txt
                      zlcbh
                      zpffl
                      zsqbm
                      zptbm
                      zzjzr
                      .

    LOOP AT pt_excel INTO DATA(ls_excel).
      ls_excel-zkey =
                      ls_excel-zht_id   &&   '-' &&
                      ls_excel-zfllx    &&   '-' &&
                      ls_excel-zxybstyp &&   '-' &&
                      ls_excel-ekgrp    &&   '-' &&
                      ls_excel-zdffs_h  &&   '-' &&
                      ls_excel-zpayday  &&   '-' &&
                      ls_excel-zbegin   &&   '-' &&
                      ls_excel-zend     &&   '-' &&
                      ls_excel-zhscj    &&   '-' &&
                      ls_excel-zcgjl    &&   '-' &&
                      ls_excel-zcgzj    &&   '-' &&
                      ls_excel-zfldfsj  &&   '-' &&
                      ls_excel-ztk_txt  &&   '-' &&
                      ls_excel-zlcbh    &&   '-' &&
                      ls_excel-zpffl    &&   '-' &&
                      ls_excel-zsqbm    &&   '-' &&
                      ls_excel-zptbm    &&   '-' &&
                      ls_excel-zzjzr.
      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.

*    zkey 继续分组
    SORT pt_excel BY zkey.
    LOOP AT pt_excel INTO ls_excel.
      AT NEW zkey.
        CLEAR lv_int.
      ENDAT.

      lv_int = lv_int + 1.

      ls_excel-seg = lv_int DIV 700.

      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.

    LOOP AT pt_excel INTO ls_excel.
*      组建新的ZKEY
      ls_excel-zkey = ls_excel-zkey && ls_excel-seg.
      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.




  ELSEIF pv_flg_impt = '02'.
    SORT pt_excel BY
                      ztk_id .

    LOOP AT pt_excel INTO ls_excel.
      ls_excel-zkey =
                      ls_excel-ztk_id   .
      MODIFY pt_excel FROM ls_excel.
    ENDLOOP.

  ENDIF.


ENDFORM.

FORM frm_data_impt_exe    USING  pv_flg_impt     TYPE char2
                                  pv_pass        TYPE char1
                                  pt_bukrs_excel TYPE tt_bukrs
                                  pt_werks_excel TYPE tt_werks
                                  pt_matnr_excel TYPE tt_matnr
                                  pt_zzlbm_excel TYPE tt_t81
                                  pt_zqdbm_excel TYPE tt_t82
                        CHANGING
                                 pt_msglist   TYPE scp1_general_errors
                                 pt_excel     TYPE tt_excel
                                 lv_mtype_tk  TYPE bapi_mtype
                                 lv_msg_tk    TYPE bapi_msg.

  DATA:
    le_s_data_base TYPE  ty_data_base,
    le_s_ta01      TYPE LINE OF tt_ta01,
    le_s_ta02      TYPE LINE OF tt_ta02,
    le_s_tc05      TYPE LINE OF tt_tc05,
    le_t_tc04_set  TYPE tt_tc04,
    le_t_t06       TYPE tt_t06,
    le_t_t11_all   TYPE tt_t11,
    le_t_t11       TYPE tt_t11,
    le_t_t12       TYPE tt_t12,
    le_t_t13       TYPE tt_t13,
    le_t_t14       TYPE tt_t14,
    le_t_t44       TYPE tt_t44,
    le_t_t44_all   TYPE tt_t44,
    le_t_tc02      TYPE tt_tc02,
    le_t_tc03      TYPE tt_tc03,
    le_t_ta03      TYPE tt_ta03,
    le_t_ta04      TYPE tt_ta04,
    le_t_ta05      TYPE tt_ta05,
    le_t_ta06      TYPE tt_ta06,
    le_t_t58       TYPE tt_t58,
    le_t_t76       TYPE tt_t76,
    le_t_t81       TYPE tt_t81,
    le_t_t82       TYPE tt_t82,
    le_t_t84       TYPE tt_t84,
    le_t_bukrs_set TYPE tt_bukrs,
    le_t_dcwrk_set TYPE tt_dcwrk,
    le_t_werks_set TYPE tt_werks,
    le_t_ekorg_set TYPE tt_ekorg,
    le_t_matnr_set TYPE tt_matnr.


*  数据格式转换 调用41 保存逻辑
  PERFORM frm_data_conver_excel_2_tk USING  pv_flg_impt
                                            pt_excel
                                            pt_bukrs_excel
                                            pt_werks_excel
                                            pt_matnr_excel
                                            pt_zzlbm_excel
                                            pt_zqdbm_excel
                                     CHANGING
                                            le_s_data_base
                                            le_s_ta01
                                            le_s_ta02
                                            le_s_tc05
                                            le_t_tc04_set
                                            le_t_t11_all
                                            le_t_t11
                                            le_t_t12
                                            le_t_t13
                                            le_t_t14
                                            le_t_t44
                                            le_t_t44_all
                                            le_t_tc02
                                            le_t_tc03
                                            le_t_ta03
                                            le_t_ta04
                                            le_t_t76
                                            le_t_t81
                                            le_t_t82
                                            le_t_bukrs_set
                                            le_t_dcwrk_set
                                            le_t_werks_set
                                            le_t_ekorg_set
                                            le_t_matnr_set.

  PERFORM frm_check_data_main     USING pv_pass
                                  CHANGING
                                       pt_msglist
                                       lv_mtype_tk
                                       lv_msg_tk
                                       le_s_data_base
                                       le_t_t13
                                       le_s_ta01
                                       le_s_ta02
                                       le_t_ta03
                                       le_t_ta04
                                       le_t_ta05
                                       le_t_ta06
                                       le_t_tc02
                                       le_s_tc05
                                       le_t_t11
                                       le_t_t44
                                       le_t_t44_all
                                       le_t_t76
                                       le_t_tc04_set
                                       le_t_t81
                                       le_t_t82
                                       le_t_t84
                                       le_t_bukrs_set
                                       le_t_dcwrk_set
                                       le_t_werks_set
                                       le_t_ekorg_set
                                       le_t_matnr_set.

  IF lv_mtype_tk = 'S'.
    PERFORM frm_save_data         USING pv_pass
                                  CHANGING
                                         lv_mtype_tk
                                         lv_msg_tk
                                         le_s_data_base
                                         le_s_ta01
                                         le_s_ta02
                                         le_s_tc05
                                         le_t_tc04_set
                                         le_t_t11_all
                                         le_t_t11
                                         le_t_t12
                                         le_t_t13
                                         le_t_t14
                                         le_t_t44
                                         le_t_t44_all
                                         le_t_tc02
                                         le_t_tc03
                                         le_t_ta03
                                         le_t_ta04
                                         le_t_ta05
                                         le_t_ta06
                                         le_t_t58
                                         le_t_t76
                                         le_t_t81
                                         le_t_t82
                                         le_t_t84
                                         le_t_bukrs_set
                                         le_t_dcwrk_set
                                         le_t_werks_set
                                         le_t_ekorg_set
                                         le_t_matnr_set.

*    成功后会写数据到ALV中
    PERFORM frm_data_conver_tk_2_excel USING  le_s_ta02
                                              le_t_tc02
                                       CHANGING
                                              pt_excel.


  ELSE.
    RETURN.
  ENDIF.


ENDFORM.

FORM frm_data_conver_excel_2_tk USING pv_flg_impt TYPE char2
                                         pt_excel TYPE tt_excel
                                         pt_bukrs_excel TYPE tt_bukrs
                                         pt_werks_excel TYPE tt_werks
                                         pt_matnr_excel TYPE tt_matnr
                                         pt_zzlbm_excel TYPE tt_t81
                                         pt_zqdbm_excel TYPE tt_t82
                                   CHANGING
                                         ps_data_base   TYPE ty_data_base
                                         ps_ta01        TYPE LINE OF tt_ta01
                                         ps_ta02        TYPE LINE OF tt_ta02
                                         ps_tc05        TYPE LINE OF tt_tc05
                                         pt_tc04        TYPE  tt_tc04
                                         pt_t11_all     TYPE  tt_t11
                                         pt_t11         TYPE  tt_t11
                                         pt_t12         TYPE  tt_t12
                                         pt_t13         TYPE  tt_t13
                                         pt_t14         TYPE  tt_t14
                                         pt_t44         TYPE  tt_t44
                                         pt_t44_all     TYPE  tt_t44
                                         pt_tc02        TYPE  tt_tc02
                                         pt_tc03        TYPE  tt_tc03
                                         pt_ta03        TYPE  tt_ta03
                                         pt_ta04        TYPE  tt_ta04
                                         pt_t76         TYPE  tt_t76
                                         pt_t81         TYPE  tt_t81
                                         pt_t82         TYPE  tt_t82
                                         pt_bukrs       TYPE  tt_bukrs
                                         pt_dcwrk       TYPE  tt_dcwrk
                                         pt_werks       TYPE  tt_werks
                                         pt_ekorg       TYPE  tt_ekorg
                                         pt_matnr       TYPE  tt_matnr
                                          .

  DATA:
        ls_tc02 TYPE LINE OF tt_tc02.

  READ TABLE pt_excel INTO DATA(ls_excel) INDEX 1.


*  PS_DATA_BASE 数据处理
  IF pv_flg_impt = '01'.
    ps_data_base-actvt = '01'.
  ELSEIF   pv_flg_impt = '02'.
    ps_data_base-actvt = '02'.
  ENDIF.
  ps_data_base-zflg_cprog = 'ZRED0041'.

  ps_data_base-zfllx    = ls_excel-zfllx.
  ps_data_base-zht_id   = ls_excel-zht_id.
  ps_data_base-ztmpid   = ls_excel-ztmpid.
  ps_data_base-ztk_idr  = ''.
  ps_data_base-zfjtk    = ''.
  ps_data_base-ztk_id   = ls_excel-ztk_id.
  ps_data_base-zxybstyp = ls_excel-zxybstyp.
  ps_data_base-ztk_id   = ls_excel-ztk_id.
  ps_data_base-zflg_type = 'GD' .

  PERFORM frm_set_zflg_type USING ps_data_base-zxybstyp CHANGING ps_data_base-zflg_type.

*  PS_TA01 数据处理
  ps_ta01-zht_id = ls_excel-zht_id.
  ps_ta01-zhtlx = ls_excel-zhtlx.
  ps_ta01-zbukrs = ls_excel-zbukrs_ht.
  ps_ta01-ekgrp = ls_excel-ekgrp.

*  PS_TA02 数据处理
  MOVE-CORRESPONDING ls_excel TO ps_ta02.
  ps_ta02-zdffs = ls_excel-zdffs_h.

*  PS_TA02-ZTK_ID = LS_EXCEL-ZTK_ID.
*  PS_TA02-ZTK_TXT = LS_EXCEL-ZTK_TXT.
*  PS_TA02-EKGRP = LS_EXCEL-EKGRP.
*  PS_TA02-ZPAYDAY = LS_EXCEL-ZPAYDAY.
*  PS_TA02-ZHT_ID = LS_EXCEL-ZHT_ID.
*  PS_TA02-ZXYBSTYP = LS_EXCEL-ZXYBSTYP.
*  PS_TA02-ZFLLX = LS_EXCEL-ZFLLX.
*  PS_TA02-ZTMPID = LS_EXCEL-ZTMPID.
*  PS_TA02-ZBEGIN = LS_EXCEL-ZBEGIN.
*  PS_TA02-ZEND = LS_EXCEL-ZEND.
*  PS_TA02-ZHSTYPE = LS_EXCEL-ZHSTYPE.
*  PS_TA02-ZJSZQ = LS_EXCEL-ZJSZQ.
*  PS_TA02-ZHSZQ = LS_EXCEL-ZHSZQ.
*  PS_TA02-ZTKTYPE = LS_EXCEL-ZTKTYPE.
*  PS_TA02-ZCLRID = LS_EXCEL-ZCLRID.

*@锚点XY
  LOOP AT pt_excel INTO ls_excel.
    DATA(lv_tabix) = sy-tabix.
    ls_excel-seq_seg = lv_tabix.
    MODIFY pt_excel FROM ls_excel.
    CLEAR ls_tc02.
    ls_tc02-sel_man = 'X'.
*    ls_tc02-zitems_key = ls_excel-seq.  "此处不能用SEQ 因为ITEM 长度为3位，会导致溢出
    ls_tc02-zitems_key = ls_excel-seq_seg.
    ls_tc02-zctgr      = ls_excel-zctgr.
    ls_tc02-zbukrs     = ls_excel-zbukrs.
    ls_tc02-zflsqf     = ls_excel-zflsqf.
    ls_tc02-zflzff     = ls_excel-zflzff.
    ls_tc02-zdffs      = ls_excel-zdffs_i.
    ls_tc02-zitemtxt   = ls_excel-zitemtxt.
    ls_tc02-zje        = ls_excel-zje.
    ls_tc02-zmwskz     = ls_excel-zmwskz.
    ls_tc02-matnr      = ls_excel-matnr.
    ls_tc02-zpaytp     = ls_excel-zpaytp.
    ls_tc02-zdate      = ls_excel-zdate.
    ls_tc02-zflbz      = ls_excel-zflbz.
    ls_tc02-zfljs      = ls_excel-zfljs.
    ls_tc02-zzsbs      = ls_excel-zzsbs.
    ls_tc02-zjzzd      = ls_excel-zjzzd.
    ls_tc02-zsqbm_xy      = ls_excel-zsqbm_xy.
    APPEND ls_tc02 TO pt_tc02.

  ENDLOOP.

*  外部支付方
  SELECT
    *
    FROM zret0044 AS a JOIN @pt_excel AS i
                         ON a~zxy_id = i~ztk_id
                        AND a~zxy_id NE ''
    INTO CORRESPONDING FIELDS OF TABLE  @pt_t44_all.


  SORT pt_excel BY seq seq_seg.
  DATA(pt_bukrs_excel_tmp) = pt_bukrs_excel[].
  LOOP AT pt_bukrs_excel_tmp INTO DATA(ls_bukrs_excel).
    READ TABLE pt_excel INTO ls_excel WITH KEY seq = ls_bukrs_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_bukrs_excel-zitems_key = ls_excel-seq_seg.
      MODIFY pt_bukrs_excel_tmp FROM ls_bukrs_excel.
    ELSE.
      DELETE pt_bukrs_excel_tmp.
    ENDIF.
  ENDLOOP.

  pt_bukrs[] = pt_bukrs_excel_tmp[].

**********************************************************************
  SORT pt_excel BY seq seq_seg.
  DATA(pt_werks_excel_tmp) = pt_werks_excel[].
  LOOP AT pt_werks_excel_tmp INTO DATA(ls_werks_excel).
    READ TABLE pt_excel INTO ls_excel WITH KEY seq = ls_werks_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_werks_excel-zitems_key = ls_excel-seq_seg.
      MODIFY pt_werks_excel_tmp FROM ls_werks_excel.
    ELSE.
      DELETE pt_werks_excel_tmp.
    ENDIF.
  ENDLOOP.

  pt_werks[] = pt_werks_excel_tmp[].
**********************************************************************
  DATA(pt_matnr_excel_tmp) = pt_matnr_excel[].
  LOOP AT pt_matnr_excel_tmp INTO DATA(ls_matnr_excel).
    READ TABLE pt_excel INTO ls_excel WITH KEY seq = ls_matnr_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_matnr_excel-zitems_key = ls_excel-seq_seg.
      MODIFY pt_matnr_excel_tmp FROM ls_matnr_excel.
    ELSE.
      DELETE pt_matnr_excel_tmp.
    ENDIF.
  ENDLOOP.

  pt_matnr[] = pt_matnr_excel_tmp[].
**********************************************************************
  DATA(pt_zzlbm_excel_tmp) = pt_zzlbm_excel[].
  LOOP AT pt_zzlbm_excel_tmp INTO DATA(ls_zzlbm_excel).
    READ TABLE pt_excel INTO ls_excel WITH KEY seq = ls_zzlbm_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_zzlbm_excel-zitems_key = ls_excel-seq_seg.
      MODIFY pt_zzlbm_excel_tmp FROM ls_zzlbm_excel.
    ELSE.
      DELETE pt_zzlbm_excel_tmp.
    ENDIF.
  ENDLOOP.

  pt_t81[] = pt_zzlbm_excel_tmp[].
**********************************************************************
  DATA(pt_zqdbm_excel_tmp) = pt_zqdbm_excel[].
  LOOP AT pt_zqdbm_excel_tmp INTO DATA(ls_zqdbm_excel).
    READ TABLE pt_excel INTO ls_excel WITH KEY seq = ls_zqdbm_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_zqdbm_excel-zitems_key = ls_excel-seq_seg.
      MODIFY pt_zqdbm_excel_tmp FROM ls_zqdbm_excel.
    ELSE.
      DELETE pt_zqdbm_excel_tmp.
    ENDIF.
  ENDLOOP.

  pt_t82[] = pt_zqdbm_excel_tmp[].
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_UPDATA_DATA_ALV
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LV_MTYPE
*&      --> LT_EXCEL
*&      <-- PT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_updata_data_alv  USING    pv_mtype TYPE bapi_mtype
                                   lt_excel TYPE tt_excel
                          CHANGING pt_excel TYPE tt_excel.

  SORT lt_excel BY seq.
  LOOP AT pt_excel INTO DATA(ls_excel) WHERE sel_man = 'X'.
    READ TABLE lt_excel TRANSPORTING NO FIELDS WITH KEY seq = ls_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      IF pv_mtype = 'E'.
        ls_excel-status = '1'.
        ls_excel-ztype_impt = 'E'.
      ELSE.
        ls_excel-status = '3'.
        ls_excel-ztype_impt = 'S'.
      ENDIF.

      PERFORM frm_set_status  CHANGING ls_excel.
      MODIFY pt_excel FROM ls_excel.
    ENDIF.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CONVER_EXCEL_U
*&---------------------------------------------------------------------*
*& EXCEL 上载数据与EXCEL 主体数据转换
*&---------------------------------------------------------------------*
*&      --> P_
*&      <-- LS_EXCEL_U
*&      <-- LS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_conver_excel_u  USING    pv_flg TYPE char1   "A EXCEL_U ---> EXCEL  B EXCEL----> EXCEL_U
                         CHANGING ps_excel_u  TYPE LINE OF tt_excel_u
                                  ps_excel  TYPE LINE OF tt_excel.
  FIELD-SYMBOLS:
    <fs_excel>   TYPE any,
    <fs_excel_u> TYPE any.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZTK_ID' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZTK_ID' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZCTGR' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZCTGR' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZBUKRS' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZBUKRS' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZFLSQF' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZFLSQF' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZFLZFF' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZFLZFF' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZDFFS_I' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZDFFS_I' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZITEMTXT' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZITEMTXT' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'MATNR' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'MATNR' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZJE' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZJE' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZMWSKZ' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZMWSKZ' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.


*  UNASSIGN <fs_excel>.
*  ASSIGN COMPONENT 'ZBEGIN' OF STRUCTURE ps_excel TO <fs_excel>.
*  UNASSIGN <fs_excel_u>.
*  ASSIGN COMPONENT 'ZBEGIN' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
*  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
*    IF pv_flg = 'A'.
*      <fs_excel> = <fs_excel_u>.
*    ELSE.
*      <fs_excel_u> = <fs_excel>.
*    ENDIF.
*  ENDIF.
*
*  UNASSIGN <fs_excel>.
*  ASSIGN COMPONENT 'ZEND' OF STRUCTURE ps_excel TO <fs_excel>.
*  UNASSIGN <fs_excel_u>.
*  ASSIGN COMPONENT 'ZEND' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
*  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
*    IF pv_flg = 'A'.
*      <fs_excel> = <fs_excel_u>.
*    ELSE.
*      <fs_excel_u> = <fs_excel>.
*    ENDIF.
*  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZPAYTP' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZPAYTP' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.


  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZDATE' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZDATE' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZFLBZ' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZFLBZ' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.


  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZFLJS' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZFLJS' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZZSBS' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZZSBS' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

**********************************************************************

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'MATNR_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'MATNR_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'BUKRS_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'BUKRS_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'BUKLX_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'BUKLX_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'WERKS_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'WERKS_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'WERPC_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'WERPC_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZZLBM_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZZLBM_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZQDBM_X' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZQDBM_X' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZJZZD' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZJZZD' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

  UNASSIGN <fs_excel>.
  ASSIGN COMPONENT 'ZSQBM_XY' OF STRUCTURE ps_excel TO <fs_excel>.
  UNASSIGN <fs_excel_u>.
  ASSIGN COMPONENT 'ZSQBM_XY' OF STRUCTURE ps_excel_u TO <fs_excel_u>.
  IF <fs_excel> IS ASSIGNED AND <fs_excel_u> IS ASSIGNED.
    IF pv_flg = 'A'.
      <fs_excel> = <fs_excel_u>.
    ELSE.
      <fs_excel_u> = <fs_excel>.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_DATA_CONVER_TK_2_EXCEL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LE_S_TA02
*&      --> LE_T_TC02
*&      <-- PT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_data_conver_tk_2_excel  USING    ps_ta02 TYPE LINE OF tt_ta02
                                          pt_tc02 TYPE  tt_tc02
                                 CHANGING pt_excel  TYPE tt_excel.

  SORT pt_tc02 BY zitems_key.
  LOOP AT pt_excel INTO DATA(ls_excel).
    ls_excel-ztk_id = ps_ta02-ztk_id.
    READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems_key = ls_excel-seq_seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_excel-zxy_id = ls_tc02-zxy_id.
    ENDIF.
    MODIFY pt_excel FROM ls_excel.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_DATA_CONVER_TK_2_EXCEL_02
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LT_EXCEL_TMP
*&      <-- PT_EXCEL
*&---------------------------------------------------------------------*
FORM frm_data_conver_tk_2_excel_02  USING    pt_excel_tmp TYPE tt_excel
                                    CHANGING pt_excel TYPE tt_excel.

  SORT pt_excel_tmp BY seq.
  LOOP AT pt_excel INTO DATA(ls_excel).

    READ TABLE pt_excel_tmp INTO DATA(ls_excel_tmp) WITH KEY seq = ls_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_excel-zxy_id = ls_excel_tmp-zxy_id.
      ls_excel-ztk_id = ls_excel_tmp-ztk_id.
    ENDIF.

    MODIFY pt_excel FROM ls_excel.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_IMPT_01_02
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PV_FLG_IMPT
*&      <-- LT_EXCEL
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_data_impt_01_02   USING  pv_flg_impt     TYPE char2
                                   pv_pass        TYPE char1
                                   pt_bukrs_excel TYPE tt_bukrs
                                   pt_werks_excel TYPE tt_werks
                                   pt_matnr_excel TYPE tt_matnr
                                   pt_zzlbm_excel TYPE tt_t81
                                   pt_zqdbm_excel TYPE tt_t82
                          CHANGING
                                   pt_excel   TYPE tt_excel
                                   lt_excel   TYPE tt_excel
                                   lv_mtype   TYPE bapi_mtype
                                   lv_msg     TYPE bapi_msg.

  DATA:
       lt_excel_saved TYPE tt_excel.
  DATA:lt_msglist_sub TYPE scp1_general_errors.
  DATA:lt_msglist_all TYPE scp1_general_errors.

*  分组
  PERFORM frm_set_zkey_excel USING pv_flg_impt
                            CHANGING lt_excel.

*  分组循环检查并保存
  DATA(lt_excel_head) = lt_excel[].
  SORT lt_excel_head BY zkey.
  DELETE ADJACENT DUPLICATES FROM lt_excel_head COMPARING zkey.
  LOOP AT lt_excel_head INTO DATA(ls_excel_head).
    DATA(lt_excel_tmp) = lt_excel[].
    DELETE lt_excel_tmp WHERE zkey NE ls_excel_head-zkey.

    CLEAR: lv_mtype,lv_msg,lt_msglist_sub[].
    PERFORM frm_data_impt_exe USING  pv_flg_impt
                                     pv_pass
                                     pt_bukrs_excel
                                     pt_werks_excel
                                     pt_matnr_excel
                                     pt_zzlbm_excel
                                     pt_zqdbm_excel
                            CHANGING lt_msglist_sub
                                     lt_excel_tmp
                                     lv_mtype
                                     lv_msg.

    IF lt_msglist_sub IS NOT INITIAL .
      APPEND LINES OF lt_msglist_sub TO lt_msglist_all.
    ENDIF.

    IF lv_mtype = 'E'.
      ROLLBACK WORK.
      EXIT.
    ELSE.
*      成功后将数据临时保存到 LT_EXCEL_SAVED 中
      APPEND LINES OF lt_excel_tmp TO lt_excel_saved.
    ENDIF.
  ENDLOOP.

  PERFORM frm_show_message_list USING lt_msglist_all.

  IF lv_mtype = 'S'.
    COMMIT WORK AND WAIT .
*    成功后统一会写数据到ALV中，失败则不回写
    PERFORM frm_data_conver_tk_2_excel_02 USING  lt_excel_saved
                                          CHANGING
                                                  pt_excel.

    PERFORM frm_update_zcs.
  ELSE.
    ROLLBACK WORK .
  ENDIF.

ENDFORM.

FORM frm_data_impt_03     USING    pv_flg_impt  TYPE char2
                                         pt_bukrs_excel TYPE tt_bukrs
                                         pt_werks_excel TYPE tt_werks
                                         pt_matnr_excel TYPE tt_matnr
                                         pt_zzlbm_excel TYPE tt_t81
                                         pt_zqdbm_excel TYPE tt_t82
                          CHANGING
                                   pt_excel   TYPE tt_excel
                                   lt_excel   TYPE tt_excel
                                   lv_mtype   TYPE bapi_mtype
                                   lv_msg     TYPE bapi_msg.


  LOOP AT lt_excel INTO DATA(ls_excel).

    PERFORM frm_author_check_impt_03 CHANGING ls_excel.

    IF ls_excel-ztype_impt NE 'E'.
      PERFORM frm_data_impt_exe_03 USING pt_matnr_excel CHANGING ls_excel.
    ENDIF.

    PERFORM frm_set_status  CHANGING ls_excel.
    MODIFY lt_excel FROM ls_excel.
  ENDLOOP.

  PERFORM frm_data_impt_exe_03_b USING ls_excel
                                       pt_bukrs_excel
                                       pt_werks_excel
                                       pt_matnr_excel
                                       pt_zzlbm_excel
                                       pt_zqdbm_excel
                                       .




  SORT lt_excel BY seq.
  LOOP AT pt_excel INTO DATA(ps_excel) WHERE sel_man = 'X'.
    READ TABLE lt_excel INTO ls_excel WITH KEY seq = ps_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ps_excel-status = ls_excel-status.
      ps_excel-ztype_impt = ls_excel-ztype_impt.
      ps_excel-zmsg_excel = ls_excel-zmsg_excel.
      MODIFY pt_excel FROM ps_excel.
    ENDIF.
  ENDLOOP.



  lv_mtype = 'S'.
  lv_msg = '导入完成'.

ENDFORM.


FORM frm_data_impt_04     USING    pv_flg_impt  TYPE char2
                          CHANGING
                                   pt_excel   TYPE tt_excel
                                   lt_excel   TYPE tt_excel
                                   lv_mtype   TYPE bapi_mtype
                                   lv_msg     TYPE bapi_msg.


  LOOP AT lt_excel INTO DATA(ls_excel).

    PERFORM frm_author_check_impt_03 CHANGING ls_excel.

    IF ls_excel-ztype_impt NE 'E'.
      PERFORM frm_data_impt_exe_04 CHANGING ls_excel.
    ENDIF.

    PERFORM frm_set_status  CHANGING ls_excel.
    MODIFY lt_excel FROM ls_excel.
  ENDLOOP.


  SORT lt_excel BY seq.
  LOOP AT pt_excel INTO DATA(ps_excel) WHERE sel_man = 'X'.
    READ TABLE lt_excel INTO ls_excel WITH KEY seq = ps_excel-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ps_excel-status = ls_excel-status.
      ps_excel-ztype_impt = ls_excel-ztype_impt.
      ps_excel-zmsg_excel = ls_excel-zmsg_excel.
      MODIFY pt_excel FROM ps_excel.
    ENDIF.
  ENDLOOP.



  lv_mtype = 'S'.
  lv_msg = '导入完成'.

ENDFORM.

FORM frm_data_impt_exe_03_b     USING    ps_excel TYPE ty_excel
                                         pt_bukrs_excel TYPE tt_bukrs
                                         pt_werks_excel TYPE tt_werks
                                         pt_matnr_excel TYPE tt_matnr
                                         pt_zzlbm_excel TYPE tt_t81
                                         pt_zqdbm_excel TYPE tt_t82.

  DATA:
    ls_zret0014 TYPE zret0014,
    ls_zret0071 TYPE zret0071,
    ls_zret0081 TYPE zret0081,
    ls_zret0082 TYPE zret0082,
    lt_zret0014 TYPE TABLE OF zret0014,
    lt_zret0071 TYPE TABLE OF zret0071,
    lt_zret0081 TYPE TABLE OF zret0081,
    lt_zret0082 TYPE TABLE OF zret0082.

  IF ps_excel-bukrs_x_n IS NOT INITIAL.
    SELECT a~*
      FROM zret0014 AS a
     WHERE a~zxy_id = @ps_excel-zxy_id
       AND a~zzzlx = 'A'
       AND a~zzzpc = ''
      INTO TABLE @DATA(lt_zret0014_del).
  ENDIF.

  IF ps_excel-werks_x_n IS NOT INITIAL.
    SELECT a~*
      FROM zret0014 AS a
     WHERE a~zxy_id = @ps_excel-zxy_id
       AND a~zzzlx = 'S'
       AND a~zzzpc = ''
      APPENDING TABLE @lt_zret0014_del.
  ENDIF.

  SELECT
    a~*
    FROM @pt_matnr_excel AS i JOIN zret0071 AS a
                                ON i~zxy_id = a~zxy_id
    INTO TABLE @DATA(lt_zret0071_del).

  SELECT
    a~*
    FROM @pt_zzlbm_excel AS i JOIN zret0081 AS a
                                ON i~zxy_id = a~zxy_id
    INTO TABLE @DATA(lt_zret0081_del).

  SELECT
    a~*
    FROM @pt_zqdbm_excel AS i JOIN zret0082 AS a
                                ON i~zxy_id = a~zxy_id
    INTO TABLE @DATA(lt_zret0082_del).

  LOOP AT pt_bukrs_excel INTO DATA(ls_bukrs_excel) WHERE bukrs IS NOT INITIAL .
    CLEAR ls_zret0014.
    ls_zret0014-zxy_id = ls_bukrs_excel-zxy_id.
    ls_zret0014-zzzid = ls_bukrs_excel-bukrs.
    ls_zret0014-zzzlx = 'A'.
    APPEND ls_zret0014 TO lt_zret0014.
  ENDLOOP.

  LOOP AT pt_werks_excel INTO DATA(ls_werks_excel) WHERE werks IS NOT INITIAL .
    CLEAR ls_zret0014.
    ls_zret0014-zxy_id = ls_werks_excel-zxy_id.
    ls_zret0014-zzzid = ls_werks_excel-werks.
    ls_zret0014-zzzlx = 'S'.
    ls_zret0014-zzzpc = ls_werks_excel-exclude.
    APPEND ls_zret0014 TO lt_zret0014.
  ENDLOOP.


  LOOP AT pt_matnr_excel INTO DATA(ls_matnr_excel) WHERE matnr IS NOT INITIAL .
    CLEAR ls_zret0071.
    ls_zret0071-zxy_id = ls_matnr_excel-zxy_id.
    ls_zret0071-matnr = ls_matnr_excel-matnr.
    APPEND ls_zret0071 TO lt_zret0071.
  ENDLOOP.

  LOOP AT pt_zzlbm_excel INTO DATA(ls_zzlbm_excel) WHERE zzlbm IS NOT INITIAL .
    CLEAR ls_zret0081.
    ls_zret0081-zxy_id = ls_zzlbm_excel-zxy_id.
    ls_zret0081-zzlbm  = ls_zzlbm_excel-zzlbm.
    APPEND ls_zret0081 TO lt_zret0081.
  ENDLOOP.

  LOOP AT pt_zqdbm_excel INTO DATA(ls_zqdbm_excel) WHERE zqdbm IS NOT INITIAL .
    CLEAR ls_zret0082.
    ls_zret0082-zxy_id = ls_zqdbm_excel-zxy_id.
    ls_zret0082-zqdbm = ls_zqdbm_excel-zqdbm.
    APPEND ls_zret0082 TO lt_zret0082.
  ENDLOOP.

  DELETE zret0014 FROM TABLE lt_zret0014_del.
  DELETE zret0071 FROM TABLE lt_zret0071_del.
  DELETE zret0081 FROM TABLE lt_zret0081_del.
  DELETE zret0082 FROM TABLE lt_zret0082_del.
  MODIFY zret0014 FROM TABLE lt_zret0014.
  MODIFY zret0071 FROM TABLE lt_zret0071.
  MODIFY zret0081 FROM TABLE lt_zret0081.
  MODIFY zret0082 FROM TABLE lt_zret0082.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_IMPT_03
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_author_check_impt_03  CHANGING ls_excel TYPE LINE OF tt_excel.


  DATA:
    lv_mtype_auth TYPE bapi_mtype,
    lv_msg_auth   TYPE scp1_general_error-msgv1.

  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.

  DATA:
    ls_ta01 TYPE LINE OF tt_ta01,
    ls_ta02 TYPE LINE OF tt_ta02,
    lt_tc02 TYPE  tt_tc02.

  PERFORM frm_data_conver_excel_2_tk_03 USING ls_excel
                                        CHANGING ls_ta01 ls_ta02 lt_tc02.


  PERFORM frm_author_check_tk USING ls_ta01
                                    ls_ta02
                                    lt_tc02
                                    '01'
                                    'B'
                              CHANGING lt_msglist
                                       lv_mtype_auth
                                       lv_msg_auth..

  IF lv_mtype_auth NE 'E'.

    PERFORM frm_author_check_zbukrs_tk USING ls_excel-zbukrs
                                         '01'
                                   CHANGING
                                         lv_mtype_auth
                                         lv_msg_auth.

  ENDIF.




  IF lv_mtype_auth NE 'S'.
    ls_excel-status = '1'.
    ls_excel-ztype_impt = 'E'.
    ls_excel-zmsg_excel = ls_excel-zmsg_excel && '/' && lv_msg_auth.
*  ELSE.
*    ls_excel-status = '3'.
*    ls_excel-ztype_impt = 'S'.
*    ls_excel-zmsg_excel = '导入成功！'.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CONVER_EXCEL_2_TK_03
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL
*&      <-- LS_TA01
*&      <-- LS_TA02
*&      <-- LT_TC02
*&---------------------------------------------------------------------*
FORM frm_data_conver_excel_2_tk_03  USING    ps_excel TYPE LINE OF tt_excel
                                    CHANGING ps_ta01  TYPE LINE OF tt_ta01
                                             ps_ta02  TYPE LINE OF tt_ta02
                                             pt_tc02  TYPE tt_tc02.

  CLEAR: ps_ta01,ps_ta02,pt_tc02.

  SELECT SINGLE a~* FROM zreta001 AS a WHERE zht_id = @ps_excel-zht_id INTO CORRESPONDING FIELDS OF @ps_ta01.
  SELECT SINGLE a~* FROM zreta002 AS a WHERE ztk_id = @ps_excel-ztk_id INTO CORRESPONDING FIELDS OF @ps_ta02.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_IMPT_EXE_03
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_data_impt_exe_03  USING pt_matnr_excel TYPE tt_matnr CHANGING ls_excel TYPE LINE OF tt_excel.

  DATA:
    ls_zret0008 TYPE zret0008,
    ls_zret0044 TYPE zret0044,
    lt_zret0044 TYPE TABLE OF zret0044,
    lt_matnr    TYPE tt_matnr,
    lv_zzsbs    TYPE zret0006-zzsbs.


  lt_matnr[] = pt_matnr_excel[].
  DELETE lt_matnr WHERE zxy_id <> ls_excel-zxy_id.
  MOVE-CORRESPONDING ls_excel TO ls_zret0008.
  ls_zret0008-zxy_itemid = '1'.
  IF lines( lt_matnr ) = 1.
    ls_zret0008-matnr = lt_matnr[ 1 ]-matnr.
  ENDIF.

  MODIFY zret0008 FROM ls_zret0008.

  IF ls_excel-zzsbs = 'Q'.
    lv_zzsbs = ''.
  ELSE.
    lv_zzsbs = ls_excel-zzsbs.
  ENDIF.

  UPDATE zret0006    SET zdffs  = ls_excel-zdffs_i
                         zzsbs  = lv_zzsbs
                         zctgr  = ls_excel-zctgr
                   WHERE zxy_id = ls_excel-zxy_id.

  IF ls_excel-zflzff IS NOT INITIAL.
    UPDATE zret0006 SET zitzff = '' WHERE zxy_id = ls_excel-zxy_id.

    ls_zret0044-zxy_id = ls_excel-zxy_id.
    ls_zret0044-zflzff = ls_excel-zflzff.
    ls_zret0044-zpaytp = ls_excel-zpaytp.
    DELETE FROM zret0044 WHERE zxy_id = ls_excel-zxy_id.
    MODIFY zret0044 FROM ls_zret0044.
  ENDIF.


  COMMIT WORK AND WAIT.
  ls_excel-status = '3'.
  ls_excel-ztype_impt = 'S'.
  ls_excel-zmsg_excel = '导入成功！'.

ENDFORM.

FORM frm_data_impt_exe_04  CHANGING ls_excel TYPE LINE OF tt_excel.

  DATA:
        ls_excel_t TYPE LINE OF tt_excel_t.



  SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_excel-ztk_id INTO @DATA(ls_zreta002).
  SELECT
    zxy_id,
    zfllx,
    zbegin,
    zend
    FROM zret0006
    WHERE ztk_id = @ls_excel-ztk_id
    INTO TABLE @DATA(lt_t06).

  SELECT SINGLE b~zxybstyp FROM zreta002 AS a JOIN zret0002 AS b
                                                ON a~zfllx = b~zfllx
                                              WHERE a~ztk_id = @ls_excel-ztk_id
                                            INTO @DATA(lv_zxybstyp).
  IF NOT ( lv_zxybstyp EQ 'F' OR lv_zxybstyp EQ 'Q' ).
*    返利类型，开始时间和结束时间，这3个字段仅对固定类有效，即非固定类，录入也不起作用
    CLEAR:
          ls_excel-zfllx,
          ls_excel-zbegin,
          ls_excel-zend.
  ENDIF.

  IF NOT ( ls_zreta002-zxybstyp EQ 'F' OR ls_zreta002-zxybstyp EQ 'Q' ).
*    返利类型，开始时间和结束时间，这3个字段仅对固定类有效，即非固定类，录入也不起作用
    CLEAR:
          ls_excel-zfllx,
          ls_excel-zbegin,
          ls_excel-zend.
  ENDIF.



  CLEAR lv_zxybstyp.

  IF ls_excel-ztk_id  IS NOT INITIAL. ls_zreta002-ztk_id  = ls_excel-ztk_id .  ENDIF.
  IF ls_excel-zfllx   IS NOT INITIAL. ls_zreta002-zfllx   = ls_excel-zfllx  .  ENDIF.
  IF ls_excel-ekgrp   IS NOT INITIAL. ls_zreta002-ekgrp   = ls_excel-ekgrp  .  ENDIF.
  IF ls_excel-zdffs_h IS NOT INITIAL. ls_zreta002-zdffs = ls_excel-zdffs_h.  ENDIF.
  IF ls_excel-zpayday IS NOT INITIAL. ls_zreta002-zpayday = ls_excel-zpayday.  ENDIF.
  IF ls_excel-zbegin  IS NOT INITIAL. ls_zreta002-zbegin  = ls_excel-zbegin .  ENDIF.
  IF ls_excel-zend    IS NOT INITIAL. ls_zreta002-zend    = ls_excel-zend   .  ENDIF.
  IF ls_excel-zhscj   IS NOT INITIAL. ls_zreta002-zhscj   = ls_excel-zhscj  .  ENDIF.
  IF ls_excel-zaccer  IS NOT INITIAL. ls_zreta002-zaccer  = ls_excel-zaccer .  ENDIF.
  IF ls_excel-zcgjl   IS NOT INITIAL. ls_zreta002-zcgjl   = ls_excel-zcgjl  .  ENDIF.
  IF ls_excel-zcgzj   IS NOT INITIAL. ls_zreta002-zcgzj   = ls_excel-zcgzj  .  ENDIF.
  IF ls_excel-zfldfsj IS NOT INITIAL. ls_zreta002-zfldfsj = ls_excel-zfldfsj.  ENDIF.
  IF ls_excel-ztk_txt IS NOT INITIAL. ls_zreta002-ztk_txt = ls_excel-ztk_txt.  ENDIF.
  IF ls_excel-zlcbh   IS NOT INITIAL. ls_zreta002-zlcbh   = ls_excel-zlcbh.    ENDIF.
  IF ls_excel-zpffl   IS NOT INITIAL. ls_zreta002-zpffl   = COND #( WHEN ls_excel-zpffl = '是' THEN  'X' ELSE '' ) .    ENDIF.
  IF ls_excel-zsqbm   IS NOT INITIAL. ls_zreta002-zsqbm   = ls_excel-zsqbm.    ENDIF.
  IF ls_excel-zptbm   IS NOT INITIAL. ls_zreta002-zptbm   = ls_excel-zptbm.    ENDIF.
  IF ls_excel-zzjzr   IS NOT INITIAL. ls_zreta002-zzjzr   = ls_excel-zzjzr.    ENDIF.


  ls_zreta002-zxgrq = sy-datum.
  ls_zreta002-zxgsj = sy-uzeit.
  ls_zreta002-zxgr = sy-uname.

  MODIFY zreta002 FROM ls_zreta002.

  LOOP AT lt_t06 INTO DATA(ls_t06).
    IF ls_excel-zfllx IS NOT INITIAL.
      ls_t06-zfllx = ls_excel-zfllx.
    ENDIF.
    IF ls_excel-zbegin IS NOT INITIAL.
      ls_t06-zbegin = ls_excel-zbegin.
    ENDIF.
    IF ls_excel-zend IS NOT INITIAL.
      ls_t06-zend = ls_excel-zend.
    ENDIF.

    UPDATE zret0006 SET
      zfllx = ls_t06-zfllx
      zbegin = ls_t06-zbegin
      zend = ls_t06-zend
      zxgrq = sy-datum
      zxgsj = sy-uzeit
      zxgr = sy-uname
      WHERE zxy_id = ls_t06-zxy_id .
  ENDLOOP.

  COMMIT WORK AND WAIT.
  ls_excel-status = '3'.
  ls_excel-ztype_impt = 'S'.
  ls_excel-zmsg_excel = '导入成功！'.

*  IF sy-subrc EQ 0.
*    COMMIT WORK AND WAIT.
*    ls_excel-status = '3'.
*    ls_excel-ztype_impt = 'S'.
*    ls_excel-zmsg_excel = '导入成功！'.
*  ELSE.
*    ls_excel-status = '1'.
*    ls_excel-ztype_impt = 'E'.
*    ls_excel-zmsg_excel = ls_excel-zmsg_excel && '/' && '导入失败！'.
*    ROLLBACK WORK .
*  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CONV_DATA_BUKRS_I
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL_I
*&      <-- PT_BUKRS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_conv_data_org_i  USING    ps_excel_i TYPE LINE OF tt_excel_i
                            CHANGING
                                     pt_bukrs_excel TYPE tt_bukrs
                                     pt_werks_excel TYPE tt_werks
                                     pt_matnr_excel TYPE tt_matnr
                                     pt_zzlbm_excel TYPE tt_t81
                                     pt_zqdbm_excel TYPE tt_t82
  .


  PERFORM frm_conv_data_org_exe USING ps_excel_i-seq
                                      ps_excel_i-bukrs_x
                                      ps_excel_i-buklx_x
                                      ps_excel_i-werks_x
                                      ps_excel_i-werpc_x
                                      ps_excel_i-matnr_x
                                      ps_excel_i-zzlbm_x
                                      ps_excel_i-zqdbm_x
                             CHANGING
                                      pt_bukrs_excel
                                      pt_werks_excel
                                      pt_matnr_excel
                                      pt_zzlbm_excel
                                      pt_zqdbm_excel
                                      .

ENDFORM.

FORM frm_conv_data_org_u  USING    ps_excel_u TYPE LINE OF tt_excel_u
                            CHANGING
                                     pt_bukrs_excel TYPE tt_bukrs
                                     pt_werks_excel TYPE tt_werks
                                     pt_matnr_excel TYPE tt_matnr
                                     pt_zzlbm_excel TYPE tt_t81
                                     pt_zqdbm_excel TYPE tt_t82
  .


  PERFORM frm_conv_data_org_exe USING ps_excel_u-seq
                                      ps_excel_u-bukrs_x
                                      ps_excel_u-buklx_x
                                      ps_excel_u-werks_x
                                      ps_excel_u-werpc_x
                                      ps_excel_u-matnr_x
                                      ps_excel_u-zzlbm_x
                                      ps_excel_u-zqdbm_x
                             CHANGING
                                      pt_bukrs_excel
                                      pt_werks_excel
                                      pt_matnr_excel
                                      pt_zzlbm_excel
                                      pt_zqdbm_excel
                                      .

ENDFORM.


FORM frm_conv_data_org_c  USING    ps_excel_c TYPE LINE OF tt_excel_c
                            CHANGING
                                     pt_bukrs_excel TYPE tt_bukrs
                                     pt_werks_excel TYPE tt_werks
                                     pt_matnr_excel TYPE tt_matnr
                                     pt_zzlbm_excel TYPE tt_t81
                                     pt_zqdbm_excel TYPE tt_t82.


  PERFORM frm_conv_data_org_exe_c USING ps_excel_c-zxy_id
                                        ps_excel_c-seq
                                        ps_excel_c-bukrs_x
                                        ps_excel_c-buklx_x
                                        ps_excel_c-werks_x
                                        ps_excel_c-werpc_x
                                        ps_excel_c-matnr_x
                                        ps_excel_c-zzlbm_x
                                        ps_excel_c-zqdbm_x
                               CHANGING pt_bukrs_excel
                                        pt_werks_excel
                                        pt_matnr_excel
                                        pt_zzlbm_excel
                                        pt_zqdbm_excel
                                        .

ENDFORM.

FORM frm_conv_data_org_exe  USING    pv_seq TYPE i
                                     pv_bukrs_x TYPE string
                                     pv_buklx_x TYPE string
                                     pv_werks_x TYPE string
                                     pv_werpc_x TYPE string
                                     pv_matnr_x TYPE string
                                     pv_zzlbm_x TYPE string
                                     pv_zqdbm_x TYPE string
                            CHANGING
                                     pt_bukrs_excel TYPE tt_bukrs
                                     pt_werks_excel TYPE tt_werks
                                     pt_matnr_excel TYPE tt_matnr
                                     pt_zzlbm_excel TYPE tt_t81
                                     pt_zqdbm_excel TYPE tt_t82
  .



  DATA:
    ls_bukrs TYPE LINE OF tt_bukrs,
    ls_werks TYPE LINE OF tt_werks,
    ls_matnr TYPE LINE OF tt_matnr,
    ls_zzlbm TYPE LINE OF tt_t81,
    ls_zqdbm TYPE LINE OF tt_t82.

  DATA:
    lt_split_table   TYPE TABLE OF string,
    lt_split_table_x TYPE TABLE OF string.
  DATA:lv_index TYPE sy-tabix.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].

  SPLIT pv_bukrs_x AT '/' INTO TABLE lt_split_table.
  SPLIT pv_buklx_x AT '/' INTO TABLE lt_split_table_x.
  LOOP AT lt_split_table INTO DATA(ls_split_table).
    CLEAR: ls_bukrs.
    lv_index = sy-tabix.
    ls_bukrs-seq = pv_seq.
    ls_bukrs-bukrs = ls_split_table.
    READ TABLE lt_split_table_x INTO DATA(ls_split_table_x) INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_bukrs-zmdsx = ls_split_table_x.
    ENDIF.
    APPEND ls_bukrs TO pt_bukrs_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].

  SPLIT pv_werks_x AT '/' INTO TABLE lt_split_table.
  SPLIT pv_werpc_x AT '/' INTO TABLE lt_split_table_x.
  LOOP AT lt_split_table INTO ls_split_table.
    CLEAR: ls_werks.
    lv_index = sy-tabix.
    ls_werks-seq = pv_seq.
    ls_werks-werks = ls_split_table.
    READ TABLE lt_split_table_x INTO ls_split_table_x INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_werks-exclude = ls_split_table_x.
    ENDIF.
    APPEND ls_werks TO pt_werks_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_matnr_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_matnr.
    ls_matnr-seq = pv_seq.
    ls_matnr-matnr = ls_split_table.
    ls_matnr-matnr = |{ ls_matnr-matnr ALPHA = IN WIDTH = 18 }|.
    APPEND ls_matnr TO pt_matnr_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_zzlbm_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_zzlbm.
    ls_zzlbm-seq = pv_seq.
    ls_zzlbm-zzlbm = ls_split_table.
    APPEND ls_zzlbm TO pt_zzlbm_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_zqdbm_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_zqdbm.
    ls_zqdbm-seq = pv_seq.
    ls_zqdbm-zqdbm = ls_split_table.
    APPEND ls_zqdbm TO pt_zqdbm_excel.
  ENDLOOP.

ENDFORM.


FORM frm_conv_data_org_exe_c  USING  pv_zxy_id TYPE string " zret0006-zxy_id
                                     pv_seq TYPE i
                                     pv_bukrs_x TYPE string
                                     pv_buklx_x TYPE string
                                     pv_werks_x TYPE string
                                     pv_werpc_x TYPE string
                                     pv_matnr_x TYPE string
                                     pv_zzlbm_x TYPE string
                                     pv_zqdbm_x TYPE string
                            CHANGING pt_bukrs_excel TYPE tt_bukrs
                                     pt_werks_excel TYPE tt_werks
                                     pt_matnr_excel TYPE tt_matnr
                                     pt_zzlbm_excel TYPE tt_t81
                                     pt_zqdbm_excel TYPE tt_t82.



  DATA:
    ls_bukrs TYPE LINE OF tt_bukrs,
    ls_werks TYPE LINE OF tt_werks,
    ls_matnr TYPE LINE OF tt_matnr,
    ls_zzlbm TYPE LINE OF tt_t81,
    ls_zqdbm TYPE LINE OF tt_t82.
  DATA:
    lt_split_table   TYPE TABLE OF string,
    lt_split_table_x TYPE TABLE OF string.
  DATA:lv_index TYPE sy-tabix.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].
  SPLIT pv_bukrs_x AT '/' INTO TABLE lt_split_table.
  SPLIT pv_buklx_x AT '/' INTO TABLE lt_split_table_x.
  LOOP AT lt_split_table INTO DATA(ls_split_table).

    CLEAR: ls_bukrs.
    lv_index = sy-tabix.
    ls_bukrs-zxy_id = pv_zxy_id.
    ls_bukrs-seq    = pv_seq.
    ls_bukrs-bukrs  = ls_split_table.
    READ TABLE lt_split_table_x INTO DATA(ls_split_table_x) INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_bukrs-zmdsx = ls_split_table_x.
    ENDIF.
    APPEND ls_bukrs TO pt_bukrs_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].
  SPLIT pv_werks_x AT '/' INTO TABLE lt_split_table.
  SPLIT pv_werpc_x AT '/' INTO TABLE lt_split_table_x.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_werks.
    lv_index = sy-tabix.
    ls_werks-zxy_id = pv_zxy_id.
    ls_werks-seq    = pv_seq.
    ls_werks-werks  = ls_split_table.
    READ TABLE lt_split_table_x INTO ls_split_table_x INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_werks-exclude = ls_split_table_x.
    ENDIF.
    APPEND ls_werks TO pt_werks_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_matnr_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_matnr.
    ls_matnr-zxy_id = pv_zxy_id.
    ls_matnr-matnr = ls_split_table.
    ls_matnr-matnr = |{ ls_matnr-matnr ALPHA = IN WIDTH = 18 }|.
    APPEND ls_matnr TO pt_matnr_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_zzlbm_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_zzlbm.
    ls_zzlbm-zxy_id = pv_zxy_id.
    ls_zzlbm-zzlbm = ls_split_table.
    APPEND ls_zzlbm TO pt_zzlbm_excel.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT pv_zqdbm_x AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_zqdbm.
    ls_zqdbm-zxy_id = pv_zxy_id.
    ls_zqdbm-zqdbm = ls_split_table.
    APPEND ls_zqdbm TO pt_zqdbm_excel.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_BUKRS_X
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_bukrs_x  USING    ps_excel TYPE ty_excel
                        CHANGING pt_msglist TYPE  scp1_general_errors.

  DATA:lt_split_table   TYPE TABLE OF string,
       lt_split_table_x TYPE TABLE OF string.

  IF ps_excel-bukrs_x IS INITIAL AND ps_excel-buklx_x IS NOT INITIAL   .
    PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq '公司代码为空时,公司类型列无需维护值'  CHANGING pt_msglist.
  ELSEIF ps_excel-bukrs_x IS NOT INITIAL AND  ps_excel-buklx_x IS  INITIAL.
    PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq '公司代码不为空时,公司类型列需维护值'  CHANGING pt_msglist.
  ELSE.
    REFRESH:lt_split_table,lt_split_table_x.
    IF ps_excel-bukrs_x IS NOT INITIAL .
      SPLIT ps_excel-bukrs_x  AT '/' INTO TABLE lt_split_table.
    ENDIF.

    IF ps_excel-buklx_x IS NOT INITIAL .
      SPLIT ps_excel-buklx_x AT '/' INTO TABLE lt_split_table_x.
    ENDIF.

    IF lines( lt_split_table ) > 1.
      PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq '公司代码列维护时,需和协议主体保持一致!'  CHANGING pt_msglist.
    ELSEIF ps_excel-bukrs_x IS NOT INITIAL AND ps_excel-bukrs_x  <> ps_excel-zbukrs.
      PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq '公司代码列维护时,需和协议主体保持一致!'  CHANGING pt_msglist.
    ENDIF.

    IF lines( lt_split_table ) <> lines( lt_split_table_x ) AND lines( lt_split_table_x ) > 1.
      PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq '公司代码列需和公司类型列一一对应(公司类型默认为1种类型除外)!'  CHANGING pt_msglist.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZSFSX_FLLX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL
*&      <-- LS_EXCEL
*&---------------------------------------------------------------------*
FORM frm_check_zsfsx_fllx  USING    ps_excel TYPE ty_excel
                                    pv_zsfsx_ht
                                    pv_zsfsx_fllx
                           CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:lv_msgv1      TYPE scp1_general_error-msgv1.

  IF pv_zsfsx_ht IS NOT INITIAL  AND pv_zsfsx_fllx IS NOT INITIAL AND pv_zsfsx_ht <> pv_zsfsx_fllx.

    SELECT SINGLE ddtext INTO @DATA(lv_ddtext) FROM dd07t   WHERE domname = 'ZRED_ZSFSX' AND domvalue_l = @pv_zsfsx_fllx.
    lv_msgv1 = |返利类型'{ ps_excel-zfllx }'为'{ lv_ddtext }'返利专用，请更正为与对应合同属性一致!|.
    CONDENSE lv_msgv1 NO-GAPS.
    PERFORM frm_add_msg_2(zbcs0001) USING  ps_excel-seq lv_msgv1  CHANGING pt_msglist.
  ENDIF.

ENDFORM.