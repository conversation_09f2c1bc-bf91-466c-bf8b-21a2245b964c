*&---------------------------------------------------------------------*
*& 包含               ZRED0035_T01
*&---------------------------------------------------------------------*

TYPE-POOLS:slis,vrm.



*组织结构模板抬头表
TYPES:
  BEGIN OF ty_tc01.
    INCLUDE TYPE zretc001.
TYPES:
  sel        TYPE char1,
  ztmpid_bak TYPE zretc001-ztmpid,
  zfjtk      TYPE char1,                  "附加条款
  END OF ty_tc01,
  tt_tc01 TYPE TABLE OF ty_tc01,

*组织结构模板行项目表
  BEGIN OF ty_tc02.
    INCLUDE TYPE zretc002.
TYPES:
  zicon_zghf  TYPE icon-id,
  zicon_bukrs TYPE icon-id,
  zicon_dcwrk TYPE icon-id,
  zicon_werks TYPE icon-id,
  zicon_ekorg TYPE icon-id,
  sel         TYPE char1,
  sel_man     TYPE char1,                  " 勾选项
  ztmpid_bak  TYPE zretc001-ztmpid,

*  ZRED0040 所用
  zjt_id      TYPE zret0011-zjt_id,
*  zdffs       TYPE zret0010-zdffs,
  zxy_id      TYPE zret0006-zxy_id,
  ztk_id      TYPE zreta002-ztk_id,

  zitems_copy TYPE zretc002-zitems,           "   复制的行

  zitems_key  TYPE zretc002-zitems,           "   行项目KEY
  zstatus_del TYPE char1,                     "   删除标识
  zitems_insr TYPE char1,                     "   插入的行

*  固定金额
  zje         TYPE zret0008-zje,
  zmwskz      TYPE zret0008-zmwskz,
*  固定数量
  zdate       TYPE zret0008-zdate,         "  d)归属日期
  zflbz       TYPE zret0008-zflbz,         "  e)返利标准
  zfljs       TYPE zret0008-zfljs,         "  f)返利基数
  matnr       TYPE zret0008-matnr,
  zsl         TYPE zret0008-zsl,
  meins       TYPE mara-meins,
  maktx       TYPE makt-maktx,


*  促销
  zspz_id     TYPE zret0009-zspz_id,
  zspzid_txt  TYPE zret0009-zspzid_txt,

  zbukrs_t    TYPE t001-butxt,

  zflg_exist  TYPE char1,         "已保存的数据
  frgsx       TYPE zret0006-frgsx,    "审批策略
  kolnr       TYPE zret0006-kolnr,    "审批代码
  frgc1       TYPE zretc007-frgc1,    "审批代码
  zxyzt_06    TYPE zret0006-zxyzt,    "协议状态
  zxybstyp_06 TYPE zret0006-zxybstyp, "协议类型
  zfklx       TYPE zret0006-zfklx,    "付款类型
  zzsbs       TYPE zret0006-zzsbs,    "专属标识
  ztxhyq      TYPE zret0006-ztxhyq,   "同享合约券
  zjzzd       TYPE zret0008-zjzzd,    "基准字段
  zsqbm_xy    TYPE zret0008-zsqbm_xy, "收取部门

  zcjrq       TYPE zret0006-zcjrq,
  zcjsj       TYPE zret0006-zcjsj,
  zcjr        TYPE zret0006-zcjr,
  zxgrq       TYPE zret0006-zxgrq,
  zxgsj       TYPE zret0006-zxgsj,
  zxgr        TYPE zret0006-zxgr,
  zsprq       TYPE zret0006-zsprq,
  zspsj       TYPE zret0006-zspsj,
  zspr        TYPE zret0006-zspr,
  zqxrq       TYPE zret0006-zqxrq,
  zqxsj       TYPE zret0006-zqxsj,
  zqxr        TYPE zret0006-zqxr,


  END OF ty_tc02,
  tt_tc02 TYPE TABLE OF ty_tc02,

*组织结构模板行项目-组织代码
  BEGIN OF ty_tc03.
    INCLUDE TYPE zretc003.
TYPES:
  sel        TYPE char1,
  ztmpid_bak TYPE zretc001-ztmpid,
  zxy_id     TYPE zret0006-zxy_id,
  zitems_key TYPE zretc002-zitems,
  END OF ty_tc03,
  tt_tc03 TYPE TABLE OF ty_tc03,

*供货方
  BEGIN OF ty_tc04.
    INCLUDE TYPE zretc004.
TYPES:
  sel        TYPE char1,
  name1      TYPE lfa1-name1,
  ztmpid_bak TYPE zretc001-ztmpid,
  zxy_id     TYPE zret0006-zxy_id,
  zitems_key TYPE zretc002-zitems,
  END OF ty_tc04,
  tt_tc04 TYPE TABLE OF ty_tc04.

**支付方
*  BEGIN OF ty_tc18.
*    INCLUDE TYPE zretc004.
*TYPES:
*  sel        TYPE char1,
*  name1      TYPE lfa1-name1,
*  ztmpid_bak TYPE zretc001-ztmpid,
*  zxy_id     TYPE zret0006-zxy_id,
*  zitems_key TYPE zretc002-zitems,
*  END OF ty_tc18,
*  tt_tc18 TYPE TABLE OF ty_tc18.

*全局数据
TYPES:
  BEGIN OF ty_data_base,
    zxybstyp            TYPE zretc009-zxybstyp, "协议类型
    subscreen           TYPE   sy-dynnr,        "屏幕
    subscreen_mass      TYPE   sy-dynnr,        " MASS 屏幕
    sub_4200            TYPE   sy-dynnr,        " 促销返利屏幕
    sub_2300            TYPE   sy-dynnr,        " 促销返利屏幕

    zfllx               TYPE zreta002-zfllx,    "返利类型
    ztmpid              TYPE zretc001-ztmpid,   "模板ID
    zht_id              TYPE zreta001-zht_id,   "合同编码
    ztk_idr             TYPE zreta002-ztk_id,   "参考条款ID
    ztk_id              TYPE zreta002-ztk_id,   "
    zfjtk               TYPE char1,             "附加条款

    zflg_cprog          TYPE syst-cprog,        "程序标识
    zflg_ztmpid_changed TYPE char1,             "模板ID更改标识
    zflg_type           TYPE char2,             "条款类型 :固定/计算
    zflg_zlrsi          TYPE char1,             "是否允许增加行项目
    zlrsi               TYPE zretc009-zlrsi,    "行项目级别审批

    zghf_t              TYPE char35 ,            "供货方文本
    zitsplr_t           TYPE char35 ,            "内部供货文本
    zetsplr_t           TYPE char35 ,            "条款供货方文本
    zghf_tt             TYPE char35,             "外部供货方文本
    zspz_id_t           TYPE char35,             "任务商品组文本
    zflg_ok_code        TYPE sy-ucomm,           "MASS OK_CODE

    actvt               TYPE activ_auth,         "权限actvt
    zflg_rb             TYPE char2,              "单选按钮

  END OF ty_data_base.


TYPES:
*  子类
  BEGIN OF ty_t81.
    INCLUDE TYPE zret0081.
TYPES:
  sel        TYPE char1,
  zitems_key TYPE zretc002-zitems,
  zzlms      TYPE zretcm10-zzlms,
  seg        TYPE i,    "   分组号
  seq        TYPE i,    "   序号
  END OF ty_t81,
  tt_t81 TYPE TABLE OF ty_t81,
*  渠道
  BEGIN OF ty_t82.
    INCLUDE TYPE zret0082.
TYPES:
  sel        TYPE char1,
  exclude    TYPE char1,
  zitems_key TYPE zretc002-zitems,
  zqdms      TYPE zretcm11-zqdms,
  seg        TYPE i,    "   分组号
  seq        TYPE i,    "   序号
  END OF ty_t82,
  tt_t82 TYPE TABLE OF ty_t82,
*  供应商
  BEGIN OF ty_t84.
    INCLUDE TYPE zret0084.
TYPES:
  sel        TYPE char1,
  ztmpid     TYPE zretc001-ztmpid,
  ztmpid_bak TYPE zretc001-ztmpid,
  zitems     TYPE zretc020-zitems,
  zitems_key TYPE zretc002-zitems,
  name1      TYPE lfa1-name1,
  END OF ty_t84,
  tt_t84 TYPE TABLE OF ty_t84,
*公司代码

  BEGIN OF ty_bukrs,
    sel        TYPE char1,
    bukrs      TYPE t001-bukrs,
    butxt      TYPE t001-butxt,
    zmdsx      TYPE zret0014-zmdsx,
    exclude    TYPE char1,
*    zitems     TYPE zretc002-zitems,
    zitems_key TYPE zretc002-zitems,
    seg        TYPE i,    "   分组号
    seq        TYPE i,    "   序号
    zxy_id     TYPE zret0006-zxy_id,
  END OF ty_bukrs,
  tt_bukrs TYPE TABLE OF ty_bukrs,

*  DC代码
  BEGIN OF ty_dcwrk,
    sel        TYPE char1,
    dcwrk      TYPE t001w-werks,
    name1      TYPE t001w-name1,
    exclude    TYPE char1,
*    zitems     TYPE zretc002-zitems,
    zitems_key TYPE zretc002-zitems,
  END OF ty_dcwrk,
  tt_dcwrk TYPE TABLE OF ty_dcwrk,

*  门店代码
  BEGIN OF ty_werks,
    sel        TYPE char1,
    werks      TYPE t001w-werks,
    name1      TYPE t001w-name1,
    exclude    TYPE char1,
*    zitems     TYPE zretc002-zitems,
    zitems_key TYPE zretc002-zitems,
    seg        TYPE i,    "   分组号
    seq        TYPE i,    "   序号
    zxy_id     TYPE zret0006-zxy_id,
  END OF ty_werks,
  tt_werks TYPE TABLE OF ty_werks,

*  采购组织
  BEGIN OF ty_ekorg,
    sel        TYPE char1,
    ekorg      TYPE t024e-ekorg,
    ekotx      TYPE t024e-ekotx,
    exclude    TYPE char1,
*    zitems     TYPE zretc002-zitems,
    zitems_key TYPE zretc002-zitems,
  END OF ty_ekorg,
  tt_ekorg TYPE TABLE OF ty_ekorg,

*  商品
  BEGIN OF ty_matnr.
    INCLUDE TYPE zret0008.
TYPES:
  sel        TYPE char1,
  maktx      TYPE makt-maktx,
  zitems     TYPE zretc002-zitems,
  zitems_key TYPE zretc002-zitems,
  ztmpid_bak TYPE zretc001-ztmpid,
  ztmpid     TYPE zretc001-ztmpid,
  seg        TYPE i,    "   分组号
  seq        TYPE i,    "   序号
  END OF ty_matnr,
  tt_matnr TYPE TABLE OF ty_matnr.


*  组织结构层级检查表
TYPES:
  BEGIN OF ty_wrk_check,
    werks   TYPE t001w-werks,
    exclude TYPE char1,
  END OF ty_wrk_check,
  tt_wrk_check TYPE TABLE OF ty_wrk_check.



*阶梯抬头数据
TYPES:
  BEGIN OF ty_t10.
    INCLUDE TYPE zret0010.
TYPES:
  zjt_id_tmp TYPE zret0010-zjt_id,
  END OF ty_t10,
  tt_t10 TYPE TABLE OF ty_t10.

*阶梯明细数据
TYPES:
  BEGIN OF ty_t11.
    INCLUDE TYPE zret0011.
TYPES:
  sel        TYPE char1,
  zflg_1st   TYPE char1,  "首行标识
  zflg_del   TYPE char1,  "删除标识
  zitems     TYPE zretc002-zitems,
  zitems_key TYPE zretc002-zitems,
  zjt_id_tmp TYPE zret0011-zjt_id,    "复制后的新阶梯号码


  END OF ty_t11,
  tt_t11 TYPE TABLE OF ty_t11.


*外部支付方
TYPES:
  BEGIN OF ty_t44.
    INCLUDE TYPE zret0044.
TYPES:
  name1      TYPE lfa1-name1,
  sel        TYPE char1,
  zitems_key TYPE zretc002-zitems,
  seq        TYPE i,
  ztmpid_bak TYPE zretc001-ztmpid,
  ztmpid     TYPE zretc001-ztmpid,
  zitems     TYPE zretc002-zitems,
  END OF ty_t44,
  tt_t44 TYPE TABLE OF ty_t44.

*计算规则模板数据
TYPES:
  BEGIN OF ty_tc05.
    INCLUDE TYPE zretc005.
TYPES:
  sel        TYPE char1,
  ztk_id_bak TYPE zreta002-ztk_id,
  zfjtk      TYPE char1,
  zghf_t     TYPE char35,
  zflzff_t   TYPE char35,
  lifnr_t    TYPE char35,
  END OF ty_tc05,
  tt_tc05 TYPE TABLE OF ty_tc05.

TYPES:
  BEGIN OF ty_ta02.
    INCLUDE TYPE zreta002.
TYPES:
  sel          TYPE char1,
  ztk_id_bak   TYPE zreta002-ztk_id,
  zfjtk        TYPE char1,
  zghf_t       TYPE char35,
  zflzff_t     TYPE char35,
  lifnr_t      TYPE char35,
  eknam        TYPE t024-eknam,
  ztmptxt      TYPE zretc001-ztmptxt,
  zspzid_txt   TYPE zret0009-zspzid_txt,
  zflspzid_txt TYPE zret0009-zspzid_txt,
  zfrgtx       TYPE zretc007-zfrgtx,
  frgc1        TYPE zretc007-frgc1,
  zdytk        TYPE zreta007-zdjbm,
  END OF ty_ta02,
  tt_ta02 TYPE TABLE OF ty_ta02.

*条款动态文本
TYPES:
  BEGIN OF ty_screen_lab,
    zbm TYPE char20,
    zms TYPE char20.
TYPES:
END OF ty_screen_lab.

*审批参数
TYPES:BEGIN OF ty_approval,
        frgsx  TYPE zretc007-frgsx,
        kolnr  TYPE zretc007-kolnr,
        frgc1  TYPE zretc007-frgc1,
        zfrgtx TYPE zretc007-zfrgtx,
        zxyzt  TYPE zreta002-zxyzt,
      END OF ty_approval.


** 返利屏幕字段属性
*TYPES:
*  BEGIN OF ty_data_scn.
*    INCLUDE TYPE zres0040.
*TYPES:
*END OF ty_data_scn,
*tt_data_scn TYPE TABLE OF ty_data_scn,
*
** 屏幕字段控制
*  BEGIN OF ty_data_scn_ctrl,
*    zxybstyp  TYPE  zretc009-zxybstyp,
*    ztktype   TYPE  zretc001-ztktype,
*    tcodetype TYPE  char1,
*    zfllx      TYPE  zreta002-zfllx,
*  END OF ty_data_scn_ctrl.


*批量编辑、显示结构
TYPES:
  BEGIN OF ty_mass,
    sel          TYPE char1,
    zflg_1st     TYPE char1,  "首行标识
    zflg_del     TYPE char1,  "删除标识

    zjt_id       TYPE zret0011-zjt_id,
    zjt_from     TYPE zret0011-zjt_from,
    zjt_by       TYPE zret0011-zjt_by,
    zjt_fz       TYPE zret0011-zjt_fz,
    zjt_fm       TYPE zret0011-zjt_fm,
    zitems       TYPE zretc002-zitems,
    zitems_key   TYPE zretc002-zitems,

    zbukrs       TYPE zreta001-zbukrs,
    zghf         TYPE zretc004-zghf,
    zzzpc        TYPE zretc004-zzzpc,
    zflzff       TYPE zret0044-zflzff,
    name1_zghf   TYPE lfa1-name1,
    name1_zflzff TYPE lfa1-name1,

  END OF ty_mass,
  tt_mass TYPE TABLE OF ty_mass.


*返利条款与附加条款关系
TYPES:
  BEGIN OF ty_ta03.
    INCLUDE TYPE zreta003.
TYPES:
  sel TYPE char1,
  END OF ty_ta03,
  tt_ta03 TYPE TABLE OF ty_ta03.

*返利协议与附加协议关系
TYPES:
  BEGIN OF ty_ta04.
    INCLUDE TYPE zreta004.
TYPES:
  sel               TYPE char1,
  ztk_id_plus       TYPE zreta002-ztk_id,
  zitems_key        TYPE zretc002-zitems,
  zitems_key_zxy_id TYPE zretc002-zitems,    "在未生成协议号时，用于记录 A类型附加协议的行项目
  END OF ty_ta04,
  tt_ta04 TYPE TABLE OF ty_ta04.




**********************************************************************
* 35 & 41 程序共用的全局变量区

CONSTANTS:
  gc_max_dec TYPE  zret0011-zjt_by VALUE '999999999999999', "阶梯 至最大值
  gc_all     TYPE c LENGTH 4 VALUE 'ALL'.           "组织机构 写入ALL值


DATA:
  gt_vls_ztk_id_plus TYPE vrm_values,
  gt_vls_zatkrl      TYPE vrm_values,
  gt_vls_zctgr       TYPE vrm_values,
  gt_vls_zspz_id     TYPE vrm_values,
  gt_vls_zclrid      TYPE vrm_values,
  gt_vls_ztmpid      TYPE vrm_values,
  gt_vls_zjsff       TYPE vrm_values,
  gt_vls_zhtlx       TYPE vrm_values,
  gt_vls_zfllx       TYPE vrm_values,
  gt_vls_zhszq       TYPE vrm_values,
  gt_vls_zjszq       TYPE vrm_values,
  gt_vls_zhsjz       TYPE vrm_values,
  gt_vls_zxybstyp    TYPE vrm_values,
  gt_vls_zatktp      TYPE vrm_values,
  gt_vls_zfldfsj     TYPE vrm_values.


DATA:
*  gt_tc01      TYPE tt_tc01,
  gt_tc02      TYPE tt_tc02,
  gs_tc02_sub  TYPE LINE OF tt_tc02,
  gt_tc03      TYPE tt_tc03,


  gs_tc01      TYPE LINE OF tt_tc01,
  gs_tc02      TYPE LINE OF tt_tc02,
  gs_tc03      TYPE LINE OF tt_tc03,

  gt_tc04_set  TYPE tt_tc04,      "行项目供货方-合集

  gt_zzlbm_set TYPE tt_t81,
  gt_zqdbm_set TYPE tt_t82,
  gt_zzgys_set TYPE tt_t84,
  gt_bukrs_set TYPE tt_bukrs,
  gt_dcwrk_set TYPE tt_dcwrk,
  gt_werks_set TYPE tt_werks,
  gt_ekorg_set TYPE tt_ekorg,

  gt_tc04      TYPE tt_tc04,

  gt_zzlbm     TYPE tt_t81,
  gt_zqdbm     TYPE tt_t82,
  gt_zzgys     TYPE tt_t84,
  gt_bukrs     TYPE tt_bukrs,
  gt_dcwrk     TYPE tt_dcwrk,
  gt_werks     TYPE tt_werks,
  gt_ekorg     TYPE tt_ekorg,
  gt_matnr     TYPE tt_matnr,     "商品明细-行项目
  gt_matnr_set TYPE tt_matnr,     "商品明细-合集
  gs_werks     TYPE LINE OF tt_werks,
  gs_dcwrk     TYPE LINE OF tt_dcwrk,
  gs_zzlbm     TYPE LINE OF tt_t81,
  gs_zqdbm     TYPE LINE OF tt_t82,
  gs_zzgys     TYPE LINE OF tt_t84,
  gs_bukrs     TYPE LINE OF tt_bukrs,
  gs_ekorg     TYPE LINE OF tt_ekorg,
  gs_matnr     TYPE LINE OF tt_matnr,
  gs_tc04      TYPE LINE OF tt_tc04.

DATA:
  gv_icon_xy_sub TYPE icon-id,
  gv_icon_jt_sub TYPE icon-id,
  gv_icon_zflzff TYPE icon-id,
  gv_icon_zghf   TYPE icon-id,
  gv_icon_zzlbm  TYPE icon-id,
  gv_icon_zqdbm  TYPE icon-id,
  gv_icon_bukrs  TYPE icon-id,
  gv_icon_dcwrk  TYPE icon-id,
  gv_icon_werks  TYPE icon-id,
  gv_icon_ekorg  TYPE icon-id,
  gv_icon_matnr  TYPE icon-id,
  gv_icon_zmzmx  TYPE icon-id,
  gv_icon_lifnr  TYPE icon-id.


DATA:
  gt_t44_all TYPE tt_t44,           "行项目返利支付方-合集
  gt_t44_sub TYPE tt_t44,
  gs_t44_sub TYPE LINE OF  tt_t44,
  gs_t11     TYPE ty_t11,
  gt_t11     TYPE tt_t11,
  gs_t11_sub TYPE LINE OF tt_t11,
  gt_t11_sub TYPE tt_t11,
  gt_t11_all TYPE tt_t11.       "行项目阶梯数据

DATA:
  gt_tc05 TYPE  tt_tc05,
  gs_tc05 TYPE LINE OF tt_tc05.

DATA:
  gs_ta03     TYPE LINE OF tt_ta03,
  gt_ta03     TYPE  tt_ta03,
  gt_ta04     TYPE  tt_ta04,
  gs_ta04_sub TYPE LINE OF  tt_ta04,
  gt_ta04_sub TYPE  tt_ta04.


DATA:
  ok_code          TYPE sy-ucomm,                          "
  gv_code          TYPE sy-ucomm,                          "
  gv_zjt_fz_t      TYPE char10,                            "分子
  gv_zjt_fm_t      TYPE char10,                            "分母
*  gv_actvt         TYPE activ_auth,                        "权限actvt
  gv_flg_rb        TYPE char2,                             "单选按钮
  gv_flg_call      TYPE char2,                             "调用标识
  gv_cursor_line   TYPE i,                                 "光标行
  gv_index_line    TYPE i,                                 "内表行
  gv_flg_comm      TYPE char10,                            "command 标识
  gv_flg_spz_exist TYPE char1,                             "商品组新增标识
  gv_title         TYPE char20,                            "程序标题
  gv_title_01      TYPE char20,                            "主屏幕标题1
  gv_title_02      TYPE char20,                            "主屏幕标题2
  gv_title_mass01  TYPE char20,                            "MASS标题1
  gv_title_mass02  TYPE char20,                            "MASS标题2
  gs_screen_lab    TYPE ty_screen_lab.
DATA:
  gs_data_base     TYPE ty_data_base.

DATA:
  gt_data_scn      TYPE zrei0024,
  gs_data_scn_ctrl TYPE zres0056.
*  gt_data_scn      TYPE tt_data_scn,
*  gs_data_scn_ctrl TYPE ty_data_scn_ctrl.


DATA:
  gt_mass TYPE tt_mass,
  gs_mass TYPE ty_mass.

DATA:
  gv_zitem_key    TYPE zretc002-zitems,      "全局唯一行项目KEY
  gv_zitem_key_cx TYPE zretc002-zitems.      "全局唯一行项目KEY-促销返利行项目规则



**********************************************************************