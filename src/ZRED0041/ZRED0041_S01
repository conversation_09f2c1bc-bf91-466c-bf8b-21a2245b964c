*&---------------------------------------------------------------------*
*& 包含               ZRED0035_S01
*&---------------------------------------------------------------------*

*TABLES:
*  zreta001.

**********************************************************************
*   屏幕元素分组 101 基本输入组  102 保存后仍可编辑 103 仅显示但是受控配置表
*                2* 显示/隐藏   201 仅35程序显示 202 仅41程序显示 203 付款返利时隐藏
*                3* 输入/变灰   301 行项目字段  302 付款返利时 变灰
*                4* 根据条款类型限制 显示/隐藏   401 固定类隐藏
*                                                402 计算类隐藏
*                                                403 促销类隐藏
*                                                404 固定类、促销类隐藏
*                                                501 添加条款屏幕控制
*                                                411 MASS 阶梯
*                                                412 MASS 供货方
*                                                413 MASS 付款方
*
**********************************************************************


TABLES:
  sscrfields.


DATA:

  gs_ta01 TYPE LINE OF tt_ta01,
  gs_ta02 TYPE LINE OF tt_ta02.

*DATA:
*  gs_data_base     TYPE ty_data_base,
*  gt_data_scn      TYPE tt_data_scn,
*  gs_data_scn_ctrl TYPE ty_data_scn_ctrl.

*DATA: gs_data_base_35 TYPE ty_data_base_35.

DATA:
  gs_tk_add  TYPE ty_tk_plus.
*  gs_tk_plus TYPE ty_tk_plus,
*  gt_tk_plus TYPE tt_tk_plus.


DATA:
*  gt_t06 TYPE tt_t06,
*  gt_t08 TYPE tt_t08,
  gt_t14 TYPE tt_t14,
  gt_t13 TYPE tt_t13,
  gt_t12 TYPE tt_t12,
  gs_t13 TYPE LINE OF tt_t13,
  gs_t12 TYPE LINE OF tt_t12,
  gt_t44 TYPE tt_t44,   "外部支付方
  gs_t44 TYPE LINE OF tt_t44.


DATA: functxt TYPE smp_dyntxt.


DATA gv_fname_ic1 TYPE field_name.


SELECTION-SCREEN BEGIN OF BLOCK b01 WITH FRAME TITLE TEXT-001.

PARAMETERS:

  p_zfllx  TYPE zreta002-zfllx AS LISTBOX  VISIBLE LENGTH 20  DEFAULT '' USER-COMMAND uc02 MODIF ID m01,
  p_zxybtp TYPE zretc009-zxybstyp AS LISTBOX  VISIBLE LENGTH 20  DEFAULT '' USER-COMMAND uc05 MODIF ID m01,
  p_zht_id TYPE zreta001-zht_id MODIF ID m01  MATCHCODE OBJECT zresh0016,
  p_ztmpid TYPE zretc001-ztmpid MODIF ID m01 AS LISTBOX  VISIBLE LENGTH 20 USER-COMMAND uc03.

**PARAMETERS:
**  p_tk_idr TYPE zreta002-ztk_id MODIF ID m01 MATCHCODE OBJECT zresh0012 MEMORY ID ztk_idr NO-DISPLAY.

PARAMETERS:
  p_ztk_id TYPE zreta002-ztk_id MODIF ID m02 MATCHCODE OBJECT zresh0018 MEMORY ID ztk_id.

PARAMETERS:
  cb_fjtk TYPE char1 AS CHECKBOX MODIF ID m01 USER-COMMAND uc04.

PARAMETERS:
  p_call   TYPE char1 NO-DISPLAY MEMORY ID zcall.

PARAMETERS:
  p_file TYPE rlgrap-filename MODIF ID m05 .

PARAMETERS:
  p_file_c TYPE rlgrap-filename MODIF ID m07 .

SELECTION-SCREEN END OF BLOCK b01.


SELECTION-SCREEN BEGIN OF BLOCK b02 WITH FRAME TITLE TEXT-002 .

PARAMETERS:
  rb_add   TYPE char1 RADIOBUTTON GROUP g1 USER-COMMAND uc01 MODIF ID m10,      "新增
  rb_edit  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,                        "修改
  rb_dis   TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,                        "显示
  rb_rles  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,                        "审批
  rb_zlrsi TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,                        "新增行项目
  rb_chag  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10.    "预估返利          "固定协议预付返利调整

SELECTION-SCREEN END OF BLOCK b02.


SELECTION-SCREEN BEGIN OF BLOCK b03 WITH FRAME TITLE TEXT-002 .

PARAMETERS:
  rb_nref TYPE char1 RADIOBUTTON GROUP g2 DEFAULT 'X' USER-COMMAND uc04 MODIF ID m03,"不参考创建
  rb_ref  TYPE char1 RADIOBUTTON GROUP g2 MODIF ID m03.                              "参考其他条款
PARAMETERS:
  p_tk_idr TYPE zreta002-ztk_id MATCHCODE OBJECT zresh0018 MODIF ID m04.

SELECTION-SCREEN END OF BLOCK b03.


SELECTION-SCREEN BEGIN OF BLOCK b04 WITH FRAME TITLE TEXT-002 .

PARAMETERS: cb_impt TYPE char1 AS CHECKBOX MODIF ID m06 USER-COMMAND uc05."条款批量处理

PARAMETERS:rb_impti TYPE char1 RADIOBUTTON GROUP g3 MODIF ID m05.         "固定条款批量导入-创建

SELECTION-SCREEN BEGIN OF LINE.
PARAMETERS: rb_imptu TYPE char1 RADIOBUTTON GROUP g3 MODIF ID m05.        "固定条款批量导入-新增协议
SELECTION-SCREEN COMMENT 3(20) TEXT-100 MODIF ID m05.
PARAMETERS: cb_pass TYPE char1 AS CHECKBOX MODIF ID m05 DEFAULT ''.       "不需审批
SELECTION-SCREEN COMMENT 26(10) TEXT-101 MODIF ID m05.
SELECTION-SCREEN END OF LINE.

PARAMETERS:rb_imptt TYPE char1 RADIOBUTTON GROUP g3 MODIF ID m05.         "条款批量修改

SELECTION-SCREEN END OF BLOCK b04.

SELECTION-SCREEN BEGIN OF BLOCK b05 WITH FRAME TITLE TEXT-002 .

PARAMETERS:
  cb_yg TYPE char1 AS CHECKBOX DEFAULT '' USER-COMMAND uc06 MODIF ID m01.

PARAMETERS:
  p_tk_idy TYPE zreta002-ztk_id MODIF ID m01 MATCHCODE OBJECT zresh0018 MEMORY ID zht_idy .

SELECTION-SCREEN END OF BLOCK b05.

SELECTION-SCREEN FUNCTION KEY 1.
SELECTION-SCREEN FUNCTION KEY 2.
SELECTION-SCREEN FUNCTION KEY 3.

INITIALIZATION.

  functxt-icon_id   = icon_xls.
  functxt-quickinfo = '下载模板'.
  functxt-icon_text = '下载模板'.
  sscrfields-functxt_01 = functxt.


*  functxt-icon_id   = icon_xls.
*  functxt-quickinfo = '固定条款批量创建导入模板'.
*  functxt-icon_text = '固定条款批量创建导入模板'.
*  sscrfields-functxt_01 = functxt.

*  functxt-icon_id   = icon_xls.
*  functxt-quickinfo = '固定条款批量修改导入模板'.
*  functxt-icon_text = '固定条款批量修改导入模板'.
*  sscrfields-functxt_02 = functxt.
*
  functxt-icon_id   = icon_xls.
  functxt-quickinfo = '固定协议批量调整模板'.
  functxt-icon_text = '固定协议批量调整模板'.
  sscrfields-functxt_03 = functxt.

  PERFORM frm_screen_init.

  PERFORM frm_get_data_list_box        CHANGING   gt_vls_zfllx
                                                  gt_vls_zatkrl
                                                  gt_vls_zctgr
*                                                  gt_vls_zjsff
                                                  gt_vls_zhszq
                                                  gt_vls_zjszq
                                                  gt_vls_zhsjz
                                                  gt_vls_zhtlx
                                                  gt_vls_zxybstyp
                                                  gt_vls_zatktp
                                                  gt_vls_zfldfsj.

  PERFORM frm_set_list_box             USING      'P_ZFLLX'
                                                  gt_vls_zfllx.

  PERFORM frm_set_screen_data_1000.




AT SELECTION-SCREEN OUTPUT.

  PERFORM frm_set_screen.

  PERFORM frm_set_screen_data_1000.



AT SELECTION-SCREEN.

  PERFORM frm_set_screen_data_1000.

  PERFORM frm_fresh_screen USING gt_vls_zxybstyp
                           CHANGING p_zxybtp.

  CASE sscrfields-ucomm.
    WHEN 'FC01'.
      CASE 'X'.
        WHEN rb_impti.
          PERFORM frm_download_template(zbcs0001) USING 'ZRED0004' '固定条款批量导入-创建模板'.
        WHEN rb_imptu.
          PERFORM frm_download_template(zbcs0001) USING 'ZRED0005' '固定条款批量导入-新增协议模板'.
        WHEN rb_imptt.
          PERFORM frm_download_template(zbcs0001) USING 'ZRED0007' '条款批量修改模板'.
        WHEN OTHERS.
      ENDCASE.
*      PERFORM frm_download_template(zbcs0001) USING 'ZRED0004' '固定条款批量创建导入模板'.
*    WHEN 'FC02'.
*      PERFORM frm_download_template(zbcs0001) USING 'ZRED0005' '固定条款批量修改导入模板'.
    WHEN 'FC03'.
      PERFORM frm_download_template(zbcs0001) USING 'ZRED0006' '固定协议批量调整导入模板'.
    WHEN OTHERS.
  ENDCASE.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR p_file.
  PERFORM frm_select_file(zbcs0001) CHANGING p_file.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR p_file_c.
  PERFORM frm_select_file(zbcs0001) CHANGING p_file_c.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR p_zfllx.
  PERFORM frm_charg_vls_zfllx(zbcs0001)  USING p_zht_id
                                      CHANGING gt_vls_zfllx.
*  PERFORM frm_get_data_dynp USING 'P_ZTMPID'
*                                  ''.



START-OF-SELECTION.


  IF cb_impt = 'X'."条款批量处理
    PERFORM frm_main_impt.
  ELSEIF rb_chag = 'X'."固定协议批量调整
    PERFORM frm_main_impt_chag.
  ELSE.
    PERFORM frm_main.
  ENDIF.