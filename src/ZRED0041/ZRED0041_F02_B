*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F02_B
*&---------------------------------------------------------------------*


FORM frm_get_org_data_ztk_id  USING    pv_ztk_id TYPE zret0006-ztk_id
                             CHANGING
                                      ps_tc01 TYPE LINE OF tt_tc01
                                      pt_tc02 TYPE tt_tc02
                                      pt_tc03 TYPE tt_tc03
                                      pt_tc04 TYPE tt_tc04
                                      pt_t44_all TYPE tt_t44
                                      pt_matnr TYPE tt_matnr
                                      pt_zzgys TYPE tt_t84
  .

  DATA:
    ls_tc02  TYPE LINE OF tt_tc02,
    ls_matnr TYPE LINE OF tt_matnr,
    lt_t07   TYPE tt_t07,
    lt_t08   TYPE tt_t08,
    lt_t06   TYPE tt_t06.

  CLEAR: pt_tc02,pt_matnr.


  SELECT
    *
    FROM zret0006
    WHERE ztk_id = @pv_ztk_id
    INTO CORRESPONDING FIELDS OF TABLE @lt_t06.

  SELECT
    a~*
    FROM @lt_t06 AS i JOIN zret0007 AS a
                        ON i~zxy_id = a~zxy_id
    WHERE ztk_id = @pv_ztk_id
    INTO CORRESPONDING FIELDS OF TABLE @lt_t07.

  SELECT
    a~*
    FROM @lt_t06 AS i JOIN zret0008 AS a
                        ON i~zxy_id = a~zxy_id
    WHERE ztk_id = @pv_ztk_id
    INTO CORRESPONDING FIELDS OF TABLE @lt_t08.

  SELECT
    a~*
    FROM @lt_t06 AS i JOIN zret0071 AS a
                        ON i~zxy_id = a~zxy_id
    WHERE ztk_id = @pv_ztk_id
    INTO  TABLE @DATA(lt_t71).

  SELECT
    a~*
    FROM @lt_t06 AS i JOIN zret0084 AS a
                        ON i~zxy_id = a~zxy_id
    WHERE ztk_id = @pv_ztk_id
    INTO  TABLE @DATA(lt_t84).

  LOOP AT lt_t06 INTO DATA(ls_t06).
    CLEAR ls_tc02.
    PERFORM frm_data_conver_t06a USING ls_t06
                                CHANGING ls_tc02.
*****    获取key值
****    PERFORM frm_get_zitems_key CHANGING ls_tc02-zitems_key.
*    ls_tc02-zitems_key = ls_tc02-zitems.  %%%%%
    ls_tc02-zflg_exist = 'X'.
    APPEND ls_tc02 TO pt_tc02.
  ENDLOOP.

  LOOP AT lt_t07 INTO DATA(ls_t07).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zxy_id = ls_t07-zxy_id .
    IF sy-subrc EQ 0.
      ls_tc02-zspz_id = ls_t07-zspz_id.
      PERFORM frm_get_data_zspz_id USING    ls_tc02-zspz_id
                                 CHANGING ls_tc02-zspzid_txt.
      MODIFY pt_tc02 FROM ls_tc02 INDEX sy-tabix.
    ENDIF.
  ENDLOOP.

  LOOP AT lt_t08 INTO DATA(ls_t08).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zxy_id = ls_t08-zxy_id .
    IF sy-subrc EQ 0.
      ls_tc02-zje = ls_t08-zje.
      ls_tc02-zmwskz = ls_t08-zmwskz.
      ls_tc02-zdate = ls_t08-zdate.
      ls_tc02-matnr = ls_t08-matnr.
      ls_tc02-zsl = ls_t08-zsl.
      ls_tc02-zdate = ls_t08-zdate.
      ls_tc02-zflbz = ls_t08-zflbz.
      ls_tc02-zfljs = ls_t08-zfljs.
      ls_tc02-zjzzd = ls_t08-zjzzd.
      ls_tc02-zsqbm_xy = ls_t08-zsqbm_xy.
      PERFORM frm_get_data_matnr USING    ls_tc02-matnr
                                 CHANGING ls_tc02-meins
                                          ls_tc02-maktx.
      MODIFY pt_tc02 FROM ls_tc02 INDEX sy-tabix.

**      得到商品明细信息
*      CLEAR ls_matnr.
*      ls_matnr-zxy_id = ls_t08-zxy_id.
*      ls_matnr-zitems = ls_tc02-zitems.
*      ls_matnr-matnr = ls_t08-matnr.
*      ls_matnr-zmwskz = ls_t08-zmwskz.
*      APPEND ls_matnr TO pt_matnr.

    ENDIF.
  ENDLOOP.

  LOOP AT lt_t71 INTO DATA(ls_t71).

*      得到商品明细信息
    CLEAR ls_matnr.
    ls_matnr-zxy_id = ls_t71-zxy_id.
*      ls_matnr-zitems = ls_tc02-zitems.
    ls_matnr-matnr = ls_t71-matnr.
*      ls_matnr-zmwskz = ls_t08-zmwskz.
    PERFORM frm_get_maktx(zbcs0001) USING ls_matnr-matnr CHANGING ls_matnr-maktx.
    APPEND ls_matnr TO pt_matnr.

  ENDLOOP.

  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0013 AS a
                         ON i~zxy_id = a~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc04.


  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0044 AS a
                         ON i~zxy_id = a~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t44_all.


  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0014 AS a
                         ON i~zxy_id = a~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc03.

  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0084 AS a
                         ON i~zxy_id = a~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_zzgys.

  LOOP AT pt_tc03 INTO DATA(ls_tc03).
    READ TABLE lt_t06 INTO ls_t06 WITH KEY zxy_id = ls_tc03-zxy_id .
    IF sy-subrc EQ 0.
      ls_tc03-zitems = ls_t06-zitems.
      MODIFY pt_tc03 FROM ls_tc03.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_tc04 INTO DATA(ls_tc04).
    READ TABLE lt_t06 INTO ls_t06 WITH KEY zxy_id = ls_tc04-zxy_id .
    IF sy-subrc EQ 0.
      ls_tc04-zitems = ls_t06-zitems.
      MODIFY pt_tc04 FROM ls_tc04.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_matnr INTO ls_matnr.
    READ TABLE lt_t06 INTO ls_t06 WITH KEY zxy_id = ls_matnr-zxy_id .
    IF sy-subrc EQ 0.
      ls_matnr-zitems = ls_t06-zitems.
      MODIFY pt_matnr FROM ls_matnr.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_t44_all INTO DATA(ls_t44_all).
    READ TABLE lt_t06 INTO ls_t06 WITH KEY zxy_id = ls_t44_all-zxy_id .
    IF sy-subrc EQ 0.
      ls_t44_all-zitems = ls_t06-zitems.
      MODIFY pt_t44_all FROM ls_t44_all.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_zzgys INTO DATA(ls_zzgys).
    READ TABLE lt_t06 INTO ls_t06 WITH KEY zxy_id = ls_zzgys-zxy_id .
    IF sy-subrc EQ 0.
      ls_zzgys-zitems = ls_t06-zitems.
      MODIFY pt_zzgys FROM ls_zzgys.
    ENDIF.
  ENDLOOP.


  CLEAR:
        ps_tc01.
  SELECT SINGLE a~* FROM zretc001 AS a  JOIN zreta002 AS b
                                          ON a~ztmpid = b~ztmpid
    WHERE b~ztk_id = @pv_ztk_id
    INTO CORRESPONDING FIELDS OF @ps_tc01.


  PERFORM frm_pro_data_org CHANGING
                                  ps_tc01
                                  pt_tc02
                                  pt_tc03
                                  pt_tc04
                                  pt_t44_all
                                  pt_matnr
                                  pt_zzgys
                                  .

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_DATA_CONVER_01
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_T06
*&      <-- LS_TC02
*&---------------------------------------------------------------------*
FORM frm_data_conver_t06a  USING    ps_t06  TYPE LINE OF tt_t06
                         CHANGING ps_tc02 TYPE LINE OF tt_tc02.

*@锚点XY
* ls_tc02-ztmpid    = ls_t06-ztmpid.
  ps_tc02-zitems      = ps_t06-zitems.
* ls_tc02-zitemtxt  = ls_t06-zitemtxt.
  ps_tc02-zctgr       = ps_t06-zctgr.
  ps_tc02-zbukrs      = ps_t06-zbukrs.
  ps_tc02-zflsqf      = ps_t06-zflsqf.
* ps_tc02-zflzff    = ps_t06-zflzff.
* ls_tc02-zitzff    = ls_t06-zitzff.
  ps_tc02-zdffs       = ps_t06-zdffs.
  ps_tc02-zitsplr     = ps_t06-zitsplr.
  ps_tc02-zetsplr     = ps_t06-zetsplr.
  ps_tc02-zrtbuks     = ps_t06-zrtbuks.
  ps_tc02-zdffs       = ps_t06-zdffs.
  ps_tc02-zxyzt_06    = ps_t06-zxyzt.
  ps_tc02-zitzff      = ps_t06-zitzff.

  ps_tc02-zxy_id      = ps_t06-zxy_id.
  ps_tc02-zjt_id      = ps_t06-zjt_id.
  ps_tc02-ztk_id      = ps_t06-ztk_id.
  ps_tc02-zitemtxt    = ps_t06-zxy_txt.
  ps_tc02-frgsx       = ps_t06-frgsx.
  ps_tc02-kolnr       = ps_t06-kolnr.
  ps_tc02-zxybstyp_06 = ps_t06-zxybstyp.
  ps_tc02-zzsbs       = ps_t06-zzsbs.
  ps_tc02-ztxhyq      = ps_t06-ztxhyq.
  ps_tc02-zfklx       = ps_t06-zfklx.
  ps_tc02-ztkgys      = ps_t06-ztkgys.
*  ps_tc02-zpaytp   = ps_t06-zpaytp.

  ps_tc02-zcjrq       = ps_t06-zcjrq.
  ps_tc02-zcjsj       = ps_t06-zcjsj.
  ps_tc02-zcjr        = ps_t06-zcjr.
  ps_tc02-zxgrq       = ps_t06-zxgrq.
  ps_tc02-zxgsj       = ps_t06-zxgsj.
  ps_tc02-zxgr        = ps_t06-zxgr.
  ps_tc02-zsprq       = ps_t06-zsprq.
  ps_tc02-zspsj       = ps_t06-zspsj.
  ps_tc02-zspr        = ps_t06-zspr.
  ps_tc02-zqxrq       = ps_t06-zqxrq.
  ps_tc02-zqxsj       = ps_t06-zqxsj.
  ps_tc02-zqxr        = ps_t06-zqxr.

  SELECT SINGLE ztmpid INTO ps_tc02-ztmpid FROM zreta002 WHERE ztk_id = ps_tc02-ztk_id.
  SELECT SINGLE frgc1  INTO ps_tc02-frgc1  FROM zretc007 WHERE frgsx = ps_tc02-frgsx AND kolnr = ps_tc02-kolnr .

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_DATA_CONVER_02
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      <-- PS_T06
*&---------------------------------------------------------------------*
FORM frm_data_conver_t06b  USING  ps_tc02 TYPE LINE OF tt_tc02
                                  ps_ta01 TYPE LINE OF tt_ta01
                                  ps_ta02 TYPE LINE OF tt_ta02
                                  pv_flg_zlrsi TYPE char1
                         CHANGING ps_t06  TYPE LINE OF tt_t06.


*@锚点XY
  ps_t06-zitems    =  ps_tc02-zitems   .
  ps_t06-zctgr     =  ps_tc02-zctgr     .
  ps_t06-zbukrs    =  ps_tc02-zbukrs   .
  ps_t06-zflsqf    =  ps_tc02-zflsqf   .
* ps_t06-zflzff  =  ps_tc02-zflzff   .
  ps_t06-zdffs     =  ps_tc02-zdffs     .
  ps_t06-zitsplr   =  ps_tc02-zitsplr .
  ps_t06-zetsplr   =  ps_tc02-zetsplr .
  ps_t06-zrtbuks   =  ps_tc02-zrtbuks .
  ps_t06-zxy_id    =  ps_tc02-zxy_id   .
  ps_t06-ztk_id    =  ps_tc02-ztk_id   .
  ps_t06-zjt_id    =  ps_tc02-zjt_id   .
* ps_t06-zdffs   =  ps_tc02-zdffs   .
  ps_t06-zitzff    =  ps_tc02-zitzff   .
  ps_t06-zxy_txt   =  ps_tc02-zitemtxt   .
*  ps_t06-zpaytp =  ps_tc02-zpaytp   .
  ps_t06-zcjrq     = ps_tc02-zcjrq   .
  ps_t06-zcjsj     = ps_tc02-zcjsj   .
  ps_t06-zcjr      = ps_tc02-zcjr    .
  ps_t06-zxgrq     = ps_tc02-zxgrq   .
  ps_t06-zxgsj     = ps_tc02-zxgsj   .
  ps_t06-zxgr      = ps_tc02-zxgr    .
  ps_t06-zsprq     = ps_tc02-zsprq   .
  ps_t06-zspsj     = ps_tc02-zspsj   .
  ps_t06-zspr      = ps_tc02-zspr    .
  ps_t06-zqxrq     = ps_tc02-zqxrq   .
  ps_t06-zqxsj     = ps_tc02-zqxsj   .
  ps_t06-zqxr      = ps_tc02-zqxr    .
  ps_t06-zhtlx     = ps_ta01-zhtlx   .
  ps_t06-ztktype   = ps_ta02-ztktype   .
  ps_t06-zfllx     = ps_ta02-zfllx   .
  ps_t06-zbegin    = ps_ta02-zbegin   .
  ps_t06-zend      = ps_ta02-zend   .
  ps_t06-zhstype   = ps_ta02-zhstype   .
  ps_t06-zjszq     = ps_ta02-zjszq   .
  ps_t06-zhszq     = ps_ta02-zhszq   .
  ps_t06-zxybstyp  = ps_ta02-zxybstyp   .
  ps_t06-zzsbs     = ps_tc02-zzsbs   .
  ps_t06-ztxhyq    = ps_tc02-ztxhyq  .
  ps_t06-zfklx     = ps_tc02-zfklx   .
  ps_t06-ztkgys    = ps_tc02-ztkgys  .
*  IF ps_tc02-sel_man = '' AND ps_tc02-zxy_id IS NOT INITIAL.
*    ps_t06-zxyzt = 'D'.
*  ELSE.
*    ps_t06-zxyzt = 'N'.
*  ENDIF.
**  IF ps_tc02-zstatus = 'D'.
**    ps_t06-zxyzt = 'D'.
**  ELSE.
**    ps_t06-zxyzt =  ps_tc02-zstatus.
**  ENDIF.

*  IF rb_zlrsi = 'X' .
  IF pv_flg_zlrsi = 'X' .
    IF ps_tc02-zxyzt_06 = 'D' OR
       ps_tc02-zxyzt_06 = 'N' OR
       ps_tc02-zxyzt_06 = 'R' OR
       ps_tc02-zxyzt_06 = ''   .
      PERFORM frm_get_frgsx     USING    ps_ta01
                                         ps_ta02
                               CHANGING  ps_t06.

      IF ps_tc02-sel_man = '' .
        ps_t06-zxyzt = 'D'.
      ELSE.
        ps_t06-zxyzt = 'N'.
      ENDIF.
    ELSE.
      ps_t06-zxyzt = ps_tc02-zxyzt_06.
      ps_t06-frgsx = ps_tc02-frgsx.
      ps_t06-kolnr = ps_tc02-kolnr.
    ENDIF.

  ELSE.
    IF ps_tc02-sel_man = ''.
      ps_t06-zxyzt = 'D'.
    ELSE.
      ps_t06-zxyzt = 'N'.
    ENDIF.
    ps_t06-frgsx = ps_ta02-frgsx.
    ps_t06-kolnr = ps_ta02-kolnr.
  ENDIF.

  "创建单据协议级创建的日期缺失补充
  IF ps_t06-zcjrq IS INITIAL OR ps_t06-zcjrq = '00000000'  .
    ps_t06-zcjrq     = ps_ta02-zcjrq   .
    ps_t06-zcjsj     = ps_ta02-zcjsj   .
    ps_t06-zcjr      = ps_ta02-zcjr    .
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_TK_DELETE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_TA03
*&      <-- GT_TA04
*&---------------------------------------------------------------------*
FORM frm_tk_delete  CHANGING pt_ta03  TYPE tt_ta03
                             pt_ta04 TYPE tt_ta04.




  SELECT
    a~zxy_id
    FROM @pt_ta03 AS i JOIN zret0006 AS a
                        ON i~zrlid = a~ztk_id
                       AND i~sel  = 'X'
                       AND i~zatktp = 'P'
    INTO TABLE @DATA(lt_t06_tmp).

  SORT lt_t06_tmp BY zxy_id.
  LOOP AT pt_ta04 INTO DATA(ls_ta04).
    READ TABLE lt_t06_tmp TRANSPORTING NO FIELDS WITH KEY zxy_id = ls_ta04-zrlid BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE pt_ta04.
      CONTINUE.
    ENDIF.
  ENDLOOP.

  LOOP AT pt_ta03 INTO DATA(ls_ta03) WHERE sel = 'X' AND zatktp = 'A'.
    DELETE pt_ta04 WHERE zitems = ls_ta03-zitems AND zatktp = 'A'.
  ENDLOOP.

  DELETE pt_ta03 WHERE sel = 'X'.



*  若附加条款关系表为空，则附加协议关系表也为空
  IF pt_ta03[] IS INITIAL.
    CLEAR pt_ta04[].
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_DATA_TK_PLUS_DIS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TK_ADD
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_data_tk_plus_dis  USING    ps_tk_add TYPE ty_tk_plus
                                    ps_ta02 TYPE LINE OF tt_ta02
                                    ps_tc05 TYPE LINE OF tt_tc05
                           CHANGING pv_mtype       TYPE bapi_mtype
                                    pv_msg         TYPE bapi_msg
                                    pt_tc02 TYPE tt_tc02
                                    pt_ta03  TYPE tt_ta03
                                    pt_ta04 TYPE tt_ta04
                                    lt_msglist TYPE scp1_general_errors.

  DATA:
*    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.

  DATA(lt_tc02)  = pt_tc02[].

  PERFORM frm_get_valid_tc02 CHANGING lt_tc02.


*****  IF ps_tk_add-zctgr = 'C'.
*****    LOOP AT lt_tc02 TRANSPORTING NO FIELDS WHERE sel_man = 'X' AND zctgr NE 'C'.
*****      EXIT.
*****    ENDLOOP.
*****    IF sy-subrc EQ 0.
*****      PERFORM frm_add_msg USING '组织机构中组织级别不能大于项目公司'  CHANGING lt_msglist.
*****    ENDIF.
*****  ELSEIF ps_tk_add-zctgr = 'R'.
*****    LOOP AT lt_tc02 TRANSPORTING NO FIELDS WHERE sel_man = 'X' AND zctgr EQ 'A'.
*****      EXIT.
*****    ENDLOOP.
*****    IF sy-subrc EQ 0.
*****      PERFORM frm_add_msg USING '组织机构中组织级别不能大于区域平台'  CHANGING lt_msglist.
*****    ENDIF.
*****  ENDIF.

  SELECT SINGLE zleib INTO @DATA(lv_zleib) FROM  zreta002 WHERE ztk_id = @ps_tk_add-ztk_id_plus.
  IF sy-subrc = 0 AND lv_zleib = 'R' .
    PERFORM frm_add_msg USING '该条款为条款申请,不能附加'  CHANGING lt_msglist.
  ENDIF.

  IF ps_tk_add-zatktp = 'P'.
    READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zrlid = ps_tk_add-ztk_id_plus.
    IF sy-subrc EQ 0.
      PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'  CHANGING lt_msglist.
    ENDIF.
  ELSEIF ps_tk_add-zatktp = 'A'.
    READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zatktp = ps_tk_add-zatktp zatkrl = ps_tk_add-zatkrl zctgr = ps_tk_add-zctgr.
    IF sy-subrc EQ 0.
      PERFORM frm_add_msg USING '该附加条款已经添加,不能重复添加'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.



  PERFORM frm_data_check_tk_plus USING ps_tk_add CHANGING lt_msglist.


  READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zatktp = 'A' .
  IF sy-subrc EQ 0.
    MESSAGE i888(sabapdocu) WITH '该条款目前已存在总返利分摊附加条款，请知悉！' DISPLAY LIKE 'I'.
  ENDIF.

  READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zatktp = 'P' .
  IF sy-subrc EQ 0.
    MESSAGE i888(sabapdocu) WITH '该条款目前已存在附加条款,请知悉！' DISPLAY LIKE 'I'.
  ENDIF.


  IF lt_msglist[] IS INITIAL.

*    行项目ID自增
    SORT pt_ta03 BY zitems DESCENDING.
    READ TABLE pt_ta03 INTO DATA(ls_ta03_max) INDEX 1.
    ps_tk_add-zitems = ls_ta03_max-zitems + 1.

    PERFORM frm_tk_plus_dis USING     ps_tk_add
                                      lt_tc02
                                      ps_ta02
                                      ps_tc05
                            CHANGING
                                      pt_ta03
                                      pt_ta04
                                      lt_msglist.

  ENDIF.

  IF lt_msglist[] IS NOT INITIAL.

*        CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
*          EXPORTING
*            title_text    = '消息提示'
*            sort_by_level = ' '
*            show_ids      = ''
*            message_list  = gt_cmd_msglist[].

    pv_mtype = 'E'.
    pv_msg = '数据检查未通过，操作已终止!'.
  ELSE.
    pv_mtype = 'S'.
    pv_msg = '附加条款添加成功!'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_TK_PLUS_DIS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TK_ADD
*&      --> LT_TC02
*&      <-- PT_TA04
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_tk_plus_dis  USING    ps_tk_add  TYPE ty_tk_plus
                               pt_tc02  TYPE tt_tc02
                               ps_ta02  TYPE LINE OF tt_ta02
                                    ps_tc05 TYPE LINE OF tt_tc05
                      CHANGING

                               pt_ta03  TYPE tt_ta03
                               pt_ta04  TYPE tt_ta04
                               pt_msglist TYPE scp1_general_errors.

  DATA:
    lt_tc02_plus TYPE tt_tc02,
    lt_ta04      TYPE  tt_ta04,
    lt_ta04_all  TYPE  tt_ta04,
    ls_ta04      TYPE LINE OF tt_ta04,
    ls_ta03      TYPE LINE OF tt_ta03.


  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.
  DATA:
    lv_flg_plused     TYPE char1,     "单行匹配成功
    lv_flg_plused_any TYPE char1.     "任意一条匹配成功

  DATA:
        lt_t06 TYPE tt_t06.
  DATA:
        lv_flg TYPE char1.

  CLEAR lv_flg_plused_any.


  IF ps_tk_add-zatktp = 'P'.
    IF ps_tc05-zjsff = 'R' .
      PERFORM frm_add_msg USING '若阶梯计算方法为“R-补差”，则附加条款不允许增加"附加条款类型为P“的附加条款'  CHANGING pt_msglist.
    ENDIF.
  ELSEIF ps_tk_add-zatktp = 'A'.

*    核算基准检查
    PERFORM frm_check_zhsjz USING ps_tc05-zhsjz CHANGING pt_msglist.

  ENDIF.

  IF ps_tk_add-zatktp = 'A' AND ps_tk_add-zatkrl = 'F' AND ps_tk_add-zctgr = 'F'.
    LOOP AT pt_ta03 TRANSPORTING NO FIELDS WHERE zatktp = 'A' AND ( zatkrl NE 'F' OR zctgr NE 'F' ).
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      PERFORM frm_add_msg USING '若条款类型=A,条款规则F与其他类型不能同时添加'
                           CHANGING pt_msglist.
    ENDIF.
  ENDIF.

  IF pt_msglist[] IS NOT INITIAL.
    RETURN.
  ENDIF.

*  获取附加条款对应的附加协议
*  SELECT
*    a~*
*    FROM zret0006 AS a JOIN zreta004 AS b
*                         ON a~ztk_id = b~zrlid
*    WHERE a~ztk_id = @ps_tk_add-ztk_id_plus
*    AND   b~zatkrl = @ps_tk_add-zatkrl
*    AND   a~zctgr  = @ps_tk_add-zctgr
*    INTO TABLE @DATA(lt_t06).

  SELECT
    a~*
    FROM zret0006 AS a
    WHERE a~ztk_id = @ps_tk_add-ztk_id_plus
*    AND   a~zctgr  = @ps_tk_add-zctgr
    AND   a~zxyzt NE 'D'
    INTO CORRESPONDING FIELDS OF TABLE @lt_t06.
*    INTO TABLE @DATA(lt_t06).


  CLEAR:lt_ta04_all[].
*    将所有的附加协议放在一起统一进行检查
  APPEND LINES OF pt_ta04 TO lt_ta04_all.

  IF ps_tk_add-zatktp = 'P'.

    LOOP AT pt_tc02 INTO DATA(ls_tc02).


      CLEAR lv_flg_plused.

      CLEAR ls_ta04.
      ls_ta04-zatkrl      = ps_tk_add-zatkrl.
      ls_ta04-zctgr       = ps_tk_add-zctgr.
      ls_ta04-ztk_id_plus = ps_tk_add-ztk_id_plus.    "附加条款

      ls_ta04-zitems_key  = ls_tc02-zitems_key.
      ls_ta04-zxy_id  = ls_tc02-zxy_id.

*      ls_ta04-zatktp      = ps_ta02-ztktype.
      ls_ta04-zatktp      = ps_tk_add-zatktp.
      ls_ta04-zatktxt     = ps_ta02-ztk_txt.
      ls_ta04-zatkpi      = 'C'.

      IF ps_tk_add-zctgr = 'A'.
        READ TABLE lt_t06 INTO DATA(ls_t06) WITH KEY zctgr = 'A'  .
        IF sy-subrc EQ 0.

          PERFORM frm_tk_plus_exe_p USING ls_tc02
                                        ls_t06
                                        lt_ta04_all
                                  CHANGING ls_ta04
                                           lv_flg_plused
                                           pt_msglist.

*          READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
*          IF sy-subrc EQ 0.
*            CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*            EXIT.
*          ELSE.
*            ls_ta04-zrlid = ls_t06-zxy_id.
*            lv_flg_plused = 'X'.
*          ENDIF.

        ELSE.
          CLEAR lv_msgv1. lv_msgv1 = '没有匹配到全国量的协议' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
          EXIT.
        ENDIF.
      ELSEIF ps_tk_add-zctgr = 'R'.

        IF ls_tc02-zctgr = 'A'.

          READ TABLE lt_t06 INTO ls_t06 WITH KEY  zctgr = 'A'.
          IF sy-subrc EQ 0.

            PERFORM frm_tk_plus_exe_p USING ls_tc02
                                          ls_t06
                                          lt_ta04_all
                                    CHANGING ls_ta04
                                             lv_flg_plused
                                             pt_msglist.

*            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
*            IF sy-subrc EQ 0.
*              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
*            ELSE.
*              ls_ta04-zrlid = ls_t06-zxy_id.
*              lv_flg_plused = 'X'.
*            ENDIF.

          ELSE.
            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到项目公司的协议' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

            EXIT.
          ENDIF.
        ELSE.
          READ TABLE lt_t06 INTO ls_t06 WITH KEY zbukrs = ls_tc02-zrtbuks .
          IF sy-subrc EQ 0.

            PERFORM frm_tk_plus_exe_p USING ls_tc02
                                          ls_t06
                                          lt_ta04_all
                                    CHANGING ls_ta04
                                             lv_flg_plused
                                             pt_msglist.

*            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
*            IF sy-subrc EQ 0.
*              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
*            ELSE.
*              ls_ta04-zrlid = ls_t06-zxy_id.
*              lv_flg_plused = 'X'.
*            ENDIF.

          ELSE.
            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到上级平台公司的协议' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

            EXIT.
          ENDIF.

        ENDIF.


      ELSEIF ps_tk_add-zctgr = 'C'.

        IF ls_tc02-zctgr = 'A'.

          READ TABLE lt_t06 INTO ls_t06 WITH KEY  zctgr = 'A'.
          IF sy-subrc EQ 0.

            PERFORM frm_tk_plus_exe_p USING ls_tc02
                                          ls_t06
                                          lt_ta04_all
                                    CHANGING ls_ta04
                                             lv_flg_plused
                                             pt_msglist.

*            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
*            IF sy-subrc EQ 0.
*              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
*            ELSE.
*              ls_ta04-zrlid = ls_t06-zxy_id.
*              lv_flg_plused = 'X'.
*            ENDIF.

          ELSE.
            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到项目公司的协议' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

            EXIT.
          ENDIF.
        ELSE.
          READ TABLE lt_t06 INTO ls_t06 WITH KEY zbukrs = ls_tc02-zbukrs zctgr = ls_tc02-zctgr.
          IF sy-subrc EQ 0.

            PERFORM frm_tk_plus_exe_p USING ls_tc02
                                          ls_t06
                                          lt_ta04_all
                                    CHANGING ls_ta04
                                             lv_flg_plused
                                             pt_msglist.

*            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
*            IF sy-subrc EQ 0.
*              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
*            ELSE.
*              ls_ta04-zrlid = ls_t06-zxy_id.
*              lv_flg_plused = 'X'.
*            ENDIF.

          ELSE.
            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到项目公司的协议' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

            EXIT.
          ENDIF.

        ENDIF.

      ELSE.
        CLEAR lv_msgv1. lv_msgv1 = '不支持的行项目关联规则' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

        EXIT.
      ENDIF.


      IF lv_flg_plused = 'X'.
        lv_flg_plused_any = 'X'.
      ELSE.
*        若没有匹配成功，则跳出不保存 ta04表
        CONTINUE.
      ENDIF.


*    保存分配后的附加协议和主协议的关系
      SELECT SINGLE  ztktype
                     ztk_id     "附加协议对应的附加条款
        INTO ( ls_ta04-zatktp ,ls_ta04-ztk_id_plus ) FROM zret0006 WHERE zxy_id = ls_ta04-zrlid.
      APPEND ls_ta04 TO lt_ta04.

*      同时也汇入全部的集合中，用于数据检查
      APPEND ls_ta04 TO lt_ta04_all.

    ENDLOOP.

  ELSEIF ps_tk_add-zatktp = 'A'.




    LOOP AT pt_tc02 INTO ls_tc02 WHERE sel_man = 'X' ."AND zctgr = ps_tk_add-zatkrl .
      IF ps_tk_add-zatkrl NE 'F' AND ls_tc02-zctgr NE ps_tk_add-zatkrl.
        CONTINUE.
      ENDIF.


      DATA(lt_tc02_tmp) = pt_tc02[].
*    删除自身,防止匹配到自身
      DELETE lt_tc02_tmp WHERE zitems_key = ls_tc02-zitems_key.


      CLEAR lv_flg_plused.

      CLEAR ls_ta04.
      ls_ta04-zatkrl      = ps_tk_add-zatkrl.
      ls_ta04-zctgr       = ps_tk_add-zctgr.
      ls_ta04-ztk_id_plus = ps_tk_add-ztk_id_plus.    "附加条款

      ls_ta04-zitems_key  = ls_tc02-zitems_key.
      ls_ta04-zxy_id  = ls_tc02-zxy_id.

      ls_ta04-zatktp      = ps_tk_add-zatktp.
      ls_ta04-zatktxt     = ps_ta02-ztk_txt.
      ls_ta04-zatkpi      = 'B'.

      IF ps_tk_add-zctgr = 'A'.
        READ TABLE lt_tc02_tmp INTO DATA(ls_tc02_tmp) WITH KEY sel_man = 'X' zctgr = 'A'  .
        IF sy-subrc EQ 0.
          READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
          IF sy-subrc EQ 0.
            CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
            EXIT.
          ELSE.
            ls_ta04-zrlid = ls_tc02_tmp-zxy_id.
            ls_ta04-zitems_key_zxy_id = ls_tc02_tmp-zitems_key.
            lv_flg_plused = 'X'.
          ENDIF.

        ELSE.
          CLEAR lv_msgv1. lv_msgv1 = '没有匹配到全国量的协议' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
          EXIT.
        ENDIF.
      ELSEIF ps_tk_add-zctgr = 'R'.

        READ TABLE lt_tc02_tmp INTO ls_tc02_tmp WITH KEY sel_man = 'X' zctgr = 'R' zbukrs = ls_tc02-zrtbuks .
        IF sy-subrc EQ 0.

          READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
          IF sy-subrc EQ 0.
            CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
            EXIT.
          ELSE.
            ls_ta04-zrlid = ls_tc02_tmp-zxy_id.
            ls_ta04-zitems_key_zxy_id = ls_tc02_tmp-zitems_key.
            lv_flg_plused = 'X'.
          ENDIF.

        ELSE.
*          IF ps_tk_add-zatkrl = 'C' AND ps_tk_add-zctgr = 'R' AND ls_tc02-zrtbuks = '3040'.

*            READ TABLE lt_tc02_tmp INTO ls_tc02_tmp WITH KEY sel_man = 'X' zctgr = 'A' zbukrs = '3040' .
          READ TABLE lt_tc02_tmp INTO ls_tc02_tmp WITH KEY sel_man = 'X'  zbukrs = ls_tc02-zrtbuks .
          IF sy-subrc EQ 0.

            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
            IF sy-subrc EQ 0.
              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
              EXIT.
            ELSE.
              ls_ta04-zrlid = ls_tc02_tmp-zxy_id.
              ls_ta04-zitems_key_zxy_id = ls_tc02_tmp-zitems_key.
              lv_flg_plused = 'X'.
            ENDIF.

*            ELSE.
*              CLEAR lv_msgv1. lv_msgv1 = '没有匹配到上级平台公司的协议' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
          ENDIF.

*          ELSE.
*            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到上级平台公司的协议' && '行项目ID:' && ls_tc02-zitems.
*            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*            EXIT.
*        ENDIF.
        ENDIF.
      ELSEIF ps_tk_add-zatkrl = 'F' AND ps_tk_add-zctgr = 'F'.

        PERFORM frm_get_zzff_attr USING ls_tc02-zflzff CHANGING lv_flg.
        IF lv_flg = 'I'.
          DATA(lv_zflzff_tmp) = ls_tc02-zflzff.
          lv_zflzff_tmp = |{ lv_zflzff_tmp ALPHA = OUT }|.
          IF ls_tc02-zpaytp = 'B'.
            DATA(lv_zctgr) = 'R'.
          ELSE.
*            lv_zctgr = ls_tc02-zctgr.
            lv_zctgr = ls_tc02-zpaytp.
          ENDIF .
          CONDENSE lv_zflzff_tmp.
          READ TABLE lt_tc02_tmp INTO ls_tc02_tmp WITH KEY sel_man = 'X'
                                                           zbukrs  = lv_zflzff_tmp
                                                           zctgr   = lv_zctgr .
          IF sy-subrc EQ 0.

            READ TABLE lt_ta04_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key .
            IF sy-subrc EQ 0.
*              CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*              PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*              EXIT.
            ELSE.
              ls_ta04-zrlid = ls_tc02_tmp-zxy_id.
              ls_ta04-zitems_key_zxy_id = ls_tc02_tmp-zitems_key.
              lv_flg_plused = 'X'.
            ENDIF.

          ELSE.
            CLEAR lv_msgv1. lv_msgv1 = '没有匹配到协议' && '行项目ID:' && ls_tc02-zitems.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
            EXIT.
          ENDIF.
        ENDIF.

      ELSE.
        CLEAR lv_msgv1. lv_msgv1 = '不支持的行项目关联规则' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

        EXIT.
      ENDIF.


      IF lv_flg_plused = 'X'.
        lv_flg_plused_any = 'X'.
      ELSE.
        CONTINUE.
      ENDIF.

*    保存分配后的附加协议和主协议的关系
      SELECT SINGLE ztk_id     "附加协议对应的附加条款
        INTO ( ls_ta04-ztk_id_plus ) FROM zret0006 WHERE zxy_id = ls_ta04-zrlid.
      APPEND ls_ta04 TO lt_ta04.

*      同时也汇入全部的集合中，用于数据检查
      APPEND ls_ta04 TO lt_ta04_all.

    ENDLOOP.

  ENDIF.

  IF lv_flg_plused_any = ''.
    CLEAR lv_msgv1. lv_msgv1 = '没有匹配到对应的附加条款' .
    PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

  ENDIF.


  IF pt_msglist[] IS INITIAL .

    IF ps_tk_add-zflg_add = 'X'.



*    保存分配后的附加条款和主条款的关系
      CLEAR ls_ta03.
      ls_ta03-ztk_id  = ps_ta02-ztk_id.
*    READ TABLE pt_ta03 TRANSPORTING NO FIELDS WITH KEY zrlid = ps_tk_add-ztk_id_plus.
*    IF sy-subrc NE 0.
      ls_ta03-zrlid = ps_tk_add-ztk_id_plus.
*      ls_ta03-zatktp = ps_ta02-ztktype.
      ls_ta03-zatktp = ps_tk_add-zatktp.
      IF ps_tk_add-zatktp = 'A'.
        ls_ta03-zatkpi = 'B'.
      ELSE.
        ls_ta03-zatkpi = 'C'.
      ENDIF.
      ls_ta03-zatktxt = ps_ta02-ztk_txt.
      ls_ta03-zatkrl = ps_tk_add-zatkrl.
      ls_ta03-zctgr = ps_tk_add-zctgr.
*    SELECT SINGLE ztktype INTO ls_ta03-zatktp FROM zret0006 WHERE ztk_id = ls_ta03-zrlid.

*    更新行项目ID
      ls_ta03-zitems = ps_tk_add-zitems.

      APPEND ls_ta03 TO pt_ta03.
*    ENDIF.

    ENDIF.

*    若无错误则将 附加协议和主协议的关系 、 主条款和附加条款的关系 添加到主线

*    更新行项目ID
    LOOP AT lt_ta04 INTO ls_ta04.
      ls_ta04-zitems = ps_tk_add-zitems.
      MODIFY lt_ta04 FROM ls_ta04.
    ENDLOOP.

    APPEND LINES OF lt_ta04 TO pt_ta04.

  ENDIF.


ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZHSJZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TC05
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_zhsjz  USING    pv_zhsjz  TYPE zretc005-zhsjz
                      CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_int TYPE int2.

  SELECT SINGLE * FROM zret0003 WHERE zhsjz = @pv_zhsjz INTO @DATA(ls_zret0003).

  IF ls_zret0003-zpur = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zsale = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zdist = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zactlpay = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zduepay = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zlower = 'X'.    lv_int = lv_int + 1.  ENDIF.
  IF ls_zret0003-zhigher = 'X'.    lv_int = lv_int + 1.  ENDIF.

  IF lv_int >= 2.
    PERFORM frm_add_msg USING '核算基准包括两种类型时，不允许创建母子分摊类附加条款!'  CHANGING pt_msglist.
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_DATA_CHECK_TK_PLUS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TK_ADD
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_data_check_tk_plus  USING    ps_tk_add TYPE ty_tk_plus
                             CHANGING lt_msglist TYPE scp1_general_errors.

  SELECT SINGLE * FROM zretc012 WHERE zatkrl = @ps_tk_add-zatkrl AND zctgr  = @ps_tk_add-zctgr INTO @DATA(lv_zatkrl).
  IF sy-subrc NE 0.
    IF ps_tk_add-zatktp = 'A'.
      PERFORM frm_add_msg USING '请输入正确的上下级协议级别'  CHANGING lt_msglist.
    ELSEIF ps_tk_add-zatktp = 'P'.
      PERFORM frm_add_msg USING '请输入正确的附加条款规则/行项目关联规则'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_VALID_TC02
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LT_TC02
*&---------------------------------------------------------------------*
FORM frm_get_valid_tc02  CHANGING pt_tc02 TYPE tt_tc02.

  DELETE pt_tc02 WHERE sel_man = ''.
*  DELETE pt_tc02 WHERE zxyzt_06 = 'D'.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_FRGSX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA01
*&      <-- PS_TA02
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F03
*&---------------------------------------------------------------------*
FORM frm_get_frgsx   USING        ps_ta01        TYPE LINE OF tt_ta01
                                   ps_ta02       TYPE LINE OF tt_ta02
                     CHANGING      ps_t06        TYPE LINE OF tt_t06 .

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_zrekey TYPE zres0042.
  DATA:lv_frgsx  TYPE frgsx.
  DATA:lt_return TYPE TABLE OF bapiret2.
  DATA:ls_return TYPE bapiret2.
  DATA:ls_approval TYPE ty_approval.

  ls_zrekey-zhtlx   =  ps_ta01-zhtlx.
  ls_zrekey-zbukrs  =  ps_ta01-zbukrs.
  ls_zrekey-ekgrp   =  ps_ta02-ekgrp.
  ls_zrekey-zfllx   =  ps_ta02-zfllx.
  ls_zrekey-ztktype =  ps_ta02-ztktype.

  CALL FUNCTION 'ZREFM0025'
    EXPORTING
      iv_zrestype = 'RA'
      is_zrekey   = ls_zrekey
    IMPORTING
      ev_frgsx    = lv_frgsx
    TABLES
      et_return   = lt_return.

  IF lv_frgsx IS NOT INITIAL.
    ps_t06-frgsx = lv_frgsx.

    MOVE-CORRESPONDING ps_ta02 TO ls_approval.
    CLEAR:ls_approval-kolnr.
    PERFORM frm_get_kolnr  CHANGING   ls_approval
                                      lt_msglist.
    MOVE-CORRESPONDING ls_approval TO ps_t06.

  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_TK_PLUS_EXE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      --> LT_TA04_ALL
*&      <-- LS_TA04
*&      <-- LV_FLG_PLUSED
*&      <-- PT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_tk_plus_exe_p  USING    ls_tc02  TYPE LINE OF tt_tc02
                               ls_t06 TYPE LINE OF tt_t06
                               lt_ta04_all  TYPE tt_ta04
                      CHANGING ls_ta04  TYPE LINE OF tt_ta04
                               lv_flg_plused  TYPE char1
                               pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.


  READ TABLE lt_ta04_all INTO DATA(ls_ta04_all)  WITH KEY zitems_key = ls_tc02-zitems_key .
  IF sy-subrc EQ 0.
    EXIT.
  ELSE.
    ls_ta04-zrlid = ls_t06-zxy_id.
    lv_flg_plused = 'X'.
  ENDIF.


*  DATA(lt_ta04_tmp) = lt_ta04_all[].
*  DELETE lt_ta04_tmp WHERE zatktp EQ 'A'.
*
*  READ TABLE lt_ta04_all INTO DATA(ls_ta04_all)  WITH KEY zitems_key = ls_tc02-zitems_key .
*  IF sy-subrc EQ 0.
*    CLEAR lv_msgv1. lv_msgv1 = '已经存在附加条款，不允许添加' && '行项目ID:' && ls_tc02-zitems.
*    PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
**    EXIT.
*  ELSE.
*    ls_ta04-zrlid = ls_t06-zxy_id.
*    lv_flg_plused = 'X'.
*  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_TK_PLUS_DIS_RE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02
*&      --> PT_TC02
*&      --> PT_TK_ADD
*&      <-- PT_TA03
*&      <-- PT_TA04
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_tk_plus_dis_re  USING    ps_ta02 TYPE LINE OF tt_ta02
                                  pt_tc02 TYPE  tt_tc02
                                    ps_tc05 TYPE LINE OF tt_tc05
                         CHANGING pt_ta03 TYPE tt_ta03
                                  pt_ta04 TYPE tt_ta04
                                  pt_msglist TYPE scp1_general_errors.

  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_tk_add  TYPE ty_tk_plus.


  CHECK pt_ta03[] IS NOT INITIAL.




*  重新匹配前需要删除掉已匹配附加条款的数据
*  删除前先备份
  DATA(lt_ta04_bak) = pt_ta04[].
  CLEAR pt_ta04[].
  SORT pt_ta03 BY zatktp.
  LOOP AT pt_ta03 INTO DATA(ls_ta03).


    DATA(lt_tc02)  = pt_tc02[].

    PERFORM frm_get_valid_tc02 CHANGING lt_tc02.

    CHECK lt_tc02[] IS NOT INITIAL.

*  重新匹配前需要删除掉已匹配附加条款的数据
*    LOOP AT lt_tc02 INTO DATA(ls_tc02).
*      READ TABLE pt_ta04 TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key
*                                                         ztk_id_plus = ls_ta03-zrlid.
*      IF sy-subrc EQ 0.
*        DELETE lt_tc02.
*        CONTINUE.
*      ENDIF.
*    ENDLOOP.


    CLEAR ls_tk_add.
    IF ls_ta03-zatktp = 'P'.
      ls_tk_add-zatktp = ls_ta03-zatktp.
      ls_tk_add-zatkrl = ls_ta03-zatkrl.
      ls_tk_add-zctgr = ls_ta03-zctgr.
      ls_tk_add-ztk_id_plus = ls_ta03-zrlid.
      ls_tk_add-zitems = ls_ta03-zitems.
      ls_tk_add-zflg_add = ''.
    ELSEIF ls_ta03-zatktp = 'A'.
      ls_tk_add-zatktp = ls_ta03-zatktp.
      ls_tk_add-zatkrl = ls_ta03-zatkrl.
      ls_tk_add-zctgr = ls_ta03-zctgr.
      ls_tk_add-zitems = ls_ta03-zitems.
      ls_tk_add-zflg_add = ''.
    ENDIF.




    PERFORM frm_tk_plus_dis USING ls_tk_add
                                  lt_tc02
                                  ps_ta02
                                  ps_tc05
                            CHANGING
*                                      pt_tk_plus
                                     pt_ta03
                                     pt_ta04
                                     lt_msglist.
    IF lt_msglist[] IS NOT INITIAL.
      EXIT.
    ENDIF.

    CLEAR lt_tc02[].
  ENDLOOP.

  IF lt_msglist[] IS NOT INITIAL.
    APPEND LINES OF lt_msglist[] TO pt_msglist[].
*    如果报错后 将匹配结果还原
    pt_ta04[]  = lt_ta04_bak[].
  ENDIF.


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_VLS_2400
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TK_ADD
*&      <-- GT_VLS_TMP_ZATKRL
*&      <-- GT_VLS_TMP_ZCTGR
*&---------------------------------------------------------------------*
FORM frm_pro_data_vls_2400  USING    ps_tk_add  TYPE ty_tk_plus
                            CHANGING pt_vls_zatkrl  TYPE vrm_values
                                     pt_vls_zctgr   TYPE vrm_values.

  CLEAR:pt_vls_zatkrl,pt_vls_zctgr.

  SELECT
    zatkrl  AS  key ,
    ztxt      AS  text
    FROM zretc010
    WHERE zatktp = @ps_tk_add-zatktp
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zatkrl.

  READ TABLE pt_vls_zatkrl TRANSPORTING NO FIELDS WITH KEY key = ps_tk_add-zatkrl.
  IF sy-subrc NE 0.
    CLEAR ps_tk_add-zatkrl.
  ENDIF.



  SELECT
    zctgr  AS  key ,
    ztxt      AS  text
    FROM zretc011
    WHERE zatktp = @ps_tk_add-zatktp
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zctgr.

  READ TABLE pt_vls_zctgr TRANSPORTING NO FIELDS WITH KEY key = ps_tk_add-zctgr.
  IF sy-subrc NE 0.
    CLEAR ps_tk_add-zctgr.
  ENDIF.
ENDFORM.