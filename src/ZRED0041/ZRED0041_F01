*&---------------------------------------------------------------------*
*& 包含               ZRED0035_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_SCREEN_INIT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_screen_init .


  IF p_call = ''.
    CLEAR:
          rb_add,
          rb_edit,
          rb_dis,
          rb_rles,
          rb_zlrsi,
          rb_chag.
  ENDIF.
*  根据事务码控制进入后的界面显示
  CASE sy-tcode.
    WHEN 'ZRED0041A'.
      rb_add = 'X'.
      gv_title = '返利平台：返利条款创建'.
    WHEN 'ZRED0041B'.
      rb_edit = 'X'.
      gv_title = '返利平台：返利条款修改'.
    WHEN 'ZRED0041C'.
      rb_dis = 'X'.
      gv_title = '返利平台：返利条款显示'.
    WHEN 'ZRED0041D'.
      rb_rles = 'X'.
      gv_title = '返利平台：返利条款审批'.
    WHEN 'ZRED0041E'.
      rb_zlrsi = 'X'.
      gv_title = '返利平台：返利条款新增行项目'.
    WHEN 'ZRED0041F'.
      rb_chag = 'X'.
      gv_title = '返利平台：固定协议批量调整'.
    WHEN OTHERS.
      rb_add = 'X'.
      gv_title = '返利平台：返利条款创建'.
  ENDCASE.

  PERFORM frm_set_title  USING gv_title.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen .


*  M01--新增
*  M02--修改，显示，审批
*  M03--单选按钮
*  M04--参考创建
*  M05--批导


*  新增
  IF rb_add = 'X'.

    LOOP AT SCREEN.
*      IF screen-group1 = 'M02' .
      IF screen-group1 IS NOT INITIAL
          AND screen-group1 NE 'M10'
          AND  screen-group1 NE 'M01'
          AND  screen-group1 NE 'M03'
          AND  screen-group1 NE 'M04'
          AND  screen-group1 NE 'M05'
          AND  screen-group1 NE 'M06'.
        screen-active = '0'.
      ELSE.
        screen-active = '1'.
      ENDIF.

      IF screen-name CP '*P_TK_IDY*'.
        IF cb_yg = 'X'.
          screen-active = '1'.
        ELSE.
          screen-active = '0'.
          CLEAR: p_tk_idy.
        ENDIF.
      ENDIF.

      IF rb_ref = 'X'.
        IF screen-group1 = 'M01'.
          screen-input = '0'.
        ELSE.
          screen-input = '1'.
        ENDIF.
        CLEAR: cb_yg,p_tk_idy.
      ENDIF.

      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF rb_chag = 'X'.
    LOOP AT SCREEN.
*      IF screen-group1 = 'M01'
*        OR screen-group1 = 'M02'
*        OR screen-group1 = 'M03'
*        OR screen-group1 = 'M04'
*        OR screen-group1 = 'M05'
*        OR  screen-group1 = 'M06'.
      IF screen-group1 IS NOT INITIAL
          AND screen-group1 NE 'M10'
          AND  screen-group1 NE 'M07'.
        screen-active = '0'.
      ELSEIF screen-group1 = 'M07'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ELSE.

*  非新增
    LOOP AT SCREEN.
*      IF screen-group1 = 'M01' OR screen-group1 = 'M03' OR screen-group1 = 'M04' OR screen-group1 = 'M05' OR  screen-group1 = 'M06'.
      IF screen-group1 IS NOT INITIAL
          AND screen-group1 NE 'M10'
          AND  screen-group1 NE 'M02'.
        screen-active = '0'.
      ELSEIF screen-group1 = 'M02'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.


*  若非参考创建
  IF rb_nref = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M04'.
        screen-active = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
*  若参考创建
  ELSEIF rb_ref = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M04'.
        screen-active = '1'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.

  ENDIF.


*  若批导
  IF cb_impt = 'X'.
    LOOP AT SCREEN.
*      IF screen-group1 = 'M01' OR screen-group1 = 'M02' OR screen-group1 = 'M03' OR screen-group1 = 'M04' OR screen-group1 = 'M10'.
      IF screen-group1 IS NOT INITIAL
          AND screen-group1 NE 'M06'
          AND  screen-group1 NE 'M05'.
        screen-active = '0'.
      ELSEIF screen-group1 = 'M05'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
*  非批导
  ELSE.
    LOOP AT SCREEN.
*      IF screen-group1 = 'M01' OR screen-group1 = 'M02' OR screen-group1 = 'M03' OR screen-group1 = 'M04' OR screen-group1 = 'M10'.
      IF screen-group1 IS NOT INITIAL
          AND screen-group1 NE 'M06'
          AND  screen-group1 NE 'M05'.
        screen-active = '1'.
      ELSEIF screen-group1 = 'M05'.
        screen-active = '0'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.


  LOOP AT SCREEN.
*      IF screen-group1 = 'M01' OR screen-group1 = 'M02' OR screen-group1 = 'M03' OR screen-group1 = 'M04' OR screen-group1 = 'M10'.
    IF screen-group1 IS NOT INITIAL
        AND screen-group1 NE 'M06'
        AND  screen-group1 NE 'M05'.

      IF cb_impt = 'X'.
        screen-active = '0'.
      ELSE.
        screen-active = '1'.
      ENDIF.

    ELSEIF screen-group1 = 'M05'.

      IF cb_impt = 'X'.
        screen-active = '1'.
      ELSE.
        screen-active = '0'.
      ENDIF.

    ENDIF.
    MODIFY SCREEN.
  ENDLOOP.


*  通过事务码进入后隐藏单选按钮
  IF sy-tcode NE 'SE38'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M10'.
        screen-active = '0'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


  DATA lt_fcode TYPE TABLE OF sy-ucomm.
  IF sy-tcode NE 'ZRED0041A' AND sy-tcode NE 'SE38'.
    APPEND  'FC01' TO lt_fcode.
    APPEND  'FC02' TO lt_fcode.
    IF sy-tcode NE 'ZRED0041F'.
      APPEND  'FC03' TO lt_fcode.
    ENDIF.
  ELSE.
    CLEAR: lt_fcode.
  ENDIF.

  CALL FUNCTION 'RS_SET_SELSCREEN_STATUS'
    EXPORTING
      p_status  = sy-pfkey
    TABLES
      p_exclude = lt_fcode.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_main .

  PERFORM frm_init_data.

  PERFORM frm_author_check.

  PERFORM frm_check_screen .



  CASE gv_flg_rb.
    WHEN '01'.
      IF rb_nref = 'X'.
        PERFORM frm_charg_zfllx_value.
      ENDIF.

      IF rb_ref = 'X'.
        PERFORM frm_get_data USING gs_data_base
                               CHANGING
                                         gs_ta02
                                         gt_t13
                                         gt_t44
                                         gt_ta03
                                         gt_ta05
                                         gt_ta06
                                         gt_t58_set.

      ENDIF.

*      新增时默认值设置
      IF rb_nref = 'X'.
        PERFORM frm_data_dft ..
      ENDIF.
    WHEN '02'.

      PERFORM frm_pro_data_lock               USING p_ztk_id
                                                      ''.
      PERFORM frm_get_data       USING gs_data_base
                              CHANGING gs_ta02
                                       gt_t13
                                       gt_t44
                                       gt_ta03
                                       gt_ta05
                                       gt_ta06
                                       gt_t58_set.


      PERFORM frm_zxyzt_check USING gs_ta02.

      PERFORM frm_get_editor_text  TABLES gt_edittab
                                    USING gs_ta02
                                          gt_t76.

    WHEN '03' OR '04' OR '05'.

      IF gv_flg_rb = '04' OR gv_flg_rb = '05' .
        PERFORM frm_pro_data_lock  USING p_ztk_id
                                         ''.
      ENDIF.

      PERFORM frm_get_data      USING gs_data_base
                             CHANGING gs_ta02
                                      gt_t13
                                      gt_t44
                                      gt_ta03
                                      gt_ta05
                                      gt_ta06
                                      gt_t58_set..

      PERFORM frm_get_editor_text  TABLES gt_edittab
                                    USING gs_ta02
                                          gt_t76.
    WHEN OTHERS.
  ENDCASE.


  PERFORM frm_pro_data_base USING gs_ta02 CHANGING gs_data_base.

  PERFORM frm_get_data_org USING gs_data_base
                              CHANGING
                                    gs_tc01
                                    gt_tc02
                                    gt_tc03
                                    gt_tc04_set
                                    gt_t44_all
                                    gt_bukrs_set
                                    gt_dcwrk_set
                                    gt_werks_set
                                    gt_ekorg_set
                                    gt_matnr_set
                                    gt_zzgys_set
                                    .


  PERFORM frm_pro_data  USING   gs_data_base
                                gt_tc02
                        CHANGING
                                gs_ta01
                                gs_ta02
                                gt_t20
                                gt_t09
                                gt_tc04
                                gt_tc05
                                gs_tc05
                                gt_t44_all
                                gt_t11_all
                                gt_t11
                                gt_t12
                                gt_ta04
                                gt_zzlbm_set
                                gt_zqdbm_set
*                                gt_zzgys_set
                                .


  PERFORM frm_pro_data_end CHANGING
                                gt_tc02
                                gs_ta02
                                gt_ta04
                                gt_t11_all
                                gs_data_base
                                gs_data_scn_ctrl.


  IF rb_ref = 'X'.
    PERFORM frm_pro_data_ref CHANGING gs_ta02
                                      gt_tc02
                                      gt_ta03
                                      gt_ta04
                                      gt_ta06
                                      gt_t11_all
                                      gt_matnr_set
                                      gt_zzgys_set.
  ENDIF.

  PERFORM frm_get_data_screen_attr  USING gs_data_scn_ctrl
                                    CHANGING gt_data_scn.


  PERFORM frm_set_zlrsi .

*  设置显示文本
  PERFORM frm_set_text CHANGING gs_data_base.
*  设置显示文本2（返利申请需求）
  PERFORM frm_set_text2    CHANGING gs_ta02 gs_screen_lab gv_title_01.
*  设置计算模板和商品组下拉框
  PERFORM frm_set_list_box_02 USING gt_tc05
                                    gt_t09
                              CHANGING gt_vls_zclrid
                                       gt_vls_zspz_id.

  PERFORM frm_set_list_box_zjsff USING gs_ta02-zxybstyp
                              CHANGING gt_vls_zjsff.

*  PERFORM frm_set_list_box_01        CHANGING   gt_vls_ztmpid.

*  权限检查
  DATA: lt_msglist    TYPE scp1_general_errors,
        lv_mtype_auth TYPE bapi_mtype,
        lv_msg_auth   TYPE scp1_general_error-msgv1.

  PERFORM frm_author_check_tk USING gs_ta01
                                    gs_ta02
                                    gt_tc02
*                                    gv_actvt
                                    gs_data_base-actvt
                                    'A'
                              CHANGING lt_msglist
                                       lv_mtype_auth
                                       lv_msg_auth.

*  审批权限检查
*  IF gv_flg_rb = '04'.
*    PERFORM frm_author_check_frgc1 USING gs_ta01
*                                         gs_ta02
*                                         'A'
*                                   CHANGING lv_mtype_auth
*                                            lv_msg_auth.
*
*  ENDIF.


  PERFORM frm_call_screen.


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_CHECK_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_check_screen .

  CASE gv_flg_rb.
    WHEN '01'.


      IF rb_ref = 'X'.
        PERFORM frm_check_ref USING p_tk_idr.
      ELSE.

        IF p_zfllx IS INITIAL.

          MESSAGE s888(sabapdocu) WITH '返利类型必填' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.

        ENDIF.

        IF p_zxybtp IS INITIAL.

          MESSAGE s888(sabapdocu) WITH '协议类型必填' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.

        ENDIF.

        IF p_zht_id IS INITIAL.

          MESSAGE s888(sabapdocu) WITH '合同号码必填' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        ELSE.
          SELECT SINGLE COUNT(*) FROM zreta001 WHERE zht_id = p_zht_id.
          IF sy-subrc NE 0.
            MESSAGE s888(sabapdocu) WITH '合同号码不存在' DISPLAY LIKE 'E'.
            LEAVE LIST-PROCESSING.
          ENDIF.
        ENDIF.
      ENDIF.


      IF cb_yg = 'X'.
        IF p_tk_idy IS INITIAL.
          MESSAGE s888(sabapdocu) WITH '次年预估原参考条款必填' DISPLAY LIKE 'E'.
          RETURN.
        ELSE.
          PERFORM frm_check_ztk_id_yg USING p_tk_idy.
        ENDIF.
      ENDIF.

    WHEN '02' OR '03' OR '04' OR '05'.

      IF p_ztk_id IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '条款编码必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.

      ENDIF.
      PERFORM frm_check_ztk_id USING p_ztk_id.

      IF gv_flg_rb = '05'.
        PERFORM frm_check_ztk_id_zlrsi USING p_ztk_id.
      ENDIF.
    WHEN OTHERS.
  ENDCASE.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_author_check .

  DATA:lv_subrc TYPE c,
       lv_mess  TYPE bapiret2-message.
  DATA:
    lv_flg_err1 TYPE char1,
    ls_zreta001 TYPE zreta002.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_INIT_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_init_data .

  PERFORM frm_clear_data.

  CLEAR gv_flg_rb.
  CASE 'X'.
    WHEN rb_add .      gs_data_base-zflg_rb = '01'    .     gv_flg_rb = '01'.  gv_title_02 = '创建'.        gs_data_base-actvt = '01'    .       gs_data_scn_ctrl-tcodetype = 'N'.
    WHEN rb_edit .     gs_data_base-zflg_rb = '02'    .     gv_flg_rb = '02'.  gv_title_02 = '修改'.        gs_data_base-actvt = '02'    .       gs_data_scn_ctrl-tcodetype = 'U'.
    WHEN rb_dis .      gs_data_base-zflg_rb = '03'    .     gv_flg_rb = '03'.  gv_title_02 = '显示'.        gs_data_base-actvt = '03'    .       gs_data_scn_ctrl-tcodetype = 'D'.
    WHEN rb_rles .     gs_data_base-zflg_rb = '04'    .     gv_flg_rb = '04'.  gv_title_02 = '审批'.        gs_data_base-actvt = '03'    .       gs_data_scn_ctrl-tcodetype = 'D'.
    WHEN rb_zlrsi .    gs_data_base-zflg_rb = '05'    .     gv_flg_rb = '05'.  gv_title_02 = '新增行项目'.  gs_data_base-actvt = '02'    .       gs_data_scn_ctrl-tcodetype = 'D'.
    WHEN OTHERS.
  ENDCASE.



  IF gv_flg_rb = '01'.
*    创建时清空修改和显示的条款号
    CLEAR p_ztk_id.
  ELSE.
*    非创建模式 参考按钮无效
    CLEAR rb_ref.
    rb_nref = 'X'.
  ENDIF.

  IF rb_nref = 'X'.
    CLEAR p_tk_idr.
  ELSE.
    CLEAR p_tk_idy.
  ENDIF.

  gv_title_01 = '返利条款'.

  gs_data_base-zflg_cprog = 'ZRED0041'.

  IF rb_ref = 'X'.
    PERFORM frm_init_ref.
  ENDIF.


  IF gv_flg_rb = '01'.
    gs_data_base-zfllx    = p_zfllx.
    gs_data_base-zht_id   = p_zht_id.
    gs_data_base-ztmpid   = p_ztmpid.
    gs_data_base-ztk_idr  = p_tk_idr.
    gs_data_base-zfjtk    = cb_fjtk.
    gs_data_base-ztk_id   = p_ztk_id.
    gs_data_base-zxybstyp = p_zxybtp.
  ELSEIF gv_flg_rb = '02'.
    PERFORM frm_get_tksq USING p_ztk_id.
    gs_data_base-ztk_id   = p_ztk_id.
  ELSE.
    gs_data_base-ztk_id   = p_ztk_id.
  ENDIF.



*  SELECT SINGLE zxybstyp  FROM zret0002 WHERE  zfllx = @p_zfllx  INTO  @gs_data_base-zxybstyp.  #####

  IF p_call = 'X'.
    gv_flg_call = 'X'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CALL_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_call_screen .
  CALL SCREEN 2000.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_DFT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_data_dft .

  gs_ta02-zht_id = p_zht_id.
  gs_ta02-ztmpid = p_ztmpid.
  gs_ta02-zfllx = p_zfllx.
  gs_ta02-zxybstyp = p_zxybtp.
  IF gs_ta02-zdffs IS INITIAL.
    IF gs_ta02-zfllx = 'RB04'.
      gs_ta02-zdffs = 'M'.
    ELSE.
      gs_ta02-zdffs = 'O'.
    ENDIF.

  ENDIF.
  gs_ta02-zhstype = 'A'.
  gs_ta02-zhszq = '1M'.
*  gs_ta02-zjszq = '12M'.   "结算周期默认为 12M
*  gs_ta02-zbukrs = p_zbukrs.
  PERFORM frm_set_zjszq USING gs_ta02-zhstype gs_ta02-zhszq CHANGING gs_ta02-zjszq.

  IF cb_fjtk = 'X'.
    gs_ta02-ztktype  = 'P'.
    gs_ta02-zfjtk = 'X'.
  ENDIF.

  IF cb_yg = 'X'.
    gs_ta02-zcnyg = 'X'.
    gs_ta02-zcnygsg = 'X'.
    gs_ta02-zcnygtk_id = p_tk_idy.
  ENDIF.

*  通过合同带入
  PERFORM frm_bin_data_from_ta01 CHANGING gs_ta02
                                          gt_t44.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_2100
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_2000 .
  PERFORM frm_set_screen_pub.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_PUB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_pub .


  PERFORM frm_set_screen_type USING gs_data_base.




  LOOP AT SCREEN.
    IF screen-group1 = '101'   OR screen-group1 = '102' OR screen-group1 = '103'.
      PERFORM frm_set_screen_attr USING gt_data_scn
                                  CHANGING screen.
      IF sy-subrc = 0.
        LOOP AT tc_item-cols INTO DATA(ls_cols) WHERE screen-name = screen-name .
          ls_cols-invisible = screen-invisible.
          MODIFY tc_item-cols  FROM ls_cols.
        ENDLOOP.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.

  IF gv_flg_rb = '03' OR gv_flg_rb = '04' OR gv_flg_rb = '05'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' OR screen-group1 = '102'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  IF gv_flg_comm = 'SAVE'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CLEAR_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_clear_data .

  CLEAR:
        gs_ta02.

  CLEAR:
        gs_ta02,
        gs_data_base,
        gt_bukrs_set,
        gt_dcwrk_set,
        gt_werks_set,
        gt_ekorg_set,
        gt_matnr_set,
        gv_zitem_key,gv_zitem_key_cx.



ENDFORM.

FORM frm_pro_data_scn_ctrl USING ps_data_base TYPE ty_data_base
                                 ps_ta02 TYPE LINE OF tt_ta02
                           CHANGING ps_data_scn_ctrl TYPE zres0056.

  ps_data_scn_ctrl-zxybstyp = ps_data_base-zxybstyp.
  ps_data_scn_ctrl-ztktype  = ps_ta02-ztktype.
  ps_data_scn_ctrl-zfllx  = ps_ta02-zfllx.

ENDFORM.

FORM frm_set_list_box_01  CHANGING
                                    pt_vls_ztmp_id   TYPE vrm_values
                                    pt_vls_zxybstyp   TYPE vrm_values.
  DATA: lv_where TYPE char50.

  CLEAR:
        pt_vls_ztmp_id,pt_vls_zxybstyp.

  SELECT SINGLE * INTO @DATA(ls_zreta001) FROM zreta001 WHERE zht_id = @p_zht_id.

  IF cb_fjtk = 'X'.
    lv_where = | A~ZTKTYPE = 'P' |.
  ELSE.
    lv_where = | A~ZTKTYPE = '' |.
  ENDIF.

  SELECT
    DISTINCT
    a~ztmpid  AS  key ,
    a~ztmptxt      AS  text
    FROM zretc001 AS a JOIN zretc002 AS b
                         ON a~ztmpid = b~ztmpid
                        AND b~zstatus NE 'D'
    WHERE a~zhtlx = @ls_zreta001-zhtlx
    AND   ( a~zbukrs = @ls_zreta001-zbukrs OR a~zbukrs = '' )
    AND   a~ztmpty = 'S'
    AND   (lv_where)
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_ztmp_id.


  SELECT
    a~zxybstyp  AS  key ,
    b~ddtext      AS  text
    FROM zretc009 AS a JOIN dd07t AS b
                         ON a~zxybstyp = b~domvalue_l
                        AND b~domname = 'ZREM_ZXYBSTYP'
                        AND b~ddlanguage = @sy-langu
    WHERE a~zfllx = @p_zfllx
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zxybstyp.

ENDFORM.

FORM frm_set_list_box_03 USING ps_ta02 TYPE LINE OF tt_ta02
                               ps_tc05 TYPE LINE OF tt_tc05
                         CHANGING pt_vls_ztk_id   TYPE vrm_values.
  DATA: lv_where TYPE char50.

  SELECT SINGLE * INTO @DATA(ls_zreta001) FROM zreta001 WHERE zht_id = @p_zht_id.



*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:35:04
*    <NOTES>  #####
*    <OLD CODES>
*  SELECT SINGLE zxybstyp FROM zret0002 WHERE zfllx = @ps_ta02-zfllx INTO @DATA(lv_zxybstyp).
*    </OLD CODES>
*    <NEW CODES>
  SELECT SINGLE zxybstyp FROM zretc009 WHERE zfllx = @ps_ta02-zfllx AND zxybstyp = @ps_ta02-zxybstyp INTO @DATA(lv_zxybstyp).
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*
  IF  lv_zxybstyp = 'V' OR lv_zxybstyp = 'A' .
    lv_where = |  B~ZXYBSTYP IN ('A','V') |.
  ELSE.
    lv_where = | |.
  ENDIF.



*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:35:56
*    <NOTES> #####
*    <OLD CODES>
*  SELECT
*    a~ztk_id  ,
*    a~ztk_txt ,
*    b~zxybstyp,
*    a~zclrid,
*    c~zjsff,
*    c~zflxs
*    FROM zreta002 AS a JOIN zret0002 AS b
*                         ON a~zfllx = b~zfllx
*                       LEFT JOIN zretc005 AS c
*                         ON a~zclrid = c~zclrid
*    WHERE a~zht_id = @ps_ta02-zht_id
**    AND   a~zfllx = @ps_ta02-zfllx
*    AND   a~ztktype = 'P'
*    AND   (lv_where)
*    INTO TABLE @DATA(lt_ta02).
*    </OLD CODES>
*    <NEW CODES>
  SELECT
    a~ztk_id  ,
    a~ztk_txt ,
    b~zxybstyp,
    a~zclrid,
    c~zjsff,
    c~zflxs
    FROM zreta002 AS a JOIN zretc009 AS b
                         ON a~zfllx = b~zfllx
                        AND a~zxybstyp = b~zxybstyp
                       LEFT JOIN zretc005 AS c
                         ON a~zclrid = c~zclrid
    WHERE a~zht_id = @ps_ta02-zht_id
*    AND   a~zfllx = @ps_ta02-zfllx
    AND   a~ztktype = 'P'
    AND   (lv_where)
    INTO TABLE @DATA(lt_ta02).
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*

  DELETE lt_ta02 WHERE zxybstyp NE lv_zxybstyp AND (  zjsff NE ps_tc05-zjsff OR zflxs NE ps_tc05-zflxs ).

  SELECT
    a~ztk_id  AS  key ,
    a~ztk_txt      AS  text
    FROM @lt_ta02 AS a
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_ztk_id.


*  SELECT
*    a~ztk_id  AS  key ,
*    a~ztk_txt      AS  text
*    FROM zreta002 AS a JOIN zret0002 AS b
*                         ON a~zfllx = b~zfllx
*    WHERE a~zht_id = @ps_ta02-zht_id
*    AND   a~zfllx = @ps_ta02-zfllx
*    AND   a~ztktype = 'P'
*    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_ztk_id.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ALV_DIS_GZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_alv_dis_gz .

  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.


  PERFORM frm_set_catalog     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout      CHANGING  ls_layout.


  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
*     is_layout_lvc            = &1
      it_fieldcat_lvc          = lt_fieldcat
      is_layout_lvc            = ls_layout
      i_callback_pf_status_set = 'FRM_STATUS_ALV_DTL'
      i_callback_user_command  = 'FRM_ALV_COMMAND_DTL'
      i_screen_start_column    = 15
      i_screen_start_line      = 2
      i_screen_end_column      = 130
      i_screen_end_line        = 22
    TABLES
      t_outtab                 = gt_tc05.


ENDFORM.

FORM frm_set_catalog CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext = &5.                 "

    CASE &4.
      WHEN 'ZCLRTP'  .      ls_fieldcat-convexit = 'ZCLRT'.
      WHEN 'ZXYBSTYP'  .      ls_fieldcat-convexit = 'ZXYBS'.
      WHEN 'ZHSJZ'  .      ls_fieldcat-convexit = 'ZHSJZ'.
      WHEN 'ZFLHSJZ'  .      ls_fieldcat-convexit = 'ZHSJZ'.
      WHEN 'ZJTLX'  .      ls_fieldcat-convexit = 'ZJTLX'.
      WHEN 'ZLHWD'  .      ls_fieldcat-convexit = 'ZLHWD'.
      WHEN 'ZJGWD'  .      ls_fieldcat-convexit = 'ZJGWD'.
      WHEN 'ZFLXS'  .      ls_fieldcat-convexit = 'ZFLXS'.
      WHEN 'ZJSFF'  .      ls_fieldcat-convexit = 'ZJSFF'.
      WHEN 'ZFLJGWD'  .      ls_fieldcat-convexit = 'ZJGWD'.
      WHEN 'WERKS'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'SEL'  .
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.


*  add_fcat  '10' '' '' 'SEL'    '选择'  .
  add_fcat  '10' '' '' 'ZCLRID'    '规则模板ID'  .
  add_fcat  '10' '' '' 'ZCLRTP'    '规则类型'  .
  add_fcat  '10' '' '' 'ZCLRTXT'    '规则模板描述'  .

  add_fcat  '10' '' '' 'ZHT_ID'    '返利合同编码'  .
  add_fcat  '10' '' '' 'ZXYBSTYP'    '协议类型'  .
  add_fcat  '10' '' '' 'ZHSJZ'    '任务计算核算基准'  .
  add_fcat  '10' '' '' 'ZFLHSJZ'    '返利计算核算基准'  .
  add_fcat  '10' '' '' 'ZJTLX'    '阶梯类型'  .
  add_fcat  '10' '' '' 'ZLHWD'    '量化纬度'  .
  add_fcat  '10' '' '' 'ZJGWD'    '价格纬度'  .
  add_fcat  '10' '' '' 'ZJTHS'    '阶梯含税'  .
  add_fcat  '10' '' '' 'ZFLXS'    '返利形式'  .
  add_fcat  '10' '' '' 'ZJSFF'    '计算方法'  .
  add_fcat  '10' '' '' 'ZFLJGWD'    '返利价格纬度'  .
  add_fcat  '10' '' '' 'ZFLHS'    '返利价格纬度含税'  .



ENDFORM.                    "SET_CATALOG

FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
  ps_layout-box_fname = 'SEL'.
  ps_layout-sel_mode = 'D'.

ENDFORM.                    "SET_LAYOUT

FORM frm_status_alv_dtl USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.
  SET PF-STATUS 'S4200' EXCLUDING pt_extab.
ENDFORM.                    "PFSTATUS_FORM

FORM frm_alv_command_dtl USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_check_changed_data.

  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.
    WHEN 'SELECT'.
      CLEAR rv_ucomm.

      DATA(lt_tc05) = gt_tc05.
      DELETE lt_tc05 WHERE sel = ''.
      IF lines( lt_tc05 ) > 1.
        MESSAGE s888(sabapdocu) WITH '只能选择一行' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

      CLEAR gs_tc05.
      READ TABLE gt_tc05 INTO gs_tc05 WITH KEY sel = 'X'.
      IF sy-subrc NE 0.
        MESSAGE s888(sabapdocu) WITH '请选择行！' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.
      LEAVE TO SCREEN 0.
    WHEN '&IC1'.
      READ TABLE gt_tc05 INTO gs_tc05 INDEX rs_selfield-tabindex.
      IF sy-subrc EQ 0.
        LEAVE TO SCREEN 0.
      ENDIF.

    WHEN OTHERS.
  ENDCASE.



ENDFORM.                    "USER_COMMAND_FORM

FORM frm_check_changed_data .
  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.
  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.
  CALL METHOD lrf_alv->check_changed_data.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_8004
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_8004 .

  IF gs_data_base-zxybstyp = 'A'.
    LOOP AT SCREEN.
      IF screen-group2 = '203'.
        screen-active = '0'.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_REFRESH_DATA_TC05
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_refresh_data_tc05 .

  PERFORM frm_get_data_tc05 USING gs_ta02
                                  gs_data_base
                          CHANGING gt_tc05
                                   gs_tc05.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_INIT_REF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_init_ref .

  SELECT SINGLE * FROM zreta002 WHERE ztk_id = @p_tk_idr INTO @DATA(ls_ta02).

  p_zfllx   = ls_ta02-zfllx.
  p_zht_id  = ls_ta02-zht_id.
  p_ztmpid  = ls_ta02-ztmpid.
  p_zfllx   = ls_ta02-zfllx.
  p_zxybtp  = ls_ta02-zxybstyp.
  IF ls_ta02-ztktype = 'P'.
    cb_fjtk = 'X'.
  ELSE.
    cb_fjtk = ''.
  ENDIF.

  p_ztk_id = p_tk_idr.


ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_ZLRSI
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_DATA_BASE_ZTK_ID
*&      <-- PS_DATA_BASE_ZLRSI
*&---------------------------------------------------------------------*
FORM frm_set_zlrsi  .

  DATA: lt_msglist    TYPE scp1_general_errors,
        lv_mtype_auth TYPE bapi_mtype,
        lv_msg_auth   TYPE scp1_general_error-msgv1.

*  是否允许行项目审批
*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:24:10
*    <NOTES>
*    <OLD CODES>
*  SELECT SINGLE *  FROM zreta002  WHERE ztk_id = @gs_ta02-ztk_id INTO @DATA(ls_ta02).
*  IF ls_ta02-zxyzt NE 'A'.
*    RETURN.
*  ENDIF.
*
*  SELECT SINGLE * FROM zret0002 WHERE zfllx = @ls_ta02-zfllx INTO @DATA(ls_t02).
*  IF ls_t02-zlrsi NE 'X'.
*    RETURN.
*  ELSE.
*    gs_data_base-zlrsi = 'X'.
*  ENDIF.
*    </OLD CODES>
*    <NEW CODES>
  SELECT SINGLE *  FROM zreta002  WHERE ztk_id = @gs_ta02-ztk_id INTO @DATA(ls_ta02).
  IF ls_ta02-zxyzt NE 'A'.
    RETURN.
  ENDIF.

  SELECT SINGLE * FROM zretc009 WHERE zfllx = @ls_ta02-zfllx AND zxybstyp = @ls_ta02-zxybstyp INTO @DATA(ls_tc09).
  IF ls_tc09-zlrsi NE 'X'.
    RETURN.
  ELSE.
    gs_data_base-zlrsi = 'X'.
  ENDIF.

*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*

*  是否允许 新增 行项目
  IF gv_flg_rb = '05'.
    IF gs_data_base-zlrsi = 'X'..
      PERFORM frm_author_check_tk USING gs_ta01
                                        gs_ta02
                                        gt_tc02
                                        '02'
                                        'B'
                                  CHANGING lt_msglist
                                           lv_mtype_auth
                                           lv_msg_auth.

      IF lt_msglist[] IS NOT INITIAL.
        RETURN.
      ENDIF.

      gs_data_base-zflg_zlrsi = 'X'.

    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_DATA_1000
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_data_1000 .

  PERFORM frm_set_list_box_01        CHANGING   gt_vls_ztmpid
                                                gt_vls_zxybstyp.

  PERFORM frm_set_list_box             USING      'P_ZTMPID'
                                                  gt_vls_ztmpid.

  PERFORM frm_set_list_box             USING      'P_ZXYBTP'
                                                  gt_vls_zxybstyp.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_F4_ZFLSQF
*&---------------------------------------------------------------------*
*& text
*& ADD BY zsfsx = '2'
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZHTLX
*&---------------------------------------------------------------------*
FORM frm_f4_zflsqf  .

  DATA: ls_return TYPE           ddshretval,
        lt_return TYPE TABLE OF  ddshretval.

  IF gs_ta01-zhtlx+0(1) = '1' .
    CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
      EXPORTING
        tabname     = 'BUT000'
        fieldname   = 'PARTNER'
        searchhelp  = 'BUPA'
        dynpprog    = sy-repid
        dynpnr      = '2200'
        dynprofield = 'ZFLSQF'
      TABLES
        return_tab  = lt_return.
    IF sy-subrc <> 0.
* Implement suitable error handling here
    ELSE.
      READ TABLE lt_return INDEX 1 INTO ls_return.
      CHECK sy-subrc = 0.
      gs_tc02-zflsqf = ls_return-fieldval.
    ENDIF.
  ELSE.
    CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
      EXPORTING
        tabname     = 'T001'
        fieldname   = 'BUKRS'
        searchhelp  = 'C_T001'
        dynpprog    = sy-repid
        dynpnr      = '2200'
        dynprofield = 'ZFLSQF'
      TABLES
        return_tab  = lt_return.
    IF sy-subrc <> 0.
* Implement suitable error handling here
    ELSE.
      READ TABLE lt_return INDEX 1 INTO ls_return.
      CHECK sy-subrc = 0.
      gs_tc02-zflsqf = ls_return-fieldval.
    ENDIF.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PASTE_DATA_TKZFF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T44
*&---------------------------------------------------------------------*
FORM frm_paste_data_tkzff  CHANGING    pt_t44 TYPE tt_t44.

  DATA:
    lt_data    TYPE TABLE OF tdline,
    ls_t44     TYPE ty_t44,
    lt_t44     TYPE tt_t44,
    lv_partner TYPE but000-partner,
    lv_name1   TYPE but000-name_org1.

  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     LENGTH               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.
  ELSE.
    RETURN.
  ENDIF.


  LOOP AT lt_data INTO DATA(ls_data).

    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO lv_partner  lv_name1 .

    IF lv_partner IS INITIAL.
      MESSAGE |粘贴的支付方为空,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_t44.
      EXIT.
    ENDIF.

    lv_partner = |{ lv_partner ALPHA = IN }|.

    SELECT SINGLE name_org1 INTO lv_name1 FROM but000 WHERE partner = lv_partner.
    IF sy-subrc <> 0.
      MESSAGE |支付方:{ lv_partner }不存在,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_t44.
      EXIT.
    ENDIF.

    ls_t44-zflzff = lv_partner.
    ls_t44-name1   = lv_name1.
    APPEND ls_t44 TO  lt_t44.
  ENDLOOP.

  IF lt_t44[]  IS NOT INITIAL.
    APPEND LINES OF lt_t44 TO pt_t44.
  ENDIF.

  SORT pt_t44 BY zflzff.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING zflzff.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PASTE_DATA_TKZFF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T44
*&---------------------------------------------------------------------*
FORM frm_paste_data_tkghf  CHANGING    pt_t13 TYPE tt_t13.

  DATA:
    lt_data    TYPE TABLE OF tdline,
    ls_t13     TYPE ty_t13,
    lt_t13     TYPE tt_t13,
    lv_partner TYPE but000-partner,
    lv_name1   TYPE but000-name_org1,
    lv_exclude TYPE char1.

  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     LENGTH               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.
  ELSE.
    RETURN.
  ENDIF.


  LOOP AT lt_data INTO DATA(ls_data).

    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO lv_partner lv_name1 lv_exclude .

    IF lv_partner IS INITIAL.
      MESSAGE |粘贴的供货方为空,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_t13.
      EXIT.
    ENDIF.

    lv_partner = |{ lv_partner ALPHA = IN }|.

    SELECT SINGLE name_org1 INTO lv_name1 FROM but000 WHERE partner = lv_partner.
    IF sy-subrc <> 0.
      MESSAGE |供货方:{ lv_partner }不存在,请检查!| TYPE 'S' DISPLAY LIKE 'E'.
      REFRESH lt_t13.
      EXIT.
    ENDIF.

    ls_t13-zghf    = lv_partner.
    ls_t13-name1   = lv_name1.
    ls_t13-zzzpc   = lv_exclude.
    APPEND ls_t13 TO  lt_t13.
  ENDLOOP.

  IF lt_t13[]  IS NOT INITIAL.
    APPEND LINES OF lt_t13 TO pt_t13.
  ENDIF.

  SORT pt_t13 BY zghf.
  DELETE ADJACENT DUPLICATES FROM pt_t13 COMPARING zghf.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHARG_ZFLLX_VALUE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_charg_zfllx_value .

  DATA:lt_vls_zfllx TYPE vrm_values.

  CHECK p_zht_id IS NOT INITIAL.
  CHECK p_zfllx IS NOT INITIAL.

  lt_vls_zfllx = gt_vls_zfllx.

  SELECT SINGLE zsfsx
    INTO @DATA(lv_zsfsx)
    FROM zretcm13
   INNER JOIN zreta001 ON zreta001~zhtlx = zretcm13~zhtlx
    WHERE zht_id = @p_zht_id.

  SELECT *
    INTO TABLE @DATA(lt_zretcm17)
    FROM zretcm17
    WHERE zsfsx = @lv_zsfsx.

  LOOP AT lt_vls_zfllx INTO DATA(ls_vls_zfllx) .
    READ TABLE lt_zretcm17 TRANSPORTING NO FIELDS WITH KEY zfllx = ls_vls_zfllx-key.
    IF sy-subrc <> 0.
      DELETE lt_vls_zfllx.
    ENDIF.
  ENDLOOP.

  READ TABLE lt_vls_zfllx TRANSPORTING NO FIELDS WITH  KEY key =  p_zfllx.
  IF sy-subrc <> 0.
    MESSAGE |在合同{ p_zht_id }下,不支持创建选择的返利类型{ p_zfllx }条款| TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING .
  ENDIF.

ENDFORM.