*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F04
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& FORM FRM_MAIN_IMPT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_main_impt .


  IF cb_impt = 'X'.
    CASE 'X'.
      WHEN rb_impti . gv_flg_impt = '01'."固定条款批量导入-创建
      WHEN rb_imptu . gv_flg_impt = '02'."固定条款批量导入-新增协议
      WHEN rb_imptt . gv_flg_impt = '04'."条款批量修改
      WHEN OTHERS.
    ENDCASE.

    IF gv_flg_impt = '02'.
      gv_pass = cb_pass.
    ELSE.
      gv_pass = ''.
    ENDIF.
  ENDIF.


  CLEAR: gt_excel_i,gt_excel_u,gt_excel_t,gt_excel.

  "ERP-17167-月结锁定期间---新月份返利的计算与分摊
  "月结锁定控制注释,检查设置到数据检查部分
  "月结锁定不许导入月结期前日期的条款和协议
  DATA:lv_sign TYPE c.
  PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
  IF lv_sign = '' .
    PERFORM frm_check_zq.
  ENDIF.


  PERFORM frm_load_data .

  PERFORM frm_get_data_excel USING gt_excel_i gt_excel_u gt_excel_t
                             CHANGING gt_excel
                                      gt_bukrs_excel
                                      gt_werks_excel
                                      gt_matnr_excel
                                      gt_zzlbm_excel
                                      gt_zqdbm_excel
                                      .

  PERFORM frm_pro_data_excel USING gv_flg_impt
                             CHANGING gt_excel.

  PERFORM frm_check_data_excel USING gv_flg_impt
                                     gv_pass
                                     gt_bukrs_excel
                                     gt_werks_excel
                                     gt_matnr_excel
                                     gt_zzlbm_excel
                                     gt_zqdbm_excel
                             CHANGING gt_excel.


  PERFORM frm_pro_data_excel_end  USING gv_flg_impt
                                  CHANGING gt_excel.

  CALL SCREEN 9901.


ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_LOAD_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_load_data .
  CASE gv_flg_impt.
    WHEN '01'.
      PERFORM frm_excel_load_a(zbcs0001)  TABLES gt_excel_i USING p_file 4.
    WHEN '02'.
      PERFORM frm_excel_load_a(zbcs0001)  TABLES gt_excel_u USING p_file 4.
    WHEN '03'.
      PERFORM frm_excel_load_a(zbcs0001)  TABLES gt_excel_c USING p_file_c 3.
    WHEN '04'.
      PERFORM frm_excel_load_a(zbcs0001)  TABLES gt_excel_t USING p_file 3.
    WHEN OTHERS.
  ENDCASE.

  PERFORM frm_set_seq(zbcs0001) TABLES gt_excel_i USING 'SEQ' .
  PERFORM frm_set_seq(zbcs0001) TABLES gt_excel_u USING 'SEQ' .
  PERFORM frm_set_seq(zbcs0001) TABLES gt_excel_c USING 'SEQ' .
  PERFORM frm_set_seq(zbcs0001) TABLES gt_excel_t USING 'SEQ' .
ENDFORM.
*&---------------------------------------------------------------------*
*& MODULE STATUS_9901 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9901 OUTPUT.
  SET PF-STATUS 'S9901'.
  IF gv_flg_impt = '01' OR gv_flg_impt = '02' OR gv_flg_impt = '04'.
    SET TITLEBAR 'T9901' WITH '固定金额条款批导'.
  ELSEIF gv_flg_impt = '03'.
    SET TITLEBAR 'T9901' WITH '固定协议批量调整'.
  ENDIF.

  PERFORM frm_alv_init_excel.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9901  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9901 INPUT.

  DATA:
        gv_code_9901  TYPE sy-ucomm.

  gv_code_9901 = sy-ucomm.


  PERFORM frm_check_changed_data_excel USING grf_alv.

  CASE gv_code_9901.
    WHEN 'SAVE'.
*      PERFORM FRM_SAVE_DATA.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.

      SET SCREEN 0.
      LEAVE SCREEN.
    WHEN OTHERS.
  ENDCASE.
  PERFORM frm_refresh_alv USING grf_alv.

ENDMODULE.

FORM frm_alv_init_excel .

  IF grf_container IS INITIAL.

    CREATE OBJECT grf_container
      EXPORTING
        side                        = cl_gui_docking_container=>dock_at_top
        extension                   = 2000
      EXCEPTIONS
        cntl_error                  = 1
        cntl_system_error           = 2
        create_error                = 3
        lifetime_error              = 4
        lifetime_dynpro_dynpro_link = 5
        OTHERS                      = 6.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
    ENDIF.

    IF grf_alv IS INITIAL.
      CREATE OBJECT grf_alv
        EXPORTING
          i_parent          = grf_container
        EXCEPTIONS
          error_cntl_create = 1
          error_cntl_init   = 2
          error_cntl_link   = 3
          error_dp_create   = 4
          OTHERS            = 5.
      IF sy-subrc <> 0.
        MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
      ENDIF.

      PERFORM frm_alv_display_excel USING grf_alv .
    ELSE.
      PERFORM frm_refresh_alv_excel USING grf_alv.
    ENDIF.
  ELSE.
    PERFORM frm_refresh_alv_excel USING grf_alv.
  ENDIF.

ENDFORM.