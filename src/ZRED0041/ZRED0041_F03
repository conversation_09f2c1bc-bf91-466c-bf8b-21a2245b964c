*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F03
*&---------------------------------------------------------------------*
FORM frm_check_frgsx   USING        ps_ta01        TYPE LINE OF tt_ta01
                     CHANGING       ps_ta02        TYPE LINE OF tt_ta02
                                    pt_msglist     TYPE scp1_general_errors .
  DATA:
      ls_msglist TYPE LINE OF scp1_general_errors.
  DATA:ls_zrekey TYPE zres0042.
  DATA:lv_frgsx  TYPE frgsx.
  DATA:lt_return TYPE TABLE OF bapiret2.
  DATA:ls_return TYPE bapiret2.
  DATA:ls_approval TYPE ty_approval.

  CHECK ps_ta02-zxyzt <> 'A'.

  ls_zrekey-zhtlx   =  ps_ta01-zhtlx.
  ls_zrekey-zbukrs  =  ps_ta01-zbukrs.
  ls_zrekey-ekgrp   =  ps_ta02-ekgrp.
  ls_zrekey-zfllx   =  ps_ta02-zfllx.
  ls_zrekey-ztktype =  ps_ta02-ztktype.

  CALL FUNCTION 'ZREFM0025'
    EXPORTING
      iv_zrestype = 'RA'
      is_zrekey   = ls_zrekey
    IMPORTING
      ev_frgsx    = lv_frgsx
    TABLES
      et_return   = lt_return.

  LOOP AT lt_return INTO ls_return  WHERE  type = 'E' .
    ls_msglist-msgty = ls_return-type.
    ls_msglist-msgid = ls_return-id.
    ls_msglist-msgno = ls_return-number.
    ls_msglist-msgv1 = ls_return-message.
    APPEND ls_msglist TO pt_msglist.
  ENDLOOP.

  IF lv_frgsx IS NOT INITIAL.
    ps_ta02-frgsx = lv_frgsx.

    MOVE-CORRESPONDING ps_ta02 TO ls_approval.
    CLEAR:ls_approval-kolnr.
    PERFORM frm_get_kolnr  CHANGING   ls_approval
                                      pt_msglist.
    MOVE-CORRESPONDING ls_approval TO ps_ta02.
  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_APPROVAL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_approval_ok_data    CHANGING ps_ta02 TYPE LINE OF tt_ta02
                                      pt_tc02 TYPE tt_tc02 .

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE ty_approval.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '398'.

  IF ps_ta02-zxyzt = 'A'.
    ls_msglist-msgv1 = '该返利条款已完全审批'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_ta02-zxyzt = 'D'.
    ls_msglist-msgv1 = '该返利条款已作废，无法审批!'.
    APPEND ls_msglist TO lt_msglist.
  ELSE.
    MOVE-CORRESPONDING ps_ta02 TO ls_approval.
    PERFORM frm_get_kolnr   CHANGING   ls_approval
                                       lt_msglist.
    IF ls_approval-zxyzt = 'A' .
      PERFORM frm_check_addtk USING ps_ta02-ztk_id CHANGING lt_msglist .
    ENDIF.
  ENDIF.


  IF lt_msglist[] IS NOT INITIAL.
****insert by jcwei 2021-6-16  ERP-11412
    LOOP AT lt_msglist INTO ls_msglist.
      MESSAGE e398(00) WITH p_ztk_id ls_msglist-msgv1.
    ENDLOOP.
*      CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
*        EXPORTING
*          title_text    = '消息提示'
*          sort_by_level = ' '
*          show_ids      = ''
*          message_list  = lt_msglist[].
****end by jcwei 2021-6-16  ERP-11412
    EXIT.
  ENDIF.


  UPDATE zreta002 SET kolnr = ls_approval-kolnr
                      zxyzt = ls_approval-zxyzt    WHERE ztk_id = ps_ta02-ztk_id.

  IF sy-subrc <> 0 .
    DATA(lv_error) = 'X'.
  ENDIF.

  IF lv_error IS INITIAL  .
    UPDATE  zret0006   SET kolnr  = ls_approval-kolnr
                           zxyzt  = ls_approval-zxyzt
                           zspr   = sy-uname
                           zspsj  = sy-uzeit
                           zsprq  = sy-datum
                     WHERE ztk_id = ps_ta02-ztk_id
                       AND frgsx  = ps_ta02-frgsx
                       AND kolnr  = ps_ta02-kolnr
                       AND zxyzt <> 'D'.

    IF ls_approval-zxyzt = 'A'.
      PERFORM frm_set_zlrsi .
      PERFORM frm_update_t76     USING ls_approval
                                       gt_t76.
    ENDIF. .
  ENDIF.

  IF lv_error IS INITIAL .

    PERFORM frm_save_rles_log USING ps_ta02-ztk_id
                                    ''
                                    ps_ta02-zxyzt
                                    ps_ta02-frgc1
                                    ls_approval.



    LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<lfs_tc02>) WHERE frgsx  = ps_ta02-frgsx
                                                         AND kolnr   = ps_ta02-kolnr
                                                         AND sel_man = 'X'   .
      <lfs_tc02>-frgsx     = ls_approval-frgsx.
      <lfs_tc02>-kolnr     = ls_approval-kolnr.
      <lfs_tc02>-frgc1     = ls_approval-frgc1.
      <lfs_tc02>-zxyzt_06  = ls_approval-zxyzt.
    ENDLOOP.
    MOVE-CORRESPONDING ls_approval TO ps_ta02.

**    COMMIT WORK AND WAIT.
    MESSAGE s398(00) WITH ps_ta02-ztk_id '审批通过，状态已更新！'.
    COMMIT WORK AND WAIT.

*    更新促销返利 门店和商品清单
    IF ps_ta02-zxyzt = 'A'.

      PERFORM frm_data_calculate(zbcs0002) USING ps_ta02-ztk_id ''.
      IF ps_ta02-zxybstyp = 'P'.
        PERFORM frm_pro_data_101_102(zbcs0002) USING ps_ta02-ztk_id.
        PERFORM frm_pro_data_bdp(zbcs0002) USING ps_ta02-ztk_id ps_ta02-zbegin ps_ta02-zend.
      ELSE.
        PERFORM frm_pro_data_116_117(zbcs0002) USING ps_ta02-ztk_id.
      ENDIF.

      IF ps_ta02-zleib = 'R' .
        PERFORM frm_sq2tk USING ps_ta02 .
      ENDIF.
    ENDIF.
  ELSE.
    ROLLBACK WORK.
    MESSAGE e398(00) WITH '数据更新异常，请稍后操作！'.
  ENDIF.


*  IF lv_error IS INITIAL  .
*    UPDATE  zret0006   SET kolnr  = ls_approval-kolnr
*                           zxyzt  = ls_approval-zxyzt
*                           zspr   = sy-uname
*                           zspsj  = sy-uzeit
*                           zsprq  = sy-datum
*                     WHERE ztk_id = ps_ta02-ztk_id
*                       AND frgsx  = ps_ta02-frgsx
*                       AND kolnr  = ps_ta02-kolnr
*                       AND zxyzt <> 'D'.
*
*
*    IF ls_approval-zxyzt = 'A'.
*      PERFORM frm_set_zlrsi .
*      PERFORM frm_update_t76     USING ls_approval
*                                       gt_t76.
*      PERFORM frm_data_calculate USING ps_ta02-ztk_id ''.
*    ENDIF. .
*  ENDIF.
*
*  IF lv_error IS INITIAL .
*
*    PERFORM frm_save_rles_log USING ps_ta02-ztk_id
*                                    ''
*                                    ps_ta02-zxyzt
*                                    ps_ta02-frgc1
*                                    ls_approval.
*
*
*
*    LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<lfs_tc02>) WHERE frgsx  = ps_ta02-frgsx
*                                                         AND kolnr   = ps_ta02-kolnr
*                                                         AND sel_man = 'X'   .
*      <lfs_tc02>-frgsx     = ls_approval-frgsx.
*      <lfs_tc02>-kolnr     = ls_approval-kolnr.
*      <lfs_tc02>-frgc1     = ls_approval-frgc1.
*      <lfs_tc02>-zxyzt_06  = ls_approval-zxyzt.
*    ENDLOOP.
*    MOVE-CORRESPONDING ls_approval TO ps_ta02.
*
***    COMMIT WORK AND WAIT.
*    MESSAGE s398(00) WITH ps_ta02-ztk_id '审批通过，状态已更新！'.
*    COMMIT WORK AND WAIT.
*
**    更新促销返利 门店和商品清单
*    IF ps_ta02-zxybstyp = 'P'.
*      IF ps_ta02-zxyzt = 'A'.
*        PERFORM frm_pro_data_101_102 USING ps_ta02-ztk_id.
*        PERFORM frm_pro_data_bdp USING ps_ta02-ztk_id ps_ta02-zbegin ps_ta02-zend.
*      ENDIF.
*    ENDIF.
*  ELSE.
*    ROLLBACK WORK.
*    MESSAGE e398(00) WITH '数据更新异常，请稍后操作！'.
*  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_APPROVAL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_approval_item_ok_data       USING  ps_ta02 TYPE LINE OF tt_ta02
                                   CHANGING ps_tc02 TYPE LINE OF tt_tc02  .

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE ty_approval.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  IF ps_tc02-zxyzt_06 = 'A'.
    ls_msglist-msgv1 = '该返利协议已完全审批'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_tc02-zxyzt_06 = 'D'.
    ls_msglist-msgv1 = '该返利协议已作废，无法审批!'.
    APPEND ls_msglist TO lt_msglist.
  ELSE.

    MOVE-CORRESPONDING ps_tc02 TO ls_approval.
    PERFORM frm_get_kolnr   CHANGING   ls_approval
                                       lt_msglist.
  ENDIF.

  IF lt_msglist[] IS NOT INITIAL.
    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].
    EXIT.
  ENDIF.

  UPDATE zret0006    SET kolnr  = ls_approval-kolnr
                         zxyzt  = ls_approval-zxyzt
                   WHERE ztk_id = ps_ta02-ztk_id
                     AND zxy_id = ps_tc02-zxy_id .
  IF sy-subrc =  0 .
    PERFORM frm_save_rles_log USING ps_ta02-ztk_id
                                    ps_tc02-zxy_id
                                    ps_ta02-zxyzt
                                    ps_ta02-frgc1
                                    ls_approval.
    MOVE-CORRESPONDING ls_approval TO ps_tc02.
    ps_tc02-zxyzt_06 = ls_approval-zxyzt .

    IF ps_tc02-zxyzt_06 = 'A'..
      IF ps_ta02-zxybstyp = 'P'.
        PERFORM frm_pro_data_101_102(zbcs0002) USING ps_ta02-ztk_id.
        PERFORM frm_pro_data_bdp(zbcs0002)     USING ps_ta02-ztk_id ps_ta02-zbegin ps_ta02-zend.
      ELSE.
        PERFORM frm_pro_data_116_117(zbcs0002) USING ps_ta02-ztk_id.
      ENDIF.
      PERFORM frm_data_calculate(zbcs0002)   USING ps_ta02-ztk_id ps_tc02-zxy_id.
    ENDIF.
    COMMIT WORK.
    MESSAGE '审批通过，状态已更新！' TYPE 'S'.
  ELSE.
    MESSAGE '数据更新异常，请稍后操作！' TYPE 'S' DISPLAY LIKE 'E'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_RLES_C
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_check_before_rles_c  USING    pv_tk_id
                              CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:ls_msglist TYPE scp1_general_error.

  CLEAR:ls_msglist.
  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  SELECT  zxy_id INTO TABLE @DATA(lt_zxy_id) FROM zret0006 WHERE ztk_id = @pv_tk_id.

  LOOP AT lt_zxy_id INTO DATA(ls_xy_id).

    SELECT COUNT(*)
      FROM zret0006
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs = 'X'.
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
      FROM zret0016
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs NE '' .
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
        FROM zret0018
        WHERE zxy_id = ls_xy_id-zxy_id
        AND zjsstate IN ('N','1','2','R').
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
        FROM zret0018
        WHERE zxy_id = ls_xy_id-zxy_id
        AND zjsstate IN ('A','F')
        AND zjsdcx_id = ''.
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
        FROM zret0018
        WHERE zxy_id = ls_xy_id-zxy_id
        AND zjsstate IN ('A','F')
        AND zjsdcx_id <> ''
        AND zjsdcx_id IN ( SELECT zjsd_id FROM zret0018 WHERE zxy_id = ls_xy_id-zxy_id AND zjsstate = 'D' ).
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_msglist  BY msgv1.
  DELETE ADJACENT DUPLICATES FROM pt_msglist COMPARING msgv1.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_RLES_C
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_check_item_before_rles_c     USING pv_xy_id
                                   CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:ls_msglist TYPE scp1_general_error.

  CLEAR:ls_msglist.
  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  SELECT  zxy_id INTO TABLE @DATA(lt_zxy_id) FROM zret0006 WHERE zxy_id = @pv_xy_id.

  LOOP AT lt_zxy_id INTO DATA(ls_xy_id).

    SELECT COUNT(*)
      FROM zret0006
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs = 'X'.
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
      FROM zret0016
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs NE '' .
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
        FROM zret0018
        WHERE zxy_id = ls_xy_id-zxy_id
        AND zjsstate IN ('N','1','2','R').
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_msglist  BY msgv1.
  DELETE ADJACENT DUPLICATES FROM pt_msglist COMPARING msgv1.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_APPROVAL_CANCEL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_approval_cancel_data   CHANGING  ps_ta02 TYPE LINE OF tt_ta02
                                          pt_tc02 TYPE tt_tc02.

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE ty_approval.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  IF ps_ta02-zxyzt = 'N'.
    ls_msglist-msgv1 = '该返利条款未审批，无需审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_ta02-zxyzt = 'R'.
    ls_msglist-msgv1 = '该返利条款已审批拒绝，无需审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_ta02-zxyzt = 'D'.
    ls_msglist-msgv1 = '该返利条款已作废，无法审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_ta02-zxyzt = 'A'.
    PERFORM frm_check_before_rles_c      USING  ps_ta02-ztk_id
                                           CHANGING  lt_msglist .
  ENDIF.

  IF ps_ta02-ztktype = 'P'.
    PERFORM frm_check_usetk_state USING ps_ta02-ztk_id CHANGING  lt_msglist.
  ENDIF.



  IF lt_msglist[] IS NOT INITIAL.
****insert by jcwei 2021-6-16  ERP-11412
    LOOP AT lt_msglist INTO ls_msglist.
      MESSAGE e398(00) WITH p_ztk_id ls_msglist-msgv1.
    ENDLOOP.
*      CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
*        EXPORTING
*          title_text    = '消息提示'
*          sort_by_level = ' '
*          show_ids      = ''
*          message_list  = lt_msglist[].
****end by jcwei 2021-6-16  ERP-11412
    EXIT.
  ELSE.
    MOVE-CORRESPONDING ps_ta02 TO ls_approval.
    CLEAR:ls_approval-kolnr.
    PERFORM frm_get_kolnr  CHANGING ls_approval  lt_msglist.
    ls_approval-zxyzt = 'R'.
    IF lt_msglist[] IS NOT INITIAL.
****insert by jcwei 2021-6-16  ERP-11412
      LOOP AT lt_msglist INTO ls_msglist.
        MESSAGE e398(00) WITH p_ztk_id ls_msglist-msgv1.
      ENDLOOP.
*        CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
*          EXPORTING
*            title_text    = '消息提示'
*            sort_by_level = ' '
*            show_ids      = ''
*            message_list  = lt_msglist[].
****end by jcwei 2021-6-16  ERP-11412

      EXIT..
    ENDIF.
  ENDIF.

  UPDATE zreta002 SET zxyzt = ls_approval-zxyzt
                      kolnr = ls_approval-kolnr
                WHERE ztk_id = ps_ta02-ztk_id
                  AND zxyzt <> 'D'.
  IF sy-subrc <> 0.
    DATA(lv_error) = 'X'.
  ENDIF.
  IF lv_error IS INITIAL .
    UPDATE zret0006   SET zxyzt = ls_approval-zxyzt
                          kolnr = ls_approval-kolnr
                           zqxr  = sy-uname
                           zqxsj  = sy-uzeit
                           zqxrq  = sy-datum
                    WHERE ztk_id = ps_ta02-ztk_id
                      AND zxyzt <> 'D' .
  ENDIF.
  IF  lv_error IS INITIAL.

    PERFORM frm_save_rles_log USING ps_ta02-ztk_id
                                    ''
                                    ps_ta02-zxyzt
                                    ps_ta02-frgc1
                                    ls_approval..
    PERFORM frm_process_cancel USING ps_ta02-ztk_id ''.
    LOOP AT pt_tc02 ASSIGNING FIELD-SYMBOL(<lfs_tc02>) WHERE sel_man = 'X' AND ( zxyzt_06 = 'P' OR zxyzt_06 = 'A')   .
      <lfs_tc02>-frgsx     = ls_approval-frgsx.
      <lfs_tc02>-kolnr     = ls_approval-kolnr.
      <lfs_tc02>-frgc1     = ls_approval-frgc1.
      <lfs_tc02>-zxyzt_06  = ls_approval-zxyzt.
    ENDLOOP.
    MOVE-CORRESPONDING  ls_approval TO ps_ta02.
    CLEAR:gs_data_base-zlrsi.

    MESSAGE s398(00) WITH ps_ta02-ztk_id '返利条款已拒绝，状态已更新！'.
    COMMIT WORK AND WAIT.
  ELSE.
    ROLLBACK WORK.
    MESSAGE e398(00) WITH '数据更新异常，请稍后操作！'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_APPROVAL_CANCEL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_approval_item_cancel_data   USING  ps_ta02 TYPE LINE OF tt_ta02
                                   CHANGING  ps_tc02 TYPE LINE OF tt_tc02.

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE ty_approval.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  IF ps_tc02-zxyzt_06 = 'N'.
    ls_msglist-msgv1 = '该返利条款未审批，无需审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_tc02-zxyzt_06 = 'R'.
    ls_msglist-msgv1 = '该返利条款已审批拒绝，无需审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_tc02-zxyzt_06 = 'D'.
    ls_msglist-msgv1 = '该返利条款已作废，无法审批拒绝!'.
    APPEND ls_msglist TO lt_msglist.
  ELSEIF ps_tc02-zxyzt_06 = 'A'.
    PERFORM frm_check_item_before_rles_c      USING  ps_tc02-zxy_id
                                           CHANGING  lt_msglist .
  ENDIF.

  MOVE-CORRESPONDING ps_tc02 TO  ls_approval.
  CLEAR:ls_approval-kolnr.
  PERFORM frm_get_kolnr               CHANGING ls_approval
                                               lt_msglist.
  ls_approval-zxyzt = 'R'.

  IF lt_msglist[] IS NOT INITIAL.
    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].

    EXIT.
  ENDIF.

  UPDATE zret0006 SET zxyzt = ls_approval-zxyzt
                      kolnr = ls_approval-kolnr  WHERE zxy_id = ps_tc02-zxy_id.
  IF sy-subrc = 0.
    PERFORM frm_save_rles_log       USING ps_ta02-ztk_id
                                          ps_tc02-zxy_id
                                          ps_ta02-zxyzt
                                          ps_ta02-frgc1
                                          ls_approval..
    PERFORM frm_item_process_cancel USING ps_ta02-ztk_id
                                          ps_tc02-zxy_id.
    COMMIT WORK.
    MOVE-CORRESPONDING  ls_approval TO ps_tc02.
    ps_tc02-zxyzt_06 = ls_approval-zxyzt .

    MESSAGE '返利协议已拒绝，状态已更新！' TYPE 'S'.
  ELSE.
    ROLLBACK WORK.
    MESSAGE '数据更新异常，请稍后操作！' TYPE 'S' DISPLAY LIKE 'E'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ABANDON_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&---------------------------------------------------------------------*
FORM frm_abandon_data  USING    ps_ta02 TYPE LINE OF tt_ta02.

  DATA: lt_msglist  TYPE scp1_general_errors,
        ls_msglist  TYPE scp1_general_error,
        lt_zreta007 TYPE  TABLE OF zreta007,
        ls_approval TYPE ty_approval.

  IF ps_ta02-zxyzt = 'N' OR ps_ta02-zxyzt = 'R'.

*    *审批参数
    ls_approval-zxyzt  = 'D'.

    PERFORM frm_save_rles_log   USING ps_ta02-ztk_id
                                      ''
                                      ps_ta02-zxyzt
                                      ps_ta02-frgc1
                                      ls_approval.

    PERFORM frm_update_zreta007 TABLES lt_zreta007
                                 USING ps_ta02.

    MODIFY zreta007 FROM TABLE lt_zreta007.
    UPDATE zret0006 SET zxyzt = 'D' WHERE ztk_id = ps_ta02-ztk_id.
    UPDATE zreta002 SET zxyzt = 'D' WHERE ztk_id = ps_ta02-ztk_id.
    IF sy-subrc = 0.
      COMMIT WORK.
      PERFORM frm_process_cancel USING ps_ta02-ztk_id ''.
      ps_ta02-zxyzt = 'D'.
      CLEAR:ps_ta02-kolnr,
            ps_ta02-frgc1,
            ps_ta02-zfrgtx.
      MESSAGE '返利条款已作废，状态已更新！' TYPE 'S'.
    ELSE.
      ROLLBACK WORK.
      MESSAGE '数据更新异常，请稍后操作！' TYPE 'S' DISPLAY LIKE 'E'.
    ENDIF.
  ELSE.
    MESSAGE '返利条款仅在新建和拒绝状态下允许删除，请检查！' TYPE 'S' DISPLAY LIKE 'E'.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ZXYZT_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&---------------------------------------------------------------------*
FORM frm_zxyzt_check  USING    ps_ta02 TYPE LINE OF tt_ta02.
  IF ps_ta02-zxyzt = 'P'.
    MESSAGE s888(sabapdocu) WITH '该返利条款审批中，不允许修改' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSEIF ps_ta02-zxyzt = 'A'.
    MESSAGE s888(sabapdocu) WITH '该返利条款已完全审批，不允许修改' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSEIF ps_ta02-zxyzt = 'D'.
    MESSAGE s888(sabapdocu) WITH '该返利条款已作废，不允许修改' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_APPROVAL_AUTHORITY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_AUTHO
*&---------------------------------------------------------------------*
FORM frm_check_approval_authority  USING    ps_ta02 TYPE LINE OF tt_ta02
                                   CHANGING pv_autho.


  pv_autho = 'X'.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ADDTK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_addtk  USING    p_ztk_id
                      CHANGING pt_msglist   TYPE scp1_general_errors..

  DATA ls_msglist TYPE scp1_general_error.
  DATA ls_str     TYPE string.

  SELECT zrlid
    INTO TABLE @DATA(lt_list)
    FROM zreta003
    INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~zrlid
    WHERE zreta003~ztk_id = @p_ztk_id
      AND zreta002~zxyzt <> 'A' .
  IF sy-subrc = 0.
    LOOP AT lt_list INTO DATA(ls_list).
      IF ls_str IS INITIAL .
        ls_str = ls_list-zrlid.
      ELSE.
        ls_str =  ls_str && '|' && ls_list-zrlid.
      ENDIF.
    ENDLOOP.
    CONDENSE ls_str NO-GAPS.
    CLEAR:ls_msglist.
    ls_msglist-msgty = 'E'.
    ls_msglist-msgid = '00'.
    ls_msglist-msgno = '001'.
    ls_msglist-msgv1 = |条款的附加条款{ ls_str  }未审批，请先审批对应附加条款！|.
    APPEND ls_msglist TO pt_msglist.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_USETK_STATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_usetk_state  USING    p_ztk_id
                            CHANGING pt_msglist   TYPE scp1_general_errors..

  DATA ls_msglist TYPE scp1_general_error.
  DATA ls_str     TYPE string.

  SELECT zreta002~ztk_id
    INTO TABLE @DATA(lt_list)
    FROM zreta003
    INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~ztk_id
    WHERE zreta003~zrlid = @p_ztk_id
      AND zreta002~zxyzt = 'A'
      AND zreta002~zleib <> 'R'.
  IF sy-subrc = 0.
    LOOP AT lt_list INTO DATA(ls_list).
      IF ls_str IS INITIAL .
        ls_str = ls_list-ztk_id.
      ELSE.
        ls_str =  ls_str && '|' && ls_list-ztk_id.
      ENDIF.
    ENDLOOP.
    CONDENSE ls_str NO-GAPS.
    CLEAR:ls_msglist.
    ls_msglist-msgty = 'E'.
    ls_msglist-msgid = '00'.
    ls_msglist-msgno = '001'.
    ls_msglist-msgv1 = |审批拒绝附加条款时，引用该条款的返利条款{ ls_str }必须都不是已审状态！|.
    APPEND ls_msglist TO pt_msglist.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_check_date  USING    pv_bukrs.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = pv_bukrs
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.
*    TABLES
*      rt_date  =.

  IF lv_moste = 'E' .
    MESSAGE e398(00) WITH '管控期内,不能执行审批操作！' .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_yjgk_check_date  USING    pv_bukrs
                                   pv_zbegin.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.
  DATA:lv_sylsd TYPE datum.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = pv_bukrs
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.
*    TABLES
*      rt_date  =.

  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND.

  IF lv_moste = 'E' AND pv_zbegin < lv_sylsd .
    MESSAGE e398(00) WITH '管控期内,不能执行管控期范围的单据审批操作！' .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_check_cnyg
*&---------------------------------------------------------------------*
*& text 次年预估检查
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_check_cnyg  USING gs_ta02-zcnyg.

  IF gs_ta02-zcnyg = 'X' .
    MESSAGE e398(00) WITH '次年预估条款,不能执行审批操作！' .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_check_ZLEIB
*&---------------------------------------------------------------------*
*& text 类别检查
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      <-- LV_MTYPE_TK
*&      <-- LV_MSG_TK
*&---------------------------------------------------------------------*
FORM frm_check_zleib  USING gs_ta02 TYPE LINE OF tt_ta02.

  IF gs_ta02-zleib = 'R' AND gs_ta02-zxyzt = 'A'  .
    MESSAGE e398(00) WITH '已审批通过条款申请,不能执行审批拒绝操作！' .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_CANCEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_item_process_cancel  USING    pv_ztk_id  TYPE zreta002-ztk_id
                                   pv_xy_id  TYPE zreta002-ztk_id.

  DATA:
    ls_t06    TYPE zret0006,
    lt_t06    TYPE TABLE OF zret0006,
    lt_t06_cx TYPE TABLE OF zret0006,
    lr_zxy_id TYPE RANGE OF zret0006-zxy_id.

  IF pv_xy_id IS NOT INITIAL .
    lr_zxy_id = VALUE #(  ( sign = 'I' option = 'EQ'  low = pv_xy_id   high = '' )   ).
  ENDIF.

  SELECT
    zxy_id
    INTO CORRESPONDING FIELDS OF TABLE lt_t06
    FROM zret0006
    WHERE ztk_id = pv_ztk_id
      AND zxy_id IN lr_zxy_id.

  CALL FUNCTION 'ZREFM0072'
    TABLES
      it_head = lt_t06[].

ENDFORM.