*&---------------------------------------------------------------------*
*& 包含               ZRED0035_M02
*&---------------------------------------------------------------------*

*&SPWIZARD: FUNCTION CODES FOR TABSTRIP 'TAG_TA02'
CONSTANTS: BEGIN OF c_tag_ta02,
             tab1 LIKE sy-ucomm VALUE 'TAG_TA02_FC1',
             tab2 LIKE sy-ucomm VALUE 'TAG_TA02_FC2',
             tab3 LIKE sy-ucomm VALUE 'TAG_TA02_FC3',
             tab4 LIKE sy-ucomm VALUE 'TAG_TA02_FC4',
           END OF c_tag_ta02.
*&SPWIZARD: DATA FOR TABSTRIP 'TAG_TA02'
CONTROLS:  tag_ta02 TYPE TABSTRIP.
DATA: BEGIN OF g_tag_ta02,
        subscreen   LIKE sy-dynnr,
        prog        LIKE sy-repid VALUE 'ZRED0041',
        pressed_tab LIKE sy-ucomm VALUE c_tag_ta02-tab1,
      END OF g_tag_ta02.
*DATA:      OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TS 'TAG_TA02'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: SETS ACTIVE TAB
MODULE tag_ta02_active_tab_set OUTPUT.
  tag_ta02-activetab = g_tag_ta02-pressed_tab.
  CASE g_tag_ta02-pressed_tab.
    WHEN c_tag_ta02-tab1.
      g_tag_ta02-subscreen = '3001'.
    WHEN c_tag_ta02-tab2.
      g_tag_ta02-subscreen = '3002'.
    WHEN c_tag_ta02-tab3.
      g_tag_ta02-subscreen = '3003'.
    WHEN c_tag_ta02-tab4.
      g_tag_ta02-subscreen = '3004'.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.


  IF gs_ta02-zxybstyp NE 'P'.
    LOOP AT SCREEN.
      IF screen-name = 'TAG_TA02_TAB4'         .
        screen-active = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TS 'TAG_TA02'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GETS ACTIVE TAB
MODULE tag_ta02_active_tab_get INPUT.
  ok_code = sy-ucomm.
  CASE ok_code.
    WHEN c_tag_ta02-tab1.
      g_tag_ta02-pressed_tab = c_tag_ta02-tab1.
    WHEN c_tag_ta02-tab2.
      g_tag_ta02-pressed_tab = c_tag_ta02-tab2.
    WHEN c_tag_ta02-tab3.
      g_tag_ta02-pressed_tab = c_tag_ta02-tab3.
    WHEN c_tag_ta02-tab4.
      g_tag_ta02-pressed_tab = c_tag_ta02-tab4.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ZJT' ITSELF
CONTROLS: tc_zjt TYPE TABLEVIEW USING SCREEN 8004.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ZJT'
DATA:     g_tc_zjt_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zjt_change_tc_attr OUTPUT.

  DESCRIBE TABLE gt_t11 LINES tc_zjt-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zjt_get_lines OUTPUT.
  g_tc_zjt_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zjt_modify INPUT.
  MODIFY gt_t11
    FROM gs_t11
    INDEX tc_zjt-current_line.

ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zjt_mark INPUT.
  DATA: g_tc_zjt_wa2 LIKE LINE OF gt_t11.
  IF tc_zjt-line_sel_mode = 1
  AND gs_t11-sel = 'X'.
    LOOP AT gt_t11 INTO g_tc_zjt_wa2
      WHERE sel = 'X'.
      g_tc_zjt_wa2-sel = ''.
      MODIFY gt_t11
        FROM g_tc_zjt_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t11
    FROM gs_t11
    INDEX tc_zjt-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zjt_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZJT'
                              'GT_T11'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ZJT' ITSELF
CONTROLS: tc_zjt_sub TYPE TABLEVIEW USING SCREEN 8005.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ZJT'
DATA:     g_tc_zjt_sub_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zjt_sub_change_tc_attr OUTPUT.

  DESCRIBE TABLE gt_t11_sub LINES tc_zjt_sub-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zjt_sub_get_lines OUTPUT.
  g_tc_zjt_sub_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zjt_sub_modify INPUT.
  MODIFY gt_t11_sub
    FROM gs_t11_sub
    INDEX tc_zjt_sub-current_line.

ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zjt_sub_mark INPUT.
  DATA: g_tc_zjt_sub_wa2 LIKE LINE OF gt_t11.
  IF tc_zjt_sub-line_sel_mode = 1
  AND gs_t11_sub-sel = 'X'.
    LOOP AT gt_t11_sub INTO g_tc_zjt_sub_wa2
      WHERE sel = 'X'.
      g_tc_zjt_sub_wa2-sel = ''.
      MODIFY gt_t11_sub
        FROM g_tc_zjt_sub_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t11_sub
    FROM gs_t11_sub
    INDEX tc_zjt_sub-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zjt_sub_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZJT_SUB'
                              'GT_T11_SUB'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_ZJT' ITSELF
CONTROLS: tc_zcxjt TYPE TABLEVIEW USING SCREEN 9300.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_ZJT'
DATA:     g_tc_zcxjt_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_zcxjt_change_tc_attr OUTPUT.

  DESCRIBE TABLE gt_ta05 LINES tc_zcxjt-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_zcxjt_get_lines OUTPUT.
  g_tc_zcxjt_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_zcxjt_modify INPUT.
  MODIFY gt_ta05
    FROM gs_ta05
    INDEX tc_zcxjt-current_line.

ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_zcxjt_mark INPUT.
  DATA: g_tc_zcxjt_wa2 LIKE LINE OF gt_ta05.
  IF tc_zcxjt-line_sel_mode = 1
  AND gs_ta05-sel = 'X'.
    LOOP AT gt_ta05 INTO g_tc_zcxjt_wa2
      WHERE sel = 'X'.
      g_tc_zjt_wa2-sel = ''.
      MODIFY gt_ta05
        FROM g_tc_zcxjt_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ta05
    FROM gs_ta05
    INDEX tc_zcxjt-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_ZJT'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_zcxjt_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZCXJT'
                              'GT_TA05'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_ghf_wb TYPE TABLEVIEW USING SCREEN 8001.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'
DATA:     g_tc_ghf_wb_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ghf_wb_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t13 LINES tc_ghf_wb-lines.

*  创建时供货方取值来源于渠道供应商
*  IF RB_ADD = 'X' .
*    DATA(LT_T13) = GT_T13[].
*    DELETE LT_T13 WHERE ZGHF IS INITIAL.
*    IF LT_T13[] IS INITIAL.
*      CLEAR GT_T13[].
*      APPEND LINES OF GT_T12 TO GT_T13.
*    ENDIF.
*  ENDIF.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ghf_wb_get_lines OUTPUT.
  g_tc_ghf_wb_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ghf_wb_modify INPUT.
  MODIFY gt_t13
    FROM gs_t13
    INDEX tc_ghf_wb-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ghf_wb_mark INPUT.
  DATA: g_tc_ghf_wb_wa2 LIKE LINE OF gt_t13.
  IF tc_ghf_wb-line_sel_mode = 1
  AND gs_t13-sel = 'X'.
    LOOP AT gt_t13 INTO g_tc_ghf_wb_wa2
      WHERE sel = 'X'.
      g_tc_ghf_wb_wa2-sel = ''.
      MODIFY gt_t13
        FROM g_tc_ghf_wb_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t13
    FROM gs_t13
    INDEX tc_ghf_wb-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ghf_wb_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_GHF_WB'
                              'GT_T13'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_zff TYPE TABLEVIEW USING SCREEN 8002.


*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'

DATA:     g_tc_zff_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR


MODULE tc_zff_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t44 LINES tc_zff-lines.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL


MODULE tc_zff_get_lines OUTPUT.
  g_tc_zff_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE


MODULE tc_zff_modify INPUT.
  MODIFY gt_t44
    FROM gs_t44
    INDEX tc_zff-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE


MODULE tc_zff_mark INPUT.
  DATA: g_tc_zff_wa2 LIKE LINE OF gt_t44.
  IF tc_zff-line_sel_mode = 1
  AND gs_t44-sel = 'X'.
    LOOP AT gt_t44 INTO g_tc_zff_wa2
      WHERE sel = 'X'.
      g_tc_zff_wa2-sel = ''.
      MODIFY gt_t44
        FROM g_tc_zff_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t44
    FROM gs_t44
    INDEX tc_zff-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND



MODULE tc_zff_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZFF'
                              'GT_T44'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.






*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_GHF' ITSELF
CONTROLS: tc_zdates TYPE TABLEVIEW USING SCREEN 9009.


*&SPWIZARD: LINES OF TABLECONTROL 'TC_GHF'

DATA:     g_tc_zdates_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR


MODULE tc_zdates_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ta06 LINES tc_zdates-lines.


ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL


MODULE tc_zdates_get_lines OUTPUT.
  g_tc_zdates_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE


MODULE tc_zdates_modify INPUT.
  MODIFY gt_ta06
    FROM gs_ta06
    INDEX tc_zdates-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE


MODULE tc_zdates_mark INPUT.
  DATA: g_tc_zdates_wa2 LIKE LINE OF gt_ta06.
  IF tc_zdates-line_sel_mode = 1
  AND gs_ta06-sel = 'X'.
    LOOP AT gt_ta06 INTO g_tc_zdates_wa2
      WHERE sel = 'X'.
      g_tc_zdates_wa2-sel = ''.
      MODIFY gt_ta06
        FROM g_tc_zdates_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ta06
    FROM gs_ta06
    INDEX tc_zdates-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_GHF'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND



MODULE tc_zdates_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_ZDATES'
                              'GT_TA06'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZDATES  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zdates INPUT.
  PERFORM frm_check_zdates USING gs_ta06-zdates
                                 gs_ta02-zbegin
                                 gs_ta02-zend.
  IF sy-subrc ne 0.
    MESSAGE e888(sabapdocu) WITH '生效日期必须在条款的有效期范围内'.
  ENDIF.
ENDMODULE.





*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_QDGYS' ITSELF
CONTROLS: tc_qdgys TYPE TABLEVIEW USING SCREEN 8003.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_QDGYS'
DATA:     g_tc_qdgys_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_QDGYS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_qdgys_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t12 LINES tc_qdgys-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_QDGYS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_qdgys_get_lines OUTPUT.
  g_tc_qdgys_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_QDGYS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_qdgys_modify INPUT.
  MODIFY gt_t12
    FROM gs_t12
    INDEX tc_qdgys-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_QDGYS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_qdgys_mark INPUT.
  DATA: g_tc_qdgys_wa2 LIKE LINE OF gt_t12.
  IF tc_qdgys-line_sel_mode = 1
  AND gs_t12-sel = 'X'.
    LOOP AT gt_t12 INTO g_tc_qdgys_wa2
      WHERE sel = 'X'.
      g_tc_qdgys_wa2-sel = ''.
      MODIFY gt_t12
        FROM g_tc_qdgys_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t12
    FROM gs_t12
    INDEX tc_qdgys-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_QDGYS'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_qdgys_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_QDGYS'
                              'GT_T12'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.
*******
********&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_TK_PLUS' ITSELF
*******CONTROLS: TC_TK_PLUS TYPE TABLEVIEW USING SCREEN 2300.
*******
********&SPWIZARD: LINES OF TABLECONTROL 'TC_TK_PLUS'
*******DATA:     G_TC_TK_PLUS_LINES  LIKE SY-LOOPC.
*******
********DATA:     OK_CODE LIKE SY-UCOMM.
*******
********&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TK_PLUS'. DO NOT CHANGE THIS LINE!
********&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
*******MODULE TC_TK_PLUS_CHANGE_TC_ATTR OUTPUT.
*******  DESCRIBE TABLE GT_TK_PLUS LINES TC_TK_PLUS-LINES.
*******ENDMODULE.
*******
********&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TK_PLUS'. DO NOT CHANGE THIS LINE!
********&SPWIZARD: GET LINES OF TABLECONTROL
*******MODULE TC_TK_PLUS_GET_LINES OUTPUT.
*******  G_TC_TK_PLUS_LINES = SY-LOOPC.
*******ENDMODULE.
*******
********&SPWIZARD: INPUT MODULE FOR TC 'TC_TK_PLUS'. DO NOT CHANGE THIS LINE!
********&SPWIZARD: MODIFY TABLE
*******MODULE TC_TK_PLUS_MODIFY INPUT.
*******  MODIFY GT_TK_PLUS
*******    FROM GS_TK_PLUS
*******    INDEX TC_TK_PLUS-CURRENT_LINE.
*******ENDMODULE.
*******
********&SPWIZARD: INPUT MODUL FOR TC 'TC_TK_PLUS'. DO NOT CHANGE THIS LINE!
********&SPWIZARD: MARK TABLE
*******MODULE TC_TK_PLUS_MARK INPUT.
*******  DATA: G_TC_TK_PLUS_WA2 LIKE LINE OF GT_TK_PLUS.
*******  IF TC_TK_PLUS-LINE_SEL_MODE = 1
*******  AND GS_TK_PLUS-SEL = 'X'.
*******    LOOP AT GT_TK_PLUS INTO G_TC_TK_PLUS_WA2
*******      WHERE SEL = 'X'.
*******      G_TC_TK_PLUS_WA2-SEL = ''.
*******      MODIFY GT_TK_PLUS
*******        FROM G_TC_TK_PLUS_WA2
*******        TRANSPORTING SEL.
*******    ENDLOOP.
*******  ENDIF.
*******  MODIFY GT_TK_PLUS
*******    FROM GS_TK_PLUS
*******    INDEX TC_TK_PLUS-CURRENT_LINE
*******    TRANSPORTING SEL.
*******ENDMODULE.
*******
********&SPWIZARD: INPUT MODULE FOR TC 'TC_TK_PLUS'. DO NOT CHANGE THIS LINE!
********&SPWIZARD: PROCESS USER COMMAND
*******MODULE TC_TK_PLUS_USER_COMMAND INPUT.
*******  OK_CODE = SY-UCOMM.
*******  PERFORM USER_OK_TC USING    'TC_TK_PLUS'
*******                              'GT_TK_PLUS'
*******                              'SEL'
*******                     CHANGING OK_CODE.
*******  SY-UCOMM = OK_CODE.
*******ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  MDL_SET_DATA_T13  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE mdl_check_data_t13 INPUT.

  IF gs_t13-zghf = 'ALL' AND gs_t13-zzzpc = 'X'.
    MESSAGE e888(sabapdocu) WITH '供货方不能排除ALL！'.
  ENDIF.
ENDMODULE.
MODULE mdl_set_data_t13 INPUT.

*  若存在非ALL的数据，则自动删除ALL的数据
  LOOP AT gt_t13 TRANSPORTING NO FIELDS WHERE zghf IS NOT INITIAL AND zghf NE 'ALL'.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    DELETE gt_t13 WHERE zghf = 'ALL'.
  ENDIF.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_TA03' ITSELF
CONTROLS: tc_ta03 TYPE TABLEVIEW USING SCREEN 2300.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_TA03'
DATA:     g_tc_ta03_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TA03'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ta03_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ta03 LINES tc_ta03-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TA03'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ta03_get_lines OUTPUT.
  g_tc_ta03_lines = sy-loopc.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_TA03'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ta03_modify INPUT.
  MODIFY gt_ta03
    FROM gs_ta03
    INDEX tc_ta03-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_TA03'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ta03_mark INPUT.
  DATA: g_tc_ta03_wa2 LIKE LINE OF gt_ta03.
  IF tc_ta03-line_sel_mode = 1
  AND gs_ta03-sel = 'X'.
    LOOP AT gt_ta03 INTO g_tc_ta03_wa2
      WHERE sel = 'X'.
      g_tc_ta03_wa2-sel = ''.
      MODIFY gt_ta03
        FROM g_tc_ta03_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ta03
    FROM gs_ta03
    INDEX tc_ta03-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_TA03'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ta03_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_TA03'
                              'GT_TA03'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_TA04' ITSELF
CONTROLS: tc_ta04 TYPE TABLEVIEW USING SCREEN 9008.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_TA04'
DATA:     g_tc_ta04_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TA04'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_ta04_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_ta04_sub LINES tc_ta04-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_TA04'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_ta04_get_lines OUTPUT.
  g_tc_ta04_lines = sy-loopc.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_TA04'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_ta04_modify INPUT.
  MODIFY gt_ta04_sub
    FROM gs_ta04_sub
    INDEX tc_ta04-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_TA04'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_ta04_mark INPUT.
  DATA: g_tc_ta04_wa2 LIKE LINE OF gt_ta04_sub.
  IF tc_ta04-line_sel_mode = 1
  AND gs_ta04_sub-sel = 'X'.
    LOOP AT gt_ta04_sub INTO g_tc_ta04_wa2
      WHERE sel = 'X'.
      g_tc_ta04_wa2-sel = ''.
      MODIFY gt_ta04_sub
        FROM g_tc_ta04_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_ta04_sub
    FROM gs_ta04_sub
    INDEX tc_ta04-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_TA04'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_ta04_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_TA04'
                              'GT_TA04_SUB'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_SPZ' ITSELF
CONTROLS: tc_spzmx TYPE TABLEVIEW USING SCREEN 9010.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_SPZ'
DATA:     g_tc_spzmx_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_spzmx_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t58_sub LINES tc_spzmx-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_spzmx_get_lines OUTPUT.
  g_tc_spzmx_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_spzmx_modify INPUT.
  MODIFY gt_t58_sub
    FROM gs_t58_sub
    INDEX tc_spzmx-current_line.


ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_spzmx_mark INPUT.
  DATA: g_tc_spzmx_wa2 LIKE LINE OF gt_t58_sub.
  IF tc_spzmx-line_sel_mode = 1
  AND gs_t58_sub-sel = 'X'.
    LOOP AT gt_t58_sub INTO g_tc_spzmx_wa2
      WHERE sel = 'X'.
      g_tc_spzmx_wa2-sel = ''.
      MODIFY gt_t58_sub
        FROM g_tc_spzmx_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t58_sub
    FROM gs_t58_sub
    INDEX tc_spzmx-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_spzmx_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_SPZMX'
                              'GT_T58_SUB'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

MODULE mdl_set_screen_tc_spzmx OUTPUT.

  IF  gt_t58_sub[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
  PERFORM frm_set_screen_pub.

ENDMODULE.
*&---------------------------------------------------------------------*
*& MODULE MDL_SET_TC_DATA_TC_SPZ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_tc_spzmx OUTPUT.
  PERFORM frm_set_tc_data_tc_spzmx CHANGING gs_t58_sub.
ENDMODULE.

MODULE mdl_set_data_tc_spzmx_pai INPUT.


ENDMODULE.