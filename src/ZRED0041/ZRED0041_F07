*----------------------------------------------------------------------*
***INCLUDE ZRED0041_F07.
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_GET_TKSQ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_get_tksq  CHANGING    p_ztk_id.

  DATA:lt_msglist TYPE scp1_general_errors,
       ls_msglist TYPE scp1_general_error.
  DATA:lv_answer.

  SELECT SINGLE * INTO @DATA(ls_zreta002) FROM  zreta002 WHERE ztk_id = @p_ztk_id.

  CHECK sy-subrc = 0.

  SELECT SINGLE * INTO @DATA(ls_zreta007) FROM  zreta007 WHERE zdjbm  = @p_ztk_id AND zdele = ''.

  IF ls_zreta007-zdybm IS NOT INITIAL .
    MESSAGE |返利条款存在修改申请{ ls_zreta007-zdybm },现已显示此申请| TYPE 'S'.
    p_ztk_id = ls_zreta007-zdybm.
  ELSEIF ls_zreta002-zxyzt = 'A'  .
    IF ls_zreta002-zleib = 'R'.
      MESSAGE |返利申请已审批通过，不许修改！| TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ELSE.
      IF ls_zreta002-ztktype <> 'P' .

        PERFORM frm_check_before_rles_c      USING  p_ztk_id
                                          CHANGING  lt_msglist .
        IF lt_msglist[] IS INITIAL.
          MESSAGE s398(00) WITH   |条款{ p_ztk_id }无有效结算单存在，请拒绝后再修改！|.
          LEAVE LIST-PROCESSING.
        ENDIF.
      ENDIF.

      PERFORM frm_answer_data CHANGING lv_answer.

      IF lv_answer = '1' .
        PERFORM frm_call_zrefm0065 USING p_ztk_id .
      ELSE.
        LEAVE LIST-PROCESSING.
      ENDIF.
    ENDIF.


  ENDIF.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DEL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_answer_data  CHANGING pv_answer.

  CLEAR pv_answer.

  CALL FUNCTION 'POPUP_TO_CONFIRM'                "弹出小窗口
    EXPORTING
      titlebar      = '提示'
      text_question = '该条款已经审批完成，是否继续修改，生成已审批条款修改申请单？'
    IMPORTING
      answer        = pv_answer.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CALL_ZREFM0065
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_call_zrefm0065  USING    p_ztk_id.

  DATA:lv_ztk_id TYPE zreta002-ztk_id,
       ev_mtype  TYPE bapi_mtype,
       ev_msg    TYPE bapi_msg.

  CALL FUNCTION 'ZREFM0065'
    EXPORTING
      iv_commit = 'X'
      iv_ztk_id = p_ztk_id
    IMPORTING
      ev_mtype  = ev_mtype
      ev_msg    = ev_msg
      ev_ztk_id = lv_ztk_id.
  IF ev_mtype <> 'E'.
    p_ztk_id = lv_ztk_id.
  ELSE.
    MESSAGE '创建返利申请失败，请稍后重新操作！' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SQ2TK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_sq2tk  USING   ps_ta02 TYPE LINE OF tt_ta02.

  DATA:lv_ztk_id TYPE zreta002-ztk_id,
       ev_mtype  TYPE bapi_mtype,
       ev_msg    TYPE bapi_msg.
  CALL FUNCTION 'ZREFM0069'
    EXPORTING
      iv_sq_ztk_id = ps_ta02-ztk_id
    IMPORTING
      ev_mtype     = ev_mtype
      ev_msg       = ev_msg.

  SELECT SINGLE zdjbm INTO lv_ztk_id FROM zreta007 WHERE zdybm = ps_ta02-ztk_id.

  PERFORM frm_data_calculate(zbcs0002)      USING lv_ztk_id ''.

  PERFORM frm_update_t76_add     USING lv_ztk_id
                                       gt_t76.
  IF ps_ta02-zxybstyp = 'P'.
    PERFORM frm_pro_data_101_102(zbcs0002) USING lv_ztk_id.
    PERFORM frm_pro_data_bdp(zbcs0002) USING lv_ztk_id ps_ta02-zbegin ps_ta02-zend.
  ELSE.
    PERFORM frm_pro_data_116_117(zbcs0002) USING lv_ztk_id.
  ENDIF.
ENDFORM.