*&---------------------------------------------------------------------*
*& 包含               ZRED0041_F06
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_MAIN_IMPT_CHAG
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_main_impt_chag .


  gv_flg_impt = '03'.

  CLEAR: gt_excel_c,gt_excel.

  "ERP-17167-月结锁定期间---新月份返利的计算与分摊
  "月结锁定控制注释,检查设置到数据检查部分
  "月结锁定不许导入月结期前日期的条款和协议
  DATA:lv_sign TYPE c.
  PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
  IF lv_sign = '' .
    PERFORM frm_check_zq.
  ENDIF.
  PERFORM frm_load_data .

  PERFORM frm_get_data_excel_c USING gt_excel_c
                             CHANGING gt_excel
                                      gt_bukrs_excel
                                      gt_werks_excel
                                      gt_matnr_excel
                                      gt_zzlbm_excel
                                      gt_zqdbm_excel
                                      ..


  PERFORM frm_pro_data_excel USING gv_flg_impt
                             CHANGING gt_excel.

  PERFORM frm_check_data_excel USING gv_flg_impt
                                     ''
                                     gt_bukrs_excel
                                     gt_werks_excel
                                     gt_matnr_excel
                                     gt_zzlbm_excel
                                     gt_zqdbm_excel
                             CHANGING gt_excel.


  PERFORM frm_pro_data_excel_end  USING gv_flg_impt
                                  CHANGING gt_excel.

  CALL SCREEN 9901.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZQ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_check_zq .

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.
  IF lv_moste = 'E' AND rb_impti = '' .

    MESSAGE lv_meg TYPE 'S' DISPLAY LIKE  'E'.
    LEAVE LIST-PROCESSING.

  ENDIF.
ENDFORM.