*&---------------------------------------------------------------------*
*& 包含               ZRED0035_M01
*&---------------------------------------------------------------------*


**********************************************************************
*                     屏幕元素分组
*   101 通用显示/修改
*   201 调用时可以编辑，否则不可编辑
*
*
*
**********************************************************************

*&---------------------------------------------------------------------*
*& Module STATUS_2000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_2000 OUTPUT.

  DATA: pt_extab TYPE slis_t_extab.
  DATA: lv_mtype_auth TYPE bapi_mtype.
  DATA: lv_msg_auth   TYPE scp1_general_error-msgv1.

  REFRESH pt_extab.
  IF gv_flg_rb NE '01' AND gv_flg_rb NE '02' AND gv_flg_rb NE '05'.
    pt_extab = VALUE #( ( fcode = 'SAVE' ) ) .
  ENDIF.

  SET PF-STATUS 'S2000' EXCLUDING pt_extab.
  SET TITLEBAR 'T2000' WITH gv_title_01 gv_title_02 .

  PERFORM frm_set_list_box USING 'GS_TA01-ZHTLX'
                                  gt_vls_zhtlx.


  IF gv_flg_rb NE '04'.

    LOOP AT SCREEN.
      IF screen-name = 'B_TK_R_CANCEL' OR screen-name = 'B_TK_R_OK' .
        screen-active = 0.
      ENDIF.

      IF screen-name = 'B_TK_R_DEL' .
        screen-active = 0.
      ENDIF.

      MODIFY SCREEN.
    ENDLOOP.
  ELSE.
    PERFORM frm_author_check_frgc1 USING  gs_ta01
                                          gs_ta02
                                          ''
                                   CHANGING lv_mtype_auth
                                            lv_msg_auth.
    IF lv_mtype_auth <> 'S'.
      LOOP AT SCREEN.
        IF screen-name = 'B_TK_R_CANCEL' OR screen-name = 'B_TK_R_OK'   .
          screen-active = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    ENDIF.

    PERFORM frm_author_check_del     USING  gs_ta01
                                            gs_ta02
                                            gt_tc02
                                            '01'
                                   CHANGING lv_mtype_auth
                                            lv_msg_auth.
    IF lv_mtype_auth <> 'S'.
      LOOP AT SCREEN.
        IF screen-name = 'B_TK_R_DEL' .
          screen-active = 0.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    ENDIF.
  ENDIF.

  IF gs_data_base-subscreen = '4000'.
    LOOP AT SCREEN.
      IF screen-name = 'B_TK_LAST'.
        screen-active = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ELSEIF gs_data_base-subscreen = '2100'.
    LOOP AT SCREEN.
      IF screen-name = 'B_TK_NEXT' .
        screen-active = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDLOOP.
  ENDIF.

  LOOP AT SCREEN.
    IF screen-name = 'B_ZLRSI' .
      IF  gv_flg_rb = '03' AND gs_data_base-zflg_zlrsi = 'X'.
        screen-active = 1.
      ELSE.
        screen-active = 0.
      ENDIF.

      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.


  PERFORM frm_set_screen_2000.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_2000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_2000 INPUT.

  DATA:
    lv_mtype_tk TYPE bapi_mtype,
    lv_msg_tk   TYPE bapi_msg.

  DATA:
    gt_cmd_msglist TYPE scp1_general_errors,
    gt_msglist     TYPE scp1_general_errors.
  DATA:lv_sign TYPE c.

  CLEAR:
  lv_msg_tk,lv_mtype_tk.

  PERFORM frm_code_pro CHANGING   ok_code
                                  gv_code.

  CASE gv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15' OR 'B_MASS_CANCEL'.

      IF sy-dynnr = '2000' AND ( gv_flg_rb = '01' OR gv_flg_rb = '02' ).
        PERFORM frm_are_you_sure(zbcs0001) USING '未保存的数据将丢失，是否继续？' sy-title.
        IF sy-subrc EQ 0.
          PERFORM frm_pro_data_lock               USING gs_ta02-zht_id
                                                          'X'.
          LEAVE TO SCREEN 0.
        ELSE.
          RETURN.
        ENDIF.
      ELSE.
        LEAVE TO SCREEN 0.

      ENDIF.

    WHEN 'SAVE'.


      PERFORM frm_clear_data_null   CHANGING gt_tc04
                                             gt_t13
                                             gt_t44
                                             gt_t44_all
                                             gt_matnr
                                             .
      PERFORM frm_text_to_lt_t76     USING  gs_data_base-zflg_rb
                                   CHANGING gt_t76.

      PERFORM frm_check_data_main    USING  ''
                                  CHANGING gt_msglist
                                           lv_mtype_tk
                                           lv_msg_tk
*                                      gv_actvt
                                           gs_data_base
                                           gt_t13
                                           gs_ta01
                                           gs_ta02
                                           gt_ta03
                                           gt_ta04
                                           gt_ta05
                                           gt_ta06
                                           gt_tc02
                                           gs_tc05
                                           gt_t11
                                           gt_t44
                                           gt_t44_all
                                           gt_t76
                                           gt_tc04_set
                                           gt_zzlbm_set
                                           gt_zqdbm_set
                                           gt_zzgys_set
                                           gt_bukrs_set
                                           gt_dcwrk_set
                                           gt_werks_set
                                           gt_ekorg_set
                                           gt_matnr_set.

      PERFORM frm_show_message_list USING gt_msglist.


      IF lv_mtype_tk = 'S'.
        PERFORM frm_save_data USING '' CHANGING
                                             lv_mtype_tk
                                             lv_msg_tk
                                             gs_data_base
                                             gs_ta01
                                             gs_ta02
                                             gs_tc05
                                             gt_tc04_set
*                                             gt_t06
                                             gt_t11_all
                                             gt_t11
                                             gt_t12
                                             gt_t13
                                             gt_t14
                                             gt_t44
                                             gt_t44_all
                                             gt_tc02
                                             gt_tc03
                                             gt_ta03
                                             gt_ta04
                                             gt_ta05
                                             gt_ta06
                                             gt_t58_set
                                             gt_t76
                                             gt_zzlbm_set
                                             gt_zqdbm_set
                                             gt_zzgys_set
                                             gt_bukrs_set
                                             gt_dcwrk_set
                                             gt_werks_set
                                             gt_ekorg_set
                                             gt_matnr_set.
        IF lv_mtype_tk = 'S'.
          COMMIT WORK AND WAIT.
          PERFORM frm_update_zcs.
        ELSE.
          ROLLBACK WORK .
          lv_msg_tk = '数据更新失败！'.
        ENDIF.

*        gv_flg_comm = 'SAVE'.    "保存后可继续编辑
      ELSE.
        ROLLBACK WORK.
      ENDIF.

      MESSAGE s888(sabapdocu) WITH lv_msg_tk DISPLAY LIKE lv_mtype_tk.
      RETURN.
    WHEN 'BT_BUKRS' OR 'BT_DCWRK' OR 'BT_WERKS' OR 'BT_EKORG' OR
          'BT_ZGHF'  OR 'BT_ZFLZFF' OR 'BT_MATNR' OR 'BT_XY_SUB' OR
          'BT_ZZLBM' OR 'BT_ZQDBM' OR 'BT_ZZGYS'.
      CHECK gt_tc02[] IS NOT INITIAL.
      CHECK gv_cursor_line IS NOT INITIAL.


      IF gv_code = 'BT_ZFLZFF'.
*        自动将行项目返利支付方添加到返利支付方列表中第一行
        PERFORM frm_pro_pbo_zflzff USING
                                         gv_code
                                         gv_cursor_line
                                         gt_tc02
                                   CHANGING gt_t44_all.
        DATA(lv_flg_zflzff) = 'X'.
        DATA(lv_code_zflzff) =  gv_code.
        DATA(lv_cursor_line_zflzff) =  gv_cursor_line.
      ENDIF.

      PERFORM frm_dtl_perform USING gv_code
                                 gv_cursor_line
                                 gt_tc02
                              CHANGING gs_tc02_sub.

      PERFORM frm_pro_data_double CHANGING
                                             gt_t44_all
                                             gt_tc04_set
                                             gt_zzlbm_set
                                             gt_zqdbm_set
                                             gt_zzgys_set
                                             gt_bukrs_set
                                             gt_dcwrk_set
                                             gt_werks_set
                                             gt_ekorg_set
                                             gt_matnr_set.
*      IF gv_code = 'BT_ZFLZFF'.
      IF lv_flg_zflzff = 'X'.
*        自动将返利支付方列表中第一行添加到行项目返利支付方
        PERFORM frm_pro_pai_zflzff USING
                                         lv_code_zflzff
                                         lv_cursor_line_zflzff
                                         gt_tc02
                                   CHANGING gt_t44_all.
        lv_flg_zflzff = ''.
        lv_code_zflzff = ''.
        lv_cursor_line_zflzff = ''.
      ENDIF.
*      买赠明细
    WHEN 'B_ZMZMX'.
      CHECK gt_ta05[] IS NOT INITIAL.
      CHECK gv_cursor_line IS NOT INITIAL.

      IF gs_ta02-zspz_id IS INITIAL.
        MESSAGE s888(sabapdocu) WITH '商品组不允许为空' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

*      抬头商品组更换后，删除不符合的数据
      DELETE gt_t58_set WHERE zspz_id NE gs_ta02-zspz_id.
*      若行项目为空时，将商品组ZRET0020中明细复制至该表，
      PERFORM frm_add_spz_to_t58_set USING
                                           gv_code
                                           gv_cursor_line
                                           gt_ta05
                                           gs_ta02-zspz_id
                                     CHANGING gt_t58_set.

      PERFORM frm_dtl_perform_zmzmx USING gv_code
                                          gv_cursor_line
                                          gt_ta05
                                       CHANGING gs_ta05.

    WHEN 'B_TK_JT_SUB'.
      PERFORM frm_dtl_perform_jt USING gv_code
                                 gv_cursor_line
                                 gs_tc05
                                 CHANGING gt_tc02
                                          gs_tc02_sub
                                          gt_t11_all
                                          gt_t11_sub.
*    外部供货方
    WHEN 'B_T13'.
      CALL SCREEN '8001'.
      SORT gt_t13 BY zghf zzzpc.
      DELETE ADJACENT DUPLICATES FROM gt_t13 COMPARING zghf zzzpc.
*    渠道供应商
    WHEN 'B_T12'. CALL SCREEN '8003'.
*    外部支付方
    WHEN 'B_T44'.
      CALL SCREEN '8002'.
      SORT gt_t44 BY zflzff.
      DELETE ADJACENT DUPLICATES FROM gt_t44 COMPARING zflzff.

*    规则
    WHEN 'B_GZ_01'  OR 'B_GZ_02' OR 'B_GZ_03' OR 'B_GZ_04' OR 'B_GZ_05'.
      PERFORM frm_set_data_tc05_bt USING gv_code
                                      gt_tc05
                                CHANGING gs_tc05.
*    模板
*    WHEN 'B_ZCLRID'.
*      PERFORM frm_set_data_tc05_man CHANGING gs_tc05.
*    规则列表
    WHEN 'B_GZ_LIST'.
      PERFORM frm_alv_dis_gz.
*    添加规则
    WHEN 'B_GZ_ADD'.
      SUBMIT zred0038
        WITH p_zclrtp = 'M'
        WITH p_zxytyp = gs_data_base-zxybstyp
        WITH p_zht_id = gs_ta01-zht_id
        WITH p_call = 'X'
        WITH rb_add = 'X'
        VIA SELECTION-SCREEN
        AND RETURN .
      PERFORM frm_refresh_data_tc05.

*    修改规则
    WHEN 'B_GZ_EDIT'.
      IF gs_tc05-zclrid IS INITIAL.
        MESSAGE s888(sabapdocu) WITH '规则ID为空' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.
      IF gs_tc05-zclrtp = 'S'.
        MESSAGE s888(sabapdocu) WITH '规则类型为S，不允许修改' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

*      SUBMIT zred0038
*        WITH p_zclrtp = gs_tc05-zclrtp
*        WITH p_zht_id = gs_ta01-zht_id
*        WITH p_call = 'X'
*        WITH rb_edit = 'X'
*        VIA SELECTION-SCREEN
*        AND RETURN .
      SET PARAMETER ID 'ZCLRID' FIELD gs_tc05-zclrid.
      CALL TRANSACTION 'ZREM0007' AND SKIP FIRST SCREEN.
      SET PARAMETER ID 'ZCLRID' FIELD ''.


      PERFORM frm_refresh_data_tc05.

*    下一步
    WHEN 'B_TK_NEXT'.

      gs_data_base-subscreen = '2100'.
      CALL SCREEN 2000.

      CASE gv_code.
        WHEN 'BACK' OR 'EXIT' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15' OR 'B_MASS_CANCEL'.
          LEAVE TO SCREEN 0.
        WHEN OTHERS.
      ENDCASE.

*    上一步
    WHEN 'B_TK_LAST'.
      PERFORM frm_set_sub_screen CHANGING gs_data_base .
*      gs_data_base-subscreen = '8004'.
      LEAVE TO SCREEN '2000'.
*    添加条款
    WHEN 'B_TK_PLUS'.

      PERFORM frm_set_list_box_03 USING gs_ta02
                                        gs_tc05
                                  CHANGING   gt_vls_ztk_id_plus.

      CALL SCREEN '2400' STARTING AT 30 5
                         ENDING AT 80 12.
*    附加条款删除
    WHEN 'B_TK_DELE'.
      READ TABLE gt_ta03 TRANSPORTING NO FIELDS WITH KEY sel = 'X' .
      IF sy-subrc NE 0.
        MESSAGE s888(sabapdocu) WITH '请选择需要删除的行' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.
      PERFORM frm_tk_delete CHANGING gt_ta03
                                     gt_ta04.
*    附件条款分配
    WHEN 'B_OK'.
      CLEAR gt_cmd_msglist[].
      gs_tk_add-zflg_add = 'X'.
      PERFORM frm_data_tk_plus_dis USING gs_tk_add
                                         gs_ta02
                                         gs_tc05
                                   CHANGING
                                             lv_mtype_tk
                                             lv_msg_tk
*                                             gt_tk_plus
                                             gt_tc02
                                             gt_ta03
                                            gt_ta04
                                            gt_cmd_msglist.

      IF gt_cmd_msglist[] IS NOT INITIAL.

        CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
          EXPORTING
            title_text    = '消息提示'
            sort_by_level = ' '
            show_ids      = ''
            message_list  = gt_cmd_msglist[].
      ENDIF.

      MESSAGE s888(sabapdocu) WITH lv_msg_tk DISPLAY LIKE lv_mtype_tk.
      IF lv_mtype_tk = 'S'.
        LEAVE TO SCREEN 0.
      ENDIF.

*    添加商品组
    WHEN 'B_SPZ_ADD'.
      DATA:lv_zusage TYPE zret0009-zusage.
      PERFORM frm_set_zusage  USING gs_ta02-zxybstyp CHANGING lv_zusage.

      SUBMIT zrem0002
                      WITH rb_add   = 'X'
                      WITH rb_edit  = ''
                      WITH rb_dis   = ''
                      WITH p_zht_id = gs_ta01-zht_id
                      WITH p_zusage = lv_zusage
                      WITH p_call   = 'X'
                      AND RETURN.
*      SET PARAMETER ID 'VL' FIELD ''.
    WHEN 'B_SPZ_ADD'.
      CLEAR gs_t09_sub.
      gs_t09_sub-zht_id = gs_ta01-zht_id.
      gs_t09_sub-zflg_call = 'X'.

      PERFORM frm_set_zusage  USING gs_ta02-zxybstyp CHANGING gs_t09_sub-zusage.

      PERFORM frm_set_mid_zspz_id USING gs_ta02-zxybstyp.

*      IF gs_ta02-zxybstyp = 'P'.
*        SET PARAMETER ID 'VL' FIELD 'P'.
*      ELSE.
*        SET PARAMETER ID 'VL' FIELD ''.
*      ENDIF.
      CALL SCREEN '4100'.
      SET PARAMETER ID 'VL' FIELD ''.

    WHEN 'B_CANCEL'.  LEAVE TO SCREEN 0.
*    返回主合同
    WHEN 'B_HT_BACK'.

      IF  ( gv_flg_rb = '01' OR gv_flg_rb = '02' ).
        PERFORM frm_are_you_sure(zbcs0001) USING '未保存的数据将丢失，是否继续？' sy-title.
        IF sy-subrc EQ 0.
          PERFORM frm_ht_back USING gv_flg_call gs_ta02.
        ELSE.
          RETURN.
        ENDIF.
      ELSE.
        PERFORM frm_ht_back USING gv_flg_call gs_ta02.
      ENDIF.
*    审批通过
    WHEN 'B_TK_R_OK'.
      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
      IF lv_sign = '' .
        PERFORM frm_check_date           USING gs_ta01-zbukrs .
      ELSE.
        PERFORM frm_yjgk_check_date         USING gs_ta01-zbukrs
                                                gs_ta02-zbegin.
      ENDIF.
      PERFORM frm_check_cnyg              USING gs_ta02-zcnyg .
      PERFORM frm_approval_ok_data  CHANGING gs_ta02 gt_tc02.


*    审批拒绝
    WHEN 'B_TK_R_CANCEL'.
      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
      IF lv_sign = '' .
        PERFORM frm_check_date               USING gs_ta01-zbukrs  .
      ELSE.
        PERFORM frm_yjgk_check_date          USING gs_ta01-zbukrs
                                                   gs_ta02-zbegin.
      ENDIF.

      PERFORM frm_check_cnyg               USING gs_ta02-zcnyg .
      PERFORM frm_check_zleib              USING gs_ta02 .
      PERFORM frm_approval_cancel_data  CHANGING gs_ta02 gt_tc02 .
*    作废
    WHEN 'B_TK_R_DEL'.
      PERFORM frm_abandon_data           USING gs_ta02.
*    审批日志
    WHEN 'B_RLES_LOG'.
      CHECK gs_ta02-ztk_id IS NOT INITIAL.
      PERFORM frm_alv_dis_rles_log   USING gs_ta02-ztk_id.
*    修改日志
    WHEN 'B_MODS_LOG'.
      CHECK gs_ta02-ztk_id IS NOT INITIAL.
      PERFORM frm_alv_dis_mods_log   USING gs_ta02-ztk_id.
*    行审批通过
    WHEN 'B_TK_R_OK_S'.

      PERFORM frm_get_line_data_tc_02 USING gv_code
                                            gv_cursor_line
                                            gt_tc02
                                   CHANGING gs_tc02_sub.

      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
      IF lv_sign = '' .
        PERFORM frm_check_date            USING gs_tc02_sub-zbukrs  .
      ELSE.
        PERFORM frm_yjgk_check_date          USING gs_tc02_sub-zbukrs
                                                         gs_ta02-zbegin.
      ENDIF.

      PERFORM frm_check_cnyg              USING gs_ta02-zcnyg .
      PERFORM frm_approval_item_ok_data   USING gs_ta02
                                       CHANGING gs_tc02_sub.

      MODIFY gt_tc02 FROM gs_tc02_sub INDEX  gv_cursor_line.
*    行审批拒绝
    WHEN 'B_TK_R_CANCEL_S'.

      PERFORM frm_get_line_data_tc_02 USING gv_code
                                            gv_cursor_line
                                            gt_tc02
                                   CHANGING gs_tc02_sub.

      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      PERFORM frm_get_turn_on_sign(zre0001) CHANGING lv_sign IF FOUND .
      IF lv_sign = '' .
        PERFORM frm_check_date            USING gs_tc02_sub-zbukrs  .
      ELSE.
        PERFORM frm_yjgk_check_date          USING gs_tc02_sub-zbukrs
                                                         gs_ta02-zbegin.
      ENDIF.
      PERFORM frm_check_cnyg          USING gs_ta02-zcnyg .
      PERFORM frm_approval_item_cancel_data    USING gs_ta02
                                            CHANGING gs_tc02_sub  .

      MODIFY gt_tc02 FROM gs_tc02_sub INDEX  gv_cursor_line.
*    商品组明细
    WHEN 'XXXX'.

***      gv_flg_comm = gv_code.
***      CHECK gv_cursor_line IS NOT INITIAL.
***      PERFORM frm_get_index_line_new USING      gv_code
***                                            gv_cursor_line
***                                            'TC_ITEM'
***                                 CHANGING   lv_index_line.
    WHEN '&IC1'.

      CHECK gv_fname_ic1 = 'GS_TA02-ZTK_ID'.
      CHECK gs_ta02-ztk_id IS NOT INITIAL.
      SET PARAMETER ID 'ZTK_ID' FIELD gs_ta02-ztk_id.
      CALL TRANSACTION 'ZRER0013' AND SKIP FIRST SCREEN.
      SET PARAMETER ID 'ZTK_ID' FIELD ''.

*    行项目审批
    WHEN 'B_ZLRSI'.
      SET PARAMETER ID 'ZTK_ID' FIELD gs_ta02-ztk_id.
      CALL TRANSACTION  'ZRED0041E'  AND SKIP FIRST SCREEN .
      SET PARAMETER ID 'ZTK_ID' FIELD ''.

    WHEN 'B_SPZ_DIS'.
      PERFORM frm_call_zrem0002 USING
*                                      gs_data_base-zflg_rb
                                      '03'
                                      gs_ta02
                                      gs_ta01.
      IF gs_ta02-zspz_id IS INITIAL.
        SET PARAMETER ID 'ZSPZ_ID' FIELD  gs_ta02-zspz_id.
        SET PARAMETER ID 'ZSPZ_ID' FIELD  ''.

      ENDIF.


    WHEN 'B_ZDATES'.
      CALL SCREEN '9009'.
    WHEN OTHERS.


  ENDCASE.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_3001 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_3001 OUTPUT.

  PERFORM frm_set_list_box USING 'GS_TA02-ZFLLX'
                                  gt_vls_zfllx.

*  PERFORM frm_set_list_box USING 'GS_TA02-ZXYBSTYP'
*                                  gt_vls_zxybstyp.

  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_3002 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_3002 OUTPUT.

*  PERFORM frm_set_list_box USING 'GS_TA02-ZDFFS'
*                                  gt_vls_zdffs.
  PERFORM frm_set_list_box USING 'GS_TA02-ZHSZQ'
                                  gt_vls_zhszq.
  PERFORM frm_set_list_box USING 'GS_TA02-ZJSZQ'
                                  gt_vls_zjszq.

  PERFORM frm_set_list_box USING 'GS_TA02-ZFLDFSJ'
                                  gt_vls_zfldfsj.

  IF gv_flg_rb = '01'.
    PERFORM frm_set_list_box USING 'GS_TA02-ZTMPID'
                                    gt_vls_ztmpid.
  ELSE.
    DATA(lt_vls_ztmpid) = gt_vls_ztmpid[].
    CLEAR lt_vls_ztmpid.
    lt_vls_ztmpid =  VALUE  #( ( key = gs_ta02-ztmpid text = gs_ta02-ztmptxt ) ).
    PERFORM frm_set_list_box USING 'GS_TA02-ZTMPID'
                                    lt_vls_ztmpid.
  ENDIF.

  IF gs_ta02-zhscj = '1000004716' .
    LOOP AT SCREEN.
      IF screen-name = 'GS_TA02-ZACCER' .
        screen-input = 1.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSE.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TA02-ZACCER' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  IF gv_flg_rb NE '01'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TA02-ZTMPID' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_3003 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_3003 OUTPUT.

ENDMODULE.

*&---------------------------------------------------------------------*
*& Module set_editor OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE set_editor OUTPUT.
  IF gv_editor IS INITIAL.
    CREATE OBJECT gv_editor_container
      EXPORTING
        container_name              = 'GV_TEXT'
      EXCEPTIONS
        cntl_error                  = 1
        cntl_system_error           = 2
        create_error                = 3
        lifetime_error              = 4
        lifetime_dynpro_dynpro_link = 5.
    IF sy-subrc NE 0.

    ENDIF.

    CREATE OBJECT gv_editor
      EXPORTING
        name                       = '修改说明'
        parent                     = gv_editor_container
        wordwrap_mode              = cl_gui_textedit=>wordwrap_at_fixed_position
        wordwrap_position          = line_len
        wordwrap_to_linebreak_mode = cl_gui_textedit=>true.

    CALL METHOD gv_editor->set_toolbar_mode
      EXPORTING
        toolbar_mode = ''.
*      EXCEPTIONS
*        error_cntl_call_method = 1
*        others                 = 2

    IF  gs_data_base-zflg_rb = '02' OR  gs_data_base-zflg_rb = '03' OR  gs_data_base-zflg_rb = '04' .
      CALL METHOD gv_editor->set_text_as_r3table
        EXPORTING
          table = gt_edittab.
    ENDIF.

    IF gs_data_base-zflg_rb = '03' OR  gs_data_base-zflg_rb = '04' .

      CALL METHOD gv_editor->set_readonly_mode
        EXPORTING
          readonly_mode = 1.
    ENDIF.
  ENDIF.
ENDMODULE.

MODULE status_3004 OUTPUT.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_DATA_TC_ZJT OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_zjt OUTPUT.


  IF  gt_t11[] IS INITIAL.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  IF gs_t11-zflg_1st = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_T11-ZJT_FROM'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


ENDMODULE.

MODULE mdl_set_data_tc_zjt_sub OUTPUT.


*  IF  gt_t11_sub[] IS INITIAL.
*    LOOP AT SCREEN.
*      IF screen-group1 = '101' .
*        screen-input = 0.
*      ENDIF.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.

  IF gs_t11_sub-zflg_1st = 'X'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_T11_SUB-ZJT_FROM'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


*  PERFORM frm_set_screen_pub.


ENDMODULE.


MODULE mdl_set_data_tc_zcxjt OUTPUT.

  PERFORM frm_set_icon_zcxjt USING   gs_ta05
                                     gt_t58_set
                             CHANGING
                                      gv_icon_zmzmx
                                       .

ENDMODULE.

*&---------------------------------------------------------------------*
*& Module MDL_PBO_4100 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_pbo_8004 OUTPUT.


  PERFORM frm_set_style_zjt USING gs_tc05
                                  'GS_T11'
                                  'TC_ZJT'
                            CHANGING
*                                     gt_t11
                                     gv_zjt_fz_t
                                     gv_zjt_fm_t.

  PERFORM frm_set_data_zjt  USING    gs_tc05
                            CHANGING gt_t11.



  PERFORM frm_set_screen_8004.

  PERFORM frm_set_screen_pub.

ENDMODULE.
MODULE mdl_pbo_8005 OUTPUT.


**  DATA: lth_cols TYPE cx_tableview_column.
**  LOOP AT tc_zjt-cols INTO lth_cols.
**    IF lth_cols-screen-name = 'GS_T11-ZJT_BY'.
**      lth_cols-invisible = 1.
**      MODIFY tc_zjt-cols FROM lth_cols.
**    ENDIF.
**  ENDLOOP.

  PERFORM frm_set_style_zjt USING gs_tc05
                                  'GS_T11_SUB'
                                  'TC_ZJT_SUB'
                            CHANGING
*                                     gt_t11_sub
                                     gv_zjt_fz_t
                                     gv_zjt_fm_t.

  PERFORM frm_set_data_zjt  USING    gs_tc05
                            CHANGING gt_t11_sub.

  PERFORM frm_get_butxt USING gs_tc02_sub-zbukrs CHANGING gs_tc02_sub-zbukrs_t.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDMODULE.

MODULE mdl_pbo_8007 OUTPUT.

  PERFORM frm_set_list_box USING 'GS_TC05-ZJSFF'
                                  gt_vls_zjsff.

  PERFORM frm_set_screen_pub.

ENDMODULE.

*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZJT_FROM  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zjt_from INPUT.

  PERFORM frm_check_zjt_from_new USING
                                       gs_tc05
                                       gt_t11.

  DELETE gt_t11 WHERE zjt_from IS INITIAL AND zjt_by IS NOT INITIAL AND zflg_1st NE 'X'.

ENDMODULE.
MODULE mdl_check_zjt_from_sub INPUT.

  PERFORM frm_check_zjt_from_new USING
                                       gs_tc05
                                       gt_t11_sub.

  DELETE gt_t11_sub WHERE zjt_from IS INITIAL AND zjt_by IS NOT INITIAL AND zflg_1st NE 'X'.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_CURSOR_LINE  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_cursor_line INPUT.
  GET CURSOR LINE gv_cursor_line.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_ZJT OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_zjt OUTPUT.
  IF  gt_t11[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
  PERFORM frm_set_screen_pub.
ENDMODULE.
MODULE mdl_set_screen_tc_zjt_sub OUTPUT.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_t11_sub[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.

MODULE mdl_set_screen_tc_zcxjt OUTPUT.
  IF  gt_ta05[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_8001 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_8001 OUTPUT.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_GHF_WB OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_ghf_wb OUTPUT.
  IF  gt_t13[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_DATA_TC_GHF_WB OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_ghf_wb OUTPUT.
  PERFORM frm_set_data_tc_ghf_wb CHANGING gs_t13.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZGHF_WB  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zghf_wb INPUT.
  PERFORM frm_alpha_in_lifnr CHANGING gs_t13-zghf.
  PERFORM frm_check_zghf_wb USING gs_t13-zghf .

  PERFORM frm_get_zghf_t USING gs_t13-zghf CHANGING gs_t13-name1.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_8002 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_8002 OUTPUT.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.
ENDMODULE.

MODULE status_9009 OUTPUT.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDMODULE.

MODULE status_9010 OUTPUT.

  SET PF-STATUS 'S9000'.
  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_ZFF OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_zff OUTPUT.
  IF  gt_t44[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_pub.
ENDMODULE.


MODULE mdl_set_screen_tc_zdates OUTPUT.

  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_zlrsi USING gv_flg_comm
                                     gv_flg_rb
                                     gs_tc02_sub.
  IF  gt_ta06[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_DATA_TC_ZFF OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_zff OUTPUT.

  PERFORM frm_set_data_tc_zff CHANGING gs_t44.
*  PERFORM frm_alpha_in_lifnr CHANGING gs_t13-zghf.
*  PERFORM frm_check_zghf CHANGING gs_t13-zghf.
ENDMODULE.

MODULE mdl_set_data_tc_zdates OUTPUT.



ENDMODULE.

*&---------------------------------------------------------------------*
*& Module STATUS_8003 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_8003 OUTPUT.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_QDGYS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_qdgys OUTPUT.

  IF  gt_t12[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_DATA_TC_QDGYS OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_data_tc_qdgys OUTPUT.
  PERFORM frm_set_data_tc_zqdgys CHANGING gs_t12.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_LIFNR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_lifnr INPUT.

  PERFORM frm_alpha_in_lifnr CHANGING gs_t12-lifnr.
  PERFORM frm_check_zghf CHANGING gs_t12-lifnr.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_4000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_4000 OUTPUT.



ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_2400 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_2400 OUTPUT.
  SET PF-STATUS 'S9000'.

*  SELECT
*    DISTINCT
*      b~zctgr
*    FROM zreta002 AS a JOIN zretc002 AS b
*                         ON a~ztmpid = b~ztmpid
*   WHERE a~ztk_id = @gs_tk_add-ztk_id_plus
*    INTO TABLE @DATA(lt_zctgr).
*
*  DATA(lt_vls_zctgr) = gt_vls_zctgr[].
*
*  LOOP AT lt_vls_zctgr INTO DATA(ls_vls_zctgr).
*    READ TABLE lt_zctgr TRANSPORTING NO FIELDS WITH KEY zctgr = ls_vls_zctgr-key .
*    IF sy-subrc NE 0.
*      DELETE lt_vls_zctgr.
*      CONTINUE.
*    ENDIF.
*  ENDLOOP.


  DATA:
    gt_vls_tmp_zatkrl TYPE vrm_values,
    gt_vls_tmp_zctgr  TYPE vrm_values.


  IF gs_tk_add-zatktp IS INITIAL.
    gs_tk_add-zatktp = 'P'.
  ENDIF.


  IF gs_tk_add-zatktp = 'A'.
    CLEAR gs_tk_add-ztk_id_plus.
  ENDIF.



  IF gs_tk_add-zatktp = 'P'.
    gs_tk_add-zatkrl_t = '附加条款规则'.
  ELSEIF gs_tk_add-zatktp = 'A'.
    gs_tk_add-zatkrl_t = '下级协议级别'.
  ENDIF.

  IF gs_tk_add-zatktp = 'P'.
    gs_tk_add-zctgr_t = '行项目关联规则'.
  ELSEIF gs_tk_add-zatktp = 'A'.
    gs_tk_add-zctgr_t = '上级协议级别'.
  ENDIF.


  LOOP AT SCREEN.
    IF screen-name = 'GS_TK_ADD-ZTK_ID_PLUS' OR screen-name = 'GS_TK_ADD-ZTK_ID_PLUS' .

      IF gs_tk_add-zatktp = 'A'.
        screen-active = 0.
      ELSE.
        screen-active = 1.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.


  PERFORM frm_pro_data_vls_2400 USING gs_tk_add
                                CHANGING gt_vls_tmp_zatkrl
                                         gt_vls_tmp_zctgr.

  PERFORM frm_set_list_box USING 'GS_TK_ADD-ZCTGR'
                                  gt_vls_tmp_zctgr.
*                                  lt_vls_zctgr.

  PERFORM frm_set_list_box USING 'GS_TK_ADD-ZTK_ID_PLUS'
                                  gt_vls_ztk_id_plus.

  PERFORM frm_set_list_box USING 'GS_TK_ADD-ZATKRL'
                                  gt_vls_tmp_zatkrl.
*                                  gt_vls_zatkrl.

  PERFORM frm_set_list_box USING 'GS_TK_ADD-ZATKTP'
                                  gt_vls_zatktp.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_DATA_ZCLRID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_data_zclrid INPUT.

*  PERFORM frm_set_data_tc05_man CHANGING gs_tc05.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZFLZFF_WB  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zflzff_wb INPUT.

  PERFORM frm_alpha_in_lifnr CHANGING gs_t44-zflzff.

  PERFORM frm_set_screen_zflzff_wb USING gs_t44-zflzff CHANGING gs_t44-name1.

  PERFORM frm_check_status_zflzff USING gs_t44-zflzff.
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '该支付方' && gs_t44-zflzff && '已被冻结或删除，请检查主数据!'.
  ENDIF.

ENDMODULE.

*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZTMPID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_ztmpid INPUT.

  PERFORM frm_set_org_data_ztmpid USING gs_ta02-ztmpid
                                  CHANGING
                                        gs_tc01
                                        gt_tc02
                                        gt_tc03
                                        gt_tc04_set
                                        gt_t44_all
                                        gt_bukrs_set
                                        gt_dcwrk_set
                                        gt_werks_set
                                        gt_ekorg_set
                                        gt_matnr_set
                                        gt_zzgys_set
                                        .
  gs_data_base-zflg_ztmpid_changed = 'X'.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZTMPID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zaccer INPUT.

  PERFORM frm_get_zaccer USING gs_ta02-zhscj CHANGING gs_ta02-zaccer.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_PBO_8006 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_pbo_8006 OUTPUT.

  PERFORM frm_set_list_box USING 'GS_TC05-ZCLRID'
                                gt_vls_zclrid.
  PERFORM frm_set_list_box USING 'GS_TA02-ZSPZ_ID'
                                  gt_vls_zspz_id.
  PERFORM frm_set_list_box USING 'GS_TA02-ZFLSPZ_ID'
                                  gt_vls_zspz_id.


  PERFORM frm_set_data_txt USING gt_t12
                                  gt_t13
                                  gt_t44
                            CHANGING
                                  gs_ta02.



  LOOP AT SCREEN.
    IF screen-name = 'GS_TA02-ZFLSPZ_ID' OR screen-name = 'GS_TA02-ZFLSPZID_TXT' .
      IF gs_ta02-zbarb = 'X'.
        screen-active = 1.
      ELSE.
        screen-active = 0.
      ENDIF.
    ENDIF.




    MODIFY SCREEN.



  ENDLOOP.



  PERFORM frm_set_screen_pub.

  IF gs_ta02-zbarb = ''.
    CLEAR gs_ta02-zflspz_id.
  ENDIF.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_EKORG_41  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_ekorg_41 INPUT.
  PERFORM frm_pro_screen_ekgrp USING gs_ta02-ekgrp CHANGING gs_ta02-eknam.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_F4_ZCLRID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_f4_zclrid INPUT.
  PERFORM frm_f4_zclrid USING gt_tc05.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_F4_ZSPZ_ID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_f4_zspz_id INPUT.

  PERFORM frm_set_mid_zspz_id USING gs_ta02-zxybstyp.
  PERFORM frm_f4_zspz_id USING  'A'
                                gt_t09
                                gs_ta02-zxybstyp
                                gs_ta02-zht_id.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_F4_ZFLSPZ_ID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_f4_zflspz_id INPUT.
  PERFORM frm_set_mid_zspz_id USING gs_ta02-zxybstyp.
  PERFORM frm_f4_zspz_id USING 'B'
                               gt_t09
                               gs_ta02-zxybstyp
                                gs_ta02-zht_id.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_2300 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_2300 OUTPUT.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_SCREEN_ZSPZ_ID_T  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_screen_zspz_id_t INPUT.
  PERFORM frm_set_screen_zspz_id_t USING gs_ta02-zspz_id
                                    CHANGING gs_ta02-zspzid_txt .
ENDMODULE.
MODULE mdl_set_screen_zflspz_id_t INPUT.
  PERFORM frm_set_screen_zspz_id_t USING gs_ta02-zflspz_id
                                    CHANGING gs_ta02-zflspzid_txt .
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_GET_CURSOR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_cursor INPUT.

  GET CURSOR FIELD gv_fname_ic1.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_TC_ZFF  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_tc_zff INPUT.
  IF gs_data_base-zxybstyp = 'F'  ." OR gs_data_base-zxybstyp = 'A'.
    DATA(lt_t44_tmp_tc_zff) = gt_t44.
    DELETE lt_t44_tmp_tc_zff WHERE zflzff IS INITIAL.
    SELECT COUNT(*)  FROM @lt_t44_tmp_tc_zff AS i WHERE i~zflzff IS NOT NULL INTO @DATA(lv_count_zff).
    IF lv_count_zff  > 1.
      CLEAR lv_count_zff.
      MESSAGE e888(sabapdocu) WITH '固定金额类和付款返利条款，外部支付方只能输入一个' DISPLAY LIKE 'E'.
    ENDIF.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_9100 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9100 OUTPUT.

  PERFORM frm_pro_zhsjz USING gs_tc05-zxybstyp
                        CHANGING gt_vls_zhsjz.

  DATA(lt_vls_zhsjz) = gt_vls_zhsjz[].
  DATA(lt_vls_zhsjz_tmp) = gt_vls_zhsjz[].
  DELETE lt_vls_zhsjz WHERE key NE gs_tc05-zhsjz.
  DELETE lt_vls_zhsjz_tmp WHERE key EQ gs_tc05-zhsjz.
  APPEND LINES OF lt_vls_zhsjz_tmp TO lt_vls_zhsjz.
  PERFORM frm_set_list_box USING 'GS_TC05-ZHSJZ'
                                  lt_vls_zhsjz.

  lt_vls_zhsjz[] = gt_vls_zhsjz[].
  lt_vls_zhsjz_tmp[] = gt_vls_zhsjz[].
  DELETE lt_vls_zhsjz WHERE key NE gs_tc05-zflhsjz.
  DELETE lt_vls_zhsjz_tmp WHERE key EQ gs_tc05-zflhsjz.
  APPEND LINES OF lt_vls_zhsjz_tmp TO lt_vls_zhsjz.
  PERFORM frm_set_list_box USING 'GS_TC05-ZFLHSJZ'
                                  lt_vls_zhsjz.

  PERFORM frm_set_list_box USING 'GS_TC05-ZJSFF'
                                  gt_vls_zjsff.



  DATA: ls_zretc005_tmp TYPE zretc005.
  MOVE-CORRESPONDING gs_tc05 TO ls_zretc005_tmp.
  PERFORM frm_set_screen_9100 USING ls_zretc005_tmp.



  LOOP AT SCREEN.
    IF screen-name = 'GS_TC05-ZJTLX'   OR
        screen-name = 'GS_TC05-ZLHWD'        OR
        screen-name = 'GS_TC05-ZFLXS'        OR
        screen-name = 'GS_TC05-ZJSFF'        .

      screen-input = 0.
      MODIFY SCREEN.
    ENDIF.

    IF gs_ta02-zleib = 'R' .
      IF screen-name = 'GS_TC05-ZHSJZ'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDIF.
  ENDLOOP.


  PERFORM frm_set_screen_pub.

  IF gs_data_base-subscreen_mass IS NOT INITIAL.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_ITEM_02 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_screen_tc_item_02 OUTPUT.
  IF gs_data_base-zflg_cprog = 'ZRED0041'..
    IF gv_flg_rb = '04' AND gs_data_base-zlrsi = 'X'.

      CLEAR:lv_mtype_auth,lv_msg_auth.
      PERFORM frm_author_check_item_frgc1 USING  gs_ta01
                                                 gs_ta02
                                                 gs_tc02
                                                 ''
                                     CHANGING lv_mtype_auth
                                              lv_msg_auth.

      IF lv_mtype_auth = 'E' .
        LOOP AT SCREEN  .
          IF  screen-name = 'B_TK_R_OK_S' OR
              screen-name = 'B_TK_R_OK_SS' OR
              screen-name = 'B_TK_R_CANCEL_SS' OR
              screen-name = 'B_TK_R_CANCEL_S'.
            screen-active = 0.
          ENDIF.
          MODIFY SCREEN.
        ENDLOOP.
      ELSE.
        LOOP AT SCREEN  .
          IF  screen-name = 'B_TK_R_OK_S' OR
              screen-name = 'B_TK_R_OK_SS' OR
              screen-name = 'B_TK_R_CANCEL_SS' OR
              screen-name = 'B_TK_R_CANCEL_S'.
            screen-active = 1.
          ENDIF.
          MODIFY SCREEN.
        ENDLOOP.
      ENDIF.
    ENDIF.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZJSZQ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zjszq INPUT.
  PERFORM frm_set_zjszq USING gs_ta02-zhstype gs_ta02-zhszq CHANGING gs_ta02-zjszq.
ENDMODULE.

MODULE mdl_f4_zhscj INPUT.
  PERFORM frm_f4_zbpcode_cj USING gs_ta02-zhscj.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_8002  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_8002 INPUT.
  CASE ok_code.

    WHEN 'B_TKZFF_PASTE'.
      CLEAR:ok_code.
      PERFORM frm_paste_data_tkzff  CHANGING gt_t44.
    WHEN OTHERS.
  ENDCASE.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_8001  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_8001 INPUT.

  CASE ok_code.

    WHEN 'B_TKGHF_PASTE'.
      CLEAR:ok_code.
      PERFORM frm_paste_data_tkghf  CHANGING gt_t13.
    WHEN OTHERS.
  ENDCASE.

ENDMODULE.