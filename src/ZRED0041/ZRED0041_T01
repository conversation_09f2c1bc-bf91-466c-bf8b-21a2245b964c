*&---------------------------------------------------------------------*
*& 包含               ZRED0035_T01
*&---------------------------------------------------------------------*

TYPE-POOLS:slis,vrm.

*渠道供应商
TYPES:
  BEGIN OF ty_t12.
    INCLUDE TYPE zret0012.
TYPES:
  sel   TYPE char1,
  name1 TYPE lfa1-name1,
  END OF ty_t12,
  tt_t12 TYPE TABLE OF ty_t12.

*外部供货方
TYPES:
  BEGIN OF ty_t13.
    INCLUDE TYPE zret0013.
TYPES:
  sel   TYPE char1,
  name1 TYPE lfa1-name1,
  END OF ty_t13,
  tt_t13 TYPE TABLE OF ty_t13.


*协议主表
TYPES:
  BEGIN OF ty_t06.
    INCLUDE TYPE zret0006.
TYPES:

  sel TYPE char1,
  END OF ty_t06,
  tt_t06 TYPE TABLE OF ty_t06.

*协议明细数据-任务动态协议
TYPES:
  BEGIN OF ty_t07.
    INCLUDE TYPE zret0007.
TYPES:
  maktx      TYPE makt-maktx,
  meins      TYPE mara-meins,
  zspzid_txt TYPE zret0009-zspzid_txt,
  sel        TYPE char1,
  END OF ty_t07,
  tt_t07 TYPE TABLE OF ty_t07.

*固定协议行项目表
TYPES:
  BEGIN OF ty_t08.
    INCLUDE TYPE zret0008.
TYPES:

  sel TYPE char1,
  END OF ty_t08,
  tt_t08 TYPE TABLE OF ty_t08.

*协议组织结构表
TYPES:
  BEGIN OF ty_t14.
    INCLUDE TYPE zret0014.
TYPES:
  sel    TYPE char1,
  name1  TYPE lfa1-name1,
  zitems TYPE zretc002-zitems,
  END OF ty_t14,
  tt_t14 TYPE TABLE OF ty_t14.

*添加附加条款结构
TYPES:
  BEGIN OF ty_tk_plus,
    sel         TYPE char1,
    zatktp      TYPE zreta003-zatktp,
    ztk_id_plus TYPE zreta002-ztk_id,
    zatkrl      TYPE zreta004-zatkrl,
    zctgr       TYPE zreta004-zctgr,
    zatkrl_t    TYPE char50,
    zctgr_t     TYPE char50,
    zitems      TYPE zreta003-zitems,
    zflg_add    TYPE char1, "TA03数据新增标识  重新匹配的话不能新增TA03的数据
  END OF ty_tk_plus,
  tt_tk_plus TYPE TABLE OF ty_tk_plus.

*  期间表
TYPES:
  BEGIN OF ty_zqj,
    zbegin TYPE zret0015-zbegin,
    zend   TYPE zret0015-zend,
  END OF ty_zqj,
  tt_zqj TYPE TABLE OF ty_zqj.


*核算期间表
TYPES:
  BEGIN OF ty_t15.
    INCLUDE TYPE zret0015.
TYPES:
  sel TYPE char1,
  END OF ty_t15,
  tt_t15 TYPE TABLE OF ty_t15.

*结算期间表
TYPES:
  BEGIN OF ty_t16.
    INCLUDE TYPE zret0016.
TYPES:
  sel TYPE char1,
  END OF ty_t16,
  tt_t16 TYPE TABLE OF ty_t16.

*促销返利阶梯
TYPES:
  BEGIN OF ty_ta05.
    INCLUDE TYPE zreta005.
TYPES:
  sel        TYPE char1,
  zitems_key TYPE zretc002-zitems,
  END OF ty_ta05,
  tt_ta05 TYPE TABLE OF ty_ta05.

*促销返利生效日表
TYPES:
  BEGIN OF ty_ta06.
    INCLUDE TYPE zreta006.
TYPES:
  sel TYPE char1,
  END OF ty_ta06,
  tt_ta06 TYPE TABLE OF ty_ta06.

*商品阶梯组
TYPES:
  BEGIN OF ty_t58.
    INCLUDE TYPE zret0058.
TYPES:
  sel        TYPE char1,
  zitems_key TYPE zretc002-zitems,
  maktx      TYPE makt-maktx,
  meins      TYPE mara-meins,
  END OF ty_t58,
  tt_t58 TYPE TABLE OF ty_t58.

*条款修改说明
TYPES:
  BEGIN OF ty_t76.
    INCLUDE TYPE zret0076.
TYPES:
END OF ty_t76,
tt_t76 TYPE TABLE OF ty_t76.

DATA:
  gt_ta05       TYPE tt_ta05,
  gs_ta05       TYPE LINE OF tt_ta05,
  gt_ta06       TYPE tt_ta06,               "促销返利生效日表
  gs_ta06       TYPE LINE OF tt_ta06,       "
  gt_t58_set    TYPE tt_t58,               "商品阶梯组-ALL
  gt_t58_sub    TYPE tt_t58,               "商品阶梯组-行项目
  gs_t58_sub    TYPE LINE OF tt_t58,
  gt_t76        TYPE tt_t76.   "

CONSTANTS: line_len TYPE i VALUE 40.

DATA: gv_editor            TYPE REF TO cl_gui_textedit,
      gv_editor_container  TYPE REF TO cl_gui_custom_container,
      gt_edittab(line_len) TYPE c OCCURS 0.
.