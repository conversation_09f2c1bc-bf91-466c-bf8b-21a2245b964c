*&---------------------------------------------------------------------*
*& 包含               ZRED0040_M03
*&---------------------------------------------------------------------*


MODULE status_4100 OUTPUT.
  DATA:
        lt_extab TYPE slis_t_extab.

*  IF gv_flg_rb = '01' OR gv_flg_rb = '02'.
*    APPEND 'EXIT' TO lt_extab.
*    APPEND 'CANCEL' TO lt_extab.
*    APPEND 'BACK' TO lt_extab.
*  ELSE.
*    APPEND 'SAVE' TO lt_extab.
*    APPEND 'CEL' TO lt_extab.
*  ENDIF.
*
*  SET PF-STATUS 'G4100' EXCLUDING lt_extab.
  SET PF-STATUS 'G4100' .
  SET TITLEBAR 'T2000' WITH '商品组' gv_title_02.

*  PERFORM frm_set_screen_pub.


  PERFORM frm_set_screen_zht_id USING gs_t09_sub-zht_id
                                CHANGING gs_t09_sub-zht_txt
                                         gs_t09_sub-zbpcode.


  PERFORM frm_set_screen_spz.


  LOOP AT SCREEN.
    IF screen-name = 'GS_T09_SUB-ZHT_ID'.

      IF sy-tcode NE 'ZREM0002'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.

    IF gs_t09_sub-zspz_id IS NOT INITIAL.
      IF screen-name = 'GS_T09_SUB-ZUSAGE'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDIF.

    IF gs_t09_sub-zflg_call = 'X'.
      IF screen-name = 'GS_T09_SUB-ZUSAGE'.
        screen-input = 0.
        MODIFY SCREEN.
      ENDIF.
    ENDIF.

    IF sy-cprog = 'ZRED0040' AND gs_t09_sub-zspz_id IS INITIAL.
      IF screen-name = 'GS_T09_SUB-ZUSAGE'.
        screen-input = 1.
        MODIFY SCREEN.
      ENDIF.
    ENDIF.
  ENDLOOP.
ENDMODULE.


*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_SPZ' ITSELF
CONTROLS: tc_spz TYPE TABLEVIEW USING SCREEN 4100.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_SPZ'
DATA:     g_tc_spz_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_spz_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t20_sub LINES tc_spz-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_spz_get_lines OUTPUT.
  g_tc_spz_lines = sy-loopc.

ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_spz_modify INPUT.
  MODIFY gt_t20_sub
    FROM gs_t20_sub
    INDEX tc_spz-current_line.


ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_spz_mark INPUT.
  DATA: g_tc_spz_wa2 LIKE LINE OF gt_t20_sub.
  IF tc_spz-line_sel_mode = 1
  AND gs_t20_sub-sel = 'X'.
    LOOP AT gt_t20_sub INTO g_tc_spz_wa2
      WHERE sel = 'X'.
      g_tc_spz_wa2-sel = ''.
      MODIFY gt_t20_sub
        FROM g_tc_spz_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t20_sub
    FROM gs_t20_sub
    INDEX tc_spz-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_SPZ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_spz_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_SPZ'
                              'GT_T20_SUB'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.

FORM frm_check_matnr  USING    pv_matnr TYPE mara-matnr.

  IF  pv_matnr IS NOT INITIAL.
    pv_matnr   = |{ pv_matnr ALPHA = IN WIDTH = 18 }| .
    SELECT SINGLE matnr    FROM mara    WHERE matnr = @pv_matnr
      INTO @DATA(lv_matnr).
    IF sy-subrc NE 0.
      MESSAGE e888(sabapdocu) WITH '商品号码不存在！'.
    ENDIF.
  ENDIF.

ENDFORM.

MODULE mdl_set_screen_tc_spz OUTPUT.

  IF  gt_t20_sub[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.
*  PERFORM frm_set_screen_pub.

  PERFORM frm_set_screen_spz.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_TC_SPZ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_tc_spz OUTPUT.
  PERFORM frm_set_data_tc_spz   USING gs_t09_sub
                              CHANGING gs_t20_sub.
ENDMODULE.

FORM frm_set_data_tc_spz  USING    ps_t09 TYPE LINE OF tt_t09
                          CHANGING ps_t20 TYPE LINE OF tt_t20.

  CLEAR:
        ps_t20-maktx,
        ps_t20-meins.
*        ps_t20-zpeinh,
*        ps_t20-zpeinh_q.

  ps_t20-matnr   = |{ ps_t20-matnr ALPHA = IN WIDTH = 18 }| .
  SELECT SINGLE
    maktx
    INTO ps_t20-maktx
    FROM makt
    WHERE matnr = ps_t20-matnr.

  SELECT SINGLE
    meins
    INTO ps_t20-meins
    FROM mara
    WHERE matnr = ps_t20-matnr.

  IF ps_t20-zpeinh IS INITIAL .
    ps_t20-zpeinh = 1.
  ENDIF.

  IF ps_t20-zpeinh_q IS INITIAL .
    ps_t20-zpeinh_q = 1.
  ENDIF.

  ps_t20-zspz_id = ps_t09-zspz_id.
  ps_t20-zht_id = ps_t09-zht_id.

  IF ps_t20-fdhsj[] IS INITIAL.
    gv_fdhsj_mx = '@1F@'.
  ELSE.
    gv_fdhsj_mx = '@1E@'.
  ENDIF.

ENDFORM.

MODULE mdl_set_data_tc_spz_pai INPUT.

  GET CURSOR LINE gv_cursor_line.

  PERFORM frm_get_index_line_40 USING 'TC_SPZ'
                                 gv_cursor_line
                           CHANGING gv_index_line.

  gs_t09_sub-lines_cur = gv_index_line..

  READ TABLE gt_t20_sub TRANSPORTING NO FIELDS WITH KEY matnr = 'ALL' .
  IF sy-subrc EQ 0.
    LOOP AT gt_t20_sub TRANSPORTING NO FIELDS WHERE matnr IS NOT INITIAL AND matnr NE 'ALL'.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      MESSAGE e888(sabapdocu) WITH '如果行项目中存在商品编码是ALL的情况，则该商品组只能有该一行' DISPLAY LIKE 'E'.
    ENDIF.

    DATA(gv_tmp_lines) = 0.

    LOOP AT gt_t20_sub TRANSPORTING NO FIELDS WHERE matnr IS NOT INITIAL .
      gv_tmp_lines = gv_tmp_lines + 1.
      IF gv_tmp_lines > 1.
        EXIT.
      ENDIF.
    ENDLOOP.
    IF gv_tmp_lines > 1.
      MESSAGE e888(sabapdocu) WITH '如果行项目中存在商品编码是ALL的情况，则该商品组只能有该一行' DISPLAY LIKE 'E'.
    ENDIF.

  ENDIF.

  READ TABLE gt_t20_sub TRANSPORTING NO FIELDS WITH KEY matnr = 'ALL' zspbc = 'X'.
  IF sy-subrc EQ 0.
    MESSAGE e888(sabapdocu) WITH '如果行项目中存在商品编码是ALL的情况，则不能排除' DISPLAY LIKE 'E'.
  ENDIF.


ENDMODULE.

MODULE user_command_4100 INPUT.

  DATA:
    lv_mtype_spz TYPE bapi_mtype,
    lv_msg_spz   TYPE bapi_msg.

  CLEAR:
        lv_msg_spz,lv_mtype_spz.

  PERFORM frm_code_pro CHANGING   ok_code
                                  gv_code.

***  gv_flg_comm = gv_code. 有问题 先注释

  CASE gv_code.
    WHEN 'B_SPZ_SAVE'.
      PERFORM frm_check_data_spz_bf_save   USING     gs_t09_sub
                                           CHANGING
                                                     lv_mtype_spz
                                                     lv_msg_spz
                                                     gt_t20_sub.
      IF lv_mtype_spz = 'S'.
        PERFORM frm_save_data_spz CHANGING
                                           lv_mtype_spz
                                           lv_msg_spz
                                           gs_t09_sub
                                           gt_t20_sub.
        SET PARAMETER ID 'ZSPZ_ID' FIELD gs_t09_sub-zspz_id.
        MESSAGE s888(sabapdocu) WITH '商品组保存成功！'  DISPLAY LIKE 'S'.

        PERFORM frm_again_fl(zbcs0002) USING gs_t09_sub-zspz_id.
      ELSE.
        MESSAGE s888(sabapdocu) WITH lv_msg_spz DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

      LEAVE TO SCREEN 0.
    WHEN 'B_ADD_MTR'.
      CHECK gs_t09_sub-zbpcode IS NOT INITIAL.
      PERFORM frm_alv_dis_spz   USING gs_t09_sub-zbpcode..

    WHEN 'B_SPZ_DOWN'.
      IF gs_t09_sub-zusage = ''.
        PERFORM frm_download_template USING 'ZRED0040' '商品组模板'.
      ELSE..
        PERFORM frm_download_template USING 'ZRED0040A' '商品组模板'.
      ENDIF.
    WHEN 'B_SPZ_PASTE'.
      PERFORM frm_paste_data      USING gs_t09_sub-zusage
                                  CHANGING
                                           gt_t20_sub.


    WHEN 'B_SPZ_FDHSJ'.

      PERFORM frm_fdhsj            USING gs_t09_sub
                                CHANGING gt_t20_sub
                                        .
    WHEN 'B_SPZ_CANCEL'.
*      CLEAR gs_t09_sub.
      LEAVE TO SCREEN 0.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      LEAVE TO SCREEN 0.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.


*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_MTYPE_SPZ
*&      <-- LV_MSG_SPZ
*&      <-- GT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_check_data_spz_bf_save USING ps_t09_sub TYPE LINE OF tt_t09
                                 CHANGING pv_mtype TYPE bapi_mtype
                                  pv_msg   TYPE bapi_msg
                                  pt_t20_sub TYPE tt_t20.

  IF pv_mtype NE 'E'.
    pv_mtype = 'S'.
  ELSE.
    pv_mtype = 'E'.
  ENDIF.

  IF ps_t09_sub-zspzid_txt IS INITIAL.
    pv_mtype = 'E'.
    pv_msg = '商品组描述不能为空'.
  ENDIF.

  LOOP AT pt_t20_sub INTO DATA(ls_t20) WHERE matnr IS NOT INITIAL .

    DATA(lv_tabix) = sy-tabix.
    lv_tabix = lv_tabix + 1.
    LOOP AT pt_t20_sub TRANSPORTING NO FIELDS FROM lv_tabix WHERE matnr = ls_t20-matnr..
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      pv_mtype = 'E'.
      pv_msg = '同一个商品组下不能有重复的商品号码'.
    ENDIF.
  ENDLOOP.

  DATA(lt_tmp) = pt_t20_sub[].
  SORT lt_tmp BY zspbc.
  DELETE ADJACENT DUPLICATES FROM lt_tmp COMPARING zspbc.
  IF lines( lt_tmp ) > 1.
    pv_mtype = 'E'.
    pv_msg = '同一个商品组内，要么所有都标记排除要么都不排除'.
  ENDIF.

  IF pt_t20_sub[] IS INITIAL.
    pv_mtype = 'E'.
    pv_msg = '商品组不能为空'.

  ENDIF.

  READ TABLE pt_t20_sub TRANSPORTING NO FIELDS WITH KEY matnr = ''.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '商品编码不能为空！'.
  ENDIF.

  IF ps_t09_sub-zusage EQ 'P'.
    LOOP AT pt_t20_sub INTO ls_t20  .
      IF ls_t20-zbuy < 0.
        pv_mtype = 'E'.
        pv_msg = '【买】 不能小于0'.
        EXIT.
      ENDIF.

      IF ls_t20-zfree < 0.
        pv_mtype = 'E'.
        pv_msg = '【赠】 不能小于0'.
        EXIT.
      ENDIF.

      IF ls_t20-zbuy = 0 AND ls_t20-zfree = 0.
        pv_mtype = 'E'.
        pv_msg = '【买】和【赠】 至少有一个需要不为0'.
      ENDIF.
    ENDLOOP.

    READ TABLE pt_t20_sub TRANSPORTING NO FIELDS WITH KEY matnr = 'ALL'.
    IF sy-subrc EQ 0.
      pv_mtype = 'E'.
      pv_msg = '促销类型商品不能为ALL'.
      EXIT.
    ENDIF.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SAVE_DATA_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_MTYPE_SPZ
*&      <-- LV_MSG_SPZ
*&      <-- GS_T09_SUB
*&      <-- GT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_save_data_spz  CHANGING pv_mtype TYPE bapi_mtype
                                  pv_msg   TYPE bapi_msg
                                  ps_t09_sub TYPE LINE OF tt_t09
                                  pt_t20_sub TYPE tt_t20.
  DATA:
    lt_t09          TYPE TABLE OF zret0009,
    ls_t09          TYPE zret0009,
    lt_t20          TYPE TABLE OF zret0020,
    ls_t20          TYPE zret0020,
    lt_t20_item     TYPE TABLE OF zret0020_item,
    ls_t20_item     TYPE zret0020_item,
    lt_t09_del      TYPE TABLE OF zret0009,
    lt_t20_del      TYPE TABLE OF zret0020,
    lt_t20_item_del TYPE TABLE OF zret0020_item.

  IF ps_t09_sub-zspz_id IS INITIAL.
    PERFORM frm_get_num USING 'ZRE0003' '01' CHANGING ps_t09_sub-zspz_id.
  ENDIF.

  MOVE-CORRESPONDING ps_t09_sub TO ls_t09.
  MOVE-CORRESPONDING pt_t20_sub TO lt_t20.

  DELETE lt_t20 WHERE matnr IS INITIAL.

  ls_t20-zspz_id = ps_t09_sub-zspz_id.
  ls_t20-zht_id  = ps_t09_sub-zht_id.

  MODIFY lt_t20 FROM ls_t20 TRANSPORTING zspz_id  zht_id WHERE zspz_id NE ls_t20-zspz_id .

  LOOP AT lt_t20 INTO ls_t20.

    IF ls_t20-zpeinh IS INITIAL.
      ls_t20-zpeinh = 1.
    ENDIF.

    IF ls_t20-zpeinh_q IS INITIAL.
      ls_t20-zpeinh_q = 1.
    ENDIF.
    ls_t20-matnr = |{ ls_t20-matnr ALPHA = IN WIDTH = 18 }|.

    MODIFY lt_t20 FROM ls_t20.

  ENDLOOP.

  LOOP AT pt_t20_sub INTO DATA(ls_t20_sub) WHERE matnr IS NOT INITIAL    .
    LOOP AT ls_t20_sub-fdhsj INTO DATA(ls_fdhsj).
      ls_t20_item-zspz_id  = ps_t09_sub-zspz_id.
      ls_t20_item-matnr    = |{ ls_t20_sub-matnr ALPHA = IN WIDTH = 18 }|.
      ls_t20_item-zmgroup  = ls_t20_sub-zmgroup.
      ls_t20_item-zbegdt   = ls_fdhsj-zbegdt.
      ls_t20_item-zenddt   = ls_fdhsj-zenddt.
      ls_t20_item-zacpr    = ls_fdhsj-zacpr.
      APPEND ls_t20_item TO  lt_t20_item.
      CLEAR:ls_t20_item.
    ENDLOOP.
  ENDLOOP.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE lt_t09_del
    FROM zret0009
    WHERE zspz_id = ps_t09_sub-zspz_id
    AND   zht_id  = ps_t09_sub-zht_id.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE lt_t20_del
    FROM zret0020
    WHERE zspz_id = ps_t09_sub-zspz_id
    AND   zht_id  = ps_t09_sub-zht_id.

  IF lt_t20_del[] IS NOT INITIAL .

    SELECT
      *
      INTO CORRESPONDING FIELDS OF TABLE lt_t20_item_del
      FROM zret0020_item
      FOR ALL ENTRIES IN lt_t20_del
      WHERE  zspz_id = lt_t20_del-zspz_id
        AND  matnr   = lt_t20_del-matnr
        AND  zmgroup = lt_t20_del-zmgroup.
  ENDIF.

  IF ls_t09-zcjr IS  INITIAL  .
    ls_t09-zcjr  = sy-uname.
    ls_t09-zcjrq = sy-datum.
    ls_t09-zcjsj = sy-uzeit.
    ls_t09-zxgr  = sy-uname.
    ls_t09-zxgrq = sy-datum.
    ls_t09-zxgsj = sy-uzeit.
  ELSE.
    ls_t09-zxgr  = sy-uname.
    ls_t09-zxgrq = sy-datum.
    ls_t09-zxgsj = sy-uzeit.
  ENDIF.


  DELETE zret0009 FROM TABLE lt_t09_del.
  DELETE zret0020 FROM TABLE lt_t20_del.
  DELETE zret0020_item FROM TABLE lt_t20_item_del.
  MODIFY zret0009 FROM ls_t09.
  MODIFY zret0020 FROM TABLE lt_t20.
  MODIFY zret0020_item FROM TABLE lt_t20_item.
  COMMIT WORK AND WAIT.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_PASTE_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_paste_data  USING pv_zusage TYPE zret0009-zusage CHANGING pt_t20_sub TYPE tt_t20.
  DATA:
        lt_data TYPE TABLE OF tdline.
  DATA:
    ls_t20        TYPE LINE OF tt_t20,
    lt_t20        TYPE  tt_t20,
    ls_fdhsj      TYPE LINE OF zrei0006,
    lt_fdhsj      TYPE zrei0006,
    lv_mtype      TYPE bapi_mtype,
    lv_error_text TYPE string.


  CALL METHOD cl_gui_frontend_services=>clipboard_import
    IMPORTING
      data                 = lt_data
*     length               =
    EXCEPTIONS
      cntl_error           = 1
      error_no_gui         = 2
      not_supported_by_gui = 3
      OTHERS               = 4.
  IF sy-subrc EQ 0.

  ENDIF.


  "拆分
  LOOP AT lt_data INTO DATA(ls_data).

    PERFORM frm_get_data_t20 USING ls_data
                                   pv_zusage
                             CHANGING ls_t20
                                      ls_fdhsj
                                      lv_mtype
                                      lv_error_text.
    IF lv_mtype = 'E'.
      EXIT.
    ENDIF.

*
*    CLEAR: ls_t20.
*    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO ls_t20-matnr lv_str1 lv_str2 lv_str3 ls_t20-zspbc  .
*
**    ls_t20-zacpr = lv_str1.
**    ls_t20-zpeinh = lv_str2.
**    ls_t20-zpeinh_q = lv_str3.
*
*    CLEAR lv_mtype.
*
*    TRY .
*        MOVE:lv_str1 TO ls_t20-zacpr.
*      CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
*        DATA(lv_error_text) = lv_error->get_text( ).
*        lv_error_text       = |核算价必须为数字|  .
*        lv_mtype = 'E'.
*        EXIT.
*      CLEANUP.
*    ENDTRY.
*
*    TRY .
*        MOVE:lv_str2 TO ls_t20-zpeinh.
*      CATCH cx_sy_conversion_no_number INTO lv_error.
*        lv_error_text = lv_error->get_text( ).
*        lv_error_text       = |价格倍数必须为数字|  .
*        lv_mtype = 'E'.
*        EXIT.
*      CLEANUP.
*    ENDTRY.
*
*    TRY .
*        MOVE:lv_str3 TO ls_t20-zpeinh_q.
*      CATCH cx_sy_conversion_no_number INTO lv_error.
*        lv_error_text = lv_error->get_text( ).
*        lv_error_text       = |数量倍数必须为数字|  .
*        lv_mtype = 'E'.
*        EXIT.
*      CLEANUP.
*    ENDTRY.



    READ TABLE lt_t20 ASSIGNING FIELD-SYMBOL(<lfs_t20>) WITH  KEY matnr   = ls_t20-matnr
                                                                  zmgroup = ls_t20-zmgroup.
    IF sy-subrc <> 0.
      IF ls_fdhsj IS NOT INITIAL.
        APPEND ls_fdhsj TO lt_fdhsj.
        CLEAR:ls_fdhsj.
        ls_t20-fdhsj = lt_fdhsj.
      ENDIF.

      APPEND ls_t20 TO lt_t20.
    ELSE.

      lt_fdhsj = <lfs_t20>-fdhsj.

      IF ls_fdhsj IS NOT INITIAL .
        APPEND ls_fdhsj TO lt_fdhsj.
        CLEAR:ls_fdhsj.
      ENDIF.

      MOVE-CORRESPONDING ls_t20 TO <lfs_t20>.
      <lfs_t20>-fdhsj = lt_fdhsj.
    ENDIF.
    REFRESH lt_fdhsj.
    CLEAR:ls_t20.
*    APPEND ls_t20 TO pt_t20_sub.
  ENDLOOP.

  IF lv_mtype = 'E'..
    MESSAGE:lv_error_text TYPE 'S' DISPLAY LIKE 'E'.
  ELSE.
    APPEND LINES OF lt_t20 TO pt_t20_sub.
  ENDIF.


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_T20
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_DATA
*&      --> PV_ZUSAGE
*&      <-- LS_T20
*&      <-- LV_MTYPE
*&      <-- LV_ERROR_TEXT
*&---------------------------------------------------------------------*
FORM frm_get_data_t20  USING    ls_data TYPE tdline
                                pv_zusage TYPE zret0009-zusage
                       CHANGING ls_t20  TYPE LINE OF tt_t20
                                ls_fdhsj TYPE LINE OF zrei0006
                                lv_mtype  TYPE bapi_mtype
                                lv_error_text TYPE string.




  DATA:
    lv_begdt TYPE string,
    lv_enddt TYPE string,
    lv_str1  TYPE string,
    lv_str2  TYPE string,
    lv_str3  TYPE string,
    lv_str4  TYPE string,
    lv_str5  TYPE string,
    lv_str6  TYPE string,
    lv_str7  TYPE string,
    lv_str8  TYPE string.

  CLEAR: ls_t20,lv_mtype,lv_error_text.

  IF pv_zusage EQ ''.

    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO ls_t20-matnr lv_begdt lv_enddt  lv_str1 lv_str2 lv_str3 ls_t20-zspbc .

    TRY .
        MOVE:lv_str1 TO ls_t20-zacpr.
      CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |核算价必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.

    TRY .
        MOVE:lv_str2 TO ls_t20-zpeinh.
      CATCH cx_sy_conversion_no_number INTO lv_error.
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |价格倍数必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.

    TRY .
        MOVE:lv_str3 TO ls_t20-zpeinh_q.
      CATCH cx_sy_conversion_no_number INTO lv_error.
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |数量倍数必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.

    IF  lv_begdt IS NOT INITIAL AND lv_begdt IS NOT INITIAL  .

      MOVE-CORRESPONDING ls_t20 TO ls_fdhsj.

      TRY .
          MOVE:lv_begdt TO ls_fdhsj-zbegdt.
        CATCH cx_sy_conversion_no_number INTO lv_error.
          lv_error_text = lv_error->get_text( ).
          lv_error_text       = |起始日期必须为日期|  .
          lv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

      TRY .
          MOVE:lv_enddt TO ls_fdhsj-zenddt.
        CATCH cx_sy_conversion_no_number INTO lv_error.
          lv_error_text = lv_error->get_text( ).
          lv_error_text       = |截止日期必须为日期|  .
          lv_mtype = 'E'.
          EXIT.
        CLEANUP.
      ENDTRY.

      IF ls_fdhsj-zbegdt > ls_fdhsj-zenddt  .
        lv_error_text       = |起始日期需小于截止日期|  .
        lv_mtype = 'E'.
      ENDIF.
    ELSEIF lv_begdt IS INITIAL AND lv_begdt IS  INITIAL.

    ELSE.
      lv_error_text       = |分段核算价起始日期和截止日期必填|  .
      lv_mtype = 'E'.
    ENDIF.


  ELSE.

    SPLIT ls_data AT cl_abap_char_utilities=>horizontal_tab INTO ls_t20-zmgroup ls_t20-matnr lv_str1 lv_str2 lv_str3   .


    TRY .
        MOVE:lv_str1 TO ls_t20-zbuy.
      CATCH cx_sy_conversion_no_number INTO lv_error.
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |【买】必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.

    TRY .
        MOVE:lv_str2 TO ls_t20-zfree.
      CATCH cx_sy_conversion_no_number INTO lv_error.
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |【赠】必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.

    TRY .
        MOVE:lv_str3 TO ls_t20-zcmpst.
      CATCH cx_sy_conversion_no_number INTO lv_error.
        lv_error_text = lv_error->get_text( ).
        lv_error_text       = |【加提单价】必须为数字|  .
        lv_mtype = 'E'.
        EXIT.
      CLEANUP.
    ENDTRY.
  ENDIF.


*
*  CASE gs_t09_sub-zusage .
*
*    WHEN 'P' .
*      PERFORM set_list_value USING  ls_data
*                                    pv_zusage
*                           CHANGING ls_t20
*                                    lv_mtype
*                                   lv_error_text .
*    WHEN OTHERS.
*      PERFORM set_list_value_others USING  ls_data
*                                    pv_zusage
*                           CHANGING ls_t20
*                                    lv_mtype
*                                    lv_error_text .
*
*
*
*  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_T20
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_DATA
*&      --> PV_ZUSAGE
*&      <-- LS_T20
*&      <-- LV_MTYPE
*&      <-- LV_ERROR_TEXT
*&---------------------------------------------------------------------*
FORM set_list_value  USING    ls_data TYPE tdline
                                pv_zusage TYPE zret0009-zusage
                       CHANGING ls_t20  TYPE LINE OF tt_t20
                                lv_mtype  TYPE bapi_mtype
                                lv_error_text TYPE string.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_T20
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_DATA
*&      --> PV_ZUSAGE
*&      <-- LS_T20
*&      <-- LV_MTYPE
*&      <-- LV_ERROR_TEXT
*&---------------------------------------------------------------------*
FORM set_list_value_others  USING    ls_data TYPE tdline
                                pv_zusage TYPE zret0009-zusage
                       CHANGING ls_t20  TYPE LINE OF tt_t20
                                lv_mtype  TYPE bapi_mtype
                                lv_error_text TYPE string.
ENDFORM.




FORM frm_get_index_line_40  USING    pv_code TYPE sy-ucomm
                                  pv_cursor_line  TYPE i
                         CHANGING pv_index_line   TYPE i.


  DATA:
    lv_field_name TYPE char30,
    lv_tc_name    TYPE dynfnam.
  FIELD-SYMBOLS:
                 <fs_any> TYPE any.

  PERFORM frm_get_tc_name_40 USING pv_code
                          CHANGING lv_tc_name.

  CLEAR pv_index_line.

  lv_field_name = lv_tc_name && '-TOP_LINE'.

  ASSIGN (lv_field_name) TO <fs_any>.
  IF <fs_any> IS ASSIGNED .
    pv_index_line = <fs_any> + pv_cursor_line - 1.
  ENDIF.


ENDFORM.

FORM frm_get_tc_name_40  USING    pv_code  TYPE sy-ucomm
                      CHANGING pv_tc_name TYPE dynfnam.

  CASE pv_code.
    WHEN 'B_SPZ_ADD'. pv_tc_name = 'TC_T09'.
    WHEN 'B_SPZ_EDIT'. pv_tc_name = 'TC_T09'.
    WHEN 'B_SPZ_DELE'. pv_tc_name = 'TC_T09'.
    WHEN 'B_SPZ_DTL'. pv_tc_name = 'TC_T09'.
    WHEN '&IC1'. pv_tc_name = 'TC_T09'.
    WHEN 'TC_SPZ'. pv_tc_name = 'TC_SPZ'.
    WHEN 'B_TK_JT_SUB'. pv_tc_name = 'TC_ZJT_SUB'.
    WHEN OTHERS. pv_tc_name = 'TC_T09'.
  ENDCASE.

ENDFORM.



*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_spz .



  IF gv_flg_comm = 'B_SPZ_DTL'..
    LOOP AT SCREEN.
      IF screen-group1 = '101' OR screen-group1 = '102'.
        screen-input = 0.
      ENDIF.

      IF screen-name = 'GV_FDHSJ_MX' .
        screen-input = 1.
      ENDIF.

      MODIFY SCREEN.
    ENDLOOP.

  ENDIF.

  IF gv_flg_rb = '03'.
*    OR gv_flg_comm = 'B_SPZ_DTL'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' OR screen-group1 = '102'.
        screen-input = 0.
      ENDIF.

      IF screen-name = 'GV_FDHSJ_MX' .
        screen-input = 1.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_SCREEN_MATNR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_screen_matnr INPUT.
  PERFORM frm_set_spz_matnr          USING gs_t20_sub-matnr
                                  CHANGING gs_t20_sub-meins
                                           gs_t20_sub-maktx
                                           gs_t20_sub-fdhsj.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_SCREEN_ZBPCODE  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_screen_zbpcode INPUT.
  PERFORM frm_set_screen_zbpcode USING gs_t09_sub-zbpcode.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_ZBPCODE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T09_SUB_ZBPCODE
*&---------------------------------------------------------------------*
FORM frm_set_screen_zbpcode  USING    pv_zbpcode TYPE zreta001-zbpcode.


  PERFORM frm_alv_dis_spz   USING pv_zbpcode..

ENDFORM.
*&---------------------------------------------------------------------*
*&      Module  MDL_F4_ZBPCODE_CJ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_f4_zbpcode_cj INPUT.
  PERFORM frm_f4_zbpcode_cj USING gs_t09_sub-zbpcode.
ENDMODULE.

FORM frm_f4_zbpcode_cj  USING    pv_zbpcode TYPE zreta001-zbpcode.

  DATA dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.
  DATA:lt_ret_tab TYPE TABLE OF ddshretval WITH HEADER LINE.



*  SELECT
*     a~guidkey ,
*     a~codeitemid ,
*     a~codeitemdesc ,
*     a~b0105 ,
*     a~parentid ,
*     b~b0105 AS b0105_p
*    FROM zmmt0345 AS a LEFT JOIN zmmt0345 AS b
*                              ON a~parentid = b~codeitemid
*    INTO TABLE @DATA(lt_zmmt0345).

  SELECT partner,name_org1 INTO TABLE @DATA(lt_but000) FROM but000 WHERE bu_group = 'BP07'.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'PARTNER'
      value_org       = 'S'
      dynpprog        = sy-repid
      dynpnr          = sy-dynnr
      dynprofield     = 'GS_T09_SUB-ZBPCODE'
*     callback_program = sy-repid
*     callback_form   = 'USER_FORM'
    TABLES
      value_tab       = lt_but000[]
      return_tab      = lt_ret_tab[]
    EXCEPTIONS
      parameter_error = 1
      no_values_found = 2
      OTHERS          = 3.


  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
  ENDIF.

ENDFORM.

FORM frm_alv_dis_spz USING pv_zbpcode TYPE zreta001-zbpcode.

  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  CLEAR:
        gt_mara.

  SELECT
    a~matnr,
    c~meins,
    b~maktx
    FROM zmmt0001 AS a
    JOIN makt     AS b ON a~matnr = b~matnr
    JOIN mara     AS c ON a~matnr = c~matnr
*    JOIN zmmt0345 AS d ON a~guidkey = d~guidkey
*    WHERE d~codeitemid = @pv_zbpcode
    WHERE a~guidkey = @pv_zbpcode

    INTO CORRESPONDING FIELDS OF TABLE @gt_mara.

*  判断商品组是新增还是修改
  SELECT SINGLE COUNT(*) FROM zret0009 WHERE zspz_id = gs_t09_sub-zspz_id.
  IF sy-subrc EQ 0.
    DATA(lv_flg) = 'E'.
  ELSE.
    lv_flg = 'A'.
  ENDIF.

  LOOP AT gt_mara INTO DATA(ls_mara).
    IF lv_flg = 'A'.
      ls_mara-sel = 'X'.
    ELSE.
      READ TABLE gt_t20_sub TRANSPORTING NO FIELDS WITH KEY matnr = ls_mara-matnr.
      IF sy-subrc EQ 0.
        ls_mara-sel = 'X'.
      ENDIF.
    ENDIF.
    MODIFY gt_mara FROM ls_mara.
  ENDLOOP.

  IF gv_flg_rb = '01'.

  ENDIF.


  PERFORM frm_set_catalog_spz     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout_spz      CHANGING  ls_layout.


  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
*     is_layout_lvc            = &1
      it_fieldcat_lvc          = lt_fieldcat
      is_layout_lvc            = ls_layout
      i_callback_pf_status_set = 'FRM_STATUS_ALV_SPZ'
      i_callback_user_command  = 'FRM_ALV_COMMAND_SPZ'
      i_screen_start_column    = 15
      i_screen_start_line      = 2
      i_screen_end_column      = 130
      i_screen_end_line        = 22
    TABLES
      t_outtab                 = gt_mara.


ENDFORM.

FORM frm_set_catalog_spz CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext = &5.                 "

    CASE &4.
      WHEN 'MEINS'  .      ls_fieldcat-convexit = 'CUNIT'.

      WHEN 'MATNR'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'SEL'  .
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.


*  add_fcat  '10' '' '' 'SEL'    '选择'  .
  add_fcat  '10' '' '' 'SEL'    '选择'  .
  add_fcat  '10' 'MARA' 'MATNR' 'MATNR'    '商品编码'  .
  add_fcat  '10' '' '' 'MAKTX'    '商品描述'  .
  add_fcat  '10' '' '' 'MEINS'    '基本计量单位'  .




ENDFORM.                    "SET_CATALOG

FORM frm_set_layout_spz CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
*  ps_layout-box_fname = 'SEL'.
  ps_layout-sel_mode = 'D'.

ENDFORM.                    "SET_LAYOUT

FORM frm_status_alv_spz USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.
  SET PF-STATUS 'SSPZ' EXCLUDING pt_extab.
ENDFORM.                    "PFSTATUS_FORM

FORM frm_alv_command_spz USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_check_changed_data_spz.

  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.
    WHEN 'SEL_SPZ'.
      CLEAR rv_ucomm.

      DATA(lt_mara)  = gt_mara[].
      DELETE lt_mara WHERE sel = ''.


*      READ TABLE gt_mara INTO DATA(ls_mara) WITH KEY sel = 'X'.
*      IF sy-subrc NE 0.
*        MESSAGE s888(sabapdocu) WITH '请选择行！' DISPLAY LIKE 'E'.
*        RETURN.
*      ENDIF.

      PERFORM frm_add_data_spz USING gt_mara
                                     gs_t09_sub
                               CHANGING gt_t20_sub.

      LEAVE TO SCREEN 0.
*    WHEN '&IC1'.
*      READ TABLE gt_tc05 INTO gs_tc05 INDEX rs_selfield-tabindex.
*      IF sy-subrc EQ 0.
*        LEAVE TO SCREEN 0.
*      ENDIF.
    WHEN 'ALL'.
      PERFORM frm_set_all TABLES gt_mara USING 'SEL'.
    WHEN 'SAL'.
      PERFORM frm_set_sal TABLES gt_mara USING 'SEL'.

    WHEN OTHERS.
  ENDCASE.


  PERFORM frm_refresh_alv_spz.

ENDFORM.                    "USER_COMMAND_FORM

FORM frm_check_changed_data_spz .
  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.
  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.
  CALL METHOD lrf_alv->check_changed_data.
ENDFORM.

FORM frm_refresh_alv_spz .

  DATA:
        lrf_alv   TYPE REF TO cl_gui_alv_grid.

  DATA:
        ls_stable TYPE lvc_s_stbl.

  ls_stable-row = 'X'.
  ls_stable-col = 'X'.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.

  IF lrf_alv IS NOT INITIAL.
    CALL METHOD lrf_alv->refresh_table_display
      EXPORTING
        is_stable = ls_stable.
  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_ADD_DATA_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_MARA
*&      <-- GT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_add_data_spz  USING    pt_mara TYPE tt_mara
                                ps_t09 TYPE LINE OF tt_t09
                       CHANGING pt_t20 TYPE tt_t20.

  DATA:
        ls_t20 TYPE LINE OF tt_t20.


*  判断商品组是新增还是修改
  SELECT SINGLE COUNT(*) FROM zret0009 WHERE zspz_id = ps_t09-zspz_id.
  IF sy-subrc EQ 0.
    DATA(lv_flg) = 'E'.
  ELSE.
    lv_flg = 'A'.
  ENDIF.

  LOOP AT pt_mara INTO DATA(ls_mara).

    READ TABLE pt_t20 INTO ls_t20 WITH KEY matnr = ls_mara-matnr .
    IF sy-subrc NE 0.
      IF  ls_mara-sel = 'X'..
        CLEAR ls_t20.
        ls_t20-matnr = ls_mara-matnr.
        ls_t20-zpeinh_q = 1.
        ls_t20-zpeinh = 1.
        APPEND ls_t20 TO pt_t20.

      ENDIF.
    ELSE.
*      IF lv_flg = 'E'.
      IF ls_mara-sel = ''.
        DELETE pt_t20 WHERE matnr = ls_mara-matnr.
      ENDIF.
*      ENDIF.
    ENDIF.

  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_SCREEN_ZHT_ID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_screen_zht_id INPUT.
  PERFORM frm_set_screen_zht_id USING gs_t09_sub-zht_id
                                CHANGING gs_t09_sub-zht_txt
                                         gs_t09_sub-zbpcode.
ENDMODULE.



FORM frm_alv_dis_rles_log USING pv_ztk_id TYPE zreta002-ztk_id.

  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  CLEAR:
        gt_mara.

  SELECT
    a~*
    FROM zret0050 AS a
    WHERE a~ztk_id = @pv_ztk_id
    INTO  TABLE @DATA(lt_t50).


  PERFORM frm_set_catalog_rles_log     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout_rles_log      CHANGING  ls_layout.

  SORT lt_t50 BY ztk_id zxy_id.
  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
*     is_layout_lvc            = &1
      it_fieldcat_lvc          = lt_fieldcat
      is_layout_lvc            = ls_layout
      i_callback_pf_status_set = 'FRM_STATUS_ALV_RLES_LOG'
      i_callback_user_command  = 'FRM_ALV_COMMAND_RLES_LOG'
      i_screen_start_column    = 15
      i_screen_start_line      = 2
      i_screen_end_column      = 130
      i_screen_end_line        = 22
    TABLES
      t_outtab                 = lt_t50.


ENDFORM.



FORM frm_set_catalog_rles_log CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext = &5.                 "

    CASE &4.
      WHEN 'ZXYZT_O' OR 'ZXYZT_N' .      ls_fieldcat-convexit = 'ZXYZT'.

      WHEN 'MATNR'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'SEL'  .
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.


*  add_fcat  '10' '' '' 'SEL'    '选择'  .
  add_fcat  '10' '' '' 'ZTK_ID'    '条款ID'  .
  add_fcat  '10' '' '' 'ZXY_ID'    '协议ID'  .
*  add_fcat  '10' '' '' 'TIMESTAMP'    '时间戳'  .
  add_fcat  '10' '' '' 'ZXYZT_O'    '原返利条款状态'  .
  add_fcat  '10' '' '' 'ZXYZT_N'    '新返利条款状态'  .
  add_fcat  '10' '' '' 'FRGC1_O'    '原审批代码'  .
  add_fcat  '10' '' '' 'FRGC1_N'    '新审批代码'  .
  add_fcat  '10' '' '' 'ZCJRQ'    '操作日期'  .
  add_fcat  '10' '' '' 'ZCJSJ'    '操作时间'  .
  add_fcat  '10' '' '' 'ZCJR'    '操作人'  .



ENDFORM.                    "SET_CATALOG

FORM frm_set_layout_rles_log CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
*  ps_layout-box_fname = 'SEL'.
  ps_layout-sel_mode = 'D'.

ENDFORM.                    "SET_LAYOUT

FORM frm_status_alv_rles_log USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.

  pt_extab = VALUE #( ( fcode = 'SEL_SPZ' ) ) .

  SET PF-STATUS 'SSPZ' EXCLUDING pt_extab.
ENDFORM.                    "PFSTATUS_FORM

FORM frm_alv_command_rles_log USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_check_changed_data_spz.

  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.
    WHEN OTHERS.
  ENDCASE.


  PERFORM frm_refresh_alv_spz.

ENDFORM.                    "USER_COMMAND_FORM

FORM frm_alv_dis_mods_log USING pv_ztk_id.



  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant,
        lv_message  TYPE c LENGTH 480.

  DATA:BEGIN OF lt_t76 OCCURS 0,
         ztext1 TYPE zret0076-ztext1,
         ztext2 TYPE zret0076-ztext2,
         ztext3 TYPE zret0076-ztext3,
         ztext4 TYPE zret0076-ztext4,
         ztk_id TYPE zret0076-ztk_id,
         erdat  TYPE zret0076-erdat,
         ertim  TYPE zret0076-ertim,
         ernam  TYPE zret0076-ernam,
       END OF lt_t76.
  SELECT *
    INTO CORRESPONDING FIELDS OF TABLE lt_t76
    FROM zret0076
    WHERE ztk_id = pv_ztk_id
      AND zxyzt = 'A'
        .
  LOOP AT lt_t76 ASSIGNING FIELD-SYMBOL(<lfs_t76>) .
    CONDENSE <lfs_t76>-ztext1 NO-GAPS.
    CONDENSE <lfs_t76>-ztext2 NO-GAPS.
    CONDENSE <lfs_t76>-ztext3 NO-GAPS.
    CONDENSE <lfs_t76>-ztext4 NO-GAPS.

    lv_message = <lfs_t76>-ztext1 && <lfs_t76>-ztext2 && <lfs_t76>-ztext3 && <lfs_t76>-ztext4.
    CONDENSE lv_message NO-GAPS.

    <lfs_t76>+0(480) = lv_message.
  ENDLOOP.

  PERFORM frm_set_catalog_mods_log     CHANGING  lt_fieldcat.

  DATA(lt_check) = lt_t76[].

  DELETE lt_check WHERE ztext2 = ''.
  IF lt_check[] IS INITIAL .
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT2'.
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT3'.
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT4'.
  ENDIF.

  DELETE lt_check WHERE ztext3 = ''.
  IF lt_check[] IS INITIAL .
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT3'.
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT4'.
  ENDIF.

  DELETE lt_check WHERE ztext4 = ''.
  IF lt_check[] IS INITIAL .
    DELETE lt_fieldcat WHERE fieldname =  'ZTEXT4'.
  ENDIF.


  ls_layout-cwidth_opt = 'X'.
  ls_layout-zebra      = 'X'.
  ls_layout-sel_mode   = 'D'.

  SORT lt_t76 BY ztk_id .
  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program    = sy-repid
*     is_layout_lvc         = &1
      it_fieldcat_lvc       = lt_fieldcat
      is_layout_lvc         = ls_layout
*     i_callback_pf_status_set = 'FRM_STATUS_ALV_RLES_LOG'
*     i_callback_user_command  = 'FRM_ALV_COMMAND_RLES_LOG'
      i_screen_start_column = 15
      i_screen_start_line   = 2
      i_screen_end_column   = 130
      i_screen_end_line     = 22
    TABLES
      t_outtab              = lt_t76.
ENDFORM.

FORM frm_set_catalog_mods_log CHANGING pt_fieldcat TYPE lvc_t_fcat.

  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext   = &5.                 "
    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.


  add_fcat  '10' '' '' 'ZTK_ID'    '条款ID'  .
  add_fcat  '10' '' '' 'ZTEXT1'    '修改说明'  .
  add_fcat  '10' '' '' 'ZTEXT2'    '修改说明1'  .
  add_fcat  '10' '' '' 'ZTEXT3'    '修改说明2'  .
  add_fcat  '10' '' '' 'ZTEXT4'    '修改说明3'  .
  add_fcat  '10' '' '' 'ERDAT'     '建立日期'  .
  add_fcat  '10' '' '' 'ERTIM'     '创建时间'.
  add_fcat  '10' '' '' 'ERNAM'     '创建人'.

ENDFORM.                    "SET_CATALOG

*&---------------------------------------------------------------------*
*& Form FRM_F4_ZSPZ_ID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T09
*&---------------------------------------------------------------------*

FORM frm_f4_zspz_id  USING  pv_flg TYPE char1
                             pt_t09 TYPE tt_t09
                             pv_zxybstyp TYPE zreta002-zxybstyp
                             pv_zht_id TYPE zreta002-zht_id.

  DATA dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.
  DATA:lt_ret_tab TYPE TABLE OF ddshretval WITH HEADER LINE.
  DATA:
        lv_dynprofield TYPE help_info-dynprofld.

  DATA:
      lv_dis TYPE ddbool_d .




  IF pv_flg = 'A'.
    lv_dynprofield = 'GS_TA02-ZSPZ_ID'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TA02-ZSPZ_ID' AND screen-input EQ 0.
        lv_dis = 'X'.
      ENDIF.
    ENDLOOP.
  ELSE.
    lv_dynprofield = 'GS_TA02-ZFLSPZ_ID'.
    LOOP AT SCREEN.
      IF screen-name = 'GS_TA02-ZFLSPZ_ID' AND screen-input EQ 0.
        lv_dis = 'X'.
      ENDIF.

    ENDLOOP.
  ENDIF.

  IF pv_zxybstyp = 'P'.

    SELECT
      i~*
*    FROM @pt_t09 AS i
      FROM zret0009 AS i
      WHERE i~zusage = 'P'
      AND   i~zht_id = @pv_zht_id
      INTO TABLE @DATA(lt_t09).
  ELSE.

    SELECT
      i~*
*    FROM @pt_t09 AS i
      FROM zret0009 AS i
      WHERE i~zusage = ''
      AND   i~zht_id = @pv_zht_id
      INTO TABLE @lt_t09.
  ENDIF.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'ZSPZ_ID'
      value_org       = 'S'
      dynpprog        = sy-repid
      dynpnr          = sy-dynnr
      dynprofield     = lv_dynprofield
      display         = lv_dis
*     callback_program = sy-repid
*     callback_form   = 'USER_FORM'
    TABLES
      value_tab       = lt_t09[]
      return_tab      = lt_ret_tab[]
    EXCEPTIONS
      parameter_error = 1
      no_values_found = 2
      OTHERS          = 3.


  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Module MDL_SET_STYLE_TC_SPZ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_style_tc_spz OUTPUT.
  PERFORM frm_set_style_tc_spz USING gs_t09_sub.
ENDMODULE.

FORM frm_set_style_tc_spz USING ps_t09 TYPE LINE OF tt_t09 .


  DATA: ls_cols TYPE cx_tableview_column.

  LOOP AT tc_spz-cols INTO ls_cols.

    IF ls_cols-screen-group2 = '202'.

      IF ps_t09-zusage = ''.
        ls_cols-invisible = 1.
      ELSE.
        ls_cols-invisible = 0.
      ENDIF.
    ELSEIF ls_cols-screen-group2 = '201'.

      IF ps_t09-zusage = ''.
        ls_cols-invisible = 0.
      ELSE.
        ls_cols-invisible = 1.
      ENDIF.

    ENDIF.

    MODIFY tc_spz-cols FROM ls_cols.
  ENDLOOP.
ENDFORM.
*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_FDHSJ' ITSELF
CONTROLS: tc_fdhsj TYPE TABLEVIEW USING SCREEN 4101.
*&SPWIZARD: LINES OF TABLECONTROL 'TC_FDHSJ'
DATA:   g_tc_fdhsj_lines  LIKE sy-loopc.
*&---------------------------------------------------------------------*
*& Module TC_SPZ_CHANGE_TC_FDHSJ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE tc_spz_change_tc_fdhsj OUTPUT.
  DESCRIBE TABLE gt_fdhsj LINES tc_fdhsj-lines.
ENDMODULE.
*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_FDHSJ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_fdhsj_get_lines OUTPUT.
  g_tc_fdhsj_lines = sy-loopc.

ENDMODULE.
*&SPWIZARD: INPUT MODULE FOR TC 'TC_FDHSJ'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_fdhsj_modify INPUT.
  MODIFY gt_fdhsj
    FROM gs_fdhsj
    INDEX tc_fdhsj-current_line.


ENDMODULE.

MODULE mdl_set_screen_tc_fdhsj OUTPUT.

  IF  gt_fdhsj[] IS INITIAL.
    LOOP AT SCREEN.
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

  PERFORM frm_set_screen_spz.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module MDL_SET_TC_DATA_TC_SPZ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_set_tc_data_tc_fdhsj OUTPUT.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  TC_FDHSJ_MARK  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE tc_fdhsj_mark INPUT.

  DATA: g_tc_fdhsj_wa2 LIKE LINE OF gt_fdhsj.
  IF tc_fdhsj-line_sel_mode = 1 AND gs_fdhsj-sel = 'X'.
    LOOP AT gt_fdhsj INTO g_tc_fdhsj_wa2 WHERE sel = 'X'.
      g_tc_spz_wa2-sel = ''.
      MODIFY gt_fdhsj FROM g_tc_fdhsj_wa2 TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_fdhsj FROM gs_fdhsj  INDEX tc_fdhsj-current_line TRANSPORTING sel.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  TC_FDHSJ_USER_COMMAND  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE tc_fdhsj_user_command INPUT.

  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_FDHSJ'
                              'GT_FDHSJ'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_FDHSJ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T09_SUB
*&      <-- GT_T20_SUB
*&      <-- WHEN
*&      <-- 'B_SPZ_CANCEL'
*&---------------------------------------------------------------------*
FORM frm_fdhsj  USING    ps_t09_sub
                CHANGING pt_t20_sub.

  DATA:ls_zret0020_item TYPE zret0020_item.
  DATA:lv_cursor_line   TYPE i.

  GET CURSOR LINE lv_cursor_line.

*  获取选中的行
  DATA(lv_index) = tc_spz-top_line + lv_cursor_line - 1.

  REFRESH gt_fdhsj.
  READ TABLE gt_t20_sub ASSIGNING FIELD-SYMBOL(<lfs_t20_sub>) INDEX lv_index.
  IF sy-subrc = 0.
    MOVE-CORRESPONDING <lfs_t20_sub>-fdhsj[] TO gt_fdhsj[].

    CALL SCREEN 4101.

    REFRESH <lfs_t20_sub>-fdhsj.

    LOOP AT gt_fdhsj INTO gs_fdhsj .
      ls_zret0020_item-zspz_id   = <lfs_t20_sub>-zspz_id.
      ls_zret0020_item-matnr     = <lfs_t20_sub>-matnr.
      ls_zret0020_item-zmgroup   = <lfs_t20_sub>-zmgroup.
      ls_zret0020_item-zbegdt    = gs_fdhsj-zbegdt.
      ls_zret0020_item-zenddt    = gs_fdhsj-zenddt.
      ls_zret0020_item-zacpr     = gs_fdhsj-zacpr.
      APPEND ls_zret0020_item TO <lfs_t20_sub>-fdhsj.
      CLEAR:ls_zret0020_item.
    ENDLOOP.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Module STATUS_4101 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_4101 OUTPUT.

  DATA:lt_extab_4101 TYPE slis_t_extab.
  APPEND 'SAVE' TO lt_extab_4101.

  SET PF-STATUS 'G4100' EXCLUDING lt_extab_4101 .
  SET TITLEBAR 'T2000' WITH '商品组-分段核算价' ''.

  PERFORM frm_set_screen_spz.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_4101  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_4101 INPUT.

  DATA:lv_check TYPE c.
  CLEAR:lv_check.

  CASE ok_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL'.
      IF gv_flg_rb = '01' OR  gv_flg_rb = '02'.
        LOOP AT gt_fdhsj TRANSPORTING NO FIELDS  WHERE zbegdt IS INITIAL OR zenddt IS INITIAL .
          MESSAGE '请维护完整起始日期和截止日期'  TYPE 'S' DISPLAY LIKE 'E'.
          lv_check = 'X'.
          EXIT.
        ENDLOOP.
      ENDIF.
      CHECK lv_check IS INITIAL.

      CLEAR:ok_code,sy-ucomm.
      LEAVE TO SCREEN 0.
    WHEN OTHERS.
  ENDCASE.
ENDMODULE.