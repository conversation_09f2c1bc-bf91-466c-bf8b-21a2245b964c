*&---------------------------------------------------------------------*
*& 包含               ZRED0041_T03
*&---------------------------------------------------------------------*

"条款新增字段示范字段 zlcbh zpffl zptbm
"协议新增字段示范字段 zlcbh zpffl zptbm

TYPES:
  BEGIN OF ty_excel_i,
*@锚点
*@按照excel 列序增加
*@锚点TK---条款数据
    zht_id   TYPE string,
    zfllx    TYPE string,
    zxybstyp TYPE string,
    ekgrp    TYPE string,
    zdffs_h  TYPE string,
    zpayday  TYPE string,
    zbegin   TYPE string,
    zend     TYPE string,
    zhscj    TYPE string,
    z<PERSON><PERSON>YP<PERSON> string,
    zcgjl    TYPE string,
    zcgzj    TYPE string,
    zfldfsj  TYPE string,
    ztk_txt  TYPE string,
    zlcbh    TYPE string,
    zpffl    TYPE string,
    zsqbm    TYPE string,
    zptbm    TYPE string,
    zzjzr    TYPE string,
*@锚点XY----协议数据
    zctgr    TYPE string,
    zbukrs   TYPE string,
    zflsqf   TYPE string,
    zflzff   TYPE string,
    zpaytp   TYPE string,
    zdffs_i  TYPE string,
    zitemtxt TYPE string,
    zdate    TYPE string,
    matnr_x  TYPE string,
    zflbz    TYPE string,
    zfljs    TYPE string,
    zje      TYPE string,
    zmwskz   TYPE string,
    bukrs_x  TYPE string,
    buklx_x  TYPE string,
    werks_x  TYPE string,
    werpc_x  TYPE string,
    zzsbs    TYPE string,
    zzlbm_x  TYPE string,
    zqdbm_x  TYPE string,
    zjzzd    TYPE string,
    zsqbm_xy TYPE string,
    seq      TYPE i,

  END OF ty_excel_i,
  tt_excel_i TYPE TABLE OF ty_excel_i,

*@锚点XY
*@按照excel 列序增加
  BEGIN OF ty_excel_u,
    ztk_id   TYPE string,
    zctgr    TYPE string,
    zbukrs   TYPE string,
    zflsqf   TYPE string,
    zflzff   TYPE string,
    zpaytp   TYPE string,
    zdffs_i  TYPE string,
    zitemtxt TYPE string,
    zdate    TYPE string,
    matnr_x  TYPE string,
    zflbz    TYPE string,
    zfljs    TYPE string,
    zje      TYPE string,
    zmwskz   TYPE string,
    bukrs_x  TYPE string,
    buklx_x  TYPE string,
    werks_x  TYPE string,
    werpc_x  TYPE string,
    zzsbs    TYPE string,
    zzlbm_x  TYPE string,
    zqdbm_x  TYPE string,
    zjzzd    TYPE string,
    zsqbm_xy TYPE string,
    seq      TYPE i,

  END OF ty_excel_u,
  tt_excel_u TYPE TABLE OF ty_excel_u,

*@锚点TK
*@按照excel 列序增加
  BEGIN OF ty_excel_t,
    ztk_id  TYPE string,
    zfllx   TYPE string,
    ekgrp   TYPE string,
    zdffs_h TYPE string,
    zpayday TYPE string,
    zbegin  TYPE string,
    zend    TYPE string,
    zhscj   TYPE string,
    zaccer  TYPE string,
    zcgjl   TYPE string,
    zcgzj   TYPE string,
    zfldfsj TYPE string,
    ztk_txt TYPE string,
    zlcbh   TYPE string,
    zpffl   TYPE string,
    zsqbm   TYPE string,
    zptbm   TYPE string,
    zzjzr   TYPE string,

    seq     TYPE i,


  END OF ty_excel_t,
  tt_excel_t TYPE TABLE OF ty_excel_t,

*@锚点XY
  BEGIN OF ty_excel_c,
    zxy_id   TYPE string,
    zje      TYPE string,
    zpaytp   TYPE string,
    zflzff   TYPE string,
    zdate    TYPE string,
    matnr_x  TYPE string,
    zmwskz   TYPE string,
    zflbz    TYPE string,
    zfljs    TYPE string,
    zdffs    TYPE string,
    bukrs_x  TYPE string,
    buklx_x  TYPE string,
    werks_x  TYPE string,
    werpc_x  TYPE string,
    zzsbs    TYPE string,
    zctgr    TYPE string,
    zzlbm_x  TYPE string,
    zqdbm_x  TYPE string,
    zjzzd    TYPE string,
    zsqbm_xy TYPE string,

    seq      TYPE i,

  END OF ty_excel_c,
  tt_excel_c TYPE TABLE OF ty_excel_c.


*@锚点XY
TYPES:
  BEGIN OF ty_excel,
    zkey TYPE char300.
    INCLUDE TYPE zreta002.
TYPES:
  zdffs_h     TYPE zreta002-zdffs,
  zdffs_i     TYPE zreta002-zdffs,

  zflsqf      TYPE zretc002-zflsqf,
  zbukrs      TYPE zretc002-zbukrs,
  zctgr       TYPE zretc002-zctgr,
  zitemtxt    TYPE zretc002-zitemtxt,
  matnr       TYPE mara-matnr,
  zje         TYPE zret0008-zje,
  zmwskz      TYPE zret0008-zmwskz,
  zxy_id      TYPE zret0006-zxy_id,
  zzsbs       TYPE zret0006-zzsbs,

  bukrs_x     TYPE string,
  buklx_x     TYPE string,
  werks_x     TYPE string,
  werpc_x     TYPE string,
  matnr_x     TYPE string,
  zzlbm_x     TYPE string,
  zqdbm_x     TYPE string,
  zjzzd       TYPE string,
  zsqbm_xy    TYPE string,

  seq         TYPE i,
  seq_seg     TYPE i,   "分组后重新编的序号
  ztype_excel TYPE char1,
  zmsg_excel  TYPE char255,
  ztype_impt  TYPE char1,
  zmsg_impt   TYPE char255,
  sel         TYPE char1,
  sel_man     TYPE char1,
  status      TYPE char1,

  zhtlx       TYPE zreta001-zhtlx,
  zbukrs_ht   TYPE zreta001-zbukrs,
  zpaytp      TYPE zretc002-zpaytp,
  zdate       TYPE zret0008-zdate,
  zflbz       TYPE zret0008-zflbz,
  zfljs       TYPE zret0008-zfljs,
  seg         TYPE char8,

*****************************************
  zje_n       TYPE zret0008-zje,
  zdate_n     TYPE zret0008-zdate,
  matnr_x_n   TYPE string,
  bukrs_x_n   TYPE string,
  buklx_x_n   TYPE string,
  werks_x_n   TYPE string,
  werpc_x_n   TYPE string,
  zzlbm_x_n   TYPE string,
  zqdbm_x_n   TYPE string,
  zmwskz_n    TYPE zret0008-zmwskz,
  zflbz_n     TYPE zret0008-zflbz,
  zfljs_n     TYPE zret0008-zfljs,
  zdffs_n     TYPE zreta002-zdffs,
  zpaytp_n    TYPE zret0044-zpaytp,
  zflzff_n    TYPE zret0044-zflzff,
  zzsbs_n     TYPE zret0006-zzsbs,
  zctgr_n     TYPE zret0006-zctgr,
  zjzzd_n     TYPE zret0008-zjzzd,
  zsqbm_xy_n  TYPE zret0008-zsqbm_xy,
*****************************************
  zje_o       TYPE zret0008-zje,
  zdate_o     TYPE zret0008-zdate,
  matnr_x_o   TYPE string,
  bukrs_x_o   TYPE string,
  buklx_x_o   TYPE string,
  werks_x_o   TYPE string,
  werpc_x_o   TYPE string,
  zzlbm_x_o   TYPE string,
  zqdbm_x_o   TYPE string,
  zmwskz_o    TYPE zret0008-zmwskz,
  zflbz_o     TYPE zret0008-zflbz,
  zfljs_o     TYPE zret0008-zfljs,
  zdffs_o     TYPE zreta002-zdffs,
  zpaytp_o    TYPE zret0044-zpaytp,
  zflzff_o    TYPE zret0044-zflzff,
  zzsbs_o     TYPE zret0006-zzsbs,
  zctgr_o     TYPE zret0006-zctgr,
  zjzzd_o     TYPE zret0008-zjzzd,
  zsqbm_xy_o  TYPE zret0008-zsqbm_xy,

  END OF ty_excel,
  tt_excel TYPE TABLE OF ty_excel.


DATA:
  gt_excel   TYPE tt_excel,
  gt_excel_i TYPE tt_excel_i,
  gt_excel_u TYPE tt_excel_u,
  gt_excel_t TYPE tt_excel_t,
  gt_excel_c TYPE tt_excel_c.

DATA:
  gv_flg_impt TYPE char2, "01-固定条款批量导入-创建/02-固定条款批量导入-新增协议/03-固定协议批量调整/04-条款批量修改
  gv_pass     TYPE char1.  "无须审批标识

DATA:
  gt_bukrs_excel TYPE tt_bukrs,
  gt_werks_excel TYPE tt_werks,
  gt_matnr_excel TYPE tt_matnr,
  gt_zzlbm_excel TYPE tt_t81,
  gt_zqdbm_excel TYPE tt_t82.