*&---------------------------------------------------------------------*
*& 包含               ZRED0035_FPUB
*&---------------------------------------------------------------------*

*----------------------------------------------------------------------*
*   INCLUDE TABLECONTROL_FORMS                                         *
*----------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*&      FORM  USER_OK_TC                                               *
*&---------------------------------------------------------------------*
 FORM user_ok_tc USING    p_tc_name TYPE dynfnam
                          p_table_name
                          p_mark_name
                 CHANGING p_ok      LIKE sy-ucomm.

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA: l_ok     TYPE sy-ucomm,
         l_offset TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

*&SPWIZARD: TABLE CONTROL SPECIFIC OPERATIONS                          *
*&SPWIZARD: EVALUATE TC NAME AND OPERATIONS                            *
   SEARCH p_ok FOR p_tc_name.
   IF sy-subrc <> 0.
     EXIT.
   ENDIF.
   l_offset = strlen( p_tc_name ) + 1.
   l_ok = p_ok+l_offset.
*&SPWIZARD: EXECUTE GENERAL AND TC SPECIFIC OPERATIONS                 *
   CASE l_ok.
     WHEN 'INSR'.                      "INSERT ROW
       PERFORM fcode_insert_row USING    p_tc_name
                                         p_table_name.
*       CLEAR p_ok.

     WHEN 'DELE'.                      "DELETE ROW
       PERFORM fcode_delete_row USING    p_tc_name
                                         p_table_name
                                         p_mark_name.
*       CLEAR P_OK.

     WHEN 'P--' OR                     "TOP OF LIST
          'P-'  OR                     "PREVIOUS PAGE
          'P+'  OR                     "NEXT PAGE
          'P++'.                       "BOTTOM OF LIST
       PERFORM compute_scrolling_in_tc USING p_tc_name
                                             l_ok.
       CLEAR p_ok.
*     WHEN 'L--'.                       "TOTAL LEFT
*       PERFORM FCODE_TOTAL_LEFT USING P_TC_NAME.
*
*     WHEN 'L-'.                        "COLUMN LEFT
*       PERFORM FCODE_COLUMN_LEFT USING P_TC_NAME.
*
*     WHEN 'R+'.                        "COLUMN RIGHT
*       PERFORM FCODE_COLUMN_RIGHT USING P_TC_NAME.
*
*     WHEN 'R++'.                       "TOTAL RIGHT
*       PERFORM FCODE_TOTAL_RIGHT USING P_TC_NAME.
*
     WHEN 'MARK'.                      "MARK ALL FILLED LINES
       PERFORM fcode_tc_mark_lines USING p_tc_name
                                         p_table_name
                                         p_mark_name   .
       CLEAR p_ok.

     WHEN 'DMRK'.                      "DEMARK ALL FILLED LINES
       PERFORM fcode_tc_demark_lines USING p_tc_name
                                           p_table_name
                                           p_mark_name .
       CLEAR p_ok.

     WHEN 'DEL'.                      "DEMARK ALL FILLED LINES
       PERFORM fcode_delete_row USING    p_tc_name
                                         p_table_name
                                         p_mark_name.
       CLEAR p_ok.

     WHEN 'COPY'.                      "DELETE ROW
       PERFORM fcode_tc_copy_lines USING    p_tc_name
                                         p_table_name
                                         p_mark_name.
*     WHEN 'SASCEND'   OR
*          'SDESCEND'.                  "SORT COLUMN
*       PERFORM FCODE_SORT_TC USING P_TC_NAME
*                                   L_OK.
     WHEN 'ZDW'.                      "DELETE ROW
       PERFORM fcode_tc_zdw_lines USING  p_tc_name
                                         p_table_name.

       CLEAR p_ok.
   ENDCASE.

 ENDFORM.                              " USER_OK_TC

*&---------------------------------------------------------------------*
*&      FORM  FCODE_INSERT_ROW                                         *
*&---------------------------------------------------------------------*
 FORM fcode_insert_row
               USING    p_tc_name           TYPE dynfnam
                        p_table_name             .

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_lines_name       LIKE feld-name.
   DATA l_selline          LIKE sy-stepl.
   DATA l_lastline         TYPE i.
   DATA l_line             TYPE i.
   DATA l_table_name       LIKE feld-name.
   FIELD-SYMBOLS <tc>                 TYPE cxtab_control.
   FIELD-SYMBOLS <table>              TYPE STANDARD TABLE.
   FIELD-SYMBOLS <lines>              TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

*  ###
   DATA:
*     LV_ZITEMS_KEY_MAX TYPE ZRETC002-ZITEMS,
     lv_zitems_key_tmp TYPE zretc002-zitems.

*  #1
   DATA ls_line TYPE REF TO data.
   FIELD-SYMBOLS <fs_line>          TYPE any.
   FIELD-SYMBOLS <field>              TYPE any.
*   FIELD-SYMBOLS <FIELD_MAX>              TYPE ANY.


*   DATA: BEGIN OF LS_DATA_ZITEMS,
*           ZITEMS_KEY TYPE ZRETC002-ZITEMS,
*         END OF LS_DATA_ZITEMS,
*         LT_DATA_ZITEMS LIKE TABLE OF LS_DATA_ZITEMS.


   DATA: BEGIN OF ls_data_zitems,
           zitems TYPE zretc002-zitems,
         END OF ls_data_zitems,
         lt_data_zitems LIKE TABLE OF ls_data_zitems.



   DATA: lv_stru_name TYPE char30,
         lv_zfllx     TYPE zreta002-zfllx.
   FIELD-SYMBOLS:
                  <fs_stru> TYPE any.



   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE


*  #1
   CREATE DATA ls_line LIKE LINE OF <table>.
   ASSIGN ls_line->* TO <fs_line>.

*&SPWIZARD: GET LOOPLINES OF TABLECONTROL                              *
   CONCATENATE 'G_' p_tc_name '_LINES' INTO l_lines_name.
   ASSIGN (l_lines_name) TO <lines>.

*&SPWIZARD: GET CURRENT LINE                                           *
   GET CURSOR LINE l_selline.
   IF sy-subrc <> 0.                   " APPEND LINE TO TABLE
     l_selline = <tc>-lines + 1.
*&SPWIZARD: SET TOP LINE                                               *
     IF l_selline > <lines>.
       <tc>-top_line = l_selline - <lines> + 1 .
     ELSE.
       <tc>-top_line = 1.
     ENDIF.
   ELSE.                               " INSERT LINE INTO TABLE
     l_selline = <tc>-top_line + l_selline - 1.
     l_lastline = <tc>-top_line + <lines> - 1.
   ENDIF.
*&SPWIZARD: SET NEW CURSOR LINE                                        *
   l_line = l_selline - <tc>-top_line + 1.

*&SPWIZARD: INSERT INITIAL LINE

*   新增替换为APPEND
*   INSERT INITIAL LINE INTO <TABLE> INDEX L_SELLINE.
   IF p_tc_name = 'TC_ITEM'.

****     LOOP AT <TABLE> ASSIGNING FIELD-SYMBOL(<FS_WA>).  %%%%%
****       CLEAR LS_DATA_ZITEMS.
****       UNASSIGN <FIELD>.
****       ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <FS_WA> TO <FIELD>.
****       IF <FIELD> IS ASSIGNED .
****         LS_DATA_ZITEMS-ZITEMS_KEY = <FIELD>.
****         APPEND LS_DATA_ZITEMS TO LT_DATA_ZITEMS.
****       ENDIF.
****     ENDLOOP.
****     IF LT_DATA_ZITEMS[] IS NOT INITIAL.
****       SELECT
****         MAX( I~ZITEMS_KEY ) AS ZITEMS
****         FROM @LT_DATA_ZITEMS AS I
****         INTO @DATA(LV_MAX_ZITEMS_KEY).
****     ENDIF.
****     LV_MAX_ZITEMS_KEY = LV_MAX_ZITEMS_KEY + 1.

     PERFORM frm_get_zitems_key_symbol USING 'GV_ZITEM_KEY'
                                       CHANGING lv_zitems_key_tmp.


************     UNASSIGN <FIELD_MAX>.
************     ASSIGN ('GV_ZITEM_KEY') TO <FIELD_MAX>. "ASSIGN ('(ZRED0041)GV_ZITEM_KEY') TO <FIELD>.
************     IF <FIELD_MAX> IS ASSIGNED.
************       LV_ZITEMS_KEY_MAX = <FIELD_MAX>.
************     ENDIF.
************
************     PERFORM FRM_GET_ZITEMS_KEY CHANGING LV_ZITEMS_KEY_TMP LV_ZITEMS_KEY_MAX.
************
*************     回写全局唯一 ZITEMS_KEY
************     IF <FIELD_MAX> IS ASSIGNED.
************       <FIELD_MAX> = LV_ZITEMS_KEY_MAX.
************       UNASSIGN <FIELD_MAX>.
************     ENDIF.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = lv_zitems_key_tmp.
     ENDIF.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZCTGR' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = 'C'.
     ENDIF.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'SEL_MAN' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = 'X'.
     ENDIF.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZITEMS_INSR' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = 'X'.
     ENDIF.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZFKLX' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = '3'.
     ENDIF.
*----------------------------------------------------------------------*
*BEGIN INSERT BY  XYLIU1  11.11.2020 16:08:04
*<NOTES>  RB04 默认兑付方式 M
     lv_stru_name = 'GT_TA02'.
     CLEAR lv_zfllx.
     UNASSIGN <fs_stru>.
     ASSIGN (lv_stru_name) TO <fs_stru>.
     IF <fs_stru> IS ASSIGNED.
       UNASSIGN <field>.
       ASSIGN COMPONENT 'ZFLLX' OF STRUCTURE <fs_line> TO <field>.
       IF <field> IS ASSIGNED.
         lv_zfllx = <field>.
       ENDIF.
     ENDIF.
     IF lv_zfllx = 'RB04'.
       UNASSIGN <field>.
       ASSIGN COMPONENT 'ZDFFS' OF STRUCTURE <fs_line> TO <field>.
       IF <field> IS ASSIGNED.
         <field> = 'M'.
       ENDIF.
     ENDIF.
*END INSERT BY XYLIU1
*----------------------------------------------------------------------*

     APPEND  <fs_line> TO <table> .

   ELSEIF p_tc_name = 'TC_ZCXJT'.

*     ZITEMS_KEY 自增
     PERFORM frm_get_zitems_key_symbol USING 'GV_ZITEM_KEY_CX'
                                       CHANGING lv_zitems_key_tmp.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = lv_zitems_key_tmp.
     ENDIF.

*     行号自增
     LOOP AT <table> ASSIGNING FIELD-SYMBOL(<fs_wa>).
       CLEAR ls_data_zitems.
       UNASSIGN <field>.
       ASSIGN COMPONENT 'ZITEMS' OF STRUCTURE <fs_wa> TO <field>.
       IF <field> IS ASSIGNED .
         ls_data_zitems-zitems = <field>.
         APPEND ls_data_zitems TO lt_data_zitems.
       ENDIF.
     ENDLOOP.

     IF lt_data_zitems[] IS NOT INITIAL.
       SELECT
         MAX( i~zitems ) AS zitems
         FROM @lt_data_zitems AS i
         INTO @DATA(lv_max_zitems).
     ENDIF.
     lv_max_zitems = lv_max_zitems + 1.

     UNASSIGN <field>.
     ASSIGN COMPONENT 'ZITEMS' OF STRUCTURE <fs_line> TO <field>.
     IF <field> IS ASSIGNED.
       <field> = lv_max_zitems.
     ENDIF.


     APPEND  <fs_line> TO <table> .
   ELSEIF p_tc_name = 'TC_FDHSJ'.
     APPEND INITIAL LINE  TO <table>.
   ELSE.
     INSERT INITIAL LINE INTO <table> INDEX l_selline.
   ENDIF.

   <tc>-lines = <tc>-lines + 1.
*&SPWIZARD: SET CURSOR                                                 *
   SET CURSOR LINE l_line.

 ENDFORM.                              " FCODE_INSERT_ROW

*&---------------------------------------------------------------------*
*&      FORM  FCODE_DELETE_ROW                                         *
*&---------------------------------------------------------------------*
 FORM fcode_delete_row
               USING    p_tc_name           TYPE dynfnam
                        p_table_name
                        p_mark_name   .

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_table_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>.
   FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE

*&SPWIZARD: DELETE MARKED LINES                                        *
   DESCRIBE TABLE <table> LINES <tc>-lines.

   LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
     ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

     IF <mark_field> = 'X'.
       DELETE <table> INDEX syst-tabix.
       IF sy-subrc = 0.
         <tc>-lines = <tc>-lines - 1.
       ENDIF.
     ENDIF.
   ENDLOOP.

 ENDFORM.                              " FCODE_DELETE_ROW

 FORM fcode_tc_copy_lines USING p_tc_name
                                p_table_name
                                p_mark_name.
*&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
   DATA l_table_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>.
   FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

*  #1
   DATA ls_line TYPE REF TO data.
   DATA lt_tab_copy TYPE REF TO data.
   FIELD-SYMBOLS <fs_tab>          TYPE STANDARD TABLE.
   FIELD-SYMBOLS <fs_line>          TYPE any.
   FIELD-SYMBOLS <field>              TYPE any.
   FIELD-SYMBOLS <field_max>              TYPE any.
   DATA: lv_zitems_copy TYPE zretc002-zitems.


   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE

   CREATE DATA ls_line LIKE LINE OF <table>.
   CREATE DATA lt_tab_copy LIKE  <table>.
   ASSIGN ls_line->* TO <fs_line>.
   ASSIGN lt_tab_copy->* TO <fs_tab>.


*&SPWIZARD: MARK ALL FILLED LINES                                      *


**   DATA: BEGIN OF LS_DATA_ZITEMS,
**           ZITEMS_KEY TYPE ZRETC002-ZITEMS,
**         END OF LS_DATA_ZITEMS,
**         LT_DATA_ZITEMS LIKE TABLE OF LS_DATA_ZITEMS.

   IF p_tc_name = 'TC_ITEM'.
***     LOOP AT <TABLE> ASSIGNING FIELD-SYMBOL(<FS_WA>).  %%%%%
***       CLEAR LS_DATA_ZITEMS.
***       UNASSIGN <FIELD>.
***       ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <FS_WA> TO <FIELD>.
***       IF <FIELD> IS ASSIGNED .
***         LS_DATA_ZITEMS-ZITEMS_KEY = <FIELD>.
***         APPEND LS_DATA_ZITEMS TO LT_DATA_ZITEMS.
***       ENDIF.
***     ENDLOOP.
***     IF LT_DATA_ZITEMS[] IS NOT INITIAL.
***       SELECT
***         MAX( I~ZITEMS_KEY ) AS ZITEMS
***         FROM @LT_DATA_ZITEMS AS I
***         INTO @DATA(LV_MAX_ZITEMS_KEY).
***     ENDIF.

   ENDIF.


   LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
     ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.
     IF <mark_field> IS ASSIGNED AND <mark_field> = 'X'.

       <mark_field> = ''.
       IF <fs_line> IS ASSIGNED.
         CLEAR <fs_line>.

*         复制行数据
         <fs_line> = <wa>.


         IF p_tc_name = 'TC_ITEM'.

*         记录复制的源行KEY  ZITEMS_KEY 到 ZITEMS_COPY
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <wa> TO <field>.
           IF <field> IS ASSIGNED.
             CLEAR lv_zitems_copy.
             lv_zitems_copy = <field>.
           ENDIF.

           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZITEMS_COPY' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
             <field> = lv_zitems_copy.
           ENDIF.


*         复制的行的 KEY 自增
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZITEMS_KEY' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
*             LV_MAX_ZITEMS_KEY = LV_MAX_ZITEMS_KEY + 1. %%%%%

             DATA:
               lv_zitems_key_max TYPE zretc002-zitems,
               lv_zitems_key_tmp TYPE zretc002-zitems.

             UNASSIGN <field_max>.
             ASSIGN ('GV_ZITEM_KEY') TO <field_max>. "ASSIGN ('(ZRED0041)GV_ZITEM_KEY') TO <FIELD>.
             IF <field_max> IS ASSIGNED.
               lv_zitems_key_max = <field_max>.
             ENDIF.

             PERFORM frm_get_zitems_key CHANGING lv_zitems_key_tmp lv_zitems_key_max.
             <field> = lv_zitems_key_tmp.
*             回写全局唯一 ZITEMS_KEY
             IF <field_max> IS ASSIGNED.
               <field_max> = lv_zitems_key_max.
               UNASSIGN <field_max>.
             ENDIF.
           ENDIF.


*         清空复制的行的行号
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZITEMS' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
             <field> = ''.
           ENDIF.
*         清空复制的行的阶梯
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZJT_ID' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
             <field> = ''.
           ENDIF.
*         清空复制的行的协议号码
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZXY_ID' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
             <field> = ''.
           ENDIF.

*         清空复制的行的 已保存标识（新行标识）
           UNASSIGN <field>.
           ASSIGN COMPONENT 'ZFLG_EXIST' OF STRUCTURE <fs_line> TO <field>.
           IF <field> IS ASSIGNED.
             <field> = ''.
           ENDIF.

         ENDIF.

         APPEND <fs_line> TO <fs_tab>.

       ENDIF.
     ENDIF.
   ENDLOOP.

   UNASSIGN <fs_line>.
   DESCRIBE TABLE <fs_tab> LINES DATA(lv_lines).
   IF lv_lines > 1.
     MESSAGE s888(sabapdocu) WITH '只能选择一行复制' DISPLAY LIKE 'E'.
   ELSE.
     LOOP AT <fs_tab> ASSIGNING <fs_line>.
       IF <fs_line> IS ASSIGNED.
         APPEND <fs_line> TO <table>.
       ENDIF.
     ENDLOOP.

   ENDIF.


 ENDFORM.                                          "FCODE_TC_MARK_LINES

*&---------------------------------------------------------------------*
*&      FORM  COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_TC_NAME  NAME OF TABLECONTROL
*      -->P_OK       OK CODE
*----------------------------------------------------------------------*
 FORM compute_scrolling_in_tc USING    p_tc_name
                                       p_ok.
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_tc_new_top_line     TYPE i.
   DATA l_tc_name             LIKE feld-name.
   DATA l_tc_lines_name       LIKE feld-name.
   DATA l_tc_field_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <lines>      TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

   ASSIGN (p_tc_name) TO <tc>.
*&SPWIZARD: GET LOOPLINES OF TABLECONTROL                              *
   CONCATENATE 'G_' p_tc_name '_LINES' INTO l_tc_lines_name.
   ASSIGN (l_tc_lines_name) TO <lines>.


*&SPWIZARD: IS NO LINE FILLED?                                         *
   IF <tc>-lines = 0.
*&SPWIZARD: YES, ...                                                   *
     l_tc_new_top_line = 1.
   ELSE.
*&SPWIZARD: NO, ...                                                    *
     CALL FUNCTION 'SCROLLING_IN_TABLE'
       EXPORTING
         entry_act      = <tc>-top_line
         entry_from     = 1
         entry_to       = <tc>-lines
         last_page_full = 'X'
         loops          = <lines>
         ok_code        = p_ok
         overlapping    = 'X'
       IMPORTING
         entry_new      = l_tc_new_top_line
       EXCEPTIONS
*        NO_ENTRY_OR_PAGE_ACT  = 01
*        NO_ENTRY_TO    = 02
*        NO_OK_CODE_OR_PAGE_GO = 03
         OTHERS         = 0.
   ENDIF.

*&SPWIZARD: GET ACTUAL TC AND COLUMN                                   *
   GET CURSOR FIELD l_tc_field_name
              AREA  l_tc_name.

   IF syst-subrc = 0.
     IF l_tc_name = p_tc_name.
*&SPWIZARD: ET ACTUAL COLUMN                                           *
       SET CURSOR FIELD l_tc_field_name LINE 1.
     ENDIF.
   ENDIF.

*&SPWIZARD: SET THE NEW TOP LINE                                       *
   <tc>-top_line = l_tc_new_top_line.


 ENDFORM.                              " COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*&      FORM  COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_TC_NAME  NAME OF TABLECONTROL
*      -->P_OK       OK CODE
*----------------------------------------------------------------------*
 FORM fcode_tc_zdw_lines USING    p_tc_name
                                  p_table_name.
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_tc_new_top_line     TYPE i.
   DATA l_tc_name             LIKE feld-name.
   DATA l_tc_lines_name       LIKE feld-name.
   DATA l_tc_field_name       LIKE feld-name.
   DATA l_table_name          LIKE feld-name.
   DATA:it_value   TYPE TABLE OF sval,
        ls_value   TYPE sval,
        lv_rtn_cd  TYPE c,
        lv_zspz_id TYPE zret0009-zspz_id,
        lv_where   TYPE char100.


   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <lines>      TYPE i.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>..
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

   ASSIGN (p_tc_name) TO <tc>.
*&SPWIZARD: GET LOOPLINES OF TABLECONTROL                              *
   CONCATENATE 'G_' p_tc_name '_LINES' INTO l_tc_lines_name.
   ASSIGN (l_tc_lines_name) TO <lines>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE

   ls_value-tabname   = 'ZRES0129'.
   ls_value-fieldname = 'ZSPZ_ID'.
   ls_value-field_obl = 'X'..
   ls_value-value     = ''..
   APPEND ls_value TO it_value.
   CALL FUNCTION 'POPUP_GET_VALUES'
     EXPORTING
       popup_title = '选择商品组'
     IMPORTING
       returncode  = lv_rtn_cd
     TABLES
       fields      = it_value.

   IF lv_rtn_cd = 'A'.
     RETURN.
   ENDIF.

   READ TABLE it_value INTO ls_value INDEX 1.
   lv_zspz_id = ls_value-value.


   IF lv_zspz_id IS NOT INITIAL .
     lv_where = | ZSPZ_ID = lv_zspz_id |.
     LOOP AT <table> ASSIGNING <wa> WHERE (lv_where) .
       l_tc_new_top_line = sy-tabix.
       EXIT.
     ENDLOOP.
     IF sy-subrc = 0.
       <tc>-top_line = l_tc_new_top_line.
     ELSE.
       MESSAGE '未查询到商品组!' TYPE 'S' DISPLAY LIKE 'E'.
     ENDIF.
   ENDIF.



 ENDFORM.                              " COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*&      FORM  FCODE_TC_MARK_LINES
*&---------------------------------------------------------------------*
*       MARKS ALL TABLECONTROL LINES
*----------------------------------------------------------------------*
*      -->P_TC_NAME  NAME OF TABLECONTROL
*----------------------------------------------------------------------*
 FORM fcode_tc_mark_lines USING p_tc_name
                                p_table_name
                                p_mark_name.
*&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
   DATA l_table_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>.
   FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*


   DATA: lv_stru_name TYPE char30,
         lv_cprog     TYPE syst-cprog.
   FIELD-SYMBOLS:
                  <fs_stru> TYPE any.
   FIELD-SYMBOLS <field>              TYPE any.


   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE

   IF p_tc_name = 'TC_ITEM'.

     lv_stru_name = 'GS_DATA_BASE'.
     CLEAR lv_cprog.

     UNASSIGN <fs_stru>.
     ASSIGN (lv_stru_name) TO <fs_stru>.
     IF <fs_stru> IS ASSIGNED.
       UNASSIGN <field>.
       ASSIGN COMPONENT 'ZFLG_CPROG' OF STRUCTURE <fs_stru> TO <field>.
       IF <field> IS ASSIGNED.
         lv_cprog = <field>.
       ENDIF.
     ENDIF.
     IF lv_cprog = 'ZRED0041'.
*&SPWIZARD: MARK ALL FILLED LINES                                      *
       LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
         ASSIGN COMPONENT 'SEL_MAN' OF STRUCTURE <wa> TO <mark_field>.

         <mark_field> = 'X'.
       ENDLOOP.

     ELSE.
*&SPWIZARD: MARK ALL FILLED LINES                                      *
       LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
         ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

         <mark_field> = 'X'.
       ENDLOOP.

     ENDIF.

   ELSE.
*&SPWIZARD: MARK ALL FILLED LINES                                      *
     LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
       ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

       <mark_field> = 'X'.
     ENDLOOP.

   ENDIF.
 ENDFORM.                                          "FCODE_TC_MARK_LINES

*&---------------------------------------------------------------------*
*&      FORM  FCODE_TC_DEMARK_LINES
*&---------------------------------------------------------------------*
*       DEMARKS ALL TABLECONTROL LINES
*----------------------------------------------------------------------*
*      -->P_TC_NAME  NAME OF TABLECONTROL
*----------------------------------------------------------------------*
 FORM fcode_tc_demark_lines USING p_tc_name
                                  p_table_name
                                  p_mark_name .
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_table_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>.
   FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*


   DATA: lv_stru_name TYPE char30,
         lv_cprog     TYPE syst-cprog.
   FIELD-SYMBOLS:
                  <fs_stru> TYPE any.
   FIELD-SYMBOLS <field>              TYPE any.


   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE


   IF p_tc_name = 'TC_ITEM'.

     lv_stru_name = 'GS_DATA_BASE'.
     CLEAR lv_cprog.

     UNASSIGN <fs_stru>.
     ASSIGN (lv_stru_name) TO <fs_stru>.
     IF <fs_stru> IS ASSIGNED.
       UNASSIGN <field>.
       ASSIGN COMPONENT 'ZFLG_CPROG' OF STRUCTURE <fs_stru> TO <field>.
       IF <field> IS ASSIGNED.
         lv_cprog = <field>.
       ENDIF.
     ENDIF.

     IF lv_cprog = 'ZRED0041'.
*&SPWIZARD: MARK ALL FILLED LINES                                      *
       LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
         ASSIGN COMPONENT 'SEL_MAN' OF STRUCTURE <wa> TO <mark_field>.

         <mark_field> = space.
       ENDLOOP.

     ELSE.
*&SPWIZARD: DEMARK ALL FILLED LINES                                    *
       LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
         ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

         <mark_field> = space.
       ENDLOOP.

     ENDIF.



   ELSE.
*&SPWIZARD: DEMARK ALL FILLED LINES                                    *
     LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
       ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

       <mark_field> = space.
     ENDLOOP.


   ENDIF.


 ENDFORM.                                          "FCODE_TC_MARK_LINES

 FORM fcode_delete_row_flg
              USING    p_tc_name           TYPE dynfnam
                       p_table_name
                       p_mark_name   .

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
   DATA l_table_name       LIKE feld-name.

   FIELD-SYMBOLS <tc>         TYPE cxtab_control.
   FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
   FIELD-SYMBOLS <wa>.
   FIELD-SYMBOLS <mark_field>.
   FIELD-SYMBOLS <fs_loekz>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

   ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: GET THE TABLE, WHICH BELONGS TO THE TC                     *
   CONCATENATE p_table_name '[]' INTO l_table_name. "TABLE BODY
   ASSIGN (l_table_name) TO <table>.                "NOT HEADERLINE

*&SPWIZARD: DELETE MARKED LINES                                        *
   DESCRIBE TABLE <table> LINES <tc>-lines.

   LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: ACCESS TO THE COMPONENT 'FLAG' OF THE TABLE HEADER         *
     ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

     IF <mark_field> = 'X'.
       ASSIGN COMPONENT 'ZSTATUS' OF STRUCTURE <wa> TO <fs_loekz>.
       IF sy-subrc EQ 0.
         <fs_loekz> = 'D'.
       ENDIF.
     ENDIF.
   ENDLOOP.

 ENDFORM.                              " FCODE_DELETE_ROW

 FORM frm_set_seq TABLES pt_tab TYPE STANDARD TABLE
              USING pv_seq_field TYPE name_komp.
   FIELD-SYMBOLS: <fs>.

   LOOP AT pt_tab.
     ASSIGN COMPONENT pv_seq_field OF STRUCTURE pt_tab TO <fs>.
     IF sy-subrc EQ  0.
       <fs> = sy-tabix.
       MODIFY pt_tab.
     ENDIF.
   ENDLOOP.
 ENDFORM.                    "SET_SEQ

 FORM frm_check_inital  USING
                                 pv_value
                                 pv_txt

                        CHANGING pt_msglist TYPE scp1_general_errors.
   DATA:
         ls_msglist TYPE LINE OF scp1_general_errors.

   IF pv_value IS INITIAL.
     ls_msglist-msgty =  'E'.
     ls_msglist-msgv1 = pv_txt && '不能为空!'.
     APPEND ls_msglist TO pt_msglist.
   ENDIF.
 ENDFORM.

 FORM frm_check_inital_02  USING
                                 pv_value
                                 pv_txt
                                 pv_txt_02
                        CHANGING pt_msglist TYPE scp1_general_errors.
   DATA:
         ls_msglist TYPE LINE OF scp1_general_errors.

   IF pv_value IS INITIAL.
     ls_msglist-msgty =  'E'.
     ls_msglist-msgv1 = pv_txt && '不能为空!' && pv_txt_02.
     APPEND ls_msglist TO pt_msglist.
   ENDIF.
 ENDFORM.

 FORM frm_add_msg  USING    ps_msgv1 TYPE scp1_general_error-msgv1
                   CHANGING pt_msglist TYPE scp1_general_errors.

   DATA:

     ls_msglist TYPE scp1_general_error.

   ls_msglist-msgty = 'E'.
   ls_msglist-msgid = '00'.
   ls_msglist-msgno = '001'.
   ls_msglist-msgv1 = ps_msgv1.

   APPEND ls_msglist TO pt_msglist.


 ENDFORM.

 FORM frm_add_msg_w  USING    ps_msgv1 TYPE scp1_general_error-msgv1
                   CHANGING pt_msglist TYPE scp1_general_errors.

   DATA:

     ls_msglist TYPE scp1_general_error.

   ls_msglist-msgty = 'W'.
   ls_msglist-msgid = '00'.
   ls_msglist-msgno = '001'.
   ls_msglist-msgv1 = ps_msgv1.

   APPEND ls_msglist TO pt_msglist.

 ENDFORM.


 FORM frm_get_num      USING u_object u_range_no CHANGING c_next_num.
   DATA:
     lv_times     TYPE i VALUE 1,
     lv_times_max TYPE i VALUE 5.

   CLEAR c_next_num.

   WHILE c_next_num = '' AND lv_times <= lv_times_max.
     lv_times = lv_times + 1.

     CALL FUNCTION 'NUMBER_RANGE_ENQUEUE'
       EXPORTING
         object           = u_object
       EXCEPTIONS
         foreign_lock     = 1
         object_not_found = 2
         system_failure   = 3
         OTHERS           = 4.
     IF sy-subrc NE 0.
       WAIT UP TO 1 SECONDS .
     ELSE.
       CALL FUNCTION 'NUMBER_GET_NEXT'
         EXPORTING
           nr_range_nr             = u_range_no
           object                  = u_object
*          QUANTITY                = '1'
*          SUBOBJECT               = ' '
*          TOYEAR                  = '0000'
           ignore_buffer           = 'X'
         IMPORTING
           number                  = c_next_num "流水号
*          QUANTITY                =
*          RETURNCODE              =
         EXCEPTIONS
           interval_not_found      = 1
           number_range_not_intern = 2
           object_not_found        = 3
           quantity_is_0           = 4
           quantity_is_not_1       = 5
           interval_overflow       = 6
           buffer_overflow         = 7
           OTHERS                  = 8.
       IF sy-subrc <> 0.
         WAIT UP TO 1 SECONDS .
*        MESSAGE ID SY-MSGID TYPE SY-MSGTY NUMBER SY-MSGNO
*          WITH SY-MSGV1 SY-MSGV2 SY-MSGV3 SY-MSGV4.
       ELSE.
         CALL FUNCTION 'NUMBER_RANGE_DEQUEUE'
           EXPORTING
             object           = u_object
           EXCEPTIONS
             object_not_found = 1
             OTHERS           = 2.
         EXIT.
       ENDIF.
     ENDIF.
   ENDWHILE.

   IF c_next_num IS INITIAL.
     MESSAGE e888(sabapdocu) WITH '获取流水号失败！' DISPLAY LIKE 'E'.
   ENDIF.

 ENDFORM.                    "GET_NUM


 FORM frm_are_you_sure  USING      pv_message
                                   pv_title
                        CHANGING   pv_answer.
   CLEAR pv_answer.
   CALL FUNCTION 'POPUP_TO_DECIDE_INFO'
     EXPORTING
       defaultoption = 'N'
       textline1     = pv_message
       titel         = pv_title
       start_column  = 25
       start_row     = 6
     IMPORTING
       answer        = pv_answer
     EXCEPTIONS
       OTHERS        = 1.
*  PV_ANSWER 'J' 表示YES 'A' 表示NO
 ENDFORM.

 FORM frm_set_title  USING    pv_title.

   CALL FUNCTION 'RECA_GUI_SET_TITLEBAR'
     EXPORTING
       id_text1 = pv_title.

 ENDFORM.

 FORM frm_pro_data_lock  USING    pv_data
                                     pv_flg TYPE char1.
   DATA:
     lv_msgv1 TYPE sy-msgv1,
     lv_data  TYPE zret0036-zf01.

   lv_data = pv_data .

   IF pv_flg = ''.

     CALL FUNCTION 'ENQUEUE_EZ_ZRET0036'
       EXPORTING
*        MODE_ZRET0036  = 'X'
*        MANDT          = SY-MANDT
         zf01           = lv_data
*        X_ZF01         = ' '
*        _SCOPE         = '2'
*        _WAIT          = ' '
*        _COLLECT       = ' '
       EXCEPTIONS
         foreign_lock   = 1
         system_failure = 2
         OTHERS         = 3.
     IF sy-subrc <> 0.
       lv_msgv1 =  sy-msgv1.
       MESSAGE s888(sabapdocu) WITH  '用户'  lv_msgv1  '正在处理' DISPLAY LIKE 'E'.
       LEAVE LIST-PROCESSING.
     ENDIF.

   ELSE.
     CALL FUNCTION 'DEQUEUE_EZ_ZRET0036'
       EXPORTING
*        MODE_ZRET0036       = 'X'
*        MANDT               = SY-MANDT
         zf01 = lv_data
*        X_ZF01              = ' '
*        _SCOPE              = '3'
*        _SYNCHRON           = ' '
*        _COLLECT            = ' '
       .


*     CALL FUNCTION 'DEQUEUE_ALL'.

   ENDIF.
 ENDFORM.

 FORM frm_code_pro  CHANGING ok_code TYPE sy-ucomm
                             pv_code TYPE sy-ucomm.
   CLEAR pv_code.
   pv_code = ok_code.
   CLEAR ok_code.
   CLEAR:
         sy-ucomm.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_LIST_BOX
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> GT_VLS_ZHTLX
*&---------------------------------------------------------------------*
 FORM frm_set_list_box USING   pv_id     TYPE  vrm_id
                               pt_values TYPE vrm_values.

   CALL FUNCTION 'VRM_SET_VALUES'
     EXPORTING
       id              = pv_id
       values          = pt_values[]
     EXCEPTIONS
       id_illegal_name = 1
       OTHERS          = 2.

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_EKGRP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TA01_EKGRP
*&      <-- GS_TA01_EKNAM
*&---------------------------------------------------------------------*
 FORM frm_pro_screen_ekgrp  USING    pv_ekgrp TYPE t024-ekgrp
                     CHANGING pv_eknam TYPE t024-eknam.

   IF pv_ekgrp IS NOT INITIAL.
     CLEAR pv_eknam.
     SELECT SINGLE eknam INTO pv_eknam FROM t024 WHERE ekgrp = pv_ekgrp .
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '采购组不存在' .
     ENDIF.

   ENDIF.
 ENDFORM.



 FORM frm_get_butxt  USING    pv_bukrs TYPE t001-bukrs
                     CHANGING pv_butxt .
   IF pv_bukrs IS NOT INITIAL.
     CLEAR: pv_butxt.
     SELECT SINGLE butxt INTO pv_butxt FROM t001 WHERE bukrs = pv_bukrs.
   ENDIF.

 ENDFORM.

 FORM frm_get_werks_t USING pv_werks CHANGING pv_werks_t.
   CHECK pv_werks IS NOT INITIAL.

   CLEAR: pv_werks_t.
*   SELECT SINGLE name1 INTO pv_werks_t FROM t001w WHERE werks = pv_werks."ERP-17212  未上线批发加盟店
   SELECT SINGLE name1 INTO pv_werks_t FROM zrev010_wrk WHERE werks = pv_werks.

 ENDFORM.

 FORM frm_get_zghf_t USING pv_lifnr CHANGING pv_lifnr_t.

   IF pv_lifnr IS INITIAL.
     pv_lifnr_t = ''.
   ELSE.
     CLEAR: pv_lifnr_t.
     SELECT SINGLE name1 INTO pv_lifnr_t FROM lfa1 WHERE lifnr = pv_lifnr.
   ENDIF.

 ENDFORM.
 FORM frm_get_ekotx USING pv_ekorg CHANGING pv_ekotx.

   CHECK pv_ekorg IS NOT INITIAL.
   SELECT SINGLE   ekotx    FROM t024e
   WHERE ekorg = @pv_ekorg
   INTO @pv_ekotx.
 ENDFORM.

 FORM frm_get_zzlms USING pv_zzlbm CHANGING pv_zzlms.

   CHECK pv_zzlbm IS NOT INITIAL.
   SELECT SINGLE   zzlms    FROM zretcm10
   WHERE zzlbm = @pv_zzlbm
   INTO @pv_zzlms.
 ENDFORM.

 FORM frm_get_zqdms USING pv_zqdbm CHANGING pv_zqdms.

   CHECK pv_zqdbm IS NOT INITIAL.
   SELECT SINGLE   zqdms    FROM zretcm11
   WHERE zqdbm = @pv_zqdbm
   INTO @pv_zqdms.
 ENDFORM.

 FORM frm_get_gysms USING pv_lifnr CHANGING pv_name1.

   CHECK pv_lifnr IS NOT INITIAL.
   SELECT SINGLE   name1    FROM lfa1
   WHERE lifnr = @pv_lifnr
   INTO @pv_name1.
 ENDFORM.

 FORM frm_alpha_in_lifnr  CHANGING pv_lifnr.
   CHECK pv_lifnr IS NOT INITIAL.
   pv_lifnr = |{ pv_lifnr ALPHA = IN }|.
 ENDFORM.


 FORM frm_check_bukrs CHANGING pv_bukrs TYPE t001-bukrs.

   CHECK pv_bukrs IS NOT INITIAL.

   IF pv_bukrs NE 'ALL'.
     SELECT SINGLE bukrs    FROM t001    WHERE bukrs = @pv_bukrs
       INTO @DATA(lv_bukrs).
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '公司代码不存在！'.
     ENDIF.
   ENDIF.

 ENDFORM.
 FORM frm_check_dcwrk CHANGING pv_werks TYPE t001w-werks.
   CHECK pv_werks IS NOT INITIAL.
*   SELECT SINGLE bwkey    FROM t001k    WHERE bwkey = @pv_werks
*   INTO @DATA(lv_bwkey).
   SELECT SINGLE werks    FROM zrev009_wrk    WHERE werks = @pv_werks
     INTO @DATA(lv_werks).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '工厂不存在！'.
   ENDIF.
 ENDFORM.
 FORM frm_check_ekorg  USING    pv_ekorg TYPE t024e-ekorg.


   CHECK pv_ekorg IS NOT INITIAL.
   SELECT SINGLE ekorg    FROM t024e    WHERE ekorg = @pv_ekorg
     INTO @DATA(lv_ekorg).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '采购组织不存在！'.
   ENDIF.

 ENDFORM.

 FORM frm_screen_check_zzlbm  USING    pv_zzlbm TYPE zretcm10-zzlbm.


   CHECK pv_zzlbm IS NOT INITIAL.
   SELECT SINGLE zzlbm    FROM zretcm10    WHERE zzlbm = @pv_zzlbm
     INTO @DATA(lv_zzlbm).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '子类编码不存在！'.
   ENDIF.

 ENDFORM.

 FORM frm_screen_check_zqdbm  USING    pv_zqdbm TYPE zretcm11-zqdbm.


   CHECK pv_zqdbm IS NOT INITIAL.
   SELECT SINGLE zqdbm    FROM zretcm11    WHERE zqdbm = @pv_zqdbm
     INTO @DATA(lv_zqdbm).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '渠道编码不存在！'.
   ENDIF.

 ENDFORM.
 FORM frm_screen_check_zzgys  USING    pv_lifnr TYPE lfa1-lifnr.


   CHECK pv_lifnr IS NOT INITIAL.
   SELECT SINGLE lifnr    FROM lfa1    WHERE lifnr = @pv_lifnr
     INTO @DATA(lv_lifnr).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '供应商编码不存在！'.
   ENDIF.

 ENDFORM.

 FORM frm_check_zghf  USING    pv_lifnr TYPE lfa1-lifnr.


   CHECK pv_lifnr IS NOT INITIAL.
   SELECT SINGLE lifnr    FROM lfa1    WHERE lifnr = @pv_lifnr
     INTO @DATA(lv_lifnr).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '供货方不存在！'.
   ENDIF.

 ENDFORM.


 FORM frm_check_zflzff  USING    pv_lifnr TYPE lfa1-lifnr.

*& ADD BY zsfsx = '2'
   CHECK pv_lifnr IS NOT INITIAL.
   SELECT SINGLE partner    FROM but000    WHERE partner = @pv_lifnr
     INTO @DATA(lv_lifnr).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '支付方不存在！'.
   ENDIF.

 ENDFORM.



 FORM frm_check_zghf_35  USING    pv_lifnr TYPE lfa1-lifnr.

   DATA:
         lv_flg TYPE char1.

   CHECK pv_lifnr IS NOT INITIAL.

   SELECT SINGLE lifnr    FROM lfa1    WHERE lifnr = @pv_lifnr
     INTO @DATA(lv_lifnr).
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '供货方不存在！'.
   ENDIF.

   PERFORM frm_get_zghf_attr USING pv_lifnr CHANGING lv_flg.
   IF lv_flg NE 'I'.
     MESSAGE e888(sabapdocu) WITH '只允许录入内部供货方'.
   ENDIF.

 ENDFORM.


 FORM frm_check_zghf_wb  USING    pv_lifnr TYPE lfa1-lifnr.
   DATA:
         lv_flg TYPE char1.

   CHECK pv_lifnr IS NOT INITIAL.

   IF pv_lifnr NE 'ALL'.
     SELECT SINGLE lifnr    FROM lfa1    WHERE lifnr = @pv_lifnr
       INTO @DATA(lv_lifnr).
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '供货方不存在！'.
     ENDIF.
   ENDIF.

   IF pv_lifnr NE 'ALL'.
     PERFORM frm_get_zghf_attr USING pv_lifnr CHANGING lv_flg.
     IF lv_flg NE 'E'.
       MESSAGE e888(sabapdocu) WITH '只能维护外部供货方'.

     ENDIF.
   ENDIF.
 ENDFORM.


 FORM frm_get_ztmpid  CHANGING pv_ztmpid TYPE zretc001-ztmpid.

   PERFORM frm_get_num USING 'ZRE0007' '01' CHANGING pv_ztmpid.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_EKNAM
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_TA02_EKGRP
*&      <-- PS_TA02_EKNAM
*&---------------------------------------------------------------------*
 FORM frm_get_eknam  USING    pv_ekgrp TYPE t024-ekgrp
                      CHANGING pv_eknam TYPE t024-eknam.

   IF pv_ekgrp IS NOT INITIAL.
     CLEAR pv_eknam.
     SELECT SINGLE eknam INTO pv_eknam FROM t024 WHERE ekgrp = pv_ekgrp .
   ENDIF.

 ENDFORM.

 FORM frm_get_zflzff_t  USING    pv_zflzff TYPE zret0006-zflzff
                     CHANGING pv_name1.

   IF pv_zflzff IS NOT INITIAL.
     DATA(lv_zflzff) = |{ pv_zflzff ALPHA = IN }|.
     CLEAR pv_name1.
     SELECT SINGLE name1 INTO pv_name1 FROM lfa1 WHERE lifnr = lv_zflzff .
   ENDIF.

 ENDFORM.

 FORM frm_set_screen_zflzff  USING    pv_zflzff TYPE zret0006-zflzff
                      CHANGING pv_name1 .
*& ADD BY zsfsx = '2'
   IF pv_zflzff IS NOT INITIAL.
     CLEAR pv_name1.
     SELECT SINGLE name_org1 INTO pv_name1 FROM but000 WHERE partner = pv_zflzff .
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '支付方不存在！'.
     ENDIF.
   ENDIF.

 ENDFORM.

* FORM FRM_SET_SCREEN_ZGHF  USING    PV_ZGHF TYPE LFA1-LIFNR
*                      CHANGING PV_NAME1 .
*
*   IF PV_ZGHF IS NOT INITIAL.
*     CLEAR PV_NAME1.
*     SELECT SINGLE NAME1 INTO PV_NAME1 FROM LFA1 WHERE LIFNR = PV_ZGHF .
*     IF SY-SUBRC NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '供货方不存在！'.
*     ENDIF.
*   ENDIF.
*
* ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLZFF_WB
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GS_T44
*&---------------------------------------------------------------------*
 FORM frm_set_screen_zflzff_wb  USING    pv_zflzff TYPE zret0006-zflzff
                       CHANGING pv_name1 .


   DATA(lv_zflzff) = pv_zflzff.
   lv_zflzff = |{ lv_zflzff ALPHA = OUT }|.
   CONDENSE lv_zflzff .

   IF strlen( lv_zflzff ) <= 4.
*& ADD BY zsfsx = '2'
*     MESSAGE e888(sabapdocu) WITH '该支付方为内部支付方，该处只能维护外部支付方。'.
   ELSE.
*     SELECT SINGLE COUNT(*) FROM T001 WHERE BUKRS = LV_ZFLZFF.
*     IF SY-SUBRC EQ 0.
*       MESSAGE E888(SABAPDOCU) WITH '该支付方为内部支付方，该处只能维护外部支付方。'.
*     ENDIF.
*     SELECT SINGLE COUNT(*) FROM T001W WHERE WERKS = LV_ZFLZFF.
*     IF SY-SUBRC EQ 0.
*       MESSAGE E888(SABAPDOCU) WITH '该支付方为内部支付方，该处只能维护外部支付方。'.
*     ENDIF.
   ENDIF.



   IF pv_zflzff IS NOT INITIAL.
     CLEAR pv_name1.
*& ADD by zsfsx = '2'
     SELECT SINGLE name_org1 INTO pv_name1 FROM but000 WHERE partner = pv_zflzff .
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '支付方不存在！'.
     ENDIF.
   ENDIF.


 ENDFORM.
*  FORM FRM_SET_SCREEN_ZFLZFF_SUB  USING    PV_ZFLZFF TYPE ZRET0006-ZFLZFF
*                       CHANGING PV_NAME1 .
*
*
**   DATA(LV_ZFLZFF) = PV_ZFLZFF.
**   LV_ZFLZFF = |{ LV_ZFLZFF ALPHA = OUT }|.
**   CONDENSE LV_ZFLZFF .
**
**   IF STRLEN( LV_ZFLZFF ) <= 4.
**     MESSAGE E888(SABAPDOCU) WITH '该支付方为内部支付方，该处只能维护外部支付方。'.
**   ELSE.
**
**   ENDIF.
*
*
*   IF PV_ZFLZFF IS NOT INITIAL.
*     CLEAR PV_NAME1.
*     SELECT SINGLE NAME1 INTO PV_NAME1 FROM LFA1 WHERE LIFNR = PV_ZFLZFF .
*     IF SY-SUBRC NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '支付方不存在！'.
*     ENDIF.
*   ENDIF.
*
*
* ENDFORM.



 FORM frm_download_template  USING    objid title.
   DATA:
   l_path  TYPE char100.


   PERFORM frm_get_filepath USING l_path title.
   IF l_path IS NOT INITIAL.

     PERFORM frm_get_template USING objid l_path.
   ENDIF.
 ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FM_GET_FILEPATH
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_L_PATH  TEXT
*      -->P_TITLE  TEXT
*----------------------------------------------------------------------*
 FORM frm_get_filepath  USING    c_fullpath i_name.
   DATA:
     l_default  TYPE string,
     l_filename TYPE string,
     l_fullpath TYPE string,
     l_path     TYPE string.
   l_default = i_name.
   CALL METHOD cl_gui_frontend_services=>file_save_dialog
     EXPORTING
       default_extension    = '*.XLSX'
       default_file_name    = l_default
       file_filter          = 'EXCEL 文档|*.XLSX;*.XLS'
     CHANGING
       filename             = l_filename
       path                 = l_path
       fullpath             = l_fullpath
     EXCEPTIONS
       cntl_error           = 1
       error_no_gui         = 2
       not_supported_by_gui = 3
       OTHERS               = 4.
   c_fullpath = l_fullpath.
 ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FM_GET_TEMPLATE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_OBJID  TEXT
*      -->P_L_PATH  TEXT
*----------------------------------------------------------------------*
 FORM frm_get_template  USING    i_objid i_path.

   DATA:
     l_objid  TYPE w3objid,
     l_rc     TYPE i,
     l_path   TYPE rlgrap-filename,
     l_errtxt TYPE char200,
     lw_data  TYPE wwwdatatab.

   l_objid = i_objid.
   l_path  = i_path.

   SELECT SINGLE relid objid
     FROM wwwdata
     INTO CORRESPONDING FIELDS OF lw_data
    WHERE srtf2 = 0
      AND relid = 'MI'
   AND objid = l_objid.

   IF sy-subrc = 0.

     CALL FUNCTION 'DOWNLOAD_WEB_OBJECT'
       EXPORTING
         key         = lw_data
         destination = l_path
       IMPORTING
         rc          = l_rc.
     IF l_rc <> 0.
       CONCATENATE '模板文件：' l_objid '下载失败' INTO l_errtxt.
       MESSAGE l_errtxt TYPE 'E'.
     ENDIF.
   ELSE.
     CONCATENATE '模板文件：' l_objid '不存在' INTO l_errtxt.
     MESSAGE l_errtxt TYPE 'E'.
   ENDIF.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZGHF_ATTR
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_TMP_ZGHF
*&      <-- LV_FLG
*&---------------------------------------------------------------------*
 FORM frm_get_zghf_attr  USING    pv_lifnr TYPE lfa1-lifnr
                         CHANGING pv_flg TYPE char1.

*  I 内部 E 外部 X 不存在

   DATA(lv_zghf) = |{ pv_lifnr ALPHA = OUT }|.
   CONDENSE lv_zghf.


   CLEAR pv_flg.

   IF strlen( lv_zghf ) > 4.

     lv_zghf = |{ pv_lifnr ALPHA = IN }|.

     SELECT SINGLE COUNT(*) FROM lfa1 WHERE lifnr = lv_zghf.
     IF sy-subrc EQ 0.
       pv_flg = 'E'.
     ELSE.
       pv_flg  = 'X'.
     ENDIF.

   ELSE.

     SELECT SINGLE COUNT(*) FROM t001 WHERE bukrs = lv_zghf.
     IF sy-subrc EQ 0.
       pv_flg = 'I'.
     ELSE.
*       SELECT SINGLE COUNT(*) FROM t001w WHERE werks = lv_zghf. "ERP-17212  未上线批发加盟店
       SELECT SINGLE COUNT(*) FROM zrev010_wrk WHERE werks = lv_zghf.
       IF sy-subrc EQ 0.
         pv_flg = 'I'.
       ELSE.
         pv_flg = 'X'.
       ENDIF.
     ENDIF.
   ENDIF.

 ENDFORM.
 FORM frm_get_zzff_attr  USING    pv_lifnr TYPE lfa1-lifnr
                        CHANGING pv_flg TYPE char1.

*  I 内部 E 外部 X 不存在

   DATA(lv_zghf) = |{ pv_lifnr ALPHA = IN }|.

   SELECT SINGLE ktokk FROM lfa1 WHERE lifnr = @lv_zghf  INTO @DATA(lv_ktokk).
   IF sy-subrc EQ 0.
     IF lv_ktokk = 'Y001' OR lv_ktokk = 'Y006' OR lv_ktokk = 'Y007'.
       pv_flg = 'I'.
     ELSE.
       pv_flg = 'E'.
     ENDIF.
   ELSE.
     pv_flg  = 'X'.
   ENDIF.

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_ZSPZ_ID_T
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TA02_ZSPZ_ID
*&      <-- GS_TA02_ZSPZID_TXT
*&---------------------------------------------------------------------*
 FORM frm_set_screen_zspz_id_t  USING    pv_zspz_id  TYPE zret0009-zspz_id
                                CHANGING pv_zspzid_txt TYPE zret0009-zspzid_txt.

   IF pv_zspz_id IS NOT INITIAL.
     CLEAR pv_zspzid_txt.
     SELECT SINGLE zspzid_txt INTO pv_zspzid_txt FROM zret0009 WHERE zspz_id = pv_zspz_id .
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '商品组不存在！'.
     ENDIF.
   ENDIF.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_MATNR
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T20_SUB_MATNR
*&      <-- GS_T20_SUB_MAKTX
*&---------------------------------------------------------------------*
 FORM frm_set_screen_zspz_id  USING    pv_zspz_id  TYPE zret0009-zspz_id
                                       pv_zspzid_txt  TYPE zret0009-zspzid_txt.
   IF pv_zspz_id IS NOT INITIAL.

     CLEAR pv_zspzid_txt.
     SELECT SINGLE zspzid_txt INTO pv_zspzid_txt FROM zret0009 WHERE zspz_id = pv_zspz_id .

     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '商品组不存在！'.
     ENDIF.
   ENDIF.
 ENDFORM.
 FORM frm_set_screen_matnr  USING    pv_matnr   TYPE mara-matnr
                            CHANGING  pv_meins  TYPE mara-meins
                                      pv_maktx  TYPE makt-maktx
   .
   IF pv_matnr IS NOT INITIAL AND pv_matnr NE 'ALL'.

     pv_matnr = |{ pv_matnr ALPHA = IN WIDTH = 18 }|.

     PERFORM frm_check_matnr(zbcs0001) USING pv_matnr.
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '商品不存在！'.
     ENDIF.

   ENDIF.

   CLEAR pv_maktx.
   SELECT SINGLE maktx INTO pv_maktx FROM makt WHERE matnr = pv_matnr .

   CLEAR pv_meins.
   SELECT SINGLE meins INTO pv_meins FROM mara WHERE matnr = pv_matnr .

 ENDFORM.
 FORM frm_set_spz_matnr  USING    pv_matnr   TYPE mara-matnr
                           CHANGING  pv_meins  TYPE mara-meins
                                     pv_maktx  TYPE makt-maktx
                                     pt_fdhsj  TYPE zrei0006
  .
   IF pv_matnr IS NOT INITIAL AND pv_matnr NE 'ALL'.

     pv_matnr = |{ pv_matnr ALPHA = IN WIDTH = 18 }|.

     PERFORM frm_check_matnr(zbcs0001) USING pv_matnr.
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '商品不存在！'.
     ENDIF.

   ENDIF.

   CLEAR pv_maktx.
   SELECT SINGLE maktx INTO pv_maktx FROM makt WHERE matnr = pv_matnr .

   CLEAR pv_meins.
   SELECT SINGLE meins INTO pv_meins FROM mara WHERE matnr = pv_matnr .

   DATA:ls_fdhsj  LIKE LINE OF pt_fdhsj .
   CLEAR:ls_fdhsj.
   ls_fdhsj-matnr = pv_matnr.
   MODIFY pt_fdhsj FROM ls_fdhsj TRANSPORTING matnr WHERE matnr <> pv_matnr.
 ENDFORM.

 FORM frm_get_data_zspz_id  USING    pv_zspz_id  TYPE zret0009-zspz_id
                                     pv_zspzid_txt TYPE zret0009-zspzid_txt.
   IF pv_zspz_id IS NOT INITIAL.

     CLEAR pv_zspzid_txt.
     SELECT SINGLE zspzid_txt INTO pv_zspzid_txt FROM zret0009 WHERE zspz_id = pv_zspz_id .

     IF sy-subrc NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '商品不存在！'.
     ENDIF.
   ENDIF.


 ENDFORM.
 FORM frm_get_data_matnr  USING    pv_matnr  TYPE mara-matnr
                           CHANGING  pv_meins  TYPE mara-meins
                                     pv_maktx  TYPE makt-maktx.
   IF pv_matnr IS NOT INITIAL.
     pv_matnr = |{ pv_matnr ALPHA = IN WIDTH = 18 }|.
     CLEAR pv_maktx.
     SELECT SINGLE maktx INTO pv_maktx FROM makt WHERE matnr = pv_matnr .

     CLEAR pv_meins.
     SELECT SINGLE meins INTO pv_meins FROM mara WHERE matnr = pv_matnr .
     IF sy-subrc NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '商品不存在！'.
     ENDIF.
   ENDIF.
 ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_SET_SCREEN_ZHT_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T09_SUB_ZHT_ID
*&      <-- GS_T09_SUB_ZHT_TXT
*&---------------------------------------------------------------------*
 FORM frm_set_screen_zht_id  USING    pv_zht_id  TYPE zreta001-zht_id
                             CHANGING pv_zht_txt TYPE zreta001-zht_txt
                                      pv_zbpcode TYPE zreta001-zbpcode.
   IF pv_zht_id IS NOT INITIAL.

     CLEAR pv_zht_txt.
     SELECT SINGLE zht_txt INTO pv_zht_txt FROM zreta001 WHERE zht_id = pv_zht_id .
     IF sy-subrc NE 0.
       MESSAGE e888(sabapdocu) WITH '合同号码不存在！'.
     ENDIF.

     IF pv_zbpcode IS INITIAL.
       SELECT SINGLE zbpcode INTO pv_zbpcode FROM zreta001 WHERE zbptype = 'M' AND zht_id = pv_zht_id.
     ENDIF.

   ENDIF.
 ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_SET_ALL
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_MARA
*&---------------------------------------------------------------------*
 FORM frm_set_all  TABLES pt_tab TYPE STANDARD TABLE
                   USING pv_seq_field TYPE name_komp.

   FIELD-SYMBOLS: <fs>.

   LOOP AT pt_tab.
     ASSIGN COMPONENT pv_seq_field OF STRUCTURE pt_tab TO <fs>.
     IF sy-subrc EQ  0.
       <fs> = 'X'.
       MODIFY pt_tab.
     ENDIF.
   ENDLOOP.

 ENDFORM.
 FORM frm_set_sal  TABLES pt_tab TYPE STANDARD TABLE
                   USING pv_seq_field TYPE name_komp.

   FIELD-SYMBOLS: <fs>.

   LOOP AT pt_tab.
     ASSIGN COMPONENT pv_seq_field OF STRUCTURE pt_tab TO <fs>.
     IF sy-subrc EQ  0.
       <fs> = ''.
       MODIFY pt_tab.
     ENDIF.
   ENDLOOP.

 ENDFORM.


 FORM frm_call_transaction_tk USING pv_ztk_id TYPE zreta002-ztk_id
                                    pv_transaction  TYPE sy-tcode.
   CHECK pv_ztk_id IS NOT INITIAL.
   CHECK pv_transaction IS NOT INITIAL.

   SET PARAMETER ID 'ZTK_ID' FIELD pv_ztk_id.
   CALL TRANSACTION pv_transaction AND SKIP FIRST SCREEN.
   SET PARAMETER ID 'ZTK_ID' FIELD ''.

 ENDFORM.

 FORM frm_call_transaction_spz USING pv_zspz_id TYPE zret0009-zspz_id
                                    pv_transaction  TYPE sy-tcode.

   CHECK pv_zspz_id IS NOT INITIAL.
*  CHECK PV_TRANSACTION IS NOT INITIAL.

   SUBMIT zrem0002
     WITH p_zspzid = pv_zspz_id

     WITH rb_add = ''
     WITH rb_dis = 'X'
*        VIA SELECTION-SCREEN
     AND RETURN .

 ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZBPNAME
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZBPCODE
*&      --> GS_TA01_ZBPTYPE
*&      <-- GS_TA01_ZBPNAME
*&---------------------------------------------------------------------*
 FORM frm_set_zbpname  USING    pv_zbpcode TYPE zreta001-zbpcode
                                pv_zbptype TYPE zreta001-zbptype
                       CHANGING pv_zbpname TYPE lfa1-name1.

   CHECK pv_zbpcode IS NOT INITIAL.

   SELECT SINGLE  name_org1 INTO pv_zbpname  FROM but000 WHERE partner = pv_zbpcode.
   IF sy-subrc NE 0.
     MESSAGE e888(sabapdocu) WITH '伙伴代码不存在' .
   ENDIF.
*   IF PV_ZBPTYPE = 'S'.
*     SELECT SINGLE NAME1 INTO PV_ZBPNAME FROM LFA1 WHERE LIFNR = PV_ZBPCODE.
*     IF SY-SUBRC NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '伙伴代码不存在' .
*     ENDIF.
*   ELSEIF PV_ZBPTYPE = 'M'.
*     PV_ZBPCODE = |{ PV_ZBPCODE ALPHA = OUT } |.
*     SELECT SINGLE  CODEITEMDESC INTO PV_ZBPNAME  FROM ZMMT0345 WHERE CODEITEMID = PV_ZBPCODE.
*     IF SY-SUBRC NE 0.
*       MESSAGE E888(SABAPDOCU) WITH '伙伴代码不存在' .
*     ENDIF.
*   ENDIF.

 ENDFORM.

 FORM frm_author_check_zhtlx  USING    pv_zhtlx TYPE zreta001-zhtlx
                                       pv_actvt TYPE activ_auth
                             CHANGING  pv_mtype TYPE bapi_mtype
                                       pv_msg   TYPE scp1_general_error-msgv1.

   DATA:
         lv_actvt TYPE char10.

   CLEAR: pv_mtype,pv_msg.

*   CHECK pv_zhtlx IS NOT INITIAL.
   IF pv_zhtlx IS  INITIAL.
     pv_mtype = 'S'.
     RETURN.
   ENDIF.

   CASE pv_actvt.

     WHEN '01'.  lv_actvt = '创建'.

     WHEN '02'.  lv_actvt = '修改'.

     WHEN '03'.  lv_actvt = '显示'.

     WHEN 'A1'.  lv_actvt = '1级审批'.
     WHEN 'A2'.  lv_actvt = '2级审批'.
     WHEN 'A3'.  lv_actvt = '3级审批'.
     WHEN 'A4'.  lv_actvt = '4级审批'.
     WHEN 'A5'.  lv_actvt = '5级审批'.
     	WHEN OTHERS.
   ENDCASE.
   SELECT SINGLE ddtext FROM dd07t WHERE  ddlanguage = @sy-langu AND domname = 'ZREM_ZHTLX'AND domvalue_l = @pv_zhtlx INTO @DATA(lv_ddtext).

   AUTHORITY-CHECK OBJECT 'ZREAR006'
                       ID 'ZHTLX' FIELD pv_zhtlx
                       ID 'ACTVT' FIELD pv_actvt.
   IF sy-subrc NE 0.
     pv_mtype = 'E'.
     pv_msg  = '合同类型检查失败: ' && lv_ddtext && '/ ' && lv_actvt.
   ELSE.
     pv_mtype = 'S'.
   ENDIF.

 ENDFORM.

 FORM frm_author_check_ekgrp  USING    pv_ekgrp TYPE ekko-ekgrp
                                        pv_actvt TYPE activ_auth
                             CHANGING  pv_mtype TYPE bapi_mtype
                                       pv_msg   TYPE scp1_general_error-msgv1.

   DATA:
         lv_actvt TYPE char10.

   CLEAR: pv_mtype,pv_msg.


   IF pv_ekgrp IS  INITIAL.
     pv_mtype = 'S'.
     RETURN.
   ENDIF.

   CASE pv_actvt.

     WHEN '01'.  lv_actvt = '创建'.

     WHEN '02'.  lv_actvt = '修改'.

     WHEN '03'.  lv_actvt = '显示'.

     WHEN 'A1'.  lv_actvt = '1级审批'.
     WHEN 'A2'.  lv_actvt = '2级审批'.
     WHEN 'A3'.  lv_actvt = '3级审批'.
     WHEN 'A4'.  lv_actvt = '4级审批'.
     WHEN 'A5'.  lv_actvt = '5级审批'.
     	WHEN OTHERS.
   ENDCASE.
   SELECT SINGLE eknam  FROM t024 WHERE ekgrp = @pv_ekgrp INTO @DATA(lv_ddtext).


   AUTHORITY-CHECK OBJECT 'ZREAR008'
                       ID 'EKGRP' FIELD pv_ekgrp
                       ID 'ACTVT' FIELD pv_actvt.
   IF sy-subrc NE 0.
     pv_mtype = 'E'.
     pv_msg  = '采购组检查失败: ' && lv_ddtext && '/ ' && lv_actvt.
   ELSE.
     pv_mtype = 'S'.
   ENDIF.

 ENDFORM.


 FORM frm_author_check_zbukrs  USING    pv_bukrs TYPE t001-bukrs
                                        pv_actvt TYPE activ_auth
                             CHANGING  pv_mtype TYPE bapi_mtype
                                       pv_msg   TYPE scp1_general_error-msgv1.

   DATA:
         lv_actvt TYPE char10.

   CLEAR: pv_mtype,pv_msg.

   IF pv_bukrs IS  INITIAL.
     pv_mtype = 'S'.
     RETURN.
   ENDIF.

   CASE pv_actvt.

     WHEN '01'.  lv_actvt = '创建'.

     WHEN '02'.  lv_actvt = '修改'.

     WHEN '03'.  lv_actvt = '显示'.

     WHEN 'A1'.  lv_actvt = '1级审批'.
     WHEN 'A2'.  lv_actvt = '2级审批'.
     WHEN 'A3'.  lv_actvt = '3级审批'.
     WHEN 'A4'.  lv_actvt = '4级审批'.
     WHEN 'A5'.  lv_actvt = '5级审批'.
     	WHEN OTHERS.
   ENDCASE.
   SELECT SINGLE butxt  FROM t001 WHERE bukrs = @pv_bukrs INTO @DATA(lv_ddtext).

   AUTHORITY-CHECK OBJECT 'ZREAR007'
                       ID 'BUKRS' FIELD pv_bukrs
                       ID 'ACTVT' FIELD pv_actvt.
   IF sy-subrc NE 0.
     pv_mtype = 'E'.
     pv_msg  = '返利合同-合同主体检查失败: ' && lv_ddtext && '/ ' && lv_actvt.
   ELSE.
     pv_mtype = 'S'.
   ENDIF.

 ENDFORM.

 FORM frm_author_check_zbukrs_tk  USING    pv_bukrs TYPE t001-bukrs
                                        pv_actvt TYPE activ_auth
                             CHANGING  pv_mtype TYPE bapi_mtype
                                       pv_msg   TYPE scp1_general_error-msgv1.

   DATA:
         lv_actvt TYPE char10.

   CLEAR: pv_mtype,pv_msg.

   IF pv_bukrs IS  INITIAL.
     pv_mtype = 'S'.
     RETURN.
   ENDIF.

   CASE pv_actvt.

     WHEN '01'.  lv_actvt = '创建'.

     WHEN '02'.  lv_actvt = '修改'.

     WHEN '03'.  lv_actvt = '显示'.

     WHEN 'A1'.  lv_actvt = '1级审批'.
     WHEN 'A2'.  lv_actvt = '2级审批'.
     WHEN 'A3'.  lv_actvt = '3级审批'.
     WHEN 'A4'.  lv_actvt = '4级审批'.
     WHEN 'A5'.  lv_actvt = '5级审批'.
     	WHEN OTHERS.
   ENDCASE.
   SELECT SINGLE butxt  FROM t001 WHERE bukrs = @pv_bukrs INTO @DATA(lv_ddtext).

   AUTHORITY-CHECK OBJECT 'ZREAR009'
                       ID 'BUKRS' FIELD pv_bukrs
                       ID 'ACTVT' FIELD pv_actvt.
   IF sy-subrc NE 0.
     pv_mtype = 'E'.
     pv_msg  = '返利条款-协议主体检查失败: ' && lv_ddtext && '/ ' && lv_actvt.
   ELSE.
     pv_mtype = 'S'.
   ENDIF.

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_AUTHOR_PRO
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LV_MTYPE
*&      --> LV_MSG
*&---------------------------------------------------------------------*
 FORM frm_author_pro  USING     pv_mtype TYPE bapi_mtype
                                pv_msg   TYPE scp1_general_error-msgv1
                                    pv_flg   TYPE char1
                          CHANGING pt_msglist TYPE scp1_general_errors.

   IF pv_flg = 'A'.
     MESSAGE s888(sabapdocu) WITH pv_msg DISPLAY LIKE pv_mtype.
     LEAVE LIST-PROCESSING.
   ELSEIF pv_flg = 'B'.
     PERFORM frm_add_msg USING pv_msg  CHANGING pt_msglist.
   ELSEIF pv_flg = 'C'.
     MESSAGE e888(sabapdocu) WITH pv_msg DISPLAY LIKE pv_mtype.
   ENDIF.
 ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZTMPID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_TMPIDR
*&---------------------------------------------------------------------*
 FORM frm_check_ztk_id  USING pv_ztk_id TYPE zreta002-ztk_id.
   SELECT SINGLE *  FROM zreta002 INTO @DATA(ls_ta02) WHERE ztk_id = @pv_ztk_id.
   IF sy-subrc NE 0.
     MESSAGE s888(sabapdocu) WITH '条款编码不存在' DISPLAY LIKE 'E'.
     LEAVE LIST-PROCESSING.
   ENDIF.

 ENDFORM.

 FORM frm_check_ztk_id_yg  USING pv_ztk_id TYPE zreta002-ztk_id.
   SELECT SINGLE *  FROM zreta002 INTO @DATA(ls_ta02) WHERE ztk_id = @pv_ztk_id.
   IF sy-subrc NE 0.
     MESSAGE s888(sabapdocu) WITH '条款编码不存在' DISPLAY LIKE 'E'.
     LEAVE LIST-PROCESSING.
   ELSE.
     IF ls_ta02-zcnfyg = 'X'.
       MESSAGE s888(sabapdocu) WITH '该条款确认不做次年预估复制，不能参考' DISPLAY LIKE 'E'.
       LEAVE LIST-PROCESSING.
     ENDIF.
     SELECT SINGLE ztk_id FROM zreta002 WHERE zcnygtk_id = @pv_ztk_id INTO @DATA(lv_ztk_id).
     IF sy-subrc EQ 0.
       MESSAGE s888(sabapdocu) WITH '该条款已做过次年预估返利复制，不能参考' DISPLAY LIKE 'E'.
       LEAVE LIST-PROCESSING.
     ENDIF.
   ENDIF.

 ENDFORM.

*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_REF
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_TK_IDR
*&---------------------------------------------------------------------*
 FORM frm_check_ref  USING    pv_ztk_id TYPE zreta002-ztk_id.

   IF pv_ztk_id IS  INITIAL.
     MESSAGE s888(sabapdocu) WITH '参考条款号码必填' DISPLAY LIKE 'E'.
     LEAVE LIST-PROCESSING.
   ELSE.
     PERFORM frm_check_ztk_id USING pv_ztk_id.
   ENDIF.

 ENDFORM.

 FORM frm_check_ztk_id_zlrsi  USING    pv_ztk_id TYPE zreta002-ztk_id.

   SELECT SINGLE *  FROM zreta002 INTO @DATA(ls_ta02) WHERE ztk_id = @pv_ztk_id.
   IF ls_ta02-zxyzt NE 'A'.
     MESSAGE s888(sabapdocu) WITH '仅限审批通过的条款可以附加行项目!' DISPLAY LIKE 'E'.
     LEAVE LIST-PROCESSING.
   ENDIF.

*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:32:31
*    <NOTES> #####
*    <OLD CODES>
*   SELECT SINGLE * FROM ZRET0002 WHERE ZFLLX = @LS_TA02-ZFLLX INTO @DATA(LS_T02).
*   IF LS_T02-ZLRSI NE 'X'.
*     MESSAGE S888(SABAPDOCU) WITH '该类型返利条款不支持附加行项目!' DISPLAY LIKE 'E'.
*     LEAVE LIST-PROCESSING.
*   ENDIF.
*    </OLD CODES>
*    <NEW CODES>
   SELECT SINGLE * FROM zretc009 WHERE zfllx = @ls_ta02-zfllx  AND zxybstyp = @ls_ta02-zxybstyp INTO @DATA(ls_tc09).
   IF ls_tc09-zlrsi NE 'X'.
     MESSAGE s888(sabapdocu) WITH '该类型返利条款不支持附加行项目!' DISPLAY LIKE 'E'.
     LEAVE LIST-PROCESSING.
   ENDIF.
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZEND
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZEND
*&---------------------------------------------------------------------*
 FORM frm_check_zbegin  USING    pv_zbegin TYPE d
                         CHANGING  pv_zjzrq TYPE d.
   DATA:
         ls_t053 TYPE zret0053.

   CLEAR sy-subrc.
   CLEAR ls_t053.
   CLEAR pv_zjzrq.
   SELECT SINGLE * FROM zret0053 WHERE bname = @sy-uname INTO CORRESPONDING FIELDS OF @ls_t053.
   pv_zjzrq = ls_t053-zjzrq.
   IF sy-subrc EQ 0.

     PERFORM frm_check_zbegin_exe USING pv_zbegin
                                        ls_t053.
   ELSE.
     CLEAR ls_t053.
     SELECT SINGLE * FROM zret0053 WHERE bname = 'ALL' INTO CORRESPONDING FIELDS OF @ls_t053.
     pv_zjzrq = ls_t053-zjzrq.
     PERFORM frm_check_zbegin_exe USING pv_zbegin
                                        ls_t053.
   ENDIF.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZBEGIN_EXE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PV_ZBEGIN
*&      --> LS_T053
*&---------------------------------------------------------------------*
 FORM frm_check_zbegin_exe  USING    pv_zbegin  TYPE d
                                      ps_t053    TYPE zret0053.
   IF ps_t053 IS INITIAL .
     sy-subrc = 0.
     RETURN.
   ENDIF.

   IF ps_t053-zsfjc = 'N'.
     sy-subrc = 0.
     RETURN.
   ELSE.
     IF pv_zbegin < ps_t053-zjzrq.
       sy-subrc = '4'.
       RETURN.
     ELSE.
       sy-subrc = 0.
       RETURN.
     ENDIF.
   ENDIF.

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_STATUS
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZFLZFF
*&---------------------------------------------------------------------*
 FORM frm_check_status_zflzff  USING    pv_zflzff TYPE zret0006-zflzff.

   DATA:
         lv_zflzff TYPE zret0006-zflzff.

   lv_zflzff = |{ pv_zflzff ALPHA = IN }|.
   CLEAR sy-subrc.

   SELECT SINGLE * FROM lfa1 WHERE lifnr = @lv_zflzff INTO @DATA(ls_lfa1).
   IF sy-subrc EQ 0.
     IF ls_lfa1-sperr = 'X' OR ls_lfa1-loevm = 'X'.
       sy-subrc = 4.
*       MESSAGE E888(SABAPDOCU) WITH '该支付方' && LV_ZFLZFF && '已被冻结或删除，请检查主数据!'.
     ENDIF.
   ELSE.
     sy-subrc = 0.
   ENDIF.

 ENDFORM.

 FORM frm_check_zflzff_bukrs  USING    pv_zflzff TYPE zret0006-zflzff.

   DATA:
     lv_zflzff TYPE zret0006-zflzff,
     lv_flg(1).

   lv_zflzff = |{ pv_zflzff ALPHA = IN }|.
   CLEAR sy-subrc.


   PERFORM frm_get_zzff_attr USING pv_zflzff CHANGING lv_flg.
   IF lv_flg = 'I'.

     lv_zflzff = |{ pv_zflzff ALPHA = OUT }|.
     CONDENSE lv_zflzff.

     SELECT SINGLE bukrs FROM t001 WHERE bukrs = @lv_zflzff INTO @DATA(lv_bukrs).
   ELSE.
     CLEAR sy-subrc.
   ENDIF.


 ENDFORM.


 FORM frm_check_status_zflzff_bukrs  USING    pv_zflzff TYPE zret0006-zflzff
                                              pv_zflsqf TYPE zret0006-zflsqf.
   DATA:
      lv_zflzff TYPE zret0006-zflzff.

   lv_zflzff = |{ pv_zflzff ALPHA = IN }|.
   CLEAR sy-subrc.

   SELECT SINGLE * FROM lfb1 WHERE lifnr = @lv_zflzff AND bukrs = @pv_zflsqf  INTO @DATA(ls_lfb1).
   IF sy-subrc EQ 0.
     IF ls_lfb1-sperr = 'X' OR ls_lfb1-loevm = 'X'.
       sy-subrc = 4.
     ENDIF.
   ELSE.
     sy-subrc = 0.
   ENDIF.

 ENDFORM.

 FORM on_ctmenu_tab USING po_menu TYPE REF TO cl_ctmenu.
   CALL METHOD cl_ctmenu=>load_gui_status
     EXPORTING
       program = sy-repid
       status  = 'SRIGHTCLICK1'
       menu    = po_menu
     EXCEPTIONS
       OTHERS  = 9.
   IF sy-subrc = 0.
*    MESSAGE 'GUI STATUS LOADED.' TYPE 'S'.
   ENDIF.
 ENDFORM.                    " ON_CTMENU_TAB1

*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZITEMS_KEY
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LS_TC02_ZITEMS_KEY
*&---------------------------------------------------------------------*
 FORM frm_get_zitems_key_symbol USING pv_fieldname TYPE char20
                               CHANGING pv_zitems_key TYPE zretc002-zitems.


   DATA:
     lv_zitems_key_max TYPE zretc002-zitems.

   FIELD-SYMBOLS <field_max>              TYPE any.


   CLEAR pv_zitems_key.

   UNASSIGN <field_max>.
   ASSIGN (pv_fieldname) TO <field_max>. "ASSIGN ('(ZRED0041)GV_ZITEM_KEY') TO <FIELD>.
   IF <field_max> IS ASSIGNED.
     lv_zitems_key_max = <field_max>.
   ENDIF.

   PERFORM frm_get_zitems_key CHANGING pv_zitems_key lv_zitems_key_max.

*     回写全局唯一 ZITEMS_KEY
   IF <field_max> IS ASSIGNED.
     <field_max> = lv_zitems_key_max.
     UNASSIGN <field_max>.
   ENDIF.

 ENDFORM.


 FORM frm_get_zitems_key  CHANGING pv_zitems_key TYPE zretc002-zitems
                                   pv_zitems_key_max TYPE zretc002-zitems.

   CLEAR pv_zitems_key.
   pv_zitems_key_max = pv_zitems_key_max + 1.
   pv_zitems_key = pv_zitems_key_max.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZHT_ID
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL_ZHT_ID
*&---------------------------------------------------------------------*
 FORM frm_check_zxy_id  USING    pv_zxy_id TYPE zret0006-zxy_id.
   CLEAR sy-subrc.
   SELECT SINGLE zxy_id  FROM zret0006 WHERE zxy_id = @pv_zxy_id INTO @DATA(lv_zxy_id).
 ENDFORM.

 FORM frm_check_zht_id  USING    pv_zht_id TYPE zreta002-zht_id.
   CLEAR sy-subrc.
   SELECT SINGLE zht_id  FROM zreta001 WHERE zht_id = @pv_zht_id INTO @DATA(lv_zth_id).
 ENDFORM.

 FORM frm_check_ztk_id_impt  USING    pv_ztk_id TYPE zreta002-ztk_id.
   CLEAR sy-subrc.
   SELECT SINGLE ztk_id  FROM zreta002 WHERE ztk_id = @pv_ztk_id AND zxybstyp = 'F' AND zxyzt NE 'D' INTO @DATA(lv_ztk_id).
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLLX
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL_ZFLLX
*&---------------------------------------------------------------------*
 FORM frm_check_zfllx  USING    pv_zfllx TYPE zreta002-zfllx.
   CLEAR sy-subrc.
   SELECT SINGLE zfllx  FROM zretc009 WHERE zfllx = @pv_zfllx INTO @DATA(lv_zfllx).

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZFLLX_ZXYBSTYP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_EXCEL_ZFLLX
*&      --> LS_EXCEL_ZXYBSTYP
*&---------------------------------------------------------------------*
 FORM frm_check_zfllx_zxybstyp  USING    pv_zfllx  TYPE zreta002-zfllx
                                         pv_zxybstyp TYPE zreta002-zxybstyp.
   CLEAR sy-subrc.
   SELECT SINGLE zfllx  FROM zretc009 WHERE zfllx = @pv_zfllx AND zxybstyp = @pv_zxybstyp INTO @DATA(lv_zfllx).


 ENDFORM.

 FORM frm_check_zfldfsj  USING    pv_zfldfsj .
   DATA:
         lv_zfldfsj TYPE zretcm04-zfldfsj.
   CLEAR sy-subrc.
   SELECT SINGLE zfldfsj  INTO lv_zfldfsj FROM zretcm04 WHERE zfldfsj = pv_zfldfsj .

 ENDFORM.

 FORM frm_set_screen_attr  USING    pt_data_scn TYPE zrei0024
                          CHANGING ps_screen TYPE screen.

   READ TABLE pt_data_scn INTO DATA(ls_data_scn) WITH KEY zfdid = ps_screen-name.
   IF sy-subrc EQ 0 AND ls_data_scn-zfdatb IS NOT INITIAL.
     CASE ls_data_scn-zfdatb.
       WHEN '4'.
         ps_screen-active    = 0.        "隐藏
         ps_screen-input     = 0.
         ps_screen-invisible = 1.
       WHEN '3'.
         ps_screen-input     = 0.        "显示
         ps_screen-active    = 1.
         ps_screen-invisible = 0.
       WHEN '2'.
         ps_screen-required  = 1.        "必输
         ps_screen-active    = 1.
         ps_screen-invisible = 0.
       WHEN '1'.
         ps_screen-input     = 1.        "编辑
         ps_screen-active    = 1.
         ps_screen-invisible = 0.
       WHEN OTHERS.
     ENDCASE.

   ENDIF.

 ENDFORM.

 FORM frm_set_screen_attr_col  USING    pt_data_scn TYPE zrei0024
                                        pv_name TYPE screen-name.

   READ TABLE pt_data_scn INTO DATA(ls_data_scn) WITH KEY zfdid = pv_name
                                                          zfdatb = '4'.

 ENDFORM.

 FORM frm_get_data_screen_attr USING ps_data_scn_ctrl TYPE zres0056
                                 CHANGING pt_data_screen TYPE zrei0024.

   CALL FUNCTION 'ZREFM0020'
     EXPORTING
       is_zres0056 = ps_data_scn_ctrl
* IMPORTING
*      EV_MTYPE    =
*      EV_MSG      =
     TABLES
       et_data     = pt_data_screen.

 ENDFORM.

 FORM frm_get_data_screen_attr_s USING ps_data_scn_ctrl TYPE zres0056
                                       pv_zfdid TYPE zres0040-zfdid
                                 CHANGING pt_data_screen TYPE zrei0024.

   CALL FUNCTION 'ZREFM0020'
     EXPORTING
       is_zres0056 = ps_data_scn_ctrl
       iv_zfdid    = pv_zfdid
* IMPORTING
*      EV_MTYPE    =
*      EV_MSG      =
     TABLES
       et_data     = pt_data_screen.

 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZFLG_TYPE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_DATA_BASE_ZXYBSTYP
*&      <-- PS_DATA_BASE_ZFLG_TYPE
*&---------------------------------------------------------------------*
 FORM frm_set_zflg_type  USING    pv_zxybstyp  TYPE zreta002-zxybstyp
                         CHANGING pv_zflg_type TYPE char2.

   CASE pv_zxybstyp.
     WHEN 'V' OR 'A'.
       pv_zflg_type = 'JS'.
     WHEN 'F' OR 'Q' .
       pv_zflg_type = 'GD'.
     WHEN 'T' .
       pv_zflg_type = 'CX'.
     WHEN 'P' .
       pv_zflg_type = 'PX'.   "促销返利
     WHEN OTHERS.
   ENDCASE.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZPAYTP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TC02_ZFLZFF
*&      <-- GS_TC02_ZPAYTP
*&---------------------------------------------------------------------*
 FORM frm_set_zpaytp  USING    pv_zflzff TYPE zretc002-zflzff
                      CHANGING pv_zpaytp TYPE zretc002-zpaytp.


   DATA: lv_flg TYPE char1..

   PERFORM frm_get_zzff_attr USING pv_zflzff CHANGING lv_flg.

   IF lv_flg = 'E'.
     CLEAR pv_zpaytp.
   ELSEIF lv_flg = 'I'.
     IF pv_zpaytp IS INITIAL.
       pv_zpaytp = 'B'.
     ENDIF.
   ENDIF.
 ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_ZJSZQ
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZHSTYPE
*&      --> GS_TA01_ZHSZQ
*&      <-- GS_TA01_ZJSZQ
*&---------------------------------------------------------------------*
 FORM frm_set_zjszq  USING    pv_zhstype TYPE zreta001-zhstype
                              pv_zhszq TYPE zreta001-zhszq
                     CHANGING pv_zjszq TYPE zreta001-zjszq.

   IF pv_zhstype = 'A'.
     pv_zjszq = '12M'.
   ELSEIF pv_zhstype = 'B'.
     pv_zjszq = pv_zhszq.
   ENDIF.

 ENDFORM.

 FORM frm_set_mid_zspz_id  USING  pv_zxybstyp TYPE zreta002-zxybstyp.

   IF pv_zxybstyp = 'P'.
     SET PARAMETER ID 'VL' FIELD 'P'.
   ELSE.
     SET PARAMETER ID 'VL' FIELD ''.
   ENDIF.
 ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_ZUSAGE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZXYBSTYP
*&      <-- LV_ZUSAGE
*&---------------------------------------------------------------------*
 FORM frm_set_zusage  USING    pv_zxybstyp TYPE zreta002-zxybstyp
                      CHANGING pv_zusage TYPE zret0009-zusage.

   CLEAR pv_zusage.

   IF pv_zxybstyp = 'P'. pv_zusage = 'P' . ELSE . pv_zusage = '' .  ENDIF.
 ENDFORM.


 FORM frm_author_check_ht_new  USING    ps_ta01  TYPE zreta001
                                    pv_actvt TYPE activ_auth
                                    pv_flg   TYPE char1
                           CHANGING pt_msglist TYPE scp1_general_errors
                                    pv_mtype TYPE bapi_mtype
                                    pv_msg   TYPE scp1_general_error-msgv1.


   IF ps_ta01-zhtlx IS NOT INITIAL.
     PERFORM frm_author_check_zhtlx USING ps_ta01-zhtlx
                                          pv_actvt
                                    CHANGING pv_mtype
                                             pv_msg.

   ENDIF.

   IF pv_mtype = 'E'.
     PERFORM frm_author_pro USING pv_mtype
                                  pv_msg
                                  pv_flg
                            CHANGING pt_msglist.
     RETURN.
   ENDIF.


*****  只有审批才需要检查合同主体权限
****  IF gv_flg_rb = '04'.
****    IF ps_ta01-zbukrs IS NOT INITIAL. .
****
****      PERFORM frm_author_check_zbukrs USING ps_ta01-zbukrs
****                                           pv_actvt
****                                     CHANGING pv_mtype
****                                              pv_msg.
****    ENDIF.
****
****  ENDIF.

   IF pv_actvt NE '03'.
     PERFORM frm_author_check_zbukrs    USING ps_ta01-zbukrs
                                              pv_actvt
                                         CHANGING
                                               pv_mtype
                                               pv_msg.

   ENDIF.


   IF pv_mtype = 'E'.
     PERFORM frm_author_pro USING pv_mtype
                                  pv_msg
                                  pv_flg
                            CHANGING pt_msglist.
     RETURN.
   ENDIF.

*----------------------------------------------------------------------*
*BEGIN DELETE BY XYLIU1   15.12.2021 10:32:31
*<NOTES> ERP-12735 合同采购组权限不再校验
*<DEL CODES>
*   IF ps_ta01-ekgrp IS NOT INITIAL.
*     PERFORM frm_author_check_ekgrp USING ps_ta01-ekgrp
*                                          pv_actvt
*                                    CHANGING pv_mtype
*                                             pv_msg.
*
*   ENDIF.
*
*   IF pv_mtype = 'E'.
*     PERFORM frm_author_pro USING pv_mtype
*                                  pv_msg
*                                  pv_flg
*                            CHANGING pt_msglist.
*     RETURN.
*   ENDIF.
*</DEL CODES>
*END DELETE BY XYLIU1
*----------------------------------------------------------------------*


 ENDFORM.


 FORM frm_author_check_tk_new  USING    ps_ta02  TYPE zreta002
                                    pv_actvt TYPE activ_auth
                                    pv_flg   TYPE char1
                           CHANGING pt_msglist TYPE scp1_general_errors
                                    pv_mtype TYPE bapi_mtype
                                    pv_msg   TYPE scp1_general_error-msgv1.

   IF ps_ta02-ekgrp IS NOT INITIAL.

     PERFORM frm_author_check_ekgrp USING ps_ta02-ekgrp
                                          pv_actvt
                                    CHANGING
                                          pv_mtype
                                          pv_msg.


     IF pv_mtype = 'E'.
       PERFORM frm_author_pro USING pv_mtype
                                    pv_msg
                                    pv_flg
                              CHANGING pt_msglist.
       RETURN.
     ENDIF.

   ENDIF.

 ENDFORM.

 FORM frm_author_check_xy_new  USING    ps_t06  TYPE zret0006
                                    pv_actvt TYPE activ_auth
                                    pv_flg   TYPE char1
                           CHANGING pt_msglist TYPE scp1_general_errors
                                    pv_mtype TYPE bapi_mtype
                                    pv_msg   TYPE scp1_general_error-msgv1.

   IF ps_t06-zbukrs IS NOT INITIAL.

     PERFORM frm_author_check_zbukrs_tk USING ps_t06-zbukrs
                                          pv_actvt
                                    CHANGING
                                          pv_mtype
                                          pv_msg.

     IF pv_mtype = 'E'.
       PERFORM frm_author_pro USING pv_mtype
                                    pv_msg
                                    pv_flg
                              CHANGING pt_msglist.
       RETURN.
     ENDIF.

   ENDIF.

 ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_ZCS
*&---------------------------------------------------------------------*
*& 更新模板的使用次数
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
 FORM frm_update_zcs .

   DATA:lv_job_name TYPE tbtcjob-jobname.
   DATA:lv_guid32 TYPE guid_32 .


   CALL FUNCTION '/SAPSLL/GUID_CREATE'
     IMPORTING
       ev_guid_32 = lv_guid32.

   lv_job_name = sy-cprog && '_' && lv_guid32+28(4) && sy-datum && sy-uzeit.


   CALL FUNCTION 'ZREFM0024'
     STARTING NEW TASK lv_job_name.

 ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_QJ_EXE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_T15
*&      --> LT_T16
*&      --> LT_T06
*&---------------------------------------------------------------------*
 FORM frm_pro_data_qj_exe  TABLES   pt_t15 STRUCTURE zret0015
                                    pt_t16 STRUCTURE zret0016
                                    pt_t06 STRUCTURE zret0006.



   DATA:
     lt_zqj TYPE TABLE OF zres0089,
     ls_t15 TYPE zret0015,
     ls_t16 TYPE zret0016.

   DATA:
     lv_num     TYPE i,
     lv_flg_new TYPE char1.

   CLEAR:
          pt_t15, pt_t16.

   LOOP AT pt_t06 INTO DATA(ls_t06).

*    核算期间
     CLEAR lt_zqj.

*    若是月初第一天走原先的逻辑，否则新逻辑 整月处理
     CLEAR lv_flg_new.
*    逻辑暂时还原，
     IF ls_t06-zbegin+6(2) = '01'.
       lv_flg_new = ''.
     ELSE.
       lv_flg_new = 'X'.
     ENDIF.

     PERFORM frm_set_zqj_new   TABLES lt_zqj
                                USING ls_t06
                                      'ZHSQJ'
                                      lv_flg_new.

     CLEAR lv_num.
     LOOP AT lt_zqj INTO DATA(ls_zqj).
       CLEAR ls_t15.
       lv_num            = lv_num + 1.
       ls_t15-zhsqj_id   = lv_num.
       ls_t15-zxy_id = ls_t06-zxy_id.
       ls_t15-zbegin = ls_zqj-zbegin.
       ls_t15-zend = ls_zqj-zend.
       APPEND ls_t15 TO pt_t15.
     ENDLOOP.


*    结算期间
     CLEAR lt_zqj.

     PERFORM frm_set_zqj_new   TABLES lt_zqj
                                USING ls_t06
                                      'ZJSQJ'
                                      lv_flg_new.


     CLEAR lv_num.
     LOOP AT lt_zqj INTO ls_zqj.
       CLEAR ls_t16.
       lv_num            = lv_num + 1.
       ls_t16-zjsqj_id   = lv_num.
       ls_t16-zxy_id = ls_t06-zxy_id.
       ls_t16-zbegin = ls_zqj-zbegin.
       ls_t16-zend = ls_zqj-zend.
       APPEND ls_t16 TO pt_t16.
     ENDLOOP.

   ENDLOOP.

 ENDFORM.


 FORM frm_set_zqj_new TABLES pt_data STRUCTURE zres0089
                     USING         ps_t06 TYPE zret0006
                                   pv_flg  TYPE char10
                                   pv_flg_new TYPE char1.

   DATA:
         ls_data TYPE zres0089.
   DATA:
     lv_date    TYPE d,
     lv_add_mon TYPE t5a4a-dlymo.
   DATA:
         lv_zqs TYPE int4.

   CLEAR pt_data.
   IF pv_flg = 'ZHSQJ'.
     IF ps_t06-zhszq IS INITIAL.
       RETURN.
     ENDIF.

     SELECT SINGLE zhszqs INTO lv_zqs
       FROM zret0005
       WHERE zhszq = ps_t06-zhszq.

   ELSEIF pv_flg = 'ZJSQJ'.
     IF ps_t06-zjszq IS INITIAL.
       RETURN.
     ENDIF.

     SELECT SINGLE zjszqs INTO lv_zqs
       FROM zret0004
       WHERE zjszq = ps_t06-zjszq.
   ENDIF.


*  初始日期等于协议开始日期
   lv_date = ps_t06-zbegin.
   lv_add_mon = lv_zqs.

   IF ps_t06-zhstype = 'B' . " OR pv_flg = 'ZJSQJ'.

     DO  .

       CLEAR ls_data.
       ls_data-zbegin = lv_date.

       CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
         EXPORTING
           date      = lv_date
           days      = 0
           months    = lv_add_mon
*          SIGNUM    = U_SIGN
           years     = 0
         IMPORTING
           calc_date = ls_data-zend.

       ls_data-zend = ls_data-zend - 1.

*      整月处理
*     连续期间的 开始日期是动态变动的，因此不能仅使用 PV_FLG_NEW标识判断 ，还需要判断开始日期是否是1号
       IF pv_flg_new = 'X' AND ls_data-zbegin+6(2) NE '01'.
         PERFORM frm_set_last_day CHANGING ls_data-zend.
       ENDIF.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
       IF ls_data-zend >= ps_t06-zend.
         ls_data-zend = ps_t06-zend.
         APPEND ls_data TO pt_data.
         EXIT.
       ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
       lv_date = ls_data-zend + 1.

       CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
         EXPORTING
           date      = lv_date
           days      = 0
           months    = lv_add_mon
*          SIGNUM    = U_SIGN
           years     = 0
         IMPORTING
           calc_date = lv_date.
       lv_date = lv_date - 1.

*      整月处理(下次循环的结束日期）
*     连续期间的 开始日期是动态变动的，因此不能仅使用 PV_FLG_NEW标识判断 ，还需要判断开始日期是否是1号
       IF pv_flg_new = 'X' AND ls_data-zbegin+6(2) NE '01'.
         PERFORM frm_set_last_day CHANGING lv_date.
       ENDIF.


       IF lv_date > ps_t06-zend.
         ls_data-zend = ps_t06-zend.
         APPEND ls_data TO pt_data.
         EXIT.
       ELSE.
         APPEND ls_data TO pt_data.
*        下一次循环的开始日期等于本次循环结束日期 + 1
         lv_date = ls_data-zend + 1 .
       ENDIF.

*      防止死循环 跳出
       IF sy-index > 1000.
         EXIT.
       ENDIF.
     ENDDO.


   ELSE.

     DO  .

       CLEAR ls_data.
       ls_data-zbegin = ps_t06-zbegin.

       CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
         EXPORTING
           date      = ls_data-zbegin
           days      = 0
           months    = lv_add_mon
*          SIGNUM    = 0
           years     = 0
         IMPORTING
           calc_date = ls_data-zend.
       ls_data-zend = ls_data-zend - 1.

*      整月处理
       IF pv_flg_new = 'X' .
         PERFORM frm_set_last_day CHANGING ls_data-zend.
       ENDIF.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
       IF ls_data-zend >= ps_t06-zend.
         ls_data-zend = ps_t06-zend.
         APPEND ls_data TO pt_data.
         EXIT.
       ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
       lv_add_mon = lv_add_mon + lv_zqs.

       CLEAR:
             lv_date.
       CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
         EXPORTING
           date      = ls_data-zbegin
           days      = 0
           months    = lv_add_mon
*          SIGNUM    = U_SIGN
           years     = 0
         IMPORTING
           calc_date = lv_date.

       lv_date = lv_date - 1.

*      整月处理(下次循环的结束日期）
       IF pv_flg_new = 'X'.
         PERFORM frm_set_last_day CHANGING lv_date.
       ENDIF.

       IF lv_date > ps_t06-zend.
         ls_data-zend = ps_t06-zend.
         APPEND ls_data TO pt_data.
         EXIT.
       ELSE.
         APPEND ls_data TO pt_data..
       ENDIF.

*      防止死循环 跳出
       IF sy-index > 1000.
         EXIT.
       ENDIF.
     ENDDO.



   ENDIF.

 ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_LAST_DAY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_DATA_ZEND
*&---------------------------------------------------------------------*
 FORM frm_set_last_day  CHANGING pv_date TYPE d.

   DATA:
         lv_date_tmp TYPE d.
   CLEAR lv_date_tmp.
   lv_date_tmp = pv_date.
   lv_date_tmp = lv_date_tmp + 32.
   lv_date_tmp+6(2) = '01'.
   lv_date_tmp = lv_date_tmp - 1.
   pv_date = lv_date_tmp.   "结束日期的最后一天
 ENDFORM.


 FORM frm_get_kolnr   CHANGING    ps_approval    TYPE  zres0088
                                   pt_msglist    TYPE scp1_general_errors.


   DATA:lt_zretc007  TYPE TABLE OF zretc007.
   DATA:ls_zretc007  TYPE zretc007.
   DATA:ls_msglist   TYPE scp1_general_error.
   DATA:lv_nextline  TYPE sy-tabix.
   DATA:lv_upzttline TYPE zretc007-zstats.

   CLEAR:ls_msglist.
   ls_msglist-msgty = 'E'.
   ls_msglist-msgid = '00'.
   ls_msglist-msgno = '001'.


   SELECT * INTO TABLE lt_zretc007 FROM zretc007 WHERE frgsx = ps_approval-frgsx.
   IF sy-subrc <> 0.
     ls_msglist-msgv1 = '审批策略未找到审批策略明细！'.
     APPEND ls_msglist TO pt_msglist.
     EXIT.
   ENDIF.

   SORT lt_zretc007 BY kolnr .
   IF  ps_approval-kolnr IS INITIAL .
     READ TABLE lt_zretc007 INTO ls_zretc007 INDEX 1.
     IF sy-subrc = 0 .
       ps_approval-kolnr     = ls_zretc007-kolnr.
       ps_approval-frgc1     = ls_zretc007-frgc1.
       ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
       ps_approval-zxyzt     = 'N'.
     ENDIF.
   ELSE.
     READ TABLE lt_zretc007 INTO ls_zretc007   WITH  KEY kolnr = ps_approval-kolnr.
     IF sy-subrc = 0.
       IF ls_zretc007-zstats = 'A' .
         ps_approval-kolnr     = ls_zretc007-kolnr.
         ps_approval-frgc1     = ls_zretc007-frgc1.
         ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
         ps_approval-zxyzt     = ls_zretc007-zstats.
       ELSE.
         lv_upzttline = ls_zretc007-zstats.
         lv_nextline  =  sy-tabix + 1.


         READ TABLE lt_zretc007 INTO ls_zretc007 INDEX lv_nextline.
         IF sy-subrc <> 0 .
           ls_msglist-msgv1 = '最终审批节点未维护，保持当前审批状态！'.
           APPEND ls_msglist TO pt_msglist.
           EXIT.
         ELSE.
           ps_approval-kolnr     = ls_zretc007-kolnr.
           ps_approval-frgc1     = ls_zretc007-frgc1.
           ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
           ps_approval-zxyzt     = lv_upzttline.
         ENDIF.
       ENDIF.
     ELSE.
       ls_msglist-msgv1 = '当前审批节点未知，请修改后重新保存！'.
       APPEND ls_msglist TO pt_msglist.
       EXIT.
     ENDIF.


   ENDIF.
 ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_BUKRS_X
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_ZRETCM05
*&      <-- LS_EXCEL_BUKRS_X
*&---------------------------------------------------------------------*
 FORM frm_get_bukrs_x  TABLES   pt_zretcm05 STRUCTURE zretcm05
                       CHANGING pv_bukrs_x.

   CLEAR pv_bukrs_x.
   LOOP AT pt_zretcm05 INTO DATA(ls_zretcm05).
     IF pv_bukrs_x IS INITIAL.
       pv_bukrs_x = ls_zretcm05-bukrs.
     ELSE.
       pv_bukrs_x = |{ pv_bukrs_x  }/{ ls_zretcm05-bukrs }|.
     ENDIF.
   ENDLOOP.

 ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHARG_VLS_ZFLLX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_CHARG_VLS_ZFLLX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZHT_ID
*&      <-- GT_VLS_ZFLLX
*&---------------------------------------------------------------------*
 FORM frm_charg_vls_zfllx USING pv_zht_id
                          CHANGING pt_vls_zfllx TYPE vrm_values  .

   CHECK pv_zht_id IS NOT INITIAL.

   SELECT SINGLE zsfsx
     INTO @DATA(lv_zsfsx)
     FROM zretcm13
    INNER JOIN zreta001 ON zreta001~zhtlx = zretcm13~zhtlx
     WHERE zht_id = @pv_zht_id.

   SELECT *
     INTO TABLE @DATA(lt_zretcm17)
     FROM zretcm17
     WHERE zsfsx = @lv_zsfsx.

   LOOP AT pt_vls_zfllx INTO DATA(ls_vls_zfllx) .
     READ TABLE lt_zretcm17 TRANSPORTING NO FIELDS WITH KEY zfllx = ls_vls_zfllx-key.
     IF sy-subrc <> 0.
       DELETE pt_vls_zfllx.
     ENDIF.
   ENDLOOP.
 ENDFORM.