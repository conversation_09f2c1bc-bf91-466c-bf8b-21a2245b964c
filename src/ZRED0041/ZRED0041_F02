*&---------------------------------------------------------------------*
*& 包含               ZRED0035_F02
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_SET_TITLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_TITLE
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_LIST_BOX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_VLS_ZFLLX
*&      <-- GT_VLS_ZJSFF
*&      <-- GT_VLS_ZHSZQ
*&      <-- GT_VLS_ZJSZQ
*&      <-- GT_VLS_ZHSJZ
*&      <-- GT_VLS_ZHTLX
*&---------------------------------------------------------------------*
FORM frm_get_data_list_box  CHANGING pt_vls_zfllx   TYPE vrm_values
                                     pt_vls_zatkrl   TYPE vrm_values
                                     pt_vls_zctgr   TYPE vrm_values
*                                     pt_vls_zjsff   TYPE vrm_values
                                     pt_vls_zhszq   TYPE vrm_values
                                     pt_vls_zjszq   TYPE vrm_values
                                     pt_vls_zhsjz   TYPE vrm_values
                                     pt_vls_zhtlx   TYPE vrm_values
                                     pt_vls_zxybstyp   TYPE vrm_values
                                     pt_vls_zatktp   TYPE vrm_values
                                     pt_vls_zfldfsj   TYPE vrm_values.

  SELECT
    zfllx  AS  key ,
    zfllx_txt      AS  text,
    zpxid        AS  zpxid
    FROM zret0002
    INTO TABLE @DATA(pt_vls_zfllx_f).

  SORT pt_vls_zfllx_f BY zpxid key.

  MOVE-CORRESPONDING pt_vls_zfllx_f TO pt_vls_zfllx.

  SELECT
    zhszq  AS  key ,
    zhszq_txt      AS  text
    FROM zret0005
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhszq.

  SELECT
    zjszq  AS  key ,
    zjszq_txt      AS  text
    FROM zret0004
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjszq.

  SELECT
    zhsjz  AS  key ,
    zhsjz_txt      AS  text
    FROM zret0003
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhsjz.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZREM_ZHTLX'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhtlx.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZRED_ZATKRL'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zatkrl.

*  SELECT
*    domvalue_l  AS  key ,
*    ddtext      AS  text
*    FROM dd07t
*    WHERE domname = 'ZREM_ZJSFF'
*    AND   ddlanguage = @sy-langu
*    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjsff.
*
*  DELETE pt_vls_zjsff WHERE key = 'T'.


  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZRED_ZCTGR'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zctgr.


  SELECT
    a~zxybstyp  AS  key ,
    b~ddtext      AS  text
    FROM zretc009 AS a JOIN dd07t AS b
                         ON a~zxybstyp = b~domvalue_l
                        AND b~domname = 'ZREM_ZXYBSTYP'
                        AND b~ddlanguage = @sy-langu
*    WHERE a~zfllx = @p_zfllx
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zxybstyp.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZRED_ZATKTP'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zatktp.

  DELETE pt_vls_zatktp WHERE key = ''.


  SELECT
    zfldfsj  AS  key ,
    zfldfsj_t      AS  text
    FROM zretcm04
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zfldfsj.


ENDFORM.



*FORM frm_author_check_zhtlx  USING    pv_zhtlx TYPE zret0006-zhtlx
*                                       pv_actvt TYPE activ_auth
*                             CHANGING  pv_flg_err TYPE char1.
*
*  CLEAR pv_flg_err.
*
*  AUTHORITY-CHECK OBJECT 'ZREAR002'
*                      ID 'ZHTLX' FIELD pv_zhtlx
*                      ID 'ACTVT' FIELD pv_actvt.
*  IF sy-subrc NE 0.
*    pv_flg_err = 'E'.
*  ELSE.
*    pv_flg_err = 'S'.
*  ENDIF.
*
*ENDFORM.
*
*FORM frm_author_check_zbukrs  USING    pv_zbukrs TYPE zreta001-zbukrs
*                                       pv_actvt TYPE activ_auth
*                             CHANGING  pv_flg_err TYPE char1.
*
*  CLEAR pv_flg_err.
*
*  AUTHORITY-CHECK OBJECT 'ZMMAR005'
*                      ID 'BUKRS' FIELD pv_zbukrs
*                      ID 'ACTVT' FIELD pv_actvt.
*  IF sy-subrc NE 0.
*    pv_flg_err = 'E'.
*  ELSE.
*    pv_flg_err = 'S'.
*  ENDIF.
*
*ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZFLSQF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZBUKRS
*&---------------------------------------------------------------------*
FORM frm_check_zflsqf  USING    pv_zflsqf TYPE zret0006-zflsqf.

  IF pv_zflsqf IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '合同主体必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.
    SELECT COUNT(*)
      FROM t001
      WHERE bukrs = pv_zflsqf.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '公司代码不存在' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.

ENDFORM.



*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text 获取条款信息
*&---------------------------------------------------------------------*
*&      --> P_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_get_data  USING    ps_data_base TYPE ty_data_base
                   CHANGING
                            ps_ta02 TYPE LINE OF tt_ta02
                            pt_t13  TYPE tt_t13
                            pt_t44  TYPE tt_t44
                            pt_ta03 TYPE  tt_ta03
                            pt_ta05 TYPE  tt_ta05
                            pt_ta06 TYPE  tt_ta06
                            pt_t58  TYPE  tt_t58
  .

  SELECT SINGLE
    *
    INTO CORRESPONDING FIELDS OF ps_ta02
    FROM zreta002
    WHERE ztk_id = ps_data_base-ztk_id.

  IF ps_ta02 IS  INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有对应的数据！' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  SELECT SINGLE frgc1 zfrgtx INTO (ps_ta02-frgc1,ps_ta02-zfrgtx) FROM zretc007 WHERE frgsx = ps_ta02-frgsx AND kolnr = ps_ta02-kolnr .



  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_t13 FROM zret0013 WHERE zxy_id = ps_ta02-ztk_id.
  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_t44 FROM zret0044 WHERE zxy_id = ps_ta02-ztk_id.
  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_ta03 FROM zreta003 WHERE ztk_id = ps_ta02-ztk_id.
  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_ta05 FROM zreta005 WHERE ztk_id = ps_ta02-ztk_id.
  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_ta06 FROM zreta006 WHERE ztk_id = ps_ta02-ztk_id.
  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_t58  FROM zret0058 WHERE ztk_id = ps_ta02-ztk_id.


  LOOP AT pt_t44 INTO DATA(ls_t44).
    PERFORM frm_get_zghf_t USING ls_t44-zflzff CHANGING ls_t44-name1.
    MODIFY pt_t44 FROM ls_t44.
  ENDLOOP.

  LOOP AT pt_ta05 INTO DATA(ls_ta05).
*    获取KEY值
    PERFORM frm_get_zitems_key CHANGING ls_ta05-zitems_key gv_zitem_key_cx.
    MODIFY pt_ta05 FROM ls_ta05.
  ENDLOOP.

  LOOP AT pt_t58 INTO DATA(ls_t58).
*    获取KEY值
    READ TABLE pt_ta05 INTO ls_ta05 WITH KEY zitems = ls_t58-zitems .
    IF sy-subrc EQ 0.
      ls_t58-zitems_key = ls_ta05-zitems_key.
    ENDIF.
    MODIFY pt_t58 FROM ls_t58.
  ENDLOOP.

  SORT pt_ta06 BY zdates ASCENDING.
ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text 获取条款信息
*&---------------------------------------------------------------------*
*&      --> P_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_get_editor_text  TABLES pt_edittab LIKE gt_edittab
                          USING  ps_ta02     TYPE LINE OF tt_ta02
                                 pt_t76      TYPE tt_t76.

  DATA: lt_zret0076  TYPE TABLE OF zret0076 .
  DATA: ls_zret0076  LIKE LINE OF lt_zret0076 .
  DATA: ls_edittab   LIKE LINE OF gt_edittab .

  REFRESH: pt_edittab,pt_t76.
  SELECT * INTO TABLE lt_zret0076 FROM zret0076 WHERE ztk_id = ps_ta02-ztk_id.

  SORT lt_zret0076 DESCENDING BY erdat ertim  .

  READ TABLE lt_zret0076  INTO ls_zret0076  INDEX 1.
  IF sy-subrc = 0 AND ls_zret0076-zxyzt <> 'A'.
    APPEND ls_zret0076 TO pt_t76.
    IF ls_zret0076-ztext1 IS NOT INITIAL.
      ls_edittab =  ls_zret0076-ztext1+0(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext1+40(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext1+80(40).
      APPEND ls_edittab TO pt_edittab.
    ENDIF.

    IF ls_zret0076-ztext2 IS NOT INITIAL.
      ls_edittab =  ls_zret0076-ztext2+0(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext2+40(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext2+80(40).
      APPEND ls_edittab TO pt_edittab.
    ENDIF.

    IF ls_zret0076-ztext1 IS NOT INITIAL.
      ls_edittab =  ls_zret0076-ztext3+0(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext3+40(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext3+80(40).
      APPEND ls_edittab TO pt_edittab.
    ENDIF.

    IF ls_zret0076-ztext1 IS NOT INITIAL.
      ls_edittab =  ls_zret0076-ztext4+0(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext4+40(40).
      APPEND ls_edittab TO pt_edittab.
      ls_edittab =  ls_zret0076-ztext4+80(40).
      APPEND ls_edittab TO pt_edittab.
    ENDIF.
  ENDIF.

ENDFORM.

FORM frm_pro_data     USING ps_data_base TYPE ty_data_base
                            pt_tc02       TYPE tt_tc02
                      CHANGING
                            ps_ta01 TYPE LINE OF tt_ta01
                            ps_ta02 TYPE LINE OF tt_ta02
                            pt_t20  TYPE tt_t20
                            pt_t09  TYPE tt_t09
                            pt_tc04  TYPE tt_tc04
                            pt_tc05  TYPE tt_tc05
                            ps_tc05  TYPE LINE OF tt_tc05
                            pt_t44_all  TYPE tt_t44
                            pt_t11_all  TYPE tt_t11
                            pt_t11  TYPE tt_t11
                            pt_t12  TYPE tt_t12
*                            pt_t13  TYPE tt_t13
*                            pt_t44  TYPE tt_t44
*                            pt_ta03        TYPE  tt_ta03
                            pt_ta04        TYPE  tt_ta04
                            pt_zzlbm       TYPE  tt_t81
                            pt_zqdbm       TYPE  tt_t82.
*                            pt_zzgys       TYPE  tt_t84
  .


  SELECT SINGLE * INTO CORRESPONDING FIELDS OF ps_ta01 FROM zreta001 WHERE zht_id  = ps_ta02-zht_id.

  PERFORM frm_set_zbpname  USING ps_ta01-zbpcode ps_ta01-zbptype CHANGING ps_ta01-zbpname.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_t09
    FROM zret0009
    WHERE zht_id = ps_ta02-zht_id.

  IF pt_t09[] IS NOT INITIAL.
    SELECT
      a~*
      FROM @pt_t09 AS i JOIN zret0020 AS a
                          ON i~zspz_id = a~zspz_id
      INTO CORRESPONDING FIELDS OF TABLE @pt_t20.

  ENDIF.

  IF pt_tc02[] IS NOT INITIAL.
    SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_t12  FROM zret0012  FOR ALL ENTRIES IN pt_tc02 WHERE zxy_id = pt_tc02-zxy_id.
    SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_ta04 FROM zreta004  FOR ALL ENTRIES IN pt_tc02 WHERE zxy_id = pt_tc02-zxy_id AND zxy_id NE ''.
    LOOP AT pt_ta04 INTO DATA(ls_ta04).
*      SELECT SINGLE ztk_id INTO ls_ta04-ztk_id_plus FROM zret0006 WHERE ztk_id = ls_ta04-zrlid .
      SELECT SINGLE ztk_id INTO ls_ta04-ztk_id_plus FROM zret0006 WHERE zxy_id = ls_ta04-zrlid .  "取附加协议对应的附加条款

*      ls_ta04-zitems_key = ls_ta04-zitems.  %%%%%
      READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zxy_id = ls_ta04-zxy_id.
      IF sy-subrc EQ 0.
        ls_ta04-zitems_key = ls_tc02-zitems_key.
      ENDIF.

*      如果为A类型的附加协议，取的是行项目的协议号，需要记录行项目ID，参考保存时用行项目ID作为KEY 用于获取行项目协议号
      IF ls_ta04-zatktp = 'A'.
        CLEAR ls_tc02.
        READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zxy_id = ls_ta04-zrlid.
        IF sy-subrc EQ 0.
          ls_ta04-zitems_key_zxy_id = ls_tc02-zitems_key.
        ENDIF.

      ENDIF.

      MODIFY pt_ta04 FROM ls_ta04.
    ENDLOOP.
  ENDIF.

*  PERFORM frm_get_data_tc04 USING pt_tc02
*                            CHANGING pt_tc04.

  PERFORM frm_get_data_tc05 USING ps_ta02
                                  ps_data_base
                          CHANGING pt_tc05
                                   ps_tc05.



  PERFORM frm_get_data_t06 USING    pt_tc02
                                    ps_ta02
                           CHANGING
                                    pt_t44_all
                                    pt_t11_all
                                    pt_t11.

  PERFORM frm_get_data_zlqd USING   pt_tc02
                                    ps_ta02
                           CHANGING pt_zzlbm
                                    pt_zqdbm.
*                                    pt_zzgys.



ENDFORM.

FORM frm_pro_data_ref  CHANGING
                            ps_ta02 TYPE LINE OF tt_ta02
                            pt_tc02 TYPE  tt_tc02
                            pt_ta03 TYPE  tt_ta03
                            pt_ta04 TYPE  tt_ta04
                            pt_ta06 TYPE  tt_ta06
                            pt_t11_all  TYPE tt_t11
                            pt_matnr TYPE tt_matnr
                            pt_zzgys TYPE tt_t84
  .


  CLEAR:
        ps_ta02-ztk_id,
        ps_ta02-zjt_id,
        ps_ta02-zcjrq,
        ps_ta02-zcjsj,
        ps_ta02-zcjr,
        ps_ta02-zxgrq,
        ps_ta02-zxgsj,
        ps_ta02-zxgr,
        ps_ta02-zxyzt,
        ps_ta02-frgsx,
        ps_ta02-kolnr.


  DATA(lt_t11_all_tmp) = pt_t11_all[].
  SORT lt_t11_all_tmp BY zjt_id.
  DELETE ADJACENT DUPLICATES FROM lt_t11_all_tmp COMPARING zjt_id.

  LOOP AT lt_t11_all_tmp INTO DATA(ls_t11_tmp).

    PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING ls_t11_tmp-zjt_id_tmp.
    MODIFY lt_t11_all_tmp FROM ls_t11_tmp.
  ENDLOOP.

  SORT lt_t11_all_tmp BY zjt_id.
  LOOP AT pt_t11_all INTO DATA(ls_t11_all).
    READ TABLE lt_t11_all_tmp INTO ls_t11_tmp WITH KEY zjt_id = ls_t11_all-zjt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t11_all-zjt_id = ls_t11_tmp-zjt_id_tmp.
    ENDIF.
    MODIFY pt_t11_all FROM ls_t11_all.
  ENDLOOP.


  LOOP AT pt_tc02 INTO DATA(ls_tc02).
    CLEAR ls_tc02-zxy_id.
    READ TABLE lt_t11_all_tmp INTO ls_t11_tmp WITH KEY zjt_id = ls_tc02-zjt_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_tc02-zjt_id = ls_t11_tmp-zjt_id_tmp.
    ENDIF.
    MODIFY pt_tc02 FROM ls_tc02.
  ENDLOOP.


  LOOP AT pt_ta03 INTO DATA(ls_ta03).
    CLEAR ls_ta03-ztk_id.
    MODIFY pt_ta03 FROM ls_ta03.
  ENDLOOP.

  LOOP AT pt_ta04 INTO DATA(ls_ta04).
    CLEAR ls_ta04-zxy_id.

*    复制时 条款类型A时 取当前界面的协议，故需要清空，非A的话取附加条款对应的协议，不能清空
    IF ls_ta04-zatktp = 'A'.
      CLEAR ls_ta04-zrlid.
    ENDIF.
    MODIFY pt_ta04 FROM ls_ta04.
  ENDLOOP.

  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    CLEAR ls_ta06-ztk_id.
    MODIFY pt_ta06 FROM ls_ta06.
  ENDLOOP.

  LOOP AT pt_matnr INTO DATA(ls_matnr).
    CLEAR ls_matnr-zxy_id.
    MODIFY pt_matnr FROM ls_matnr.
  ENDLOOP.

  LOOP AT pt_zzgys INTO DATA(ls_zzgys).
    CLEAR ls_zzgys-zxy_id.
    MODIFY pt_zzgys FROM ls_zzgys.
  ENDLOOP.

*
*  LOOP AT pt_tc01 INTO DATA(ls_tc01).
*    ls_tc01-ztmpid_bak = ls_tc01-ztmpid.
*    CLEAR ls_tc01-ztmpid.
*    MODIFY pt_tc01 FROM ls_tc01.
*  ENDLOOP.
*
*  LOOP AT pt_tc02 INTO DATA(ls_tc02).
*    ls_tc02-ztmpid_bak = ls_tc02-ztmpid.
*    CLEAR ls_tc02-ztmpid.
*    MODIFY pt_tc02 FROM ls_tc02.
*  ENDLOOP.
*
*  LOOP AT pt_tc03 INTO DATA(ls_tc03).
*    ls_tc03-ztmpid_bak = ls_tc03-ztmpid.
*    CLEAR ls_tc03-ztmpid.
*    MODIFY pt_tc03 FROM ls_tc03.
*  ENDLOOP.
*
*  LOOP AT pt_tc04 INTO DATA(ls_tc04).
*    ls_tc04-ztmpid_bak = ls_tc04-ztmpid.
*    CLEAR ls_tc04-ztmpid.
*    MODIFY pt_tc04 FROM ls_tc04.
*  ENDLOOP.

ENDFORM.

FORM frm_pro_data_base USING ps_ta02 TYPE LINE OF tt_ta02 CHANGING ps_data_base TYPE ty_data_base.



*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:12:08
*    <NOTES> #####
*    <OLD CODES>
*  SELECT SINGLE zxybstyp
*    FROM    zret0002
*    WHERE   zfllx = @ps_ta02-zfllx
*    INTO    @ps_data_base-zxybstyp.
*
*  SELECT SINGLE zfllx INTO ps_data_base-zfllx FROM zreta002 WHERE ztk_id = ps_data_base-ztk_id.
*    </OLD CODES>
*    <NEW CODES>
  SELECT SINGLE zfllx zxybstyp INTO ( ps_data_base-zfllx , ps_data_base-zxybstyp ) FROM zreta002 WHERE ztk_id = ps_data_base-ztk_id.
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*

*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 14:30:25
*    <NOTES> #####
*    <OLD CODES>
*  CASE ps_data_base-zfllx.
*    WHEN 'RB01' OR 'RB05'.
*      ps_data_base-zflg_type = 'JS'.
*    WHEN 'RB02' OR 'RB03' OR 'RB04'.
*      ps_data_base-zflg_type = 'GD'.
*    WHEN 'RB06' .
*      ps_data_base-zflg_type = 'CX'.
*    WHEN OTHERS.
*  ENDCASE.
*    </OLD CODES>
*    <NEW CODES>
  PERFORM frm_set_zflg_type USING ps_data_base-zxybstyp CHANGING ps_data_base-zflg_type.


*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*

  PERFORM frm_set_sub_screen CHANGING ps_data_base .


  IF ps_ta02-ztk_id IS NOT INITIAL.
    ps_data_base-ztk_id = ps_ta02-ztk_id.
    ps_data_base-ztmpid = ps_ta02-ztmpid.
  ENDIF.

ENDFORM.

FORM frm_set_sub_screen CHANGING ps_data_base TYPE ty_data_base.

  CASE ps_data_base-zflg_type.
    WHEN 'JS' OR 'PX'.
*      ps_data_base-subscreen = '8004'.
      ps_data_base-subscreen = '4000'.
    WHEN 'GD'.
*      ps_data_base-subscreen = '2200'.
      ps_data_base-subscreen = '5000'.
    WHEN 'CX'.
      ps_data_base-subscreen = '6000'.
    WHEN OTHERS.
  ENDCASE.


  IF ps_data_base-zxybstyp = 'P'.
    ps_data_base-sub_4200 = '8007'.
    ps_data_base-sub_2300 = '2500'.
  ELSE.
    ps_data_base-sub_4200 = '8004'.
    ps_data_base-sub_2300 = '2300'.
  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&      <-- GS_TC01
*&      <-- GT_TC02
*&      <-- GT_TC03
*&      <-- GT_TC04_SET
*&      <-- GT_BUKRS_SET
*&      <-- GT_DCWRK_SET
*&      <-- GT_WERKS_SET
*&      <-- GT_EKORG_SET
*&---------------------------------------------------------------------*
FORM frm_check_data_main     USING pv_pass TYPE char1
                           CHANGING pt_msglist   TYPE scp1_general_errors
                                    pv_mtype       TYPE bapi_mtype
                                    pv_msg         TYPE bapi_msg
*                              pv_actvt       TYPE activ_auth
                                    ps_data_base   TYPE ty_data_base
                                    pt_t13        TYPE  tt_t13
                                    ps_ta01        TYPE LINE OF tt_ta01
                                    ps_ta02        TYPE LINE OF tt_ta02
                                    pt_ta03        TYPE  tt_ta03
                                    pt_ta04        TYPE  tt_ta04
                                    pt_ta05        TYPE  tt_ta05
                                    pt_ta06        TYPE  tt_ta06
                                    pt_tc02        TYPE  tt_tc02
                                    ps_tc05        TYPE LINE OF tt_tc05
                                    pt_t11         TYPE  tt_t11
                                    pt_t44         TYPE  tt_t44
                                    pt_t44_all     TYPE  tt_t44
                                    pt_t76         TYPE  tt_t76
                                    pt_tc04        TYPE  tt_tc04
                                    pt_zzlbm       TYPE  tt_t81
                                    pt_zqdbm       TYPE  tt_t82
                                    pt_zzgys       TYPE  tt_t84
                                    pt_bukrs       TYPE  tt_bukrs
                                    pt_dcwrk       TYPE  tt_dcwrk
                                    pt_werks       TYPE  tt_werks
                                    pt_ekorg       TYPE  tt_ekorg
                                    pt_matnr       TYPE  tt_matnr
  .

  DATA: ls_tc02  TYPE LINE OF tt_tc02.
  DATA: lv_msgv1 TYPE scp1_general_error-msgv1.


  IF pv_mtype NE 'E'.
    pv_mtype = 'S'.
  ELSE.
    pv_mtype = 'E'.
  ENDIF.

  REFRESH:pt_msglist.

*  抬头检查
  PERFORM frm_check_inital USING ps_ta02-ztk_txt    '条款描述' CHANGING pt_msglist .
  PERFORM frm_check_inital USING ps_ta02-zpayday    '付款期间' CHANGING pt_msglist .

  IF ps_ta02-zbegin > ps_ta02-zend.
    PERFORM frm_add_msg USING '开始日期不能大于结束日期'  CHANGING pt_msglist.
  ENDIF.

  LOOP AT pt_tc02 INTO ls_tc02.
    IF ls_tc02-zitems IS NOT INITIAL.
      SELECT COUNT(*) FROM @pt_tc02 AS i WHERE  zitems = @ls_tc02-zitems INTO @DATA(lv_count).
      IF lv_count > 1.
        CLEAR lv_count.
        CLEAR lv_msgv1. lv_msgv1 = '行项目号重复，请检查！' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

*  外部支付方和供货方检查
*& ADD BY zsfsx = '2'
  PERFORM frm_check_t44 USING pt_t44
                              ps_ta01-zhtlx
                        CHANGING pt_msglist.

*  商品组检查
  SELECT SINGLE * FROM zret0009 WHERE zspz_id = @ps_ta02-zspz_id AND zspz_id NE '' INTO @DATA(ls_zret0009)  .
  CASE ps_ta02-zxybstyp.
    WHEN 'P'.
      IF ls_zret0009-zusage NE 'P' .
        PERFORM frm_add_msg USING '促销协议只能选择促销商品组'  CHANGING pt_msglist.
      ENDIF.
    WHEN OTHERS.
      IF ls_zret0009-zusage EQ 'P' .
        PERFORM frm_add_msg USING '非促销协议只能选择非促销商品组'  CHANGING pt_msglist.
      ENDIF.
  ENDCASE.

*  只检查勾选生效的数据
  DATA(lt_tc02_tmp)  = pt_tc02.
  DATA(lt_tc02_comp) = pt_tc02.
  DELETE lt_tc02_tmp  WHERE sel_man = ''.
  DELETE lt_tc02_comp WHERE sel_man = ''.

  PERFORM frm_check_tk_repeat   USING ps_ta02
                                      lt_tc02_tmp
                             CHANGING pt_msglist.


*  计算类
  IF ps_data_base-zflg_type EQ 'JS'.

    PERFORM frm_check_public USING lt_tc02_tmp
                                 pt_tc04
                                 pt_dcwrk
                                 pt_werks
                             CHANGING pt_msglist.

    PERFORM frm_check_data_zghf USING lt_tc02_tmp
                                      pt_t13
                                      pt_tc04
                                      ps_tc05
                                      CHANGING pt_msglist .



    IF ps_tc05-zjsff NE 'R'.
      IF pt_t11[] IS INITIAL.
        PERFORM frm_add_msg USING '计算方法不等于R,必须要维护阶梯'  CHANGING pt_msglist.
      ENDIF.
    ENDIF.

*  固定类
  ELSEIF ps_data_base-zflg_type EQ 'GD'.

    LOOP AT lt_tc02_tmp INTO ls_tc02 WHERE zmwskz IS NOT INITIAL .
      IF ls_tc02-zdffs = 'O'.
        IF ls_tc02-zmwskz(1) NE 'J'.
          CLEAR lv_msgv1. lv_msgv1 = '固定费用类协议,兑付方式为票折时，只能选择进项税码' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

        ENDIF.
      ELSEIF ls_tc02-zdffs IS INITIAL AND ps_ta02-zdffs = 'O'.
        IF ls_tc02-zmwskz(1) NE 'J'.

          CLEAR lv_msgv1. lv_msgv1 = '固定费用类协议,兑付方式为票折时，只能选择进项税码' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
        ENDIF.

      ENDIF.


      IF ls_tc02-zdffs = 'A' OR ls_tc02-zdffs = 'C'.
        IF ls_tc02-zmwskz(1) NE 'X'.

          CLEAR lv_msgv1. lv_msgv1 = '固定费用类协议,兑付方式为账扣或返现时，只能选择销项税码' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
        ENDIF.
      ELSEIF ls_tc02-zdffs IS INITIAL AND ( ps_ta02-zdffs = 'A' OR ps_ta02-zdffs = 'C' ).
        IF ls_tc02-zmwskz(1) NE 'X'.

          CLEAR lv_msgv1. lv_msgv1 = '固定费用类协议,兑付方式为账扣或返现时，只能选择销项税码' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
        ENDIF.

      ENDIF.

      IF ls_tc02-zzsbs IS NOT INITIAL.
        PERFORM frm_check_oneclass USING ls_tc02
                                         pt_matnr
                                CHANGING pt_msglist.

      ENDIF.


      PERFORM frm_check_zbukrs_organization USING ls_tc02
                                                  pt_bukrs
                                                  pt_werks
                                         CHANGING pt_msglist.

    ENDLOOP.

  ELSEIF ps_data_base-zflg_type EQ 'PX'.
    IF pt_ta05[] IS INITIAL.
      CLEAR lv_msgv1. lv_msgv1 = '促销返利必须要维护阶梯' .
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.

    LOOP AT pt_ta05 INTO DATA(ls_ta05).
      IF ls_ta05-zrbamt < 0.
        CLEAR lv_msgv1. lv_msgv1 = '促销返利阶梯返利值必须大等于于0' .
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
      MODIFY pt_ta05 FROM ls_ta05.
    ENDLOOP.

    IF pt_ta06[] IS INITIAL.
      CLEAR lv_msgv1. lv_msgv1 = '促销返利必须维护生效日期' .
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.

  ENDIF.


*  外部付款方检查
  LOOP AT pt_t44 INTO DATA(ls_t44).
    PERFORM frm_check_status_zflzff USING ls_t44-zflzff .
    IF sy-subrc NE 0.
      CLEAR lv_msgv1. lv_msgv1 = '外部支付方' && ls_t44-zflzff && '已被冻结或删除，请检查主数据。'.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.
  ENDLOOP.


  LOOP AT lt_tc02_tmp INTO ls_tc02.

    PERFORM frm_check_status_zflzff USING ls_tc02-zflzff.
    IF sy-subrc NE 0.
      CLEAR lv_msgv1. lv_msgv1 = '行项目中支付方' && ls_tc02-zflzff && '已被冻结或删除，请检查主数据。' && '行项目ID:' && ls_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.

    PERFORM frm_check_status_zflzff_bukrs USING ls_tc02-zflzff ls_tc02-zflsqf.
    IF sy-subrc NE 0.
      CLEAR lv_msgv1. lv_msgv1 = '行项目中支付方' && ls_tc02-zflzff && '已被冻结或删除，请检查主数据。' && '行项目ID:' && ls_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.

    IF ls_tc02-zitzff = 'X'.
      LOOP AT pt_t44 INTO ls_t44.
        PERFORM frm_check_status_zflzff_bukrs USING ls_t44-zflzff ls_tc02-zflsqf.
        IF sy-subrc NE 0.
          CLEAR lv_msgv1. lv_msgv1 = '外部支付方' && ls_t44-zflzff && '已被冻结或删除，请检查主数据。' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
        ENDIF.
      ENDLOOP.
    ENDIF.

    IF ls_tc02-zitzff = ''.
      IF ls_tc02-zflzff IS INITIAL.
        CLEAR lv_msgv1. lv_msgv1 = '条款付款方不勾选时，付款方必输' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
    ENDIF.

    IF ps_data_base-zxybstyp = 'F'.

*      取消该检查 jira-ERP-11840
*      IF ls_tc02-zje IS INITIAL.
*        CLEAR lv_msgv1. lv_msgv1 = '固定金额的 返利条款,金额不能为空' && '行项目ID:' && ls_tc02-zitems.
*        PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.
*
*      ENDIF.

      IF ls_tc02-zmwskz IS INITIAL.

        CLEAR lv_msgv1. lv_msgv1 = '固定金额的 返利条款,税码不能为空' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
    ENDIF.

    IF ps_data_base-zxybstyp = 'F' OR ps_data_base-zxybstyp = 'Q'.
      IF ls_tc02-zitzff = 'X'.
        IF ls_tc02-zflzff IS NOT INITIAL AND pt_t44[] IS NOT INITIAL.
          CLEAR lv_msgv1. lv_msgv1 = '条款付款方勾选时，付款方+外部付款方总条目数不得超过一个' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

        ENDIF.
      ENDIF.

    ENDIF.

    IF ls_tc02-zflzff IS NOT INITIAL.
      PERFORM frm_check_zflzff_bukrs USING ls_tc02-zflzff.
      IF sy-subrc NE 0.
        CLEAR lv_msgv1. lv_msgv1 = '内部支付方时必须录入的是公司代码' && '行项目ID:' && ls_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ELSE.

        "收款主体校验
        SELECT SINGLE zsfsx INTO @DATA(lv_zsfsx) FROM zretcm13   WHERE zhtlx = @ps_ta01-zhtlx.
        IF sy-subrc = 0 AND lv_zsfsx = 1.
          PERFORM frm_check_zbukrs_type USING ls_tc02
                                              pt_t44_all
                                              lt_tc02_comp
                                              pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.

  IF pt_t44[] IS INITIAL.

    LOOP AT lt_tc02_tmp INTO ls_tc02.

      IF ls_tc02-zitzff = 'X'.
        IF ls_tc02-zflzff IS INITIAL.

          CLEAR lv_msgv1. lv_msgv1 = '抬头外部付款方、行项目付款方，不能同时为空。' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

        ENDIF.
      ENDIF.

    ENDLOOP.
  ENDIF.

  PERFORM frm_check_zdffs USING
                                ps_ta02
                                lt_tc02_tmp
                          CHANGING pt_msglist.


*  规则检查
  DATA:
        ls_zretc005 TYPE zretc005.
  MOVE-CORRESPONDING ps_tc05 TO ls_zretc005.
*  非固定类才检查
  IF ps_data_base-zflg_type EQ 'JS'.
    PERFORM frm_check_tc05 USING ls_zretc005  CHANGING pt_msglist.
  ENDIF.


*  组织机构检查
  IF ps_data_base-zflg_type EQ 'JS' OR
     ps_data_base-zflg_type EQ 'PX' OR
     ps_data_base-zflg_type EQ 'GD' .
    PERFORM frm_check_data_org USING lt_tc02_tmp
                                     pt_bukrs
                                     pt_dcwrk
                                     pt_werks
                               CHANGING pt_msglist.
  ENDIF.

*  附加条款检查
  PERFORM frm_check_tk_plus USING ps_tc05
                                  ps_ta02
                                  pt_ta03
                            CHANGING pt_msglist.

*  附加条款重新匹配
*  IF gv_flg_rb = '01' OR gv_flg_rb = '02' .
*    PERFORM frm_are_you_sure(zbcs0001) USING '是否需要重新匹配附加条款？' sy-title.
*  IF sy-subrc EQ 0.
  PERFORM frm_tk_plus_dis_re USING ps_ta02
                                   pt_tc02
                                   ps_tc05
                            CHANGING
                                     pt_ta03
                                     pt_ta04
                                     pt_msglist.
*  ENDIF.


*  ENDIF.

  IF pv_pass = ''.
    PERFORM frm_check_frgsx    USING    ps_ta01
                              CHANGING  ps_ta02
                                        pt_msglist.

  ELSE.
*    新增行项目时无须审批
    PERFORM frm_pass_frgsx_tk CHANGING ps_ta02.
  ENDIF.
*  截止日期检查
  DATA: lv_tmp_date TYPE d.
  PERFORM frm_check_zbegin USING ps_ta02-zbegin
                           CHANGING lv_tmp_date.
  IF sy-subrc NE 0.
    CLEAR lv_msgv1. lv_msgv1 = '只允许创建' && lv_tmp_date && '之后的条款' .
    PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

  ENDIF.

*  促销返利阶梯检查
  IF ps_ta02-zxybstyp = 'P'.
    SELECT
      SUM( i~zcpctg ) AS zcpctg
      FROM @pt_ta05 AS i
      INTO @DATA(lv_zcpctg).
    IF lv_zcpctg > 100.
      PERFORM frm_add_msg USING '促销返利阶梯匹配比例合计不能大于100'  CHANGING pt_msglist.
    ENDIF.

****    DESCRIBE TABLE pt_t44[] LINES DATA(lv_lines_pt_t44).
****    IF lv_lines_pt_t44 > 1.
****      PERFORM frm_add_msg USING '促销返利仅允许维护一个支付方'  CHANGING lt_msglist.
****    ENDIF.

  ENDIF.

*  权限检查
  DATA:
    lv_mtype_auth TYPE bapi_mtype,
    lv_msg_auth   TYPE scp1_general_error-msgv1.

  PERFORM frm_author_check_tk USING ps_ta01
                                    ps_ta02
                                    pt_tc02
*                                pv_actvt
                                    ps_data_base-actvt
                                    'B'
                           CHANGING pt_msglist
                                    lv_mtype_auth
                                    lv_msg_auth..


  PERFORM frm_check_text      USING ps_data_base-zflg_rb
                           CHANGING pt_t76
                                    pt_msglist.


  SORT pt_msglist.
  DELETE ADJACENT DUPLICATES FROM pt_msglist.

  IF pt_msglist[] IS NOT INITIAL .
    READ TABLE pt_msglist TRANSPORTING NO FIELDS WITH KEY msgty = 'E'.
    IF sy-subrc = 0.
      pv_mtype = 'E'.
      pv_msg = '数据检查未通过，操作已终止!'.
    ENDIF. .
  ENDIF.

ENDFORM.

FORM frm_show_message_list USING pt_msglist TYPE scp1_general_errors.

  IF pt_msglist IS NOT INITIAL .

    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = pt_msglist[].
  ENDIF.

ENDFORM.

FORM frm_save_data      USING pv_pass TYPE char1
                         CHANGING pv_mtype       TYPE bapi_mtype
                                   pv_msg         TYPE bapi_msg
                                   ps_data_base   TYPE ty_data_base
                                   ps_ta01        TYPE LINE OF tt_ta01
                                   ps_ta02        TYPE LINE OF tt_ta02
                                   ps_tc05        TYPE LINE OF tt_tc05
                                   pt_tc04        TYPE  tt_tc04
*                                   pt_t06        TYPE  tt_t06
                                   pt_t11_all     TYPE  tt_t11
                                   pt_t11         TYPE  tt_t11
                                   pt_t12         TYPE  tt_t12
                                   pt_t13         TYPE  tt_t13
                                   pt_t14         TYPE  tt_t14
                                   pt_t44         TYPE  tt_t44
                                   pt_t44_all     TYPE  tt_t44
                                   pt_tc02        TYPE  tt_tc02
                                   pt_tc03        TYPE  tt_tc03
                                   pt_ta03        TYPE  tt_ta03
                                   pt_ta04        TYPE  tt_ta04
                                   pt_ta05        TYPE  tt_ta05
                                   pt_ta06        TYPE  tt_ta06
                                   pt_t58_set     TYPE  tt_t58
                                   pt_t76         TYPE  tt_t76
                                   pt_zzlbm       TYPE  tt_t81
                                   pt_zqdbm       TYPE  tt_t82
                                   pt_zzgys       TYPE  tt_t84
                                   pt_bukrs       TYPE  tt_bukrs
                                   pt_dcwrk       TYPE  tt_dcwrk
                                   pt_werks       TYPE  tt_werks
                                   pt_ekorg       TYPE  tt_ekorg
                                   pt_matnr       TYPE  tt_matnr.

  DATA:
    ls_tc05     TYPE LINE OF tt_tc05,
    lt_t06      TYPE tt_t06,
    lt_t07      TYPE tt_t07,
    lt_t08      TYPE tt_t08,
    lt_t10      TYPE tt_t10,
    lt_t15      TYPE tt_t15,
    lt_t16      TYPE tt_t16,
    lt_t13_tc04 TYPE tt_t13.


  CLEAR:  pv_mtype,pv_msg.



  PERFORM frm_pro_before_save USING pv_pass
                              CHANGING
                                       ps_data_base
                                       ps_ta01
                                       ps_ta02
                                       ps_tc05
                                       ls_tc05
                                       lt_t06
                                       lt_t07
                                       lt_t08
                                       lt_t10
                                       pt_t11_all
                                       pt_t11
                                       pt_t12
                                       pt_t13
                                       lt_t13_tc04
                                       pt_t14
                                       lt_t15
                                       lt_t16
                                       pt_t44
                                       pt_t44_all
                                       pt_tc02
                                       pt_tc03
                                       pt_tc04
                                       pt_ta03
                                       pt_ta04
                                       pt_ta05
                                       pt_ta06
                                       pt_t58_set
                                       pt_t76
                                       pt_zzlbm
                                       pt_zqdbm
                                       pt_zzgys
                                       pt_bukrs
                                       pt_dcwrk
                                       pt_werks
                                       pt_ekorg
                                       pt_matnr .




  PERFORM frm_save_data_into_db CHANGING
                                       pv_mtype
                                       pv_msg
                                       ps_ta02
                                       ls_tc05
                                       lt_t06
                                       lt_t07
                                       lt_t08
                                       lt_t10
                                       pt_t11_all
                                       pt_t11
                                       pt_t12
                                       pt_t13
                                       lt_t13_tc04
                                       pt_t14
                                       lt_t15
                                       lt_t16
                                       pt_t44
                                       pt_t44_all
                                       pt_ta03
                                       pt_ta04
                                       pt_ta05
                                       pt_ta06
                                       pt_t58_set
                                       pt_t76
                                       pt_zzlbm
                                       pt_zqdbm
                                       pt_zzgys
                                       pt_matnr.

ENDFORM.

FORM frm_save_data_into_db CHANGING
                                   pv_mtype       TYPE bapi_mtype
                                   pv_msg         TYPE bapi_msg
                                   ps_ta02        TYPE LINE OF tt_ta02
                                   ps_tc05        TYPE LINE OF tt_tc05
                                   pt_t06         TYPE  tt_t06
                                   pt_t07         TYPE  tt_t07
                                   pt_t08         TYPE  tt_t08
                                   pt_t10         TYPE  tt_t10
                                   pt_t11_all     TYPE  tt_t11
                                   pt_t11         TYPE  tt_t11
                                   pt_t12         TYPE  tt_t12
                                   pt_t13         TYPE  tt_t13
                                   pt_t13_tc04    TYPE  tt_t13
                                   pt_t14         TYPE  tt_t14
                                   pt_t15         TYPE  tt_t15
                                   pt_t16         TYPE  tt_t16
                                   pt_t44         TYPE  tt_t44
                                   pt_t44_all     TYPE  tt_t44
                                   pt_ta03        TYPE  tt_ta03
                                   pt_ta04        TYPE  tt_ta04
                                   pt_ta05        TYPE  tt_ta05
                                   pt_ta06        TYPE  tt_ta06
                                   pt_t58_set     TYPE  tt_t58
                                   pt_t76         TYPE  tt_t76
                                   pt_t81         TYPE  tt_t81
                                   pt_t82         TYPE  tt_t82
                                   pt_t84         TYPE  tt_t84
                                   pt_matnr       TYPE tt_matnr.

  DATA:
    ls_db_zreta02    TYPE zreta002,
    ls_db_zretc05    TYPE zretc005,
    lt_db_t10_del_xy TYPE TABLE OF zret0010,
    lt_db_t10_del_tk TYPE TABLE OF zret0010,
    lt_db_t11_del_xy TYPE TABLE OF zret0011,
    lt_db_t11_del_tk TYPE TABLE OF zret0011,
    lt_db_t07_del    TYPE TABLE OF zret0007,
    lt_db_t08_del    TYPE TABLE OF zret0008,
    lt_db_t12_del    TYPE TABLE OF zret0012,
    lt_db_t13_del_tk TYPE TABLE OF zret0013,
    lt_db_t44_del_tk TYPE TABLE OF zret0013,
    lt_db_t13_del    TYPE TABLE OF zret0013,
    lt_db_t14_del    TYPE TABLE OF zret0014,
    lt_db_t15_del    TYPE TABLE OF zret0015,
    lt_db_t16_del    TYPE TABLE OF zret0016,
    lt_db_t44_del    TYPE TABLE OF zret0044,
    lt_db_t71_del    TYPE TABLE OF zret0071,


    lt_db_ta02_del   TYPE TABLE OF zreta002,
    lt_db_ta03_del   TYPE TABLE OF zreta003,
    lt_db_ta04_del   TYPE TABLE OF zreta004,
    lt_db_ta05_del   TYPE TABLE OF zreta005,
    lt_db_ta06_del   TYPE TABLE OF zreta006,
    lt_db_t58_del    TYPE TABLE OF zret0058,
    lt_db_t81_del    TYPE TABLE OF zret0081,
    lt_db_t82_del    TYPE TABLE OF zret0082,
    lt_db_t84_del    TYPE TABLE OF zret0084,

    ls_db_ta02       TYPE  zreta002,
    ls_db_ta03       TYPE  zreta003,
    ls_db_ta04       TYPE  zreta004,
    ls_db_ta05       TYPE  zreta005,
    ls_db_ta06       TYPE  zreta006,
    ls_db_t58        TYPE  zret0058,
    lt_db_ta02       TYPE TABLE OF zreta002,
    lt_db_ta03       TYPE TABLE OF zreta003,
    lt_db_ta04       TYPE TABLE OF zreta004,
    lt_db_ta05       TYPE TABLE OF zreta005,
    lt_db_ta06       TYPE TABLE OF zreta006,
    lt_db_t58        TYPE TABLE OF zret0058,
    lt_db_t71        TYPE TABLE OF zret0071,

    ls_db_t06        TYPE  zret0006,
    ls_db_t07        TYPE  zret0007,
    ls_db_t08        TYPE  zret0008,
    ls_db_t10        TYPE  zret0010,
    ls_db_t11        TYPE  zret0011,
    ls_db_t12        TYPE  zret0012,
    ls_db_t13        TYPE  zret0013,
    ls_db_t14        TYPE  zret0014,
    ls_db_t15        TYPE  zret0015,
    ls_db_t16        TYPE  zret0016,
    ls_db_t44        TYPE  zret0044,
    ls_db_t71        TYPE  zret0071,
    ls_db_t76        TYPE  zret0076,
    ls_db_t81        TYPE  zret0081,
    ls_db_t82        TYPE  zret0082,
    ls_db_t84        TYPE  zret0084,
    lt_db_t06        TYPE TABLE OF zret0006,
    lt_db_t07        TYPE TABLE OF zret0007,
    lt_db_t08        TYPE TABLE OF zret0008,
    lt_db_t10        TYPE TABLE OF zret0010,
    lt_db_t11        TYPE TABLE OF zret0011,
    lt_db_t12        TYPE TABLE OF zret0012,
    lt_db_t13        TYPE TABLE OF zret0013,
    lt_db_t14        TYPE TABLE OF zret0014,
    lt_db_t15        TYPE TABLE OF zret0015,
    lt_db_t16        TYPE TABLE OF zret0016,
    lt_db_t44        TYPE TABLE OF zret0044,
    lt_db_t76        TYPE TABLE OF zret0076,
    lt_db_t81        TYPE TABLE OF zret0081,
    lt_db_t82        TYPE TABLE OF zret0082,
    lt_db_t84        TYPE TABLE OF zret0084.


  CLEAR:  pv_mtype,pv_msg.

*  获取旧的数据----条款
  SELECT * INTO TABLE lt_db_t10_del_tk FROM zret0010 WHERE zjt_id = ps_ta02-zjt_id.
  SELECT * INTO TABLE lt_db_t11_del_tk FROM zret0011 WHERE zjt_id = ps_ta02-zjt_id.
  SELECT * INTO TABLE lt_db_ta03_del FROM zreta003 WHERE ztk_id = ps_ta02-ztk_id ..
  SELECT * INTO TABLE lt_db_ta05_del FROM zreta005 WHERE ztk_id = ps_ta02-ztk_id.
  SELECT * INTO TABLE lt_db_ta06_del FROM zreta006 WHERE ztk_id = ps_ta02-ztk_id.
  SELECT * INTO TABLE lt_db_t58_del FROM zret0058 WHERE ztk_id = ps_ta02-ztk_id.

*  IF pt_ta03[] IS NOT INITIAL.
*    SELECT * INTO TABLE lt_db_ta03_del FROM zreta003  FOR ALL ENTRIES IN pt_ta03  WHERE ztk_id = pt_ta03-ztk_id AND zrlid = pt_ta03-zrlid.
*  ENDIF.
*
*  IF pt_ta04[] IS NOT INITIAL.
*    SELECT * INTO TABLE lt_db_ta04_del FROM zreta004  FOR ALL ENTRIES IN pt_ta04  WHERE zxy_id = pt_ta04-zxy_id AND zrlid = pt_ta04-zrlid.
*  ENDIF.

  SELECT * INTO TABLE lt_db_t13_del_tk FROM zret0013 WHERE zxy_id = ps_ta02-ztk_id.

  SELECT * INTO TABLE lt_db_t44_del_tk FROM zret0044 WHERE zxy_id = ps_ta02-ztk_id.



*  获取旧的数据----协议
  IF pt_t06[] IS NOT INITIAL.
    SELECT * INTO TABLE lt_db_t10_del_xy  FROM zret0010  FOR ALL ENTRIES IN pt_t06  WHERE zjt_id = pt_t06-zjt_id.
    SELECT * INTO TABLE lt_db_t11_del_xy  FROM zret0011  FOR ALL ENTRIES IN pt_t06  WHERE zjt_id = pt_t06-zjt_id.
    SELECT * INTO TABLE lt_db_t07_del     FROM zret0007  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t08_del     FROM zret0008  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t12_del     FROM zret0012  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t13_del     FROM zret0013  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t14_del     FROM zret0014  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t15_del     FROM zret0015  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t16_del     FROM zret0016  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t44_del     FROM zret0044  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_ta04_del    FROM zreta004  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id .
    SELECT * INTO TABLE lt_db_t71_del     FROM zret0071  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t81_del     FROM zret0081  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t82_del     FROM zret0082  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.
    SELECT * INTO TABLE lt_db_t84_del     FROM zret0084  FOR ALL ENTRIES IN pt_t06  WHERE zxy_id = pt_t06-zxy_id.

  ENDIF.


  MOVE-CORRESPONDING ps_ta02 TO ls_db_zreta02.
  MOVE-CORRESPONDING ps_tc05 TO ls_db_zretc05.

  IF lt_db_t10_del_tk[] IS NOT INITIAL.
    DELETE zret0010 FROM TABLE lt_db_t10_del_tk.
  ENDIF.

  IF lt_db_t10_del_xy[] IS NOT INITIAL.
    DELETE zret0010 FROM TABLE lt_db_t10_del_xy.
  ENDIF.

  IF lt_db_t11_del_tk[] IS NOT INITIAL.
    DELETE zret0011 FROM TABLE lt_db_t11_del_tk.
  ENDIF.

  IF lt_db_t11_del_xy[] IS NOT INITIAL.
    DELETE zret0011 FROM TABLE lt_db_t11_del_xy.
  ENDIF.

  IF lt_db_t07_del[] IS NOT INITIAL.
    DELETE zret0007 FROM TABLE lt_db_t07_del.
  ENDIF.

  IF lt_db_t08_del[] IS NOT INITIAL.
    DELETE zret0008 FROM TABLE lt_db_t08_del.
  ENDIF.

  IF lt_db_t12_del[] IS NOT INITIAL.
    DELETE zret0012 FROM TABLE lt_db_t12_del.
  ENDIF.

  IF lt_db_t13_del_tk[] IS NOT INITIAL.
    DELETE zret0013 FROM TABLE lt_db_t13_del_tk.
  ENDIF.

  IF lt_db_t13_del[] IS NOT INITIAL.
    DELETE zret0013 FROM TABLE lt_db_t13_del.
  ENDIF.

  IF lt_db_t14_del[] IS NOT INITIAL.
    DELETE zret0014 FROM TABLE lt_db_t14_del.
  ENDIF.

  IF lt_db_t15_del[] IS NOT INITIAL.
    DELETE zret0015 FROM TABLE lt_db_t15_del.
  ENDIF.

  IF lt_db_t16_del[] IS NOT INITIAL.
    DELETE zret0016 FROM TABLE lt_db_t16_del.
  ENDIF.

  IF lt_db_t44_del_tk[] IS NOT INITIAL.
    DELETE zret0044 FROM TABLE lt_db_t44_del_tk.
  ENDIF.

  IF lt_db_t44_del[] IS NOT INITIAL.
    DELETE zret0044 FROM TABLE lt_db_t44_del.
  ENDIF.

  IF lt_db_ta03_del[] IS NOT INITIAL.
    DELETE zreta003 FROM TABLE lt_db_ta03_del.
  ENDIF.

  IF lt_db_ta04_del[] IS NOT INITIAL.
    DELETE zreta004 FROM TABLE lt_db_ta04_del.
  ENDIF.

  IF lt_db_ta05_del[] IS NOT INITIAL.
    DELETE zreta005 FROM TABLE lt_db_ta05_del.
  ENDIF.

  IF lt_db_ta06_del[] IS NOT INITIAL.
    DELETE zreta006 FROM TABLE lt_db_ta06_del.
  ENDIF.

  IF lt_db_t58_del[] IS NOT INITIAL.
    DELETE zret0058 FROM TABLE lt_db_t58_del.
  ENDIF.

  IF lt_db_t71_del[] IS NOT INITIAL.
    DELETE zret0071 FROM TABLE lt_db_t71_del.
  ENDIF.

  IF lt_db_t81_del[] IS NOT INITIAL.
    DELETE zret0081 FROM TABLE lt_db_t81_del.
  ENDIF.

  IF lt_db_t82_del[] IS NOT INITIAL.
    DELETE zret0082 FROM TABLE lt_db_t82_del.
  ENDIF.

  IF lt_db_t84_del[] IS NOT INITIAL.
    DELETE zret0084 FROM TABLE lt_db_t84_del.
  ENDIF.

  LOOP AT pt_t06 INTO DATA(ls_t06).
    CLEAR ls_db_t06.
    MOVE-CORRESPONDING ls_t06 TO ls_db_t06.
    APPEND ls_db_t06 TO lt_db_t06.
  ENDLOOP.

  LOOP AT pt_t07 INTO DATA(ls_t07).
    CLEAR ls_db_t07.
    MOVE-CORRESPONDING ls_t07 TO ls_db_t07.
    APPEND ls_db_t07 TO lt_db_t07.
  ENDLOOP.

  LOOP AT pt_t08 INTO DATA(ls_t08).
    CLEAR ls_db_t08.
    MOVE-CORRESPONDING ls_t08 TO ls_db_t08.
    APPEND ls_db_t08 TO lt_db_t08.
  ENDLOOP.

  LOOP AT pt_t10 INTO DATA(ls_t10).
    CLEAR ls_db_t10.
    MOVE-CORRESPONDING ls_t10 TO ls_db_t10.
    APPEND ls_db_t10 TO lt_db_t10.
  ENDLOOP.

  LOOP AT pt_t11_all INTO DATA(ls_t11_all).
    CLEAR ls_db_t11.
    MOVE-CORRESPONDING ls_t11_all TO ls_db_t11.
    APPEND ls_db_t11 TO lt_db_t11.
  ENDLOOP.

  LOOP AT pt_t11 INTO DATA(ls_t11).
    CLEAR ls_db_t11.
    MOVE-CORRESPONDING ls_t11 TO ls_db_t11.
    APPEND ls_db_t11 TO lt_db_t11.
  ENDLOOP.

  LOOP AT pt_t12 INTO DATA(ls_t12).
    CLEAR ls_db_t12.
    MOVE-CORRESPONDING ls_t12 TO ls_db_t12.
    APPEND ls_db_t12 TO lt_db_t12.
  ENDLOOP.

  LOOP AT pt_t13 INTO DATA(ls_t13).
    CLEAR ls_db_t13.
    MOVE-CORRESPONDING ls_t13 TO ls_db_t13.
    APPEND ls_db_t13 TO lt_db_t13.
  ENDLOOP.

  LOOP AT pt_t13_tc04 INTO DATA(ls_t13_tc04).
    CLEAR ls_db_t13.
    MOVE-CORRESPONDING ls_t13_tc04 TO ls_db_t13.
    APPEND ls_db_t13 TO lt_db_t13.
  ENDLOOP.

  LOOP AT pt_t14 INTO DATA(ls_t14).
    CLEAR ls_db_t14.
    MOVE-CORRESPONDING ls_t14 TO ls_db_t14.
    APPEND ls_db_t14 TO lt_db_t14.
  ENDLOOP.

  LOOP AT pt_t15 INTO DATA(ls_t15).
    CLEAR ls_db_t15.
    MOVE-CORRESPONDING ls_t15 TO ls_db_t15.
    APPEND ls_db_t15 TO lt_db_t15.
  ENDLOOP.

  LOOP AT pt_t16 INTO DATA(ls_t16).
    CLEAR ls_db_t16.
    MOVE-CORRESPONDING ls_t16 TO ls_db_t16.
    APPEND ls_db_t16 TO lt_db_t16.
  ENDLOOP.

  LOOP AT pt_ta03 INTO DATA(ls_ta03).
    CLEAR ls_db_ta03.
    MOVE-CORRESPONDING ls_ta03 TO ls_db_ta03.
    APPEND ls_db_ta03 TO lt_db_ta03.
  ENDLOOP.

  LOOP AT pt_ta04 INTO DATA(ls_ta04).
    CLEAR ls_db_ta04.
    MOVE-CORRESPONDING ls_ta04 TO ls_db_ta04.
    APPEND ls_db_ta04 TO lt_db_ta04.
  ENDLOOP.

  LOOP AT pt_ta05 INTO DATA(ls_ta05).
    CLEAR ls_db_ta05.
    MOVE-CORRESPONDING ls_ta05 TO ls_db_ta05.
    APPEND ls_db_ta05 TO lt_db_ta05.
  ENDLOOP.

  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    CLEAR ls_db_ta06.
    MOVE-CORRESPONDING ls_ta06 TO ls_db_ta06.
    APPEND ls_db_ta06 TO lt_db_ta06.
  ENDLOOP.

  LOOP AT pt_t58_set INTO DATA(ls_t58_set).
    CLEAR ls_db_t58.
    MOVE-CORRESPONDING ls_t58_set TO ls_db_t58.
    APPEND ls_db_t58 TO lt_db_t58.
  ENDLOOP.


  LOOP AT pt_t44 INTO DATA(ls_t44).
    CLEAR ls_db_t44.
    MOVE-CORRESPONDING ls_t44 TO ls_db_t44.
    APPEND ls_db_t44 TO lt_db_t44.
  ENDLOOP.

  LOOP AT pt_t44_all INTO DATA(ls_t44_all).
    CLEAR ls_db_t44.
    MOVE-CORRESPONDING ls_t44_all TO ls_db_t44.
    APPEND ls_db_t44 TO lt_db_t44.
  ENDLOOP.

  LOOP AT pt_matnr INTO DATA(ls_matnr).
    CLEAR ls_db_t71.
    MOVE-CORRESPONDING ls_matnr TO ls_db_t71.
    APPEND ls_db_t71 TO lt_db_t71.
  ENDLOOP.

  LOOP AT pt_t76 INTO DATA(ls_t76).
    CLEAR ls_db_t76.
    MOVE-CORRESPONDING ls_t76 TO ls_db_t76.
    APPEND ls_db_t76 TO lt_db_t76.
  ENDLOOP.

  LOOP AT pt_t81 INTO DATA(ls_t81).
    CLEAR ls_db_t81.
    MOVE-CORRESPONDING ls_t81 TO ls_db_t81.
    APPEND ls_db_t81 TO lt_db_t81.
  ENDLOOP.

  LOOP AT pt_t82 INTO DATA(ls_t82).
    CLEAR ls_db_t82.
    MOVE-CORRESPONDING ls_t82 TO ls_db_t82.
    APPEND ls_db_t82 TO lt_db_t82.
  ENDLOOP.

  LOOP AT pt_t84 INTO DATA(ls_t84).
    CLEAR ls_db_t84.
    MOVE-CORRESPONDING ls_t84 TO ls_db_t84.
    APPEND ls_db_t84 TO lt_db_t84.
  ENDLOOP.

  MODIFY zreta002 FROM ls_db_zreta02.
  MODIFY zretc005 FROM ls_db_zretc05.

  MODIFY zret0006 FROM TABLE lt_db_t06.
  MODIFY zret0007 FROM TABLE lt_db_t07.
  MODIFY zret0008 FROM TABLE lt_db_t08.
  MODIFY zret0011 FROM TABLE lt_db_t11.
  MODIFY zret0012 FROM TABLE lt_db_t12.
  MODIFY zret0013 FROM TABLE lt_db_t13.
  MODIFY zret0014 FROM TABLE lt_db_t14.
  MODIFY zret0015 FROM TABLE lt_db_t15.
  MODIFY zret0016 FROM TABLE lt_db_t16.
  MODIFY zret0044 FROM TABLE lt_db_t44.

  MODIFY zreta003 FROM TABLE lt_db_ta03.
  MODIFY zreta004 FROM TABLE lt_db_ta04.
  MODIFY zreta005 FROM TABLE lt_db_ta05.
  MODIFY zreta006 FROM TABLE lt_db_ta06.
  MODIFY zret0058 FROM TABLE lt_db_t58.
  MODIFY zret0071 FROM TABLE lt_db_t71.
  MODIFY zret0076 FROM TABLE lt_db_t76.
  MODIFY zret0081 FROM TABLE lt_db_t81.
  MODIFY zret0082 FROM TABLE lt_db_t82.
  MODIFY zret0084 FROM TABLE lt_db_t84.


  pv_mtype = 'S'.
  pv_msg = COND #( WHEN ps_ta02-zcnyg = 'X' THEN  |数据保存成功！请手工计算返利数据！|
                   ELSE |数据保存成功！| ).


ENDFORM.

FORM frm_pro_before_save USING pv_pass TYPE char1
                         CHANGING
                                   ps_data_base   TYPE ty_data_base
                                   ps_ta01        TYPE LINE OF tt_ta01
                                   ps_ta02        TYPE LINE OF tt_ta02
                                   ps_tc05        TYPE LINE OF tt_tc05
                                   ls_tc05        TYPE LINE OF tt_tc05
                                   pt_t06         TYPE tt_t06
                                   pt_t07         TYPE tt_t07
                                   pt_t08         TYPE tt_t08
                                   pt_t10         TYPE tt_t10
                                   pt_t11_all     TYPE  tt_t11    "协议阶梯
                                   pt_t11        TYPE  tt_t11     "条款阶梯
                                   pt_t12        TYPE  tt_t12
                                   pt_t13        TYPE  tt_t13
                                   pt_t13_tc04        TYPE  tt_t13
                                   pt_t14        TYPE  tt_t14
                                   pt_t15         TYPE tt_t15
                                   pt_t16         TYPE tt_t16
                                   pt_t44        TYPE  tt_t44
                                   pt_t44_all        TYPE  tt_t44
                                   pt_tc02        TYPE  tt_tc02
                                   pt_tc03        TYPE  tt_tc03
                                   pt_tc04        TYPE  tt_tc04
                                   pt_ta03        TYPE  tt_ta03
                                   pt_ta04        TYPE  tt_ta04
                                   pt_ta05        TYPE  tt_ta05
                                   pt_ta06        TYPE  tt_ta06
                                   pt_t58_set     TYPE  tt_t58
                                   pt_t76         TYPE  tt_t76
                                   pt_zzlbm       TYPE tt_t81
                                   pt_zqdbm       TYPE tt_t82
                                   pt_zzgys       TYPE tt_t84
                                   pt_bukrs       TYPE tt_bukrs
                                   pt_dcwrk       TYPE tt_dcwrk
                                   pt_werks       TYPE tt_werks
                                   pt_ekorg       TYPE tt_ekorg
                                   pt_matnr       TYPE tt_matnr.

  DATA:
    ls_t06     TYPE LINE OF tt_t06,
    ls_t07     TYPE LINE OF tt_t07,
    ls_t08     TYPE LINE OF tt_t08,
    ls_t44_all TYPE LINE OF tt_t44.
  DATA:
    lv_zxy_itemid TYPE zret0007-zxy_itemid,
    lv_zitem_id   TYPE zreta002-zitem_id,
    lv_zitems     TYPE zreta003-zitems.
*    lv_execute    TYPE c LENGTH 3    .

*  zreta002 处理

  IF ps_ta02-ztk_id IS INITIAL.
    ps_ta02-zcjrq  = sy-datum.
    ps_ta02-zcjsj  = sy-uzeit.
    ps_ta02-zcjr   = sy-uname.
  ELSE.
    ps_ta02-zxgrq  = sy-datum.
    ps_ta02-zxgsj  = sy-uzeit.
    ps_ta02-zxgr   = sy-uname.
  ENDIF.

  IF ps_ta02-zleib IS INITIAL.
    ps_ta02-zleib  = 'C'.
  ENDIF.

  IF ps_ta02-ztk_id IS INITIAL.
    SELECT MAX( zitem_id ) AS zitem_id FROM zreta002 WHERE zht_id = @ps_ta01-zht_id  INTO @lv_zitem_id .
    lv_zitem_id = lv_zitem_id + 1.
    lv_zitem_id = |{ lv_zitem_id ALPHA = IN }|.
    ps_ta02-zitem_id = lv_zitem_id.
    ps_ta02-ztk_id = ps_ta01-zht_id+4(6) && lv_zitem_id.
*    PERFORM frm_get_num USING 'ZRE0010' '01' CHANGING ps_ta02-ztk_id.
*    ps_ta02-ztk_id+0(8) = ps_ta01-zht_id.
  ENDIF.

  IF ps_ta02-zjt_id IS INITIAL AND pt_t11[] IS NOT INITIAL.
    PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING ps_ta02-zjt_id.
  ENDIF.



  ps_ta02-zclrid = ps_tc05-zclrid.

  CLEAR ls_tc05.
  MOVE-CORRESPONDING ps_tc05 TO ls_tc05.
  PERFORM frm_set_data_tc05_man USING ps_ta02 CHANGING ls_tc05.





*  条款阶梯数据处理 防止因未回车导致的错误
  PERFORM frm_set_data_zjt  USING    ls_tc05
                            CHANGING pt_t11.

*MODIFY at 20220516 form xinli  ERP-13605 begin
**  协议阶梯数据处理 防止因未回车导致的错误
*  PERFORM frm_set_data_zjt  USING    ls_tc05
*                            CHANGING pt_t11_all.
* MODIFY at 20220516 form xinli  ERP-13605 end
*  条款阶梯明细
  LOOP AT pt_t11 INTO DATA(ls_t11).
    ls_t11-zjt_id = ps_ta02-zjt_id.
    MODIFY pt_t11 FROM ls_t11.
  ENDLOOP.

*  渠道供应商
  LOOP AT pt_t12 INTO DATA(ls_t12).
    ls_t12-zxy_id = ps_ta02-ztk_id.
    MODIFY pt_t12 FROM ls_t12.
  ENDLOOP.

*  外部供货方
  LOOP AT pt_t13 INTO DATA(ls_t13).
    ls_t13-zxy_id = ps_ta02-ztk_id.
    MODIFY pt_t13 FROM ls_t13.
  ENDLOOP.

*  外部支付方
  LOOP AT pt_t44 INTO DATA(ls_t44).
    ls_t44-zxy_id = ps_ta02-ztk_id.
    MODIFY pt_t44 FROM ls_t44.
  ENDLOOP.

*  条款抬头支付方取外部支付方第一个
  CLEAR ls_t44.
  READ TABLE pt_t44 INTO ls_t44 INDEX 1.
  ps_ta02-zflzff = ls_t44-zflzff.

  LOOP AT pt_t76 ASSIGNING FIELD-SYMBOL(<lfs_t76>)   .
    <lfs_t76>-ztk_id = ps_ta02-ztk_id.
    PERFORM frm_set_guid   CHANGING <lfs_t76>-zguid.
    <lfs_t76>-erdat = sy-datum.
    <lfs_t76>-ertim = sy-uzeit.
    <lfs_t76>-ernam = sy-uname.
    <lfs_t76>-aedat = sy-datum.
    <lfs_t76>-aetim = sy-uzeit.
    <lfs_t76>-aenam = sy-uname.
  ENDLOOP.

*  条款行项目编号
  PERFORM frm_set_zitems USING
                               ps_ta02-ztmpid
                                ps_ta02-ztk_id
                                'B'
                          CHANGING pt_tc02.

*  条款行项目
  LOOP AT pt_tc02 INTO DATA(ls_tc02) .


*    新增行项目时，已存在的行项目不需要重新更新表，可能会把原有数据（计算相关）更新掉（结算周期 已结算标识 ）

    IF ps_data_base-zflg_zlrsi  = 'X' .
      IF NOT ( ls_tc02-zxyzt_06 = 'D' OR ls_tc02-zxyzt_06 = 'N' OR ls_tc02-zxyzt_06 = 'R' OR ls_tc02-zxyzt_06 = '' ).
        CONTINUE.
      ENDIF.
    ENDIF.


*      若行项目没有阶梯 则不生成阶梯数据
    READ TABLE pt_t11_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_tc02-zitems_key.
    IF sy-subrc NE 0.
      CLEAR ls_tc02-zjt_id.
    ENDIF.

    IF ls_tc02-sel_man = 'X' OR ls_tc02-zxy_id IS NOT INITIAL.

      ls_tc02-ztk_id = ps_ta02-ztk_id.

      IF ls_tc02-zxy_id IS INITIAL.
        PERFORM frm_get_num USING 'ZRE0001' '01' CHANGING ls_tc02-zxy_id.
        MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING zxy_id.
*        CLEAR:lv_execute.
      ENDIF.

      CLEAR ls_t06.

      PERFORM frm_data_conver_t06b USING ls_tc02
                                          ps_ta01
                                          ps_ta02
                                          ps_data_base-zflg_zlrsi
                                   CHANGING ls_t06.
      IF pv_pass = 'X'.  "新增行项目时无须审批
        PERFORM frm_pass_frgsx_xy CHANGING ls_t06.
      ENDIF.
      APPEND ls_t06 TO pt_t06.

      IF ps_data_base-zflg_type = 'GD' OR ps_data_base-zflg_type = 'CX'.
        CLEAR ls_t08.
        PERFORM frm_data_conver_t08    USING ls_tc02
                                             pt_matnr
                                    CHANGING ls_t08.
        APPEND ls_t08 TO pt_t08.

**  当[协议类型]=F固定金额 将商品明细转为08表
*        IF ps_data_base-zflg_type EQ 'GD'.
*          PERFORM frm_data_conver_matnr_2_t08 USING pt_matnr
*                                              CHANGING pt_t08.
*        ENDIF.


      ENDIF.

      IF ps_data_base-zflg_type = 'CX'..
        CLEAR ls_t07.
        PERFORM frm_data_conver_t07 USING ls_tc02  CHANGING ls_t07.
        APPEND ls_t07 TO pt_t07.

      ENDIF.


*    ELSE.
*      IF ls_tc02-zxy_id IS NOT INITIAL.
*        IF ls_tc02-zstatus_del = 'X'.
*          ls_tc02-zstatus = 'D'.
*        ENDIF.
*      ENDIF.

    ENDIF.

*    付款方级别默认
    PERFORM frm_set_zpaytp USING ls_tc02-zflzff CHANGING ls_tc02-zpaytp.

    MODIFY pt_tc02 FROM ls_tc02.
  ENDLOOP.

*  协议数据处理
  PERFORM frm_pro_data_t06 CHANGING pt_t06.

*  行项目阶梯
  LOOP AT pt_t11_all INTO DATA(ls_t11_all).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_t11_all-zitems_key .
    IF sy-subrc EQ 0.
*      ls_t11_all-zxy_id = ls_tc02-zxy_id.
      ls_t11_all-zitems = ls_tc02-zitems.
      MODIFY pt_t11_all FROM ls_t11_all.
    ENDIF.
  ENDLOOP.


*  将行项目中的支付方添加到44表中

*  针对固定金额类的协议，支付方取行项目中的字段，因此需要先清空T44, T44只能保留一行
  IF ps_ta02-zxybstyp = 'F'.
    CLEAR pt_t44_all.
  ENDIF.

  LOOP AT pt_tc02 INTO ls_tc02.
    CLEAR ls_t44_all.
    ls_t44_all-zflzff =  ls_tc02-zflzff.
    ls_t44_all-zitems_key =  ls_tc02-zitems_key.
    ls_t44_all-zpaytp =  ls_tc02-zpaytp.

    READ TABLE pt_t44_all TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_t44_all-zitems_key
                                                          zflzff     = ls_t44_all-zflzff .
    IF sy-subrc EQ 0.
      MODIFY pt_t44_all FROM ls_t44_all INDEX sy-tabix TRANSPORTING zpaytp .
    ELSE.
      APPEND ls_t44_all TO pt_t44_all.
    ENDIF.

  ENDLOOP.
  SORT pt_t44_all BY zitems_key zflzff  .
  DELETE ADJACENT DUPLICATES FROM pt_t44_all COMPARING zitems_key zflzff.
  DELETE pt_t44_all WHERE zflzff IS INITIAL.


*  行项目支付方
  LOOP AT pt_t44_all INTO ls_t44_all.
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_t44_all-zitems_key .
    IF sy-subrc EQ 0.
      ls_t44_all-zxy_id = ls_tc02-zxy_id.
    ENDIF.

*    付款方级别默认
    PERFORM frm_set_zpaytp USING ls_t44_all-zflzff CHANGING ls_t44_all-zpaytp.

    MODIFY pt_t44_all FROM ls_t44_all.
  ENDLOOP.

  LOOP AT pt_matnr INTO DATA(ls_matnr).
    ls_matnr-matnr =  |{ ls_matnr-matnr ALPHA = IN WIDTH = 18  }|.
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_matnr-zitems_key .
    IF sy-subrc EQ 0.
      ls_matnr-zxy_id = ls_tc02-zxy_id.
    ENDIF.
    MODIFY pt_matnr FROM ls_matnr.

  ENDLOOP.




*  阶梯行项目编号
  PERFORM frm_set_zjt_itemid USING ps_tc05 CHANGING pt_t11 pt_t10.
  PERFORM frm_set_zjt_itemid USING ps_tc05 CHANGING pt_t11_all pt_t10.

* 子类编码
  LOOP AT pt_zzlbm INTO DATA(ls_zzlbm).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_zzlbm-zitems_key .
    IF sy-subrc EQ 0.
      ls_zzlbm-zxy_id = ls_tc02-zxy_id.
      ls_zzlbm-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zzlbm FROM ls_zzlbm.

  ENDLOOP.

* 渠道编码
  LOOP AT pt_zqdbm INTO DATA(ls_zqdbm).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_zqdbm-zitems_key .
    IF sy-subrc EQ 0.
      ls_zqdbm-zxy_id = ls_tc02-zxy_id.
      ls_zqdbm-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zqdbm FROM ls_zqdbm.

  ENDLOOP.

* 供应商编码
  LOOP AT pt_zzgys INTO DATA(ls_zzgys).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_zzgys-zitems_key .
    IF sy-subrc EQ 0.
      ls_zzgys-zxy_id = ls_tc02-zxy_id.
      ls_zzgys-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zzgys FROM ls_zzgys.

  ENDLOOP.
*  行项目组织结构
  PERFORM frm_zzzlx_fold                        USING   pt_bukrs
                                                        pt_dcwrk
                                                        pt_werks
                                                        pt_ekorg
                                              CHANGING  pt_tc03.
*  组织结构代码处理
  LOOP AT pt_tc03 INTO DATA(ls_tc03).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_tc03-zitems_key .
    IF sy-subrc EQ 0.
      ls_tc03-zxy_id = ls_tc02-zxy_id.
      ls_tc03-zitems = ls_tc02-zitems.
      MODIFY pt_tc03 FROM ls_tc03.
    ELSE.
      DELETE pt_tc03 .
      CONTINUE.
    ENDIF.
  ENDLOOP.

*  将组织结构代码转换成协议的组织机构
  PERFORM frm_data_conver_03 USING    pt_tc03
                             CHANGING pt_t14.

*  行项目供货方也存13表 ，需要记录协议号
  LOOP AT pt_tc04 INTO DATA(ls_tc04).
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_tc04-zitems_key .
    IF sy-subrc EQ 0.
      ls_tc04-zxy_id = ls_tc02-zxy_id.
      ls_tc04-zitems = ls_tc02-zitems.
      MODIFY pt_tc04 FROM ls_tc04.

      CLEAR ls_t13.
      MOVE-CORRESPONDING ls_tc04 TO ls_t13 .
      APPEND ls_t13 TO pt_t13_tc04.
    ENDIF.
  ENDLOOP.

* 返利条款-附加条款表
*  CLEAR lv_zitems.
*  SELECT MAX( zitems ) AS zitems INTO lv_zitems  FROM zreta003
*    WHERE ztk_id = ps_ta02-ztk_id .

  LOOP AT pt_ta03 INTO DATA(ls_ta03).
*    IF ls_ta03-zitems IS INITIAL.
*      lv_zitems = lv_zitems + 1.
*      ls_ta03-zitems = lv_zitems.
*    ENDIF.
    ls_ta03-ztk_id = ps_ta02-ztk_id.
    MODIFY pt_ta03 FROM ls_ta03.
  ENDLOOP.

* 返利协议-附加协议表
  LOOP AT pt_ta04 INTO DATA(ls_ta04).

    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_ta04-zitems_key .
    IF sy-subrc EQ 0.
      ls_ta04-zxy_id = ls_tc02-zxy_id.
*      ls_ta04-zitems = ls_tc02-zitems.
    ENDIF.

*      在保存时 类型为A的附加条款会取本次新生成的协议，因此需要保存新生成的协议号，对于P类型的无须处理
    IF ls_ta04-zatktp = 'A' AND ls_ta04-zrlid IS INITIAL.
      CLEAR ls_tc02.
      READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_ta04-zitems_key_zxy_id .
      IF sy-subrc EQ 0.
        ls_ta04-zrlid = ls_tc02-zxy_id.
      ENDIF.
    ENDIF.
    MODIFY pt_ta04 FROM ls_ta04.
  ENDLOOP.


*  协议核算期间与结算期间
*  固定类不存，非固定类存
  IF ps_data_base-zflg_type NE 'GD'.
    PERFORM frm_pro_data_qj USING pt_t06
                            CHANGING pt_t15
                                     pt_t16.
  ENDIF.

**  商品明细表处理
*  LOOP AT pt_matnr INTO ls_matnr.
*    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems_key = ls_matnr-zitems_key .
*    IF sy-subrc EQ 0.
*      ls_matnr-zxy_id = ls_tc02-zxy_id.
*
*      MODIFY pt_matnr FROM ls_matnr.
*    ELSE.
*      DELETE pt_matnr .
*      CONTINUE.
*    ENDIF.
*  ENDLOOP.



*  动态协议行项目数据
  SORT pt_t07 BY zxy_id.
  DATA(lv_num) = 1.
  LOOP AT pt_t07 INTO ls_t07.

    AT NEW zxy_id.
      DATA(lv_flg_new) = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.

      CLEAR lv_num.
    ENDIF.

    lv_num = lv_num + 1.
    ls_t07-zxy_itemid = lv_num.
    MODIFY pt_t07 FROM ls_t07.
    CLEAR lv_flg_new.
  ENDLOOP.


*  静态协议行项目数据处理
  SORT pt_t08 BY zxy_id.

  LOOP AT pt_t08 INTO ls_t08.

    AT NEW zxy_id.
      lv_flg_new = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.

      CLEAR lv_num.
    ENDIF.

    lv_num = lv_num + 1.
    ls_t08-zxy_itemid = lv_num.
    MODIFY pt_t08 FROM ls_t08.
    CLEAR lv_flg_new.
  ENDLOOP.


*  若附加条款关系表为空，则附加协议关系表也为空
  IF pt_ta03[] IS INITIAL.
    CLEAR pt_ta04[].
  ENDIF.

  LOOP AT pt_ta05 INTO DATA(ls_ta05).
    ls_ta05-ztk_id = ps_ta02-ztk_id.
    MODIFY pt_ta05 FROM ls_ta05.
  ENDLOOP.

  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    ls_ta06-ztk_id = ps_ta02-ztk_id.
    MODIFY pt_ta06 FROM ls_ta06.
  ENDLOOP.


*  保存时 自动将商品组信息带入促销返利阶梯中

  LOOP AT pt_ta05 INTO ls_ta05.

    PERFORM frm_add_spz_to_t58_set_exe USING
                                         ls_ta05
                                         ps_ta02-zspz_id
                                   CHANGING pt_t58_set.
  ENDLOOP.


  SORT pt_ta05 BY zitems_key.
*  删除不符合抬头商品组的数据
  DELETE pt_t58_set WHERE zspz_id NE ps_ta02-zspz_id.
  LOOP AT pt_t58_set INTO DATA(ls_t58_set).
    ls_t58_set-ztk_id = ps_ta02-ztk_id.
*    根据zitems_key 更新行项目ID
    READ TABLE pt_ta05 INTO ls_ta05 WITH KEY zitems_key = ls_t58_set-zitems_key BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t58_set-zitems = ls_ta05-zitems.
    ELSE.
*      若没有匹配到行项目 则数据无效需要删除
      DELETE pt_t58_set.
      CONTINUE.
    ENDIF.
    MODIFY pt_t58_set FROM ls_t58_set.
  ENDLOOP.

ENDFORM.

FORM frm_pro_data_qj USING pt_t06 TYPE tt_t06
                     CHANGING pt_t15 TYPE tt_t15
                              pt_t16 TYPE tt_t16.

  DATA:
    lt_zqj TYPE tt_zqj,
    ls_t15 TYPE LINE OF tt_t15,
    ls_t16 TYPE LINE OF tt_t16.

  DATA:
    lv_num     TYPE i,
    lv_flg_new TYPE char1.

  CLEAR:
         pt_t15, pt_t16.

  LOOP AT pt_t06 INTO DATA(ls_t06).

*    核算期间
    CLEAR lt_zqj.

*    若是月初第一天走原先的逻辑，否则新逻辑 整月处理
    CLEAR lv_flg_new.
*    逻辑暂时还原，
    IF ls_t06-zbegin+6(2) = '01'.
      lv_flg_new = ''.
    ELSE.
      lv_flg_new = 'X'.
    ENDIF.

    PERFORM frm_set_zqj   USING ls_t06
                                'ZHSQJ'
                                lv_flg_new
                          CHANGING lt_zqj.

    CLEAR lv_num.
    LOOP AT lt_zqj INTO DATA(ls_zqj).
      CLEAR ls_t15.
      lv_num            = lv_num + 1.
      ls_t15-zhsqj_id   = lv_num.
      ls_t15-zxy_id = ls_t06-zxy_id.
      ls_t15-zbegin = ls_zqj-zbegin.
      ls_t15-zend = ls_zqj-zend.
      APPEND ls_t15 TO pt_t15.
    ENDLOOP.


*    结算期间
    CLEAR lt_zqj.
    PERFORM frm_set_zqj   USING ls_t06
                                'ZJSQJ'
                                lv_flg_new
                          CHANGING lt_zqj.

    CLEAR lv_num.
    LOOP AT lt_zqj INTO ls_zqj.
      CLEAR ls_t16.
      lv_num            = lv_num + 1.
      ls_t16-zjsqj_id   = lv_num.
      ls_t16-zxy_id = ls_t06-zxy_id.
      ls_t16-zbegin = ls_zqj-zbegin.
      ls_t16-zend = ls_zqj-zend.
      APPEND ls_t16 TO pt_t16.
    ENDLOOP.

  ENDLOOP.

ENDFORM.

FORM frm_set_zqj  USING         ps_t06 TYPE ty_t06
                                pv_flg  TYPE char10
                                pv_flg_new TYPE char1
                    CHANGING    pt_data TYPE tt_zqj.
  DATA:
        ls_data TYPE LINE OF tt_zqj.
  DATA:
    lv_date    TYPE d,
    lv_add_mon TYPE t5a4a-dlymo.
  DATA:
        lv_zqs TYPE int4.

  CLEAR pt_data.
  IF pv_flg = 'ZHSQJ'.
    IF ps_t06-zhszq IS INITIAL.
      RETURN.
    ENDIF.

    SELECT SINGLE zhszqs INTO lv_zqs
      FROM zret0005
      WHERE zhszq = ps_t06-zhszq.

  ELSEIF pv_flg = 'ZJSQJ'.
    IF ps_t06-zjszq IS INITIAL.
      RETURN.
    ENDIF.

    SELECT SINGLE zjszqs INTO lv_zqs
      FROM zret0004
      WHERE zjszq = ps_t06-zjszq.
  ENDIF.


*  初始日期等于协议开始日期
  lv_date = ps_t06-zbegin.
  lv_add_mon = lv_zqs.

  IF ps_t06-zhstype = 'B' . " OR pv_flg = 'ZJSQJ'.

    DO  .

      CLEAR ls_data.
      ls_data-zbegin = lv_date.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = lv_date
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = ls_data-zend.

      ls_data-zend = ls_data-zend - 1.

*      整月处理
*     连续期间的 开始日期是动态变动的，因此不能仅使用 PV_FLG_NEW标识判断 ，还需要判断开始日期是否是1号
      IF pv_flg_new = 'X' AND ls_data-zbegin+6(2) NE '01'.
        PERFORM frm_set_last_day CHANGING ls_data-zend.
      ENDIF.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
      IF ls_data-zend >= ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
      lv_date = ls_data-zend + 1.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = lv_date
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = lv_date.
      lv_date = lv_date - 1.

*      整月处理(下次循环的结束日期）
*     连续期间的 开始日期是动态变动的，因此不能仅使用 PV_FLG_NEW标识判断 ，还需要判断开始日期是否是1号
      IF pv_flg_new = 'X' AND ls_data-zbegin+6(2) NE '01'.
        PERFORM frm_set_last_day CHANGING lv_date.
      ENDIF.


      IF lv_date > ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ELSE.
        APPEND ls_data TO pt_data.
*        下一次循环的开始日期等于本次循环结束日期 + 1
        lv_date = ls_data-zend + 1 .
      ENDIF.

*      防止死循环 跳出
      IF sy-index > 1000.
        EXIT.
      ENDIF.
    ENDDO.


  ELSE.

    DO  .

      CLEAR ls_data.
      ls_data-zbegin = ps_t06-zbegin.

      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = ls_data-zbegin
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = 0
          years     = 0
        IMPORTING
          calc_date = ls_data-zend.
      ls_data-zend = ls_data-zend - 1.

*      整月处理
      IF pv_flg_new = 'X' .
        PERFORM frm_set_last_day CHANGING ls_data-zend.
      ENDIF.

*      若首次循环 核算期间结束日期大于等于协议结束日期，则停止
      IF ls_data-zend >= ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ENDIF.

*      预判断下次循环 核算期间结束日期是否大于协议结束日期 ，
*      若大于 则不进行下次循环 更改日期为协议结束日期
      lv_add_mon = lv_add_mon + lv_zqs.

      CLEAR:
            lv_date.
      CALL FUNCTION 'RP_CALC_DATE_IN_INTERVAL'
        EXPORTING
          date      = ls_data-zbegin
          days      = 0
          months    = lv_add_mon
*         SIGNUM    = U_SIGN
          years     = 0
        IMPORTING
          calc_date = lv_date.

      lv_date = lv_date - 1.

*      整月处理(下次循环的结束日期）
      IF pv_flg_new = 'X'.
        PERFORM frm_set_last_day CHANGING lv_date.
      ENDIF.

      IF lv_date > ps_t06-zend.
        ls_data-zend = ps_t06-zend.
        APPEND ls_data TO pt_data.
        EXIT.
      ELSE.
        APPEND ls_data TO pt_data..
      ENDIF.

*      防止死循环 跳出
      IF sy-index > 1000.
        EXIT.
      ENDIF.
    ENDDO.



  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CLEAR_DATA_NULL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_TC04
*&      <-- PT_BUKRS
*&      <-- PT_DCWRK
*&      <-- PT_WERKS
*&      <-- PT_EKORG
*&---------------------------------------------------------------------*
FORM frm_clear_data_null  CHANGING pt_tc04        TYPE  tt_tc04
                                   pt_t13        TYPE  tt_t13
                                   pt_t44        TYPE  tt_t44
                                   pt_t44_all        TYPE  tt_t44
                                   pt_matnr       TYPE tt_matnr
  .

  DELETE pt_tc04 WHERE zghf IS INITIAL.
  DELETE pt_t44 WHERE zflzff IS INITIAL.
  DELETE pt_t44_all WHERE zflzff IS INITIAL.
  DELETE pt_t13 WHERE zghf IS INITIAL.
  DELETE pt_matnr WHERE matnr IS INITIAL.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_DYNP
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_update_dynp  USING   pv_fieldname TYPE dynpread-fieldname
                              pv_fieldvalue TYPE dynpread-fieldvalue.
  DATA:lt_dynpfields LIKE TABLE OF dynpread,
       ls_dynpfields LIKE  dynpread.

  ls_dynpfields-fieldname = pv_fieldname.
  ls_dynpfields-fieldvalue = pv_fieldvalue.
  APPEND ls_dynpfields TO lt_dynpfields.

  CALL FUNCTION 'DYNP_VALUES_UPDATE'
    EXPORTING
      dyname               = sy-repid
      dynumb               = sy-dynnr
    TABLES
      dynpfields           = lt_dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      undefind_error       = 7
      OTHERS               = 8.
ENDFORM.

FORM frm_get_data_dynp  CHANGING   pv_fieldname TYPE dynpread-fieldname
                              pv_fieldvalue TYPE dynpread-fieldvalue.

  DATA: dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.


  CLEAR: pv_fieldvalue.
  dynpfields-fieldname = pv_fieldname.
  dynpfields-fieldvalue = ''.
  APPEND dynpfields.

  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-repid
      dynumb               = sy-dynnr
      translate_to_upper   = 'X'
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      undefind_error       = 7
      OTHERS               = 8.
  IF sy-subrc = 0.
    READ TABLE dynpfields WITH KEY fieldname = pv_fieldname.
    pv_fieldvalue = dynpfields-fieldvalue.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_REXQUEST
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- P_ZHT_ID
*&---------------------------------------------------------------------*
FORM frm_pro_rexquest  CHANGING pv_zht_id TYPE zreta001-zht_id.

  DATA: ls_return TYPE           ddshretval,
        lt_return TYPE TABLE OF  ddshretval.
  DATA: ls_shlp   TYPE           shlp_descr,
        wa_selopt LIKE LINE OF   ls_shlp-selopt.

  CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
    EXPORTING
      tabname           = 'ZRETA001'
      fieldname         = 'ZHT_ID'
      callback_program  = sy-repid
*     callback_form     = 'SET_VALUES_FOR_F4_AFASL_304'
    TABLES
      return_tab        = lt_return
    EXCEPTIONS
      field_not_found   = 1
      no_help_for_field = 2
      inconsistent_help = 3
      no_values_found   = 4
      OTHERS            = 5.

  READ TABLE lt_return INDEX 1 INTO ls_return.
  CHECK sy-subrc = 0.
  pv_zht_id = ls_return-fieldval.



ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_SET_DATA_9131
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T10
*&      <-- GS_T11
*&      <-- GV_ZJT_FZ_T
*&      <-- GV_ZJT_FM_T
*&---------------------------------------------------------------------*





*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZJT_FROM_NEW
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T11
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC_GHF_WB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_T13
*&---------------------------------------------------------------------*
FORM frm_set_data_tc_ghf_wb  CHANGING ps_t13 TYPE LINE OF tt_t13.

  PERFORM frm_get_zghf_t USING ps_t13-zghf CHANGING ps_t13-name1.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC_ZQDGYS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_T12
*&---------------------------------------------------------------------*
FORM frm_set_data_tc_zqdgys  CHANGING ps_t12 TYPE LINE OF tt_t12.
  PERFORM frm_get_zghf_t USING ps_t12-lifnr CHANGING ps_t12-name1.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_TC05
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02
*&      --> GS_DATA_BASE
*&      <-- GT_TC05
*&---------------------------------------------------------------------*
FORM frm_get_data_tc05  USING    ps_ta02  TYPE LINE OF tt_ta02
                                 ps_data_base TYPE  ty_data_base
                        CHANGING pt_tc05  TYPE tt_tc05
                                 ps_tc05  TYPE LINE OF tt_tc05.

*  条款保存时会将条款号存入到规则号中，引用的标准规则号会记录在条款上
*  因此需要优先去条款的规则号


  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_tc05
    FROM zretc005
    WHERE zxybstyp = ps_data_base-zxybstyp
    AND  ( zclrtp = 'S' OR
          ( zclrtp = 'M' AND zht_id = ps_ta02-zht_id ) OR
          zclrid = ps_ta02-ztk_id
         ).

  SORT pt_tc05 BY zcs DESCENDING.

  IF ps_tc05 IS INITIAL.
    CLEAR ps_tc05.
    IF ps_ta02-ztk_id IS NOT INITIAL.
      READ TABLE pt_tc05 INTO DATA(ls_tc05) WITH KEY zclrid = ps_ta02-ztk_id.
      MOVE-CORRESPONDING ls_tc05 TO ps_tc05.

      CLEAR ls_tc05.
      READ TABLE pt_tc05 INTO ls_tc05 WITH KEY zclrid = ps_ta02-zclrid.
      ps_tc05-zclrid = ls_tc05-zclrid.
      ps_tc05-zclrtp = ls_tc05-zclrtp.

      CLEAR ls_tc05.
    ELSE.
      READ TABLE pt_tc05 INTO ps_tc05 INDEX 1.
    ENDIF.
  ELSE.
    READ TABLE pt_tc05 INTO ps_tc05 WITH KEY zclrid = ps_tc05-zclrid.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_LIST_BOX_02
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_TC05
*&      <-- GT_VLS_ZCLRID
*&---------------------------------------------------------------------*
FORM frm_set_list_box_02  USING    pt_tc05  TYPE tt_tc05
                                    pt_t09  TYPE tt_t09
                          CHANGING pt_vls_zclrid TYPE vrm_values
                                   pt_vls_zspz_id TYPE vrm_values.

  CLEAR pt_vls_zclrid.
  CLEAR pt_vls_zspz_id.

  LOOP AT pt_tc05 INTO DATA(ls_tc05).
    pt_vls_zclrid =  VALUE  #( BASE pt_vls_zclrid ( key = ls_tc05-zclrid text = ls_tc05-zclrtxt ) ).
  ENDLOOP.

  LOOP AT pt_t09 INTO DATA(ls_t09).
    pt_vls_zspz_id =  VALUE  #( BASE pt_vls_zspz_id ( key = ls_t09-zspz_id text = ls_t09-zspzid_txt ) ).
  ENDLOOP.


ENDFORM.

FORM frm_set_list_box_zjsff  USING    pv_zxybstyp TYPE zreta002-zxybstyp
                          CHANGING pt_vls_zjsff TYPE vrm_values.

  SELECT
    a~zjsff  AS  key ,
    a~zjsff_txt      AS  text
    FROM zretc015 AS a   JOIN zretc016 AS b
                           ON a~zjsff = b~zjsff
    WHERE b~zxybstyp = @pv_zxybstyp
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjsff.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC05
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_CODE
*&      --> GT_TC05
*&      <-- GS_TC05
*&---------------------------------------------------------------------*
FORM frm_set_data_tc05_bt  USING    pv_code  TYPE sy-ucomm
                                 pt_tc05  TYPE tt_tc05
                        CHANGING ps_tc05  TYPE LINE OF tt_tc05.
  DATA:
        lv_index TYPE sy-tabix.

  CASE pv_code.
    WHEN 'B_GZ_01'. lv_index = 1.
    WHEN 'B_GZ_02'. lv_index = 2.
    WHEN 'B_GZ_03'. lv_index = 3.
    WHEN 'B_GZ_04'. lv_index = 4.
    WHEN 'B_GZ_05'. lv_index = 5.
    WHEN 'B_GZ_06'. lv_index = 6.
    WHEN 'B_GZ_07'. lv_index = 7.
    WHEN 'B_GZ_08'. lv_index = 8.
    WHEN 'B_GZ_09'. lv_index = 9.
    WHEN OTHERS.

  ENDCASE.

  CLEAR ps_tc05.
  READ TABLE pt_tc05 INTO ps_tc05 INDEX lv_index.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC05_MAN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_TC05
*&---------------------------------------------------------------------*
FORM frm_set_data_tc05_man    USING ps_ta02 TYPE LINE OF tt_ta02
                              CHANGING ps_tc05 TYPE LINE OF tt_tc05
                                     .

*  CLEAR ps_tc05.
*****  IF ps_tc05-zclrid IS NOT INITIAL.
*****    SELECT SINGLE * INTO CORRESPONDING FIELDS OF ps_tc05
*****      FROM zretc005 WHERE zclrid = ps_tc05-zclrid.
*****  ELSE..
*****    CLEAR ps_tc05.
*****  ENDIF.

*  规则ID = 条款ID
  ps_tc05-zclrid = ps_ta02-ztk_id.
  ps_tc05-zclrtp = 'A'.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_ZGHF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA01
*&      --> PT_T13
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_data_zghf  USING    pt_tc02  TYPE tt_tc02
                                   pt_t13 TYPE tt_t13
                                   pt_tc04 TYPE tt_tc04
                                   ps_tc05 TYPE LINE OF tt_tc05
                          CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
        lv_flg TYPE char1.

  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.

  DATA:
    lt_dcwrk_tmp TYPE tt_dcwrk,
    lt_werks_tmp TYPE tt_werks.

  LOOP AT pt_tc02 INTO DATA(ls_tc02).

    DATA(lt_tmp) = pt_tc04[].
    DELETE lt_tmp WHERE zitems_key NE ls_tc02-zitems_key.
    DELETE lt_tmp WHERE zghf IS INITIAL.

    DATA(lt_tc04_tmp) = lt_tmp[].

    READ TABLE lt_tmp INTO DATA(ls_tmp) INDEX 1.
    CLEAR ls_tmp.

*    将条款供货方与行项目供货方合并
    LOOP AT pt_t13 INTO DATA(ls_t13).
      CLEAR ls_tmp.
      MOVE-CORRESPONDING ls_t13 TO ls_tmp.

      APPEND ls_tmp TO lt_tc04_tmp.
    ENDLOOP.


    LOOP AT lt_tc04_tmp INTO DATA(ls_tc04_tmp).

      IF ls_tc04_tmp-zghf IS NOT INITIAL.
        PERFORM frm_get_zghf_attr USING ls_tc04_tmp-zghf CHANGING lv_flg.
        IF lv_flg EQ 'E'.

          IF ls_tc02-zetsplr = 'X'.
            READ TABLE lt_tc04_tmp TRANSPORTING NO FIELDS WITH KEY zghf = 'ALL'.
            IF sy-subrc EQ 0.
              IF ls_tc04_tmp-zghf NE 'ALL' .
                IF ls_tc04_tmp-zzzpc = ''.
                  PERFORM frm_add_msg USING '当外部供货方存在ALL值，则其他外部供货方只能排除'  CHANGING pt_msglist.
                ENDIF.
              ENDIF.
            ELSE.
              IF ls_tc04_tmp-zghf NE 'ALL' .
                IF ls_tc04_tmp-zzzpc = 'X'.
                  PERFORM frm_add_msg USING '当外部供货方不存在ALL值，则其他外部供货方不能排除'  CHANGING pt_msglist.
                ENDIF.
              ENDIF.
            ENDIF.
          ENDIF.

        ELSEIF  lv_flg = 'I'.
          IF ls_tc02-zitsplr = 'X'.
            IF ls_tc04_tmp-zzzpc = ''.
              PERFORM frm_add_msg USING '若勾选了内部供货方，则维护的内部供货方只能排除'  CHANGING pt_msglist.
            ENDIF.
          ELSE.
            IF ls_tc04_tmp-zzzpc = 'X'.
              PERFORM frm_add_msg USING '若没有勾选内部供货方，则维护的内部供货方不能排除'  CHANGING pt_msglist.
            ENDIF.
          ENDIF.

        ENDIF.
      ENDIF.

    ENDLOOP.


    IF ls_tc02-zitsplr = ''.
      IF lt_tc04_tmp[] IS INITIAL.
        IF NOT ( ps_tc05-zhsjz = '1002' OR ps_tc05-zhsjz = '1003' OR ps_tc05-zhsjz = '1005' ).
          CLEAR lv_msgv1. lv_msgv1 = '行项目或条款' && gs_data_base-zghf_t  && '不能同时为空' && '行项目ID:' && ls_tc02-zitems.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
*          PERFORM frm_add_msg USING '行项目或条款供货方不能同时为空'  CHANGING pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.









******    IF ls_tc02-zitsplr = '' AND ls_tc02-zetsplr = ''  .
******      IF lt_tmp[] IS  INITIAL.
******        PERFORM frm_add_msg USING '行项目供货方不得为空'  CHANGING pt_msglist.
******      ENDIF.
******
******
******    ELSEIF  ls_tc02-zitsplr = '' AND ls_tc02-zetsplr = 'X'  .
******      IF lt_tmp[] IS  INITIAL AND pt_t13[] IS INITIAL.
******        PERFORM frm_add_msg USING '行项目或条款供货方不能同时为空'  CHANGING pt_msglist.
******      ENDIF.
******
******      READ TABLE pt_t13 TRANSPORTING NO FIELDS WITH KEY zghf = 'ALL'.
******      IF sy-subrc EQ 0.
******        LOOP AT lt_tmp INTO DATA(ls_tmp).
******
******          IF ls_tmp-zghf IS NOT INITIAL.
******            PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
******            IF lv_flg NE 'I'.
******              PERFORM frm_add_msg USING '若行项目录入了非内部供货方，则外部供货方不能为ALL'  CHANGING pt_msglist.
******            ENDIF.
******          ENDIF.
******
******
******        ENDLOOP.
******      ENDIF.
******
******    ELSEIF  ls_tc02-zitsplr = 'X' AND ls_tc02-zetsplr = ''  .
*******      LOOP AT lt_tmp INTO ls_tmp  .
*******
*******        IF ls_tmp-zzzpc = ''.
*******          PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
*******          IF lv_flg NE 'I'.
*******            PERFORM frm_add_msg USING '若行项目勾选了内部供货，行项目不允许维护内部供货方'  CHANGING pt_msglist.
*******          ENDIF.
*******        ENDIF.
*******
*******      ENDLOOP.
******    ELSEIF  ls_tc02-zitsplr = 'X' AND ls_tc02-zetsplr = 'X'  .
******      READ TABLE pt_t13 TRANSPORTING NO FIELDS WITH KEY zghf = 'ALL'.
******      IF sy-subrc EQ 0.
******        LOOP AT lt_tmp INTO ls_tmp.
******
******          IF ls_tmp-zghf IS NOT INITIAL.
******            PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
******            IF lv_flg NE 'I'.
******              PERFORM frm_add_msg USING '若行项目录入了非内部供货方，则外部供货方不能为ALL'  CHANGING pt_msglist.
******            ENDIF.
******          ENDIF.
******
******        ENDLOOP.
******      ENDIF.
******
******    ENDIF.
******
******
******    IF  ls_tc02-zitsplr = 'X'.
******
******      LOOP AT lt_tmp INTO ls_tmp.
******
******        IF ls_tmp-zzzpc = ''.
******          IF ls_tmp-zghf IS NOT INITIAL.
******            PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
******            IF lv_flg EQ 'I'.
******              PERFORM frm_add_msg USING '若行项目勾选了内部供货，行项目不允许维护内部供货方'  CHANGING pt_msglist.
******            ENDIF.
******          ENDIF.
******        ENDIF.
******
******      ENDLOOP.
******    ENDIF.
******
******    IF ls_tc02-zitsplr = ''.
******      LOOP AT lt_tmp INTO ls_tmp.
******        IF ls_tmp-zzzpc = 'X'.
******          IF ls_tmp-zghf IS NOT INITIAL.
******            PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
******            IF lv_flg EQ 'I'.
******              PERFORM frm_add_msg USING '若行项目没有勾选内部供货，行项目不允许排除内部供货方'  CHANGING pt_msglist.
******            ENDIF.
******          ENDIF.
******        ENDIF.
******      ENDLOOP.
******    ENDIF.
******
******
******    IF  ls_tc02-zetsplr = 'X'.
******      READ TABLE pt_t13 TRANSPORTING NO FIELDS WITH KEY zzzpc = 'X'.
******      IF sy-subrc EQ 0.
******        LOOP AT lt_tmp INTO ls_tmp WHERE zzzpc = ''.
******          IF ls_tmp-zghf IS NOT INITIAL.
******            PERFORM frm_get_zghf_attr USING ls_tmp-zghf CHANGING lv_flg.
******            IF lv_flg EQ 'E'.
******              PERFORM frm_add_msg USING '条款中维护了供货方为排除，则协议中只允许排除外部供货方'  CHANGING pt_msglist.
******            ENDIF.
******            EXIT.
******          ENDIF.
******        ENDLOOP.
******      ENDIF.
******    ENDIF.




    PERFORM frm_check_data_zzzpc USING
                                      lt_tc04_tmp
                                      lt_dcwrk_tmp
                                      lt_werks_tmp
                                      ls_tc02
                                CHANGING pt_msglist.

  ENDLOOP.









ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_END
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_pro_data_end  CHANGING pt_tc02 TYPE tt_tc02
                                ps_ta02 TYPE LINE OF tt_ta02
                                pt_ta04 TYPE tt_ta04
                                pt_t11_all  TYPE tt_t11
                                ps_data_base TYPE ty_data_base
                                ps_data_scn_ctrl TYPE zres0056.



  PERFORM frm_pro_data_scn_ctrl  USING ps_data_base
                                       ps_ta02
                                 CHANGING ps_data_scn_ctrl.


  PERFORM frm_get_eknam USING ps_ta02-ekgrp CHANGING ps_ta02-eknam.


  PERFORM frm_set_screen_zspz_id_t USING ps_ta02-zspz_id
                                    CHANGING ps_ta02-zspzid_txt .

  PERFORM frm_set_screen_zspz_id_t USING ps_ta02-zspz_id
                                    CHANGING ps_ta02-zspzid_txt .

  SELECT SINGLE ztmptxt INTO ps_ta02-ztmptxt FROM zretc001 WHERE ztmpid = ps_ta02-ztmpid.

  PERFORM frm_set_seq TABLES pt_tc02 USING 'SEQ'.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_PRO_ORG_01
*&---------------------------------------------------------------------*
*& 获取行项目信息
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZTMPID
*&      <-- GS_TC01
*&      <-- GT_TC01
*&      <-- GT_TC02
*&      <-- GT_TC03
*&      <-- GT_TC04_SET
*&      <-- GT_BUKRS_SET
*&      <-- GT_DCWRK_SET
*&      <-- GT_WERKS_SET
*&      <-- GT_EKORG_SET
*&---------------------------------------------------------------------*
FORM frm_get_data_org  USING    ps_data_base  TYPE ty_data_base
                          CHANGING
                            ps_tc01 TYPE LINE OF tt_tc01
                            pt_tc02 TYPE tt_tc02
                            pt_tc03 TYPE tt_tc03
                            pt_tc04 TYPE tt_tc04
                            pt_t44_all TYPE tt_t44
                            pt_bukrs TYPE tt_bukrs
                            pt_dcwrk TYPE tt_dcwrk
                            pt_werks TYPE tt_werks
                            pt_ekorg TYPE tt_ekorg
                            pt_matnr TYPE tt_matnr
                            pt_zzgys TYPE tt_t84
  .

*  取条款中数据
  IF ps_data_base-ztk_id IS NOT INITIAL..

    PERFORM frm_get_org_data_ztk_id USING ps_data_base-ztk_id
                         CHANGING
                                  ps_tc01
                                  pt_tc02
                                  pt_tc03
                                  pt_tc04
                                  pt_t44_all
                                  pt_matnr
                                  pt_zzgys.

  ELSE.
*  取模板中数据
    PERFORM frm_get_org_data_ztmpid USING ps_data_base-ztmpid
                         CHANGING
                                  ps_tc01
                                  pt_tc02
                                  pt_tc03
                                  pt_tc04
                                  pt_t44_all
                                  pt_matnr
                                  pt_zzgys.

    DELETE pt_tc02 WHERE zstatus EQ 'D'.
  ENDIF.


*   组织结构解析
  PERFORM frm_zzzlx_unfold                USING       pt_tc03
                                          CHANGING
                                                     pt_bukrs
                                                     pt_dcwrk
                                                     pt_werks
                                                     pt_ekorg.

*  PERFORM frm_pro_ztmpid_data CHANGING
*                                ps_tc01
*                                pt_tc02
*                                pt_tc03
*                                pt_tc04
*                                pt_bukrs
*                                pt_dcwrk
*                                pt_werks
*                                pt_ekorg.

ENDFORM.



FORM frm_dtl_perform_jt  USING    pv_code TYPE sy-ucomm
                                  pv_cursor_line TYPE i
                                  ps_tc05   TYPE LINE OF tt_tc05
                         CHANGING pt_tc02   TYPE  tt_tc02
                                  ps_tc02_sub   TYPE LINE OF tt_tc02
                                  pt_t11_all TYPE tt_t11
                                  pt_t11_sub TYPE tt_t11
                                  .


  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems,
    lv_code       TYPE sy-ucomm.

  DATA:
        ls_t11_data TYPE LINE OF tt_t11.

  CLEAR ps_tc02_sub.

  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.

  PERFORM frm_dtl_get_zitems USING lv_index_line
                                   'GT_TC02[]'
                             CHANGING lv_zitems_key.

  READ TABLE pt_tc02 INTO DATA(ls_tc02) INDEX lv_index_line.
  IF sy-subrc EQ 0.

    ps_tc02_sub = ls_tc02.

*    从全部子阶梯数据中分离出当前行对应的子阶梯数据
    PERFORM frm_pro_data_zjt_sub CHANGING   ls_tc02
                                          pt_t11_all
                                          pt_t11_sub.

    CALL SCREEN '8005'.

*  阶梯数据处理 防止因未回车导致的错误
    PERFORM frm_set_data_zjt  USING    ps_tc05
                              CHANGING pt_t11_sub.

*   更新子阶梯ID
    CLEAR ls_t11_data.
    ls_t11_data-zjt_id = ls_tc02-zjt_id.
    ls_t11_data-zitems_key = ls_tc02-zitems_key.
    MODIFY pt_t11_sub FROM ls_t11_data TRANSPORTING zjt_id  zitems_key WHERE zjt_id IS INITIAL OR zitems_key IS INITIAL.

*    将屏幕上子阶梯数据合并到阶梯表中 后面一起更新到数据库中
    APPEND LINES OF pt_t11_sub TO pt_t11_all.
    MODIFY pt_tc02 FROM ls_tc02 INDEX lv_index_line.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_HSZJT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_TC02
*&      <-- PT_T11_ALL
*&      <-- PT_T11_SUB
*&---------------------------------------------------------------------*
FORM frm_pro_data_zjt_sub  CHANGING ps_tc02       TYPE LINE OF tt_tc02
                                  pt_t11_hs     TYPE tt_t11
                                  pt_t11_hs_sub TYPE tt_t11.

  CLEAR:
        pt_t11_hs_sub.

  IF ps_tc02-zjt_id IS INITIAL.
*     获取阶梯编号
    PERFORM frm_get_num USING 'ZRE0002' '01' CHANGING ps_tc02-zjt_id.
  ELSE.
*    从全部数据中获取当前行对应的子阶梯数据，同时从中删除
    pt_t11_hs_sub[] = pt_t11_hs[].

    DELETE pt_t11_hs_sub WHERE zitems_key NE ps_tc02-zitems_key.
    DELETE pt_t11_hs     WHERE zitems_key = ps_tc02-zitems_key.

*    DELETE pt_t11_hs_sub WHERE zjt_id NE ps_tc02-zjt_id.
*    DELETE pt_t11_hs     WHERE zjt_id = ps_tc02-zjt_id.

  ENDIF.


ENDFORM.


FORM frm_add_spz_to_t58_set  USING    pv_code TYPE sy-ucomm
                                      pv_cursor_line TYPE i
                                      pt_ta05 TYPE tt_ta05
                                      pv_zspz_id  TYPE zret0009-zspz_id
                              CHANGING pt_t58_set TYPE tt_t58.

  DATA:
    lv_index_line TYPE i.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.


  READ TABLE pt_ta05 INTO DATA(ls_ta05) INDEX lv_index_line.
  IF sy-subrc NE 0.
    RETURN.
  ENDIF.

  PERFORM frm_add_spz_to_t58_set_exe USING
                                       ls_ta05
                                       pv_zspz_id
                                 CHANGING pt_t58_set.

*  READ TABLE pt_t58_set TRANSPORTING NO FIELDS WITH KEY zitems_key = ls_ta05-zitems_key.
*  IF sy-subrc EQ 0.
*    RETURN.
*  ENDIF.
*
**  获取商品组明细
*  SELECT
*    a~*
*    FROM zret0020 AS a
*    WHERE zspz_id = @pv_zspz_id
*    INTO TABLE @DATA(lt_zret0020).
*
*  LOOP AT lt_zret0020 INTO DATA(ls_zret0020).
*    CLEAR ls_t58_set.
*    MOVE-CORRESPONDING ls_zret0020 TO ls_t58_set.
*    ls_t58_set-zitems_key = ls_ta05-zitems_key.
*    APPEND ls_t58_set TO pt_t58_set.
*  ENDLOOP.


ENDFORM.

FORM frm_add_spz_to_t58_set_exe  USING
                                      ps_ta05 TYPE LINE OF  tt_ta05
                                      pv_zspz_id  TYPE zret0009-zspz_id
                              CHANGING pt_t58_set TYPE tt_t58.

  DATA:
    ls_t58_set    TYPE LINE OF tt_t58.

  READ TABLE pt_t58_set TRANSPORTING NO FIELDS WITH KEY zitems_key = ps_ta05-zitems_key.
  IF sy-subrc EQ 0.
    RETURN.
  ENDIF.

*  获取商品组明细
  SELECT
    a~*
    FROM zret0020 AS a
    WHERE zspz_id = @pv_zspz_id
    INTO TABLE @DATA(lt_zret0020).

  LOOP AT lt_zret0020 INTO DATA(ls_zret0020).
    CLEAR ls_t58_set.
    MOVE-CORRESPONDING ls_zret0020 TO ls_t58_set.
    ls_t58_set-zitems_key = ps_ta05-zitems_key.
    APPEND ls_t58_set TO pt_t58_set.
  ENDLOOP.

ENDFORM.

FORM frm_dtl_perform_zmzmx  USING    pv_code TYPE sy-ucomm
                            pv_cursor_line TYPE i
                            pt_ta05 TYPE tt_ta05
                      CHANGING ps_ta05 TYPE LINE OF tt_ta05.



  DATA:
    lv_index_line TYPE i,
    lv_zitems_key TYPE zretc002-zitems,
    lv_code       TYPE sy-ucomm.

*  获取选中的行
  PERFORM frm_get_index_line USING      pv_code
                                        pv_cursor_line
                             CHANGING   lv_index_line.
*  获取行项目KEY
  PERFORM frm_dtl_get_zitems USING lv_index_line
                                   'GT_TA05[]'
                             CHANGING lv_zitems_key.

  PERFORM frm_dtl_pro_data USING pv_code 'PBO'  lv_zitems_key.

*  获取行项目数据
  CLEAR ps_ta05.
  READ TABLE pt_ta05 INTO DATA(ls_ta05) INDEX lv_index_line.
  IF sy-subrc EQ 0.
    ps_ta05 = ls_ta05.
  ENDIF.

*  屏幕跳转前记录 OK_CODE
  CLEAR lv_code.
  lv_code = pv_code.
  PERFORM frm_dtl_call_screen USING pv_code.

  PERFORM frm_dtl_pro_data USING lv_code 'PAI'  lv_zitems_key.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_T06
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_TC02
*&      <-- GT_T11_ALL
*&---------------------------------------------------------------------*
FORM frm_get_data_t06  USING    pt_tc02 TYPE tt_tc02
                                 ps_ta02 TYPE LINE OF tt_ta02
                       CHANGING
                                pt_t44_all  TYPE tt_t44
                                pt_t11_all  TYPE tt_t11
                                pt_t11  TYPE tt_t11.

  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0011 AS a
                         ON i~zjt_id = a~zjt_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t11_all.


  LOOP AT pt_t11_all INTO DATA(ls_t11_all).
    SELECT SINGLE zitems INTO ls_t11_all-zitems FROM zret0006 WHERE zjt_id = ls_t11_all-zjt_id .

    IF ls_t11_all-zitems_key IS INITIAL.
      READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zitems = ls_t11_all-zitems.
      IF sy-subrc EQ 0.
        ls_t11_all-zitems_key = ls_tc02-zitems_key.
      ENDIF.
*      ls_t11_all-zitems_key = ls_t11_all-zitems.  %%%%%
    ENDIF.
    IF ls_t11_all-zjt_from = 0.
      ls_t11_all-zflg_1st = 'X'.
    ENDIF.
    MODIFY pt_t11_all FROM ls_t11_all.
  ENDLOOP.

  SELECT * INTO CORRESPONDING FIELDS OF TABLE pt_t11 FROM zret0011 WHERE zjt_id = ps_ta02-zjt_id .


*  改为在条款的FORM中取数
*  SELECT
*    a~*
*    FROM @pt_tc02 AS i JOIN zret0044 AS a
*                         ON i~zxy_id = a~zxy_id
*    INTO CORRESPONDING FIELDS OF TABLE @pt_t44_all.


  LOOP AT pt_t44_all INTO DATA(ls_t44_all).

*    SELECT SINGLE zitems INTO ls_t44_all-zitems_key FROM zret0006 WHERE zxy_id = ls_t44_all-zxy_id .  %%%%%
*******    SELECT SINGLE zitems INTO @DATA(lv_zitems_tmp) FROM zret0006 WHERE zxy_id = @ls_t44_all-zxy_id .
*******    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zitems = lv_zitems_tmp.
*******    IF sy-subrc EQ 0.
*******      ls_t44_all-zitems_key = ls_tc02-zitems_key.
*******    ENDIF.
*******    CLEAR lv_zitems_tmp.

    PERFORM frm_get_zghf_t USING ls_t44_all-zflzff CHANGING ls_t44_all-name1.
    MODIFY pt_t44_all FROM ls_t44_all.
  ENDLOOP.


****  自动将行项目返利支付方添加到返利支付方列表中第一行
***  PERFORM frm_add_zflzff_t44_all USING pt_tc02
***                                  CHANGING pt_t44_all.


*  读取T44表中第一行数据写入到行项目
  DATA(lt_t44_all) = pt_t44_all[].
  SORT lt_t44_all BY zitems_key.

  LOOP AT pt_tc02 INTO ls_tc02.
    READ TABLE lt_t44_all INTO ls_t44_all WITH KEY zitems_key = ls_tc02-zitems_key BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_tc02-zflzff = ls_t44_all-zflzff.
      ls_tc02-zpaytp = ls_t44_all-zpaytp.
      MODIFY pt_tc02 FROM ls_tc02.
    ENDIF.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_T06
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_TC02
*&      <-- GT_T11_ALL
*&---------------------------------------------------------------------*
FORM frm_get_data_zlqd  USING    pt_tc02 TYPE tt_tc02
                                 ps_ta02 TYPE LINE OF tt_ta02
                       CHANGING
                                pt_zzlbm  TYPE tt_t81
                                pt_zqdbm  TYPE tt_t82.
*                                pt_zzgys  TYPE tt_t84.


  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0081 AS a
                         ON i~zxy_id = a~zxy_id AND a~zxy_id <> ''
    INTO CORRESPONDING FIELDS OF TABLE @pt_zzlbm  .

  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0082 AS a
                         ON i~zxy_id = a~zxy_id AND a~zxy_id <> ''
    INTO CORRESPONDING FIELDS OF TABLE @pt_zqdbm.

*  SELECT
*    a~*
*    FROM @pt_tc02 AS i JOIN zret0084 AS a
*                         ON i~zxy_id = a~zxy_id
*    INTO CORRESPONDING FIELDS OF TABLE @pt_zzgys.

  LOOP AT pt_zzlbm INTO DATA(ls_zzlbm) .
    READ TABLE pt_tc02 INTO DATA(ls_tc02) WITH KEY zxy_id = ls_zzlbm-zxy_id.
    IF sy-subrc EQ 0.
      ls_zzlbm-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zzlbm FROM  ls_zzlbm.
  ENDLOOP.
  IF sy-subrc <> 0.
    LOOP AT pt_tc02 INTO ls_tc02.
      ls_zzlbm-zitems_key = ls_tc02-zitems_key.
      ls_zzlbm-zzlbm      = '999'.
      APPEND ls_zzlbm TO pt_zzlbm.
    ENDLOOP.
  ENDIF.

  LOOP AT  pt_zqdbm INTO DATA(ls_zqdbm) ..
    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zxy_id = ls_zqdbm-zxy_id.
    IF sy-subrc EQ 0.
      ls_zqdbm-zitems_key = ls_tc02-zitems_key.
    ENDIF.
    MODIFY pt_zqdbm FROM  ls_zqdbm.
  ENDLOOP.
  IF sy-subrc <> 0.
    LOOP AT pt_tc02 INTO ls_tc02.
      ls_zqdbm-zitems_key = ls_tc02-zitems_key.
      ls_zqdbm-zqdbm      = '9999'.
      APPEND ls_zqdbm TO pt_zqdbm.
    ENDLOOP.
  ENDIF.
*  LOOP AT  pt_zzgys INTO DATA(ls_zzgys) ..
*    READ TABLE pt_tc02 INTO ls_tc02 WITH KEY zxy_id = ls_zzgys-zxy_id.
*    IF sy-subrc EQ 0.
*      ls_zzgys-zitems_key = ls_tc02-zitems_key.
*    ENDIF.
*    MODIFY pt_zzgys FROM  ls_zzgys.
*  ENDLOOP.

ENDFORM.

FORM frm_data_conver_t08  USING    ps_tc02 TYPE LINE OF tt_tc02
*                                  ps_ta01 TYPE LINE OF tt_ta01
*                                  ps_ta02 TYPE LINE OF tt_ta02
                                  pt_matnr  TYPE tt_matnr
                         CHANGING ps_t08    TYPE LINE OF tt_t08.

  DATA:lt_matnr TYPE tt_matnr.

  lt_matnr = pt_matnr[].
  DELETE  lt_matnr WHERE zitems_key <> ps_tc02-zitems_key.
  ps_t08-zxy_id  =  ps_tc02-zxy_id   .
  ps_t08-zje     =  ps_tc02-zje   .
  ps_t08-zmwskz  =  ps_tc02-zmwskz   .
  ps_t08-matnr   =  ps_tc02-matnr   .
  ps_t08-zsl     =  ps_tc02-zsl   .
  ps_t08-zdate   =  ps_tc02-zdate   .
  ps_t08-zflbz   =  ps_tc02-zflbz   .
  ps_t08-zfljs   =  ps_tc02-zfljs   .
  ps_t08-zjzzd   =  ps_tc02-zjzzd   .
  ps_t08-zsqbm_xy   =  ps_tc02-zsqbm_xy   .
  IF lines( lt_matnr[] ) = 1  .
    ps_t08-matnr = lt_matnr[ 1 ]-matnr.
  ELSE.
    CLEAR:ps_t08-matnr.
  ENDIF.
  REFRESH lt_matnr.

ENDFORM.

FORM frm_data_conver_t07  USING    ps_tc02 TYPE LINE OF tt_tc02
*                                  ps_ta01 TYPE LINE OF tt_ta01
*                                  ps_ta02 TYPE LINE OF tt_ta02
                         CHANGING ps_t07  TYPE LINE OF tt_t07.

  ps_t07-zxy_id  =  ps_tc02-zxy_id   .
  ps_t07-zspz_id     =  ps_tc02-zspz_id   .


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CONVER_03
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_TC03
*&      <-- PT_T14
*&---------------------------------------------------------------------*
FORM frm_data_conver_03  USING    pt_tc03 TYPE tt_tc03
                         CHANGING pt_t14  TYPE tt_t14.

  DATA:
        ls_t14 TYPE LINE OF tt_t14.

  LOOP AT pt_tc03 INTO DATA(ls_tc03).
    CLEAR ls_t14.
    MOVE-CORRESPONDING ls_tc03 TO ls_t14.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_3001
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T12
*&      --> GT_T13
*&      --> GT_T44
*&      <-- GS_TA02
*&---------------------------------------------------------------------*
FORM frm_set_data_txt  USING    pt_t12 TYPE tt_t12
                                 pt_t13 TYPE tt_t13
                                 pt_t44 TYPE tt_t44
                        CHANGING ps_ta02  TYPE LINE OF tt_ta02.


  CLEAR:
        ps_ta02-zghf_t,
        ps_ta02-zflzff_t,
        ps_ta02-lifnr_t.

  LOOP AT pt_t12 INTO DATA(ls_t12).

*    IF ls_t12-exclude = 'X'.
*      ps_t06-lifnr_t =  '-' && ls_t12-lifnr  && ','  && ps_t06-lifnr_t.
*    ELSE.
*      ps_t06-lifnr_t = ls_t12-lifnr && ',' && ps_t06-lifnr_t.
*    ENDIF.

    ps_ta02-lifnr_t = ls_t12-lifnr && ',' && ps_ta02-lifnr_t.

  ENDLOOP.


  LOOP AT pt_t13 INTO DATA(ls_t13).

*    IF ls_t13-exclude = 'X'.
*      ps_ta02-zghf_t =  '-' && ls_t13-zghf  && ','  && ps_ta02-zghf_t.
*    ELSE.
*      ps_ta02-zghf_t = ls_t13-zghf && ',' && ps_ta02-zghf_t.
*    ENDIF.

    ps_ta02-zghf_t = ls_t13-zghf && ',' && ps_ta02-zghf_t.

  ENDLOOP.

  LOOP AT pt_t44 INTO DATA(ls_t44).

*    IF ls_t44-exclude = 'X'.
*      ps_ta02-zflzff_t =  '-' && ls_t44-zflzff  && ','  && ps_ta02-zflzff_t.
*    ELSE.
*      ps_ta02-zflzff_t = ls_t44-zflzff && ',' && ps_ta02-zflzff_t.
*    ENDIF.
    ps_ta02-zflzff_t = ls_t44-zflzff && ',' && ps_ta02-zflzff_t.

  ENDLOOP.



ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_ORG_DATA_ZTMPID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&      <-- GT_TC03
*&      <-- GT_TC04
*&---------------------------------------------------------------------*
FORM frm_set_org_data_ztmpid  USING    pv_ztmpid TYPE zretc002-ztmpid
                               CHANGING
                                 ps_tc01 TYPE LINE OF tt_tc01
                                 pt_tc02 TYPE tt_tc02
                                 pt_tc03 TYPE tt_tc03
                                 pt_tc04 TYPE tt_tc04
                                 pt_t44_all TYPE tt_t44
                                 pt_bukrs TYPE tt_bukrs
                                 pt_dcwrk TYPE tt_dcwrk
                                 pt_werks TYPE tt_werks
                                 pt_ekorg TYPE tt_ekorg
                                 pt_matnr TYPE tt_matnr
                                 pt_zzgys TYPE tt_t84
  .


  PERFORM frm_get_org_data_ztmpid USING pv_ztmpid
                       CHANGING
                                ps_tc01
                                pt_tc02
                                pt_tc03
                                pt_tc04
                                pt_t44_all
                                pt_matnr
                                pt_zzgys.

*   组织结构解析
  PERFORM frm_zzzlx_unfold                USING       pt_tc03
                                          CHANGING
                                                     pt_bukrs
                                                     pt_dcwrk
                                                     pt_werks
                                                     pt_ekorg.

*  PERFORM frm_pro_ztmpid_data CHANGING
*                                ps_tc01
*                                pt_tc02
*                                pt_tc03
*                                pt_tc04
*                                pt_bukrs
*                                pt_dcwrk
*                                pt_werks
*                                pt_ekorg.


ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_ORG_DATA_ZTMPID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA02_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&      <-- GT_TC03
*&      <-- GT_TC04
*&---------------------------------------------------------------------*
FORM frm_get_zaccer  USING    pv_zhscj
                      CHANGING lv_zaccer.

  DATA:lv_partner TYPE but000-partner.

  IF pv_zhscj = '1000004716' .
    CLEAR:lv_zaccer.
  ELSEIF pv_zhscj IS NOT INITIAL.
*    lv_partner =  |{ pv_zhscj ALPHA = IN }|.
*    SELECT SINGLE name_org1 INTO lv_zaccer FROM but000 WHERE partner = lv_partner.
*    IF sy-subrc <> 0.
*      CLEAR:lv_zaccer.
*    ENDIF.
  ELSE.
    CLEAR:lv_zaccer.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_F4_ZCLRID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZCLRID
*&---------------------------------------------------------------------*
FORM frm_f4_zclrid  USING    pt_tc05 TYPE tt_tc05.

  DATA dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.
  DATA:lt_ret_tab TYPE TABLE OF ddshretval WITH HEADER LINE.



  SELECT
    i~*
    FROM @pt_tc05 AS i
    INTO TABLE @DATA(lt_tc05).

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'ZCLRID'
      value_org       = 'S'
      dynpprog        = sy-repid
      dynpnr          = sy-dynnr
      dynprofield     = 'GS_TC05-ZCLRID'
*     callback_program = sy-repid
*     callback_form   = 'USER_FORM'
    TABLES
      value_tab       = lt_tc05[]
      return_tab      = lt_ret_tab[]
    EXCEPTIONS
      parameter_error = 1
      no_values_found = 2
      OTHERS          = 3.


  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_TC04
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_TC02
*&      <-- PT_TC04
*&---------------------------------------------------------------------*
FORM frm_get_data_tc04  USING    pt_tc02  TYPE tt_tc02
                        CHANGING pt_tc04  TYPE tt_tc04.

  SELECT
    a~*
    FROM @pt_tc02 AS i JOIN zret0013 AS a
                         ON i~zxy_id = a~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_tc04.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_ZJT_ITEMID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_T11
*&---------------------------------------------------------------------*
FORM frm_set_zjt_itemid USING ps_tc05 TYPE LINE OF tt_tc05
                         CHANGING pt_t11 TYPE tt_t11
                                  pt_t10 TYPE tt_t10.

  DATA:
    ls_t10 TYPE LINE OF tt_t10,
    ls_t11 TYPE LINE OF tt_t11.

  DATA:
        lv_num TYPE i.

  CLEAR pt_t10.

*  核算期间子阶梯明细表
  CLEAR lv_num.
  SORT pt_t11 BY zjt_id zjt_from.

*  将阶梯表的MANDT置为空，防止 AT NEW 语句BUG
  ls_t11-mandt = ''.
  MODIFY pt_t11 FROM ls_t11 TRANSPORTING mandt WHERE mandt NE ''.
  CLEAR ls_t11.

  LOOP AT pt_t11 INTO ls_t11.
    AT NEW zjt_id.
*      阶梯号码更改后，行项目重新编号
      CLEAR lv_num.
    ENDAT.
    lv_num                = lv_num + 1.
    ls_t11-zjt_itemid  = lv_num.
    MODIFY pt_t11 FROM ls_t11.

*   根据明细表组建抬头表 因为核算期间子阶梯没有抬头数据
*   取协议主阶梯中的数据
    CLEAR: ls_t10.
    MOVE-CORRESPONDING ps_tc05 TO ls_t10.
    APPEND  ls_t10 TO pt_t10 .
  ENDLOOP.
*  去重
  SORT pt_t10 BY zjt_id.
  DELETE ADJACENT DUPLICATES FROM pt_t10 COMPARING zjt_id.
ENDFORM.

FORM frm_save_rles_log USING  pv_ztk_id
                              pv_zxy_id
                              pv_zxyzt
                              pv_frgc1
                              ps_approval  TYPE ty_approval.

  DATA: ls_t50 TYPE zret0050.

  ls_t50-ztk_id     = pv_ztk_id.
  ls_t50-zxy_id     = pv_zxy_id.
  ls_t50-timestamp  = sy-datum && sy-uzeit.
  ls_t50-zxyzt_o    = pv_zxyzt.
  ls_t50-frgc1_o    = pv_frgc1.
  ls_t50-zxyzt_n    = ps_approval-zxyzt.
  ls_t50-frgc1_n    = ps_approval-frgc1.
  ls_t50-zcjrq  = sy-datum.
  ls_t50-zcjsj  = sy-uzeit.
  ls_t50-zcjr   = sy-uname.

  MODIFY zret0050 FROM ls_t50.

ENDFORM.



*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_HT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01
*&      --> GV_ACTVT
*&---------------------------------------------------------------------*
FORM frm_author_check_tk  USING    ps_ta01  TYPE LINE OF tt_ta01
                                   ps_ta02  TYPE LINE OF tt_ta02
                                   pt_tc02  TYPE tt_tc02
                                   pv_actvt TYPE activ_auth
                                   pv_flg   TYPE char1
                          CHANGING pt_msglist TYPE scp1_general_errors
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE scp1_general_error-msgv1.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE scp1_general_error-msgv1.



  PERFORM frm_author_check_ht USING ps_ta01
                                    pv_actvt
                                    pv_flg
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

  IF pv_mtype = 'S'.
    PERFORM frm_author_check_ekgrp USING ps_ta02-ekgrp
                                         pv_actvt
                                   CHANGING
                                         pv_mtype
                                         pv_msg.

  ENDIF.

  IF pv_mtype = 'S'.


    DATA(lt_tc02_tmp) = pt_tc02[].
    LOOP AT pt_tc02 INTO DATA(ls_tc02).

      PERFORM frm_author_check_zbukrs_tk USING ls_tc02-zbukrs
                                           pv_actvt
                                     CHANGING
                                           lv_mtype
                                           lv_msg.
      IF lv_mtype = 'E'.
        IF pv_actvt = '03'.
          DELETE pt_tc02.
          CONTINUE.
        ELSE.
          pv_mtype = lv_mtype.
          pv_msg = lv_msg.
          EXIT.
        ENDIF.
      ENDIF.

    ENDLOOP.

    IF pt_tc02[] IS INITIAL AND lt_tc02_tmp[] IS NOT INITIAL.
      pv_mtype = lv_mtype.
      pv_msg = lv_msg.
    ENDIF.

  ENDIF.




  IF pv_mtype = 'E'.
    PERFORM frm_author_pro USING
                                 pv_mtype
                                 pv_msg
                                 pv_flg
                           CHANGING pt_msglist.
  ENDIF.



ENDFORM.


FORM frm_author_check_item_frgc1  USING  ps_ta01  TYPE LINE OF tt_ta01
                                         ps_ta02  TYPE LINE OF tt_ta02
                                         ps_tc02  TYPE LINE OF tt_tc02
                                         pv_flg   TYPE char1
                                CHANGING pv_mtype TYPE bapi_mtype
                                         pv_msg   TYPE scp1_general_error-msgv1.

  DATA:
     lt_msglist TYPE scp1_general_errors.

  DATA:
        lv_actvt TYPE activ_auth.

  lv_actvt = ps_tc02-frgc1.

  PERFORM frm_author_check_ht    USING ps_ta01
                                       lv_actvt
                                       pv_flg
                              CHANGING lt_msglist
                                       pv_mtype
                                       pv_msg.

  IF ps_ta02-ekgrp IS  NOT INITIAL.

    PERFORM frm_author_check_ekgrp    USING ps_ta02-ekgrp
                                            lv_actvt
                                   CHANGING pv_mtype
                                            pv_msg.

  ENDIF.

  IF pv_mtype = 'E'.
    PERFORM frm_author_pro USING pv_mtype
                                 pv_msg
                                 pv_flg
                           CHANGING lt_msglist.
  ENDIF.



ENDFORM.

FORM frm_author_check_frgc1  USING    ps_ta01  TYPE LINE OF tt_ta01
                                   ps_ta02  TYPE LINE OF tt_ta02
                                   pv_flg   TYPE char1
                          CHANGING
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE scp1_general_error-msgv1.


  DATA:
    lt_msglist TYPE scp1_general_errors.

  DATA:
        lv_actvt TYPE activ_auth.

  lv_actvt = ps_ta02-frgc1.

  PERFORM frm_author_check_ht USING ps_ta01
                                    lv_actvt
                                    pv_flg
                              CHANGING lt_msglist
                                        pv_mtype
                                        pv_msg.

  IF ps_ta02-ekgrp IS  NOT INITIAL.

    PERFORM frm_author_check_ekgrp USING ps_ta02-ekgrp
                                         lv_actvt
                                   CHANGING pv_mtype
                                            pv_msg.

  ENDIF.

  IF pv_mtype = 'E'.
    PERFORM frm_author_pro USING pv_mtype
                                 pv_msg
                                 pv_flg
                           CHANGING lt_msglist.
  ENDIF.





ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_BIN_DATA_FROM_TA01
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_TC02
*&---------------------------------------------------------------------*
FORM frm_bin_data_from_ta01  CHANGING ps_ta02 TYPE LINE OF tt_ta02
                                      pt_t44 TYPE tt_t44.

  SELECT SINGLE * INTO @DATA(ls_zreta001) FROM zreta001 WHERE zht_id = @ps_ta02-zht_id.

  ps_ta02-zbegin = ls_zreta001-zbegin.
  ps_ta02-zend = ls_zreta001-zend.
  ps_ta02-ekgrp = ls_zreta001-ekgrp.
  ps_ta02-zpayday = ls_zreta001-zpayday.
  ps_ta02-zdffs = ls_zreta001-zdffs.
  ps_ta02-zjszq = ls_zreta001-zjszq.
  ps_ta02-zhszq = ls_zreta001-zhszq.
  ps_ta02-zpayday = ls_zreta001-zpayday.
  ps_ta02-zhstype = ls_zreta001-zhstype.

  pt_t44 = VALUE  #(  ( zflzff = ls_zreta001-zflzff  ) ).
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_CANCEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA02_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_process_cancel  USING    pv_ztk_id  TYPE zreta002-ztk_id
                                   pv_xy_id  TYPE zreta002-ztk_id.

  DATA:
    ls_t06    TYPE zret0006,
    lt_t06    TYPE TABLE OF zret0006,
    lt_t06_cx TYPE TABLE OF zret0006,
    lr_zxy_id TYPE RANGE OF zret0006-zxy_id.

  IF pv_xy_id IS NOT INITIAL .
    lr_zxy_id = VALUE #(  ( sign = 'I' option = 'EQ'  low = pv_xy_id   high = '' )   ).
  ENDIF.

  SELECT
    zxy_id
    INTO CORRESPONDING FIELDS OF TABLE lt_t06
    FROM zret0006
    WHERE ztk_id = pv_ztk_id
      AND zxy_id IN lr_zxy_id.

  SELECT *
    INTO CORRESPONDING FIELDS OF TABLE lt_t06_cx
    FROM zret0006
    WHERE ztk_id = pv_ztk_id
      AND zxy_id IN lr_zxy_id.


  CALL FUNCTION 'ZREFM0019'
*   IMPORTING
*     EV_MTYPE       =
*     EV_MSG         =
    TABLES
      it_head = lt_t06[].


*  CALL FUNCTION 'ZREFM0071'
** IMPORTING
**   EV_MTYPE        =
**   EV_MSG          =
*    TABLES
*      it_zxy_id = lt_t06_cx[].
  .
  .


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_T44
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_T44
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_t44  USING    pt_t44     TYPE tt_t44
                             pv_zhtlx   TYPE zree_zhtlx
                    CHANGING pt_msglist TYPE scp1_general_errors .
  DATA:
        lv_flg TYPE char1.

  SELECT SINGLE zsfsx INTO @DATA(lv_zsfsx) FROM zretcm13 WHERE zhtlx = @pv_zhtlx.

  LOOP AT pt_t44 INTO DATA(ls_t44).
*& ADD BY zsfsx = '2'
    IF lv_zsfsx = '2' .
      PERFORM frm_check_zff_attr     USING ls_t44-zflzff
                                  CHANGING lv_flg.

      IF lv_flg NE ''.
        PERFORM frm_add_msg USING '支付方不存在表业务伙伴but000中，请检查！'  CHANGING pt_msglist.
      ENDIF.
    ELSE.
      PERFORM frm_get_zghf_attr     USING ls_t44-zflzff
                                  CHANGING lv_flg.

      IF lv_flg NE 'E'.
        PERFORM frm_add_msg USING '外部支付方只允许维护外部支付方，请检查！'  CHANGING pt_msglist.
      ENDIF.

    ENDIF.



  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_GET_ZGHF_ATTR
*&---------------------------------------------------------------------*
*& TEXT
*& ADD BY zsfsx = '2'
*&---------------------------------------------------------------------*
*&      --> LS_TMP_ZGHF
*&      <-- LV_FLG
*&---------------------------------------------------------------------*
FORM frm_check_zff_attr      USING pv_lifnr  TYPE lfa1-lifnr
                          CHANGING pv_flg    TYPE char1.

*  I 内部 E 外部 X 不存在

  DATA(lv_zghf) = |{ pv_lifnr ALPHA = OUT }|.
  CONDENSE lv_zghf.

  CLEAR pv_flg.

  SELECT SINGLE COUNT(*) FROM but000 WHERE partner = pv_lifnr.
  IF sy-subrc <> 0.
    pv_flg  = 'X'.
  ENDIF.
ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CHECK_TK_REPEAT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_T44
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_tk_repeat         USING ps_ta02        TYPE LINE OF tt_ta02
                                       pt_tc02        TYPE  tt_tc02
                              CHANGING pt_msglist TYPE scp1_general_errors .

  DATA:lt_0020_dq TYPE TABLE OF zret0020.
  DATA:lt_0020_os TYPE TABLE OF zret0020.
  DATA:lv_msg TYPE scp1_general_error-msgv1.

  SELECT DISTINCT zreta002~ztk_id,zreta002~zspz_id
     INTO TABLE @DATA(lt_ztk_id)
     FROM zreta002
     INNER JOIN zret0006 ON zret0006~ztk_id = zreta002~ztk_id AND zret0006~zxyzt NE 'D'
     FOR ALL ENTRIES IN @pt_tc02
    WHERE zret0006~zxy_id   NE @pt_tc02-zxy_id
      AND zret0006~zbukrs   EQ @pt_tc02-zbukrs
      AND zret0006~zctgr    EQ @pt_tc02-zctgr
      AND zreta002~zspz_id  NE ''
      AND zreta002~ztk_id   NE @ps_ta02-ztk_id
      AND zreta002~zxybstyp EQ @ps_ta02-zxybstyp
      AND zreta002~ztktype  EQ @ps_ta02-ztktype
      AND zreta002~zxyzt    NE 'D'
      AND ( ( zreta002~zbegin <= @ps_ta02-zbegin AND zreta002~zend >= @ps_ta02-zbegin ) OR
            ( zreta002~zbegin <= @ps_ta02-zend   AND zreta002~zend >= @ps_ta02-zend   ) OR
            ( zreta002~zbegin >= @ps_ta02-zbegin AND zreta002~zend <= @ps_ta02-zend   )
          ).
  IF sy-subrc = 0.

    READ TABLE lt_ztk_id INTO DATA(ls_ztkj_id) WITH  KEY zspz_id = ps_ta02-zspz_id.
    IF sy-subrc = 0.
      lv_msg =  |当前条款和条款{ ls_ztkj_id-ztk_id }商品组中的商品有相同的且日期范围（开始至结束日期）有交叉!|.
      PERFORM frm_add_msg_w USING lv_msg  CHANGING pt_msglist.
      EXIT.
    ENDIF.

    SELECT * INTO TABLE lt_0020_dq FROM zret0020 WHERE zspz_id = ps_ta02-zspz_id.

    LOOP AT lt_ztk_id INTO DATA(ls_tk_spz) .
      SELECT * INTO TABLE @DATA(lt_zret0020)  FROM zret0020 WHERE zspz_id = @ls_tk_spz-zspz_id .
      IF sy-subrc = 0.
        LOOP AT lt_zret0020 INTO DATA(ls_zret0020) .

          READ TABLE lt_0020_dq TRANSPORTING NO FIELDS WITH  KEY  matnr = ls_zret0020-matnr
                                                                  zspbc = ls_zret0020-zspbc.
          IF sy-subrc = 0.
            lv_msg =  |当前条款和条款{ ls_tk_spz-ztk_id }商品组中的商品有相同的且日期范围（开始至结束日期）有交叉!|.
            PERFORM frm_add_msg_w USING lv_msg  CHANGING pt_msglist.
            EXIT.
          ENDIF.
        ENDLOOP.

      ENDIF.
    ENDLOOP.
  ENDIF.

ENDFORM.








FORM frm_check_t13  USING    pt_t13 TYPE tt_t13
                    CHANGING pt_msglist TYPE scp1_general_errors .

  DATA:
      lv_flg TYPE char1.

  LOOP AT pt_t13 INTO DATA(ls_t13).

    PERFORM frm_get_zghf_attr USING ls_t13-zghf CHANGING lv_flg.

    IF lv_flg NE 'E'.
      PERFORM frm_add_msg USING '外部供货方只允许维护外部供货方，请检查！'  CHANGING pt_msglist.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_HT_BACK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_FLG_CALL
*&      --> GS_TA02
*&---------------------------------------------------------------------*
FORM frm_ht_back  USING    pv_flg_call  TYPE char2
                           ps_ta02 TYPE LINE OF tt_ta02.

  IF pv_flg_call = ''.
    SET PARAMETER ID 'ZHT_ID' FIELD ps_ta02-zht_id.
    CALL TRANSACTION  'ZRED0040C'  AND SKIP FIRST SCREEN .
    SET PARAMETER ID 'ZHT_ID' FIELD ''.
  ELSE..
    LEAVE TO SCREEN 0.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_DEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01
*&      --> GS_TA02
*&      <-- LV_MTYPE_AUTH
*&      <-- LV_MSG_AUTH
*&---------------------------------------------------------------------*
FORM frm_author_check_del USING    ps_ta01  TYPE LINE OF tt_ta01
                                   ps_ta02  TYPE LINE OF tt_ta02
                                   pt_tc02  TYPE tt_tc02
                                   pv_actvt TYPE activ_auth
                          CHANGING
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE scp1_general_error-msgv1.


*合同类型
  IF ps_ta01-zhtlx IS NOT INITIAL.
    PERFORM frm_author_check_zhtlx USING    ps_ta01-zhtlx
                                            pv_actvt
                                   CHANGING pv_mtype
                                            pv_msg.

  ENDIF.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.
*合同主体
  PERFORM frm_author_check_zbukrs_tk USING ps_ta01-zbukrs
                                           pv_actvt
                                  CHANGING
                                           pv_mtype
                                           pv_msg.

  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.
*合同采购组
  IF ps_ta01-ekgrp IS NOT INITIAL.
    PERFORM frm_author_check_ekgrp   USING ps_ta01-ekgrp
                                           pv_actvt
                                  CHANGING pv_mtype
                                           pv_msg.

    IF pv_mtype = 'E'.
      RETURN.
    ENDIF.
  ENDIF.

*条款采购组
  IF ps_ta02-ekgrp IS  NOT INITIAL.

    PERFORM frm_author_check_ekgrp USING   ps_ta02-ekgrp
                                           pv_actvt
                                  CHANGING pv_mtype
                                           pv_msg.

    IF pv_mtype = 'E'.
      RETURN.
    ENDIF.
  ENDIF.

*  DATA(lt_tc02_tmp) = pt_tc02[].
*  DATA:lv_mtype TYPE bapi_mtype,
*       lv_msg   TYPE scp1_general_error-msgv1.
*
*  LOOP AT lt_tc02_tmp INTO DATA(ls_tc02).
*
*    PERFORM frm_author_check_zbukrs_tk USING ls_tc02-zbukrs
*                                             pv_actvt
*                                   CHANGING
*                                             lv_mtype
*                                             lv_msg.
*    IF lv_mtype = 'E'.
*      IF pv_actvt = '03'.
*        DELETE lt_tc02_tmp.
*        CONTINUE.
*      ELSE.
*        pv_mtype = lv_mtype.
*        pv_msg = lv_msg.
*        EXIT.
*      ENDIF.
*    ENDIF.
*
*  ENDLOOP.
*
*  IF pt_tc02[] IS INITIAL AND lt_tc02_tmp[] IS NOT INITIAL.
*    pv_mtype = lv_mtype.
*    pv_msg = lv_msg.
*  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_TK_PLUS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TC05
*&      --> PT_TA03
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_tk_plus  USING    ps_tc05  TYPE LINE OF tt_tc05
                                 ps_ta02  TYPE LINE OF tt_ta02
                                 pt_ta03  TYPE tt_ta03
                        CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:lv_msgv1 TYPE scp1_general_error-msgv1.




*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 10:38:07
*    <NOTES>  #####
*    <OLD CODES>
*  SELECT SINGLE zxybstyp FROM zret0002 WHERE zfllx = @ps_ta02-zfllx INTO @DATA(lv_zxybstyp).
*  SELECT
*    i~zrlid,
*    a~zfllx,
*    b~zxybstyp,
*    c~zjsff,
*    c~zflxs
*    FROM @pt_ta03 AS i JOIN zreta002 AS a
*                         ON i~zrlid = a~ztk_id
*                        AND ztktype = 'P'
*                       JOIN zret0002 AS b
*                         ON a~zfllx = b~zfllx
*                       LEFT JOIN zretc005 AS c
*                         ON a~zclrid = c~zclrid
*    INTO TABLE @DATA(lt_ta02).
*    </OLD CODES>
*    <NEW CODES>

  SELECT SINGLE zxybstyp FROM zretc009 WHERE zfllx = @ps_ta02-zfllx AND zxybstyp = @ps_ta02-zxybstyp INTO @DATA(lv_zxybstyp).

  SELECT
    i~zrlid,
    a~zfllx,
    b~zxybstyp,
    c~zjsff,
    c~zflxs
    FROM @pt_ta03 AS i JOIN zreta002 AS a
                         ON i~zrlid = a~ztk_id
                        AND ztktype = 'P'
                       JOIN zretc009 AS b
                         ON a~zfllx = b~zfllx
                        AND a~zxybstyp = b~zxybstyp
                       LEFT JOIN zretc005 AS c
                         ON a~zclrid = c~zclrid
    INTO TABLE @DATA(lt_ta02).
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*



  IF lv_zxybstyp = 'A' OR lv_zxybstyp = 'V'.
    LOOP AT lt_ta02 INTO DATA(ls_ta02) WHERE zxybstyp NE lv_zxybstyp.
      IF ps_tc05-zjsff NE ls_ta02-zjsff OR ps_tc05-zflxs NE ls_ta02-zflxs.
        CLEAR lv_msgv1.  lv_msgv1 = '条款类型与附加条款类型不匹配，附加条款：' && ls_ta02-zrlid.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
        EXIT.
      ENDIF.
    ENDLOOP.
  ELSE.
    LOOP AT lt_ta02 INTO ls_ta02 WHERE zxybstyp NE lv_zxybstyp.
      CLEAR lv_msgv1.  lv_msgv1 = '条款类型与附加条款类型不匹配，附加条款：' && ls_ta02-zrlid.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      EXIT.
    ENDLOOP.
  ENDIF.





ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_FRESH_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_VLS_ZXYBSTYP
*&      <-- P_ZXYBTP
*&---------------------------------------------------------------------*
FORM frm_fresh_screen  USING    pt_vls_zxybstyp TYPE vrm_values
                       CHANGING pv_zxybtp TYPE zretc009-zxybstyp.

  READ TABLE pt_vls_zxybstyp TRANSPORTING NO FIELDS WITH KEY key = pv_zxybtp .
  IF sy-subrc NE 0.
    CLEAR pv_zxybtp.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CONVER_MATNR_2_T08
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_MATNR
*&      <-- PT_T08
*&---------------------------------------------------------------------*
FORM frm_data_conver_matnr_2_t08  USING    pt_matnr TYPE tt_matnr
                                  CHANGING pt_t08 TYPE tt_t08.
  DATA:
        ls_t08 TYPE LINE OF tt_t08.

  CHECK pt_matnr[] IS NOT INITIAL.
  LOOP AT pt_matnr INTO DATA(ls_matnr).
    CLEAR ls_t08.
    ls_t08-zxy_id = ls_matnr-zxy_id.
    ls_t08-matnr = ls_matnr-matnr.
    ls_t08-matnr = |{ ls_t08-matnr ALPHA = IN WIDTH = 18 }|..
    ls_t08-zmwskz = ls_matnr-zmwskz.
*    ls_t08-zitems = ls_matnr-zitems.
    APPEND ls_t08 TO pt_t08.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CALL_ZREM0002
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_DATA_BASE_ZFLG_RB
*&      --> GS_TA02
*&---------------------------------------------------------------------*
FORM frm_call_zrem0002  USING    pv_zflg_rb TYPE char2
                                 ps_ta02 TYPE LINE OF tt_ta02
                                 ps_ta01 TYPE LINE OF tt_ta01.

  DATA:
        lv_zusage TYPE zret0009-zusage.

  PERFORM frm_set_zusage  USING ps_ta02-zxybstyp CHANGING lv_zusage.


  IF pv_zflg_rb = '01' OR pv_zflg_rb = '02'.
    IF ps_ta02-zspz_id IS INITIAL.
      SUBMIT zrem0002
                      WITH rb_add = 'X'
                      WITH rb_edit = ''
                      WITH rb_dis = ''
                      WITH p_call = 'X'
                      WITH p_zht_id = ps_ta01-zht_id
                      WITH p_zusage = lv_zusage
        AND RETURN .
    ELSE.
      SUBMIT zrem0002 WITH p_zspzid = ps_ta02-zspz_id
                      WITH rb_add = ''
                      WITH rb_edit = 'X'
                      WITH rb_dis = ''
                      WITH p_call = 'X'
        AND RETURN .
    ENDIF.

  ELSE.
    CHECK ps_ta02-zspz_id IS NOT INITIAL.
    SUBMIT zrem0002 WITH p_zspzid = ps_ta02-zspz_id
                    WITH rb_add = ''
                    WITH rb_edit = ''
                    WITH rb_dis = 'X'
                      WITH p_call = 'X'
      AND RETURN .
  ENDIF.
ENDFORM.


FORM frm_set_icon_zcxjt  USING      ps_ta05     TYPE LINE OF tt_ta05
                                    pt_t58_set TYPE tt_t58
                         CHANGING
                              pv_icon_zmzmx  TYPE icon-id.


  DATA(lt_t58_set)  = pt_t58_set[]  .

  DELETE lt_t58_set      WHERE zitems_key NE ps_ta05-zitems_key          .

  PERFORM frm_set_icon_exe USING lt_t58_set  CHANGING pv_icon_zmzmx .


ENDFORM.


*&---------------------------------------------------------------------*
*& Form SET_TC_DATA_TC_SPZMX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GS_T58_SUB
*&---------------------------------------------------------------------*
FORM frm_set_tc_data_tc_spzmx  CHANGING ps_t58 TYPE LINE OF tt_t58.


  ps_t58-matnr   = |{ ps_t58-matnr ALPHA = IN WIDTH = 18 }| .
  SELECT SINGLE
    maktx
    INTO ps_t58-maktx
    FROM makt
    WHERE matnr = ps_t58-matnr.

  SELECT SINGLE
    meins
    INTO ps_t58-meins
    FROM mara
    WHERE matnr = ps_t58-matnr.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZDATES
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA06_ZDATES
*&      --> GS_TA02_ZBEGIN
*&      --> GS_TA02_ZEND
*&---------------------------------------------------------------------*
FORM frm_check_zdates  USING    pv_zdates TYPE d
                                pv_zbegin TYPE d
                                pv_zend TYPE d.

  IF pv_zdates >= pv_zbegin AND pv_zdates <= pv_zend.
    sy-subrc = 0.
  ELSE.
    sy-subrc = 4.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PASS_FRGSX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PS_TA02
*&---------------------------------------------------------------------*
FORM frm_pass_frgsx_tk  CHANGING ps_ta02 TYPE LINE OF tt_ta02.
  IF ps_ta02-ztk_id IS NOT INITIAL.
    SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ps_ta02-ztk_id INTO @DATA(ls_zreta002).
    ps_ta02-frgsx = ls_zreta002-frgsx.
    ps_ta02-kolnr = ls_zreta002-kolnr.
    ps_ta02-zxyzt = ls_zreta002-zxyzt.
  ENDIF.
ENDFORM.

FORM frm_pass_frgsx_xy  CHANGING ps_t06 TYPE LINE OF tt_t06.
  IF ps_t06-ztk_id IS NOT INITIAL.
    SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ps_t06-ztk_id INTO @DATA(ls_zreta002).
    ps_t06-frgsx = ls_zreta002-frgsx.
    ps_t06-kolnr = ls_zreta002-kolnr.
    ps_t06-zxyzt = 'A'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_T06
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_T06
*&---------------------------------------------------------------------*
FORM frm_pro_data_t06  CHANGING pt_t06 TYPE tt_t06.
  IF pt_t06[] IS NOT INITIAL.
    LOOP AT pt_t06 INTO DATA(ls_t06).
      SELECT SINGLE zxy_id FROM zret0006 WHERE zxy_id = @ls_t06-zxy_id INTO @DATA(lv_zxy_id).
      IF sy-subrc NE 0.
        ls_t06-zcjr = sy-uname.
        ls_t06-zcjsj = sy-uzeit.
        ls_t06-zcjrq = sy-datum.
      ELSE.
        ls_t06-zxgr = sy-uname.
        ls_t06-zxgsj = sy-uzeit.
        ls_t06-zxgrq = sy-datum.
      ENDIF.
      MODIFY pt_t06 FROM ls_t06.
    ENDLOOP.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_TEXT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_DATA_BASE
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_text  USING    pv_zflg_rb
                     CHANGING pt_t76      TYPE tt_t76
                              pt_msglist  TYPE scp1_general_errors.

  CHECK pv_zflg_rb = '02'.

  IF pt_t76[] IS INITIAL.
    PERFORM frm_add_msg USING '返利条款修改需要在【附加数据】页签添加修改说明'  CHANGING pt_msglist.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_DATS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> ET_RT_DATE
*&      --> LS_ZTK_ID
*&      <-- LS_HEAD
*&---------------------------------------------------------------------*
FORM frm_set_guid CHANGING pv_guid .

  CALL FUNCTION '/SAPSLL/GUID_CREATE'
    IMPORTING
      ev_guid_32 = pv_guid.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_TEXT_TO_LT_T76
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T76
*&---------------------------------------------------------------------*
FORM frm_text_to_lt_t76  USING   pv_zflg_rb
                         CHANGING pt_t76 TYPE tt_t76.

  DATA:ls_t76     TYPE ty_t76.
  DATA:ls_edittab LIKE LINE OF gt_edittab.
  DATA:lv_cont TYPE i VALUE 46.
  DATA:lv_text TYPE zre_text.

  REFRESH pt_t76.
  IF pv_zflg_rb = '02' AND gv_editor IS NOT INITIAL .

    CALL METHOD gv_editor->get_text_as_r3table
      IMPORTING
        table = gt_edittab.

    LOOP AT gt_edittab INTO ls_edittab.
      ls_t76+lv_cont(40) = ls_edittab.
      lv_cont = lv_cont + 40.
      IF lv_cont >= 525 .
        EXIT.
      ENDIF.
    ENDLOOP.
  ENDIF.

  IF ls_t76 IS NOT INITIAL .
    APPEND ls_t76 TO pt_t76.
    CLEAR:ls_t76.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_T76
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T76
*&---------------------------------------------------------------------*
FORM frm_update_t76  USING  ps_approval TYPE ty_approval  pt_t76 TYPE tt_t76.

  LOOP AT pt_t76 ASSIGNING FIELD-SYMBOL(<lfs_t76>).
    <lfs_t76>-zxyzt = ps_approval-zxyzt.
    <lfs_t76>-aedat = sy-datum.
    <lfs_t76>-aetim = sy-uzeit.
    <lfs_t76>-aenam = sy-uname.
  ENDLOOP.

  IF pt_t76[] IS NOT INITIAL .
    MODIFY zret0076 FROM TABLE pt_t76 .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_T76
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_T76
*&---------------------------------------------------------------------*
FORM frm_update_t76_add   USING pv_ztk_id
                                 pt_t76 TYPE tt_t76.

  LOOP AT pt_t76 ASSIGNING FIELD-SYMBOL(<lfs_t76>).
    <lfs_t76>-ztk_id = pv_ztk_id.
    <lfs_t76>-zxyzt = 'A'.
    <lfs_t76>-aedat = sy-datum.
    <lfs_t76>-aetim = sy-uzeit.
    <lfs_t76>-aenam = sy-uname.
  ENDLOOP.

  IF pt_t76[] IS NOT INITIAL .
    MODIFY zret0076 FROM TABLE pt_t76 .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ONECLASS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      --> PT_MATNR
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_oneclass  USING   ps_tc02  TYPE ty_tc02
                                  pt_matnr TYPE tt_matnr
                         CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:lt_matnr    TYPE tt_matnr,
       lv_matnr    TYPE zmmt0001-matnr,
       lv_oneclass TYPE zret0074-oneclass,
       lv_msgv1    TYPE scp1_general_error-msgv1.

  CASE ps_tc02-zzsbs .
    WHEN 'A'.
      lv_oneclass = '线上'.
    WHEN 'B' .
      lv_oneclass = '线下'.
    WHEN OTHERS.
      EXIT.
  ENDCASE.

  lt_matnr = pt_matnr[].
  DELETE lt_matnr WHERE zitems_key <> ps_tc02-zitems_key.

  IF lt_matnr[] IS NOT INITIAL .

    SELECT SINGLE a~matnr

      FROM zmmt0001       AS a
     INNER JOIN zret0074  AS b ON a~zdpt = b~zdpt
     INNER JOIN @lt_matnr AS c ON a~matnr = c~matnr
     WHERE  b~oneclass <> @lv_oneclass
      INTO @lv_matnr.
    IF sy-subrc = 0.
      CLEAR lv_msgv1. lv_msgv1 = '协议的专属返利标识与商品的大类不一致，请检查！' && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.

    ENDIF.
  ENDIF.

ENDFORM.
FORM frm_check_zbukrs_type USING ps_tc02       TYPE LINE OF tt_tc02
                                  pt_t44_all   TYPE  tt_t44
                                  pt_tc02_comp TYPE  tt_tc02
                                  pt_msglist   TYPE scp1_general_errors.

  DATA: lv_flg      TYPE char1.
  DATA:lv_msgv1     TYPE scp1_general_error-msgv1.
  DATA:lv_zflzff    TYPE zret0006-zflzff.
  DATA:ls_tc02_comp TYPE LINE OF tt_tc02.
  DATA:lrt_zbukrs   TYPE RANGE OF zret0006-zbukrs.
  DATA:lt_msglist   TYPE scp1_general_errors.
  DATA:ls_msglist   TYPE LINE OF scp1_general_errors.

  SELECT 'I'    AS sign,
         'EQ'   AS option,
         zbukrs AS low
    INTO TABLE @lrt_zbukrs FROM  zretcm07
   WHERE zbukrs NE ''
     AND zsxrq_from <= @sy-datum
     AND zsxrq_to   >= @sy-datum .

  SORT lrt_zbukrs BY low.
  DELETE ADJACENT DUPLICATES FROM lrt_zbukrs COMPARING low.

  PERFORM frm_get_zzff_attr USING ps_tc02-zflzff CHANGING lv_flg.
  IF lv_flg  EQ 'I'.
    lv_zflzff = |{ ps_tc02-zflzff ALPHA = OUT }|.

    LOOP AT pt_tc02_comp INTO ls_tc02_comp WHERE zbukrs = lv_zflzff AND zitems_key <> ps_tc02-zitems_key    .
      IF ps_tc02-zitzff IS INITIAL.
        IF ( ps_tc02-zpaytp = 'B' AND ls_tc02_comp-zctgr <> 'R') OR
           ( ps_tc02-zpaytp = 'A' AND ls_tc02_comp-zctgr <> 'A').
          CLEAR lv_msgv1. lv_msgv1 = '行项目ID:' && ps_tc02-zitems && '协议主体:' && ps_tc02-zbukrs && '和行项目ID:'  && ls_tc02_comp-zitems && '协议主体:' && ls_tc02_comp-zbukrs &&  '收付款组织级别不匹配'.
          PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.
        ELSE.
          CLEAR:lt_msglist[].
          EXIT.
        ENDIF.
      ENDIF.
    ENDLOOP.

    IF sy-subrc <> 0.
      CLEAR lv_msgv1. lv_msgv1 = '行项目ID:' && ps_tc02-zitems && '协议主体:' &&  ps_tc02-zbukrs &&  '无对应的收款方协议数据'.
      PERFORM frm_add_msg_w USING lv_msgv1  CHANGING lt_msglist.
    ENDIF.


    IF lt_msglist IS NOT INITIAL .
      IF lrt_zbukrs[] IS NOT INITIAL AND lv_zflzff IN lrt_zbukrs .
        ls_msglist-msgty = 'W'.
        MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgty  WHERE msgty = 'E'.
      ENDIF.
      APPEND  LINES OF lt_msglist TO pt_msglist.
      CLEAR:lt_msglist[].
    ENDIF.
  ENDIF.


  LOOP AT pt_t44_all INTO DATA(ls_t44_all) WHERE zitems_key = ps_tc02-zitems_key .
    "收款主体校验
    PERFORM frm_get_zzff_attr USING ls_t44_all-zflzff CHANGING lv_flg.
    IF lv_flg  EQ 'I'.
      lv_zflzff = |{ ls_t44_all-zflzff ALPHA = OUT }|.
      LOOP AT pt_tc02_comp INTO ls_tc02_comp WHERE zbukrs = lv_zflzff AND zitems_key <> ps_tc02-zitems_key    .
        IF ps_tc02-zitzff IS INITIAL.
          IF ( ls_t44_all-zpaytp = 'B' AND ls_tc02_comp-zctgr <> 'R') OR
             ( ls_t44_all-zpaytp = 'A' AND ls_tc02_comp-zctgr <> 'A').
            CLEAR lv_msgv1. lv_msgv1 = '行项目ID:' && ps_tc02-zitems && '协议主体:' && ps_tc02-zbukrs && '和行项目ID:'  && ls_tc02_comp-zitems && '协议主体:' && ls_tc02_comp-zbukrs &&  '收付款组织级别不匹配'.
            PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.
          ELSE.
            CLEAR:lt_msglist[].
            EXIT.
          ENDIF.
        ENDIF.
      ENDLOOP.
      IF sy-subrc <> 0.
        CLEAR lv_msgv1. lv_msgv1 = '行项目ID:' && ps_tc02-zitems && '协议主体:' &&  ps_tc02-zbukrs &&  '无对应的收款方协议数据'.
        PERFORM frm_add_msg_w USING lv_msgv1  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF lt_msglist IS NOT INITIAL .
      IF lrt_zbukrs[] IS NOT INITIAL AND lv_zflzff IN lrt_zbukrs .
        ls_msglist-msgty = 'W'.
        MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgty  WHERE msgty = 'E'.
      ENDIF.
      APPEND  LINES OF lt_msglist TO pt_msglist.
      CLEAR:lt_msglist[].
    ENDIF.

  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZBUKRS_ORGANIZATION
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_TC02
*&      --> PT_BUKRS
*&      --> PT_DCWRK
*&      --> PT_WERKS
*&      <-- PT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_zbukrs_organization  USING    ps_tc02 TYPE ty_tc02
                                              pt_bukrs  TYPE  tt_bukrs
                                              pt_werks  TYPE  tt_werks
                                    CHANGING pt_msglist TYPE scp1_general_errors.


  DATA:lv_msgv1    TYPE scp1_general_error-msgv1.

  DATA(lt_bukrs) = pt_bukrs[].
  DATA(lt_werks) = pt_werks[].

  DELETE lt_bukrs WHERE  zitems_key <> ps_tc02-zitems_key.
  DELETE lt_werks WHERE  zitems_key <> ps_tc02-zitems_key.

  IF lt_bukrs[] IS NOT INITIAL.
    IF lines( lt_bukrs ) > 1.
      lv_msgv1 = '维护的公司只能一个且和协议主体一致，请检查！' && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ELSE.
      READ TABLE lt_bukrs TRANSPORTING NO FIELDS
                          WITH  KEY zitems_key = ps_tc02-zitems_key
                                    bukrs      = ps_tc02-zbukrs
                                    exclude    = ''.
      IF sy-subrc <> 0.
        lv_msgv1 = '维护的公司代码和协议主体不一致，请检查！' && '行项目ID:' && ps_tc02-zitems.
        PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
      ENDIF.
    ENDIF.
  ELSE.
    SELECT SINGLE wrk~werks
      FROM @lt_werks AS wrk
      INNER JOIN zrev010_wrk_ddl ON zrev010_wrk_ddl~werks = wrk~werks
      WHERE exclude = ''
        AND zrev010_wrk_ddl~bukrs <> @ps_tc02-zbukrs
       INTO @DATA(lv_werks).
    IF sy-subrc = 0.
      lv_msgv1 = |维护的门店{ lv_werks }对应的公司和协议主体不一致，请检查！| && '行项目ID:' && ps_tc02-zitems.
      PERFORM frm_add_msg USING lv_msgv1  CHANGING pt_msglist.
    ENDIF.
  ENDIF.



ENDFORM.