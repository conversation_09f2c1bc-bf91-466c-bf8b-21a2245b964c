*&---------------------------------------------------------------------*
*& Report ZRED0063
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
REPORT zred0063.
INCLUDE zred0063top.
INCLUDE zred0063sel.
INCLUDE zred0063f01.
*----------------------------------------------------------------------*
* <2.2-选择屏幕事件>                                                    *
* Events That Occur While The Selection Screen Is Bing Processed       *
*----------------------------------------------------------------------*
INITIALIZATION.
  PERFORM frm_initialization.

* 所有选择屏幕数据传送到程序中之后触发的事件
AT SELECTION-SCREEN.
  "权限检查




*选择屏幕PBO事件，在显示选择屏幕前触发
AT SELECTION-SCREEN OUTPUT.
  PERFORM frm_set_screen.
*----------------------------------------------------------------------*
* <2.3-在选择屏幕被处理后触发的事件,程序默认的开始事件>                  *
* Event Occurs After The Selection Screen Has Been Processed           *
*----------------------------------------------------------------------*
START-OF-SELECTION.
  "获取数据

  PERFORM frm_check_zq.
  PERFORM frm_validate_check.
  PERFORM frm_get_data.
  PERFORM frm_process_data  .
*----------------------------------------------------------------------*
* <2.4-最后被触发的事件>                                                *
* The Last Of The Events Called By The Runtime Environment To Occur    *
*----------------------------------------------------------------------*
END-OF-SELECTION.

  PERFORM frm_show_alv.