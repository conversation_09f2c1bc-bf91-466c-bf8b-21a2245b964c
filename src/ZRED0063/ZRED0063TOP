*&---------------------------------------------------------------------*
*& 包含               ZRED0063TOP
*&---------------------------------------------------------------------*

TYPES: BEGIN OF ty_jsd,
         zxy_id   TYPE zret0018-zxy_id,
         zjsd_id  TYPE zret0018-zjsd_id,
         zjstype  TYPE zret0018-zjstype,
         zjsstate TYPE zret0018-zjsstate,
       END OF ty_jsd,
       tt_jsd TYPE TABLE OF ty_jsd.

DATA:BEGIN OF gs_alv.

    INCLUDE TYPE zret0006.
DATA: sel     TYPE c,
      ztype   TYPE c,
      status  TYPE c,
      z<PERSON>s   TYPE string,
      zhtyear TYPE zreta001-zhtyear,
      zht_id  TYPE zreta001-zht_id,
      zbukrst TYPE t001-butxt,
      zflsqft TYPE t001-butxt,
      eknam   TYPE t024-eknam,
      zflzfft TYPE lfa1-name1,
      jsd_sl  TYPE i,
      jsd_no  TYPE TABLE OF ty_jsd,
      END OF gs_alv,

      gt_alv LIKE TABLE OF gs_alv.

DATA:gs_jsd TYPE ty_jsd,
     gt_jsd TYPE tt_jsd.

DATA:gs_layout   TYPE lvc_s_layo.
DATA:gt_fieldcat TYPE lvc_t_fcat.

DATA:gv_grid TYPE REF TO cl_gui_alv_grid.