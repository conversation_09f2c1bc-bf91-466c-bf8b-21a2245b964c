*&---------------------------------------------------------------------*
*& 包含               ZRED0063F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_INITIALIZATION
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_initialization .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_VALIDATE_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_validate_check .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZQ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_check_zq .

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.
  IF lv_moste = 'E' .

    MESSAGE lv_meg TYPE 'S' DISPLAY LIKE  'E'.
    LEAVE LIST-PROCESSING.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data .

  SELECT zret0006~*,
           buk~butxt  AS zbukrst,
           sqf~butxt  AS zflsqft,
           lfa1~name1 AS zflzfft,
           t024~eknam ,
           zreta001~zht_id,
           zreta001~zhtyear
      INTO CORRESPONDING FIELDS OF TABLE @gt_alv
      FROM zret0006
      INNER JOIN zreta002   ON zret0006~ztk_id   = zreta002~ztk_id
      INNER JOIN zreta001   ON zreta002~zht_id   = zreta001~zht_id
      LEFT JOIN t001 AS buk ON zret0006~zbukrs  = buk~bukrs
      LEFT JOIN t001 AS sqf ON zret0006~zflsqf  = sqf~bukrs
      LEFT JOIN lfa1        ON zret0006~zflzff  = lfa1~lifnr
      LEFT JOIN t024        ON zret0006~ekgrp   = t024~ekgrp
     WHERE zret0006~zxy_id   IN @s_zxy_id
       AND zreta001~zhtlx    IN @s_zhtlx
       AND zreta001~zhtyear  IN @s_zyear
       AND zret0006~zfllx    IN @s_zfllx
       AND zret0006~zxybstyp IN @s_zbtyp
       AND zret0006~zbukrs   IN @s_zbukrs
       AND zret0006~ztk_id   IN @s_ztk_id
       AND zreta001~zht_id   IN @s_zht_id
       AND ( ( zreta002~zcnyg = '' AND  zret0006~zxyzt EQ 'A' ) OR ( zreta002~zcnyg = 'X' AND zret0006~zxyzt <> 'D'  ) )  ..

  SELECT zxy_id
         zjsd_id
         zjstype
         zjsstate
    INTO CORRESPONDING FIELDS OF TABLE gt_jsd
    FROM zret0018
    FOR ALL ENTRIES IN gt_alv
    WHERE zxy_id = gt_alv-zxy_id
      AND zjsstate   <> 'D'
      AND zjsdcx_id  =  ''
       .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_process_data .

  SORT gt_jsd BY  zxy_id.

  LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<lfs_alv>).

    READ TABLE gt_jsd TRANSPORTING NO FIELDS WITH  KEY zxy_id = <lfs_alv>-zxy_id.
    IF sy-subrc = 0.
      LOOP AT gt_jsd INTO gs_jsd FROM sy-tabix. .
        IF gs_jsd-zxy_id <> <lfs_alv>-zxy_id .
          EXIT.
        ELSE.
          APPEND gs_jsd TO <lfs_alv>-jsd_no.
        ENDIF.
      ENDLOOP.
    ENDIF.

    IF <lfs_alv>-jsd_no[] IS NOT INITIAL .
      <lfs_alv>-jsd_sl = lines( <lfs_alv>-jsd_no ).
      <lfs_alv>-ztype = 'E'.
    ENDIF.

    CASE <lfs_alv>-ztype.
      WHEN 'E' .
        <lfs_alv>-status     = 1.
      WHEN 'W' .
        <lfs_alv>-status     = 2.
      WHEN 'S' .
        <lfs_alv>-status     = 3.
      WHEN OTHERS.
        <lfs_alv>-status     = 3.
    ENDCASE.


  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SHOW_ALV
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_show_alv .

  gs_layout-zebra        = 'X'.
  gs_layout-cwidth_opt   = 'X'.
  gs_layout-sel_mode     = 'A'.
  gs_layout-box_fname    = 'SEL'.
  gs_layout-excp_fname   = 'STATUS'.

  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'STATUS'       '指示灯'                   '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHT_ID'       '返利合同'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZTK_ID'       '返利条款'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZXY_ID'       '返利协议'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZXY_TXT'      '返利协议描述'             '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHTYEAR'      '签署年度'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZHTLX'        '合同类型'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZXYBSTYP'     '协议类型'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLLX'        '返利类型'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBUKRS'       '协议主体'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBUKRST'      '协议主体名称'             '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZCTGR'        '主体级别'                 '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLSQF'       '返利收取方'               '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZFLSQFT'      '返利收取方名称'           '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'JSD_SL'       '有效结算单'               '10'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZBEGIN'       '开始日期'         '8'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZEND'         '结束日期'         '8'.
  PERFORM frm_set_fieldcat TABLES gt_fieldcat USING 'ZMESS'        '消息文本'         '40'.

  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = gs_layout
      it_fieldcat_lvc          = gt_fieldcat
      i_callback_pf_status_set = 'FRM_SET_STATUS'
      i_callback_user_command  = 'FRM_USER_COMMAND'
*     I_DEFAULT                = 'X'
      i_save                   = 'U'
    TABLES
      t_outtab                 = gt_alv
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_FIELDCAT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_set_fieldcat  TABLES pt_fieldcat TYPE lvc_t_fcat
                        USING i_fieldname TYPE char20
                              i_seltext   TYPE char40
                              i_outputlen TYPE lvc_outlen.

  DATA:ls_fieldcat TYPE lvc_s_fcat.


  IF i_fieldname = 'SEL'  .
    ls_fieldcat-checkbox = 'X'..
    ls_fieldcat-edit     = 'X'.
  ENDIF.
  ls_fieldcat-fieldname = i_fieldname.
  ls_fieldcat-coltext   = i_seltext .
  ls_fieldcat-scrtext_l = i_seltext .
  ls_fieldcat-scrtext_m = i_seltext .
  ls_fieldcat-scrtext_s = i_seltext .
  ls_fieldcat-outputlen = i_outputlen.

  CASE i_fieldname .
    WHEN 'ZXY_ID' . ls_fieldcat-hotspot   = 'X'. .
    WHEN 'ZTK_ID' . ls_fieldcat-hotspot   = 'X'. .
    WHEN 'ZHT_ID' . ls_fieldcat-hotspot   = 'X'. .
    WHEN 'ZHTLX'  . ls_fieldcat-convexit  = 'ZREHT'.ls_fieldcat-ref_field  = 'ZHTLX'.    ls_fieldcat-ref_table  = 'ZRETA001'.
    WHEN 'ZBPTYPE'. ls_fieldcat-convexit  = 'ZBPTP'.ls_fieldcat-ref_field  = 'ZBPTYPE'.  ls_fieldcat-ref_table  = 'ZRETA001'.
    WHEN 'ZDFFS'  . ls_fieldcat-convexit  = 'ZDFFS'.ls_fieldcat-ref_field  = 'ZDFFS'.    ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN 'ZFLLX'  . ls_fieldcat-convexit  = 'ZFLLX'.ls_fieldcat-ref_field  = 'ZFLLX'.    ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN 'ZFLXS'  . ls_fieldcat-convexit  = 'ZFLXS'.ls_fieldcat-ref_field  = 'ZFLXS'.    ls_fieldcat-ref_table  = 'ZRETC005'.
    WHEN 'ZHSJZ'  . ls_fieldcat-convexit  = 'ZHSJZ'.ls_fieldcat-ref_field  = 'ZHSJZ'.    ls_fieldcat-ref_table  = 'ZRETC005'.
    WHEN 'ZXYBSTYP'.ls_fieldcat-convexit  = 'ZXYBS'.ls_fieldcat-ref_field  = 'ZXYBSTYP'. ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN 'ZFLSQF' . ls_fieldcat-convexit  = 'ALPHA'.ls_fieldcat-ref_field  = 'ZFLSQF'.   ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN 'ZFLZFF '. ls_fieldcat-convexit  = 'ALPHA'.ls_fieldcat-ref_field  = 'ZFLZFF'.   ls_fieldcat-ref_table  = 'ZRET0044'.
    WHEN 'ZCTGR'  . ls_fieldcat-convexit  = 'ZCTGR'.ls_fieldcat-ref_field  = 'ZCTGR'.    ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN 'ZPAYTP' . ls_fieldcat-convexit  = 'ZPAYT'.ls_fieldcat-ref_field  = 'ZPAYTP'.   ls_fieldcat-ref_table  = 'ZRET0044'.
    WHEN 'ZFLDFSJ'. ls_fieldcat-convexit  = 'ZDFSJ'.ls_fieldcat-ref_field  = 'ZFLDFSJ'.  ls_fieldcat-ref_table  = 'ZRETA002'.
    WHEN 'ZZSBS' .  ls_fieldcat-convexit  = 'ZZSBS'.ls_fieldcat-ref_field  = 'ZZSBS'.    ls_fieldcat-ref_table  = 'ZRET0006'.
    WHEN OTHERS.
  ENDCASE.


  APPEND ls_fieldcat TO pt_fieldcat.

ENDFORM.
**&---------------------------------------------------------------------*
**&      FORM  PROCESS_COMMAND
**&---------------------------------------------------------------------*
**       TEXT
**----------------------------------------------------------------------*
**      -->RT_COM       TEXT
**      -->RS_SELFIELD  TEXT
**----------------------------------------------------------------------*
FORM frm_user_command USING rt_com      LIKE sy-ucomm
                            rs_selfield TYPE slis_selfield.

  CASE rt_com.
    WHEN 'DEL' .
      PERFORM frm_del USING rs_selfield.
      rs_selfield-refresh    = 'X'.
      rs_selfield-col_stable = 'X'.
      rs_selfield-row_stable = 'X'.
    WHEN '&IC1'.
      PERFORM frm_ic1 USING rs_selfield.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.                    "PROCESS_COMMAND

*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_status USING rt_extab TYPE slis_t_extab.
  SET PF-STATUS 'S1000' EXCLUDING rt_extab .

  IF gv_grid IS  INITIAL.
    CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR'
      IMPORTING
        e_grid = gv_grid.
  ENDIF.

ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*& Form FRM_DEL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_del  USING rs_selfield TYPE slis_selfield.

  DATA:lv_error.
  DATA:lv_answer.

  PERFORM frm_check_data CHANGING lv_error.

  CHECK lv_error IS INITIAL.

  PERFORM frm_answer_data CHANGING lv_answer.

  CHECK lv_answer = '1'.

  PERFORM frm_del_data .


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_IC1
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_ic1  USING rs_selfield TYPE slis_selfield.

  READ TABLE gt_alv INTO DATA(ls_alv) INDEX rs_selfield-tabindex.
  IF sy-subrc = 0.
    CASE rs_selfield-fieldname.
      WHEN 'ZHT_ID' .
        SET PARAMETER ID 'ZHT_ID' FIELD ls_alv-zht_id.
        CALL TRANSACTION 'ZRED0040C' AND SKIP FIRST SCREEN.
      WHEN 'ZTK_ID' .
        SET PARAMETER ID 'ZTK_ID' FIELD ls_alv-ztk_id.
        CALL TRANSACTION 'ZRED0041C' AND SKIP FIRST SCREEN.
      WHEN 'JSD_SL' .
        PERFORM frm_show_jsd TABLES ls_alv-jsd_no.
      WHEN OTHERS.
    ENDCASE.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_check_data  CHANGING pv_error.

  CLEAR:pv_error.
  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY sel = 'X'.
  IF sy-subrc <> 0.
    pv_error = 'X'.
    MESSAGE '选择要执行的数据！' TYPE 'S' DISPLAY LIKE 'E'.
  ENDIF.

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY sel = 'X' status = '1' .
  IF sy-subrc = 0.
    pv_error = 'X'.
    MESSAGE '选择的协议包含有效结算单，请检查！' TYPE 'S' DISPLAY LIKE 'E'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DEL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_answer_data  CHANGING pv_answer.

  CLEAR pv_answer.

  CALL FUNCTION 'POPUP_TO_CONFIRM'                "弹出小窗口
    EXPORTING
      titlebar      = '提示'
      text_question = '作废后无法恢复，是否继续操作？'
    IMPORTING
      answer        = pv_answer.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DEL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_del_data .
  DATA:
    lt_t06    TYPE TABLE OF zret0006.
  DATA:ls_alv   LIKE gs_alv,
       lv_error.

  LOOP AT gt_alv INTO gs_alv WHERE sel = 'X'.
    UPDATE zret0006     SET zxyzt = 'D'
*                            kolnr = ls_approval-kolnr
                            zqxr  = sy-uname
                           zqxsj  = sy-uzeit
                           zqxrq  = sy-datum
                    WHERE zxy_id  = gs_alv-zxy_id.
    IF sy-subrc <> 0.
      lv_error = 'E'.
      EXIT.
    ELSE.

    ENDIF.
  ENDLOOP.

  IF lv_error = 'E'.
    ROLLBACK WORK.
    MESSAGE '删除失败，请稍后重新执行！' TYPE 'S' DISPLAY LIKE 'E'.
  ELSE.
    COMMIT WORK AND WAIT.
    ls_alv-zmess  = '删除成功'.
    PERFORM frm_call_zrefm0019 TABLES gt_alv.
    MODIFY  gt_alv FROM ls_alv TRANSPORTING zmess WHERE sel = 'X' .
    MESSAGE '删除成功！' TYPE 'S' .
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SHOW_JSD
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_ALV_JSD_NO
*&---------------------------------------------------------------------*
FORM frm_show_jsd  TABLES   pt_jsd_no TYPE tt_jsd.

  DATA:lt_fieldcat  TYPE  slis_t_fieldcat_alv.
  DATA:ls_fieldcat  TYPE  slis_fieldcat_alv.


  lt_fieldcat = VALUE #( ( fieldname = 'ZXY_ID'   seltext_l = '协议号'     outputlen = '10' )
                         ( fieldname = 'ZJSD_ID'  seltext_l = '结算单号'   outputlen = '10' )
                         ( fieldname = 'ZJSSTATE' seltext_l = '结算单状态' outputlen = '10' )
                        ).

  CALL FUNCTION 'REUSE_ALV_POPUP_TO_SELECT'
    EXPORTING
      i_title               = '有效结算单列表'
      i_zebra               = 'X'
      i_screen_start_column = 10
      i_screen_start_line   = 5
      i_screen_end_column   = 100
      i_screen_end_line     = 20
      it_fieldcat           = lt_fieldcat
      i_tabname             = 'PT_JSD_NO'
    TABLES
      t_outtab              = pt_jsd_no
    EXCEPTIONS
      program_error         = 1
      OTHERS                = 2.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CALL_ZREFM0019
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ALV
*&---------------------------------------------------------------------*
FORM frm_call_zrefm0019  TABLES   pt_alv STRUCTURE gs_alv.

  DATA:lt_t06    TYPE TABLE OF zret0006.

  DATA(lt_sql) = pt_alv[].
  DELETE lt_sql WHERE sel = ''.
  IF lt_sql[] IS NOT INITIAL .
    SELECT
      zxy_id
      INTO CORRESPONDING FIELDS OF TABLE lt_t06
      FROM zret0006
      FOR ALL ENTRIES IN lt_sql
      WHERE zxy_id = lt_sql-zxy_id.

    CALL FUNCTION 'ZREFM0072'
*   IMPORTING
*     EV_MTYPE       =
*     EV_MSG         =
      TABLES
        it_head = lt_t06[].
  ENDIF.
ENDFORM.