*&---------------------------------------------------------------------*
*& 包含               ZRED0035_M02
*&---------------------------------------------------------------------*





*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_HT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01
*&      --> GV_ACTVT
*&---------------------------------------------------------------------*
FORM frm_author_check_ht  USING    ps_ta01  TYPE LINE OF tt_ta01
                                   pv_actvt TYPE activ_auth
                                   pv_flg   TYPE char1
                          CHANGING pt_msglist TYPE scp1_general_errors
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE scp1_general_error-msgv1.


  DATA:
        ls_zreta001 TYPE zreta001.

  MOVE-CORRESPONDING ps_ta01 TO ls_zreta001.


  PERFORM frm_author_check_ht_new USING ls_zreta001
                                        pv_actvt
                                        pv_flg
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

ENDFORM.