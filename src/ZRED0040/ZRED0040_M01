*&---------------------------------------------------------------------*
*& 包含               ZRED0035_M01
*&---------------------------------------------------------------------*


**********************************************************************
*                     屏幕元素分组
*   101 通用显示/修改
*   201 调用时可以编辑，否则不可编辑
*
*
*
**********************************************************************


*&SPWIZARD: FUNCTION CODES FOR TABSTRIP 'TAG_TA01'
CONSTANTS: BEGIN OF c_tag_ta01,
             tab1 LIKE sy-ucomm VALUE 'TAG_TA01_FC1',
             tab2 LIKE sy-ucomm VALUE 'TAG_TA01_FC2',
             tab3 LIKE sy-ucomm VALUE 'TAG_TA01_FC3',
           END OF c_tag_ta01.
*&SPWIZARD: DATA FOR TABSTRIP 'TAG_TA01'
CONTROLS:  tag_ta01 TYPE TABSTRIP.
DATA: BEGIN OF g_tag_ta01,
        subscreen   LIKE sy-dynnr,
        prog        LIKE sy-repid VALUE 'ZRED0040',
        pressed_tab LIKE sy-ucomm VALUE c_tag_ta01-tab1,
      END OF g_tag_ta01.
*DATA:      OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TS 'TAG_TA01'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: SETS ACTIVE TAB
MODULE tag_ta01_active_tab_set OUTPUT.
  tag_ta01-activetab = g_tag_ta01-pressed_tab.
  CASE g_tag_ta01-pressed_tab.
    WHEN c_tag_ta01-tab1.
      g_tag_ta01-subscreen = '3001'.
    WHEN c_tag_ta01-tab2.
      g_tag_ta01-subscreen = '3002'.
    WHEN c_tag_ta01-tab3.
      g_tag_ta01-subscreen = '3003'.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TS 'TAG_TA01'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GETS ACTIVE TAB
MODULE tag_ta01_active_tab_get INPUT.
  ok_code = sy-ucomm.
  CASE ok_code.
    WHEN c_tag_ta01-tab1.
      g_tag_ta01-pressed_tab = c_tag_ta01-tab1.
    WHEN c_tag_ta01-tab2.
      g_tag_ta01-pressed_tab = c_tag_ta01-tab2.
    WHEN c_tag_ta01-tab3.
      g_tag_ta01-pressed_tab = c_tag_ta01-tab3.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.

*&SPWIZARD: DECLARATION OF TABLECONTROL 'TC_T09' ITSELF
CONTROLS: tc_t09 TYPE TABLEVIEW USING SCREEN 4000.

*&SPWIZARD: LINES OF TABLECONTROL 'TC_T09'
DATA:     g_tc_t09_lines  LIKE sy-loopc.

*DATA:     OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_T09'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: UPDATE LINES FOR EQUIVALENT SCROLLBAR
MODULE tc_t09_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_t09 LINES tc_t09-lines.
ENDMODULE.

*&SPWIZARD: OUTPUT MODULE FOR TC 'TC_T09'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GET LINES OF TABLECONTROL
MODULE tc_t09_get_lines OUTPUT.
  g_tc_t09_lines = sy-loopc.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_T09'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MODIFY TABLE
MODULE tc_t09_modify INPUT.
  MODIFY gt_t09
    FROM gs_t09
    INDEX tc_t09-current_line.
ENDMODULE.

*&SPWIZARD: INPUT MODUL FOR TC 'TC_T09'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: MARK TABLE
MODULE tc_t09_mark INPUT.
  DATA: g_tc_t09_wa2 LIKE LINE OF gt_t09.
  IF tc_t09-line_sel_mode = 1
  AND gs_t09-sel = 'X'.
    LOOP AT gt_t09 INTO g_tc_t09_wa2
      WHERE sel = 'X'.
      g_tc_t09_wa2-sel = ''.
      MODIFY gt_t09
        FROM g_tc_t09_wa2
        TRANSPORTING sel.
    ENDLOOP.
  ENDIF.
  MODIFY gt_t09
    FROM gs_t09
    INDEX tc_t09-current_line
    TRANSPORTING sel.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TC 'TC_T09'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: PROCESS USER COMMAND
MODULE tc_t09_user_command INPUT.
  ok_code = sy-ucomm.
  PERFORM user_ok_tc USING    'TC_T09'
                              'GT_T09'
                              'SEL'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
ENDMODULE.



*&SPWIZARD: FUNCTION CODES FOR TABSTRIP 'TAG_TK'
CONSTANTS: BEGIN OF c_tag_tk,
             tab1 LIKE sy-ucomm VALUE 'TAG_TK_FC1',
             tab2 LIKE sy-ucomm VALUE 'TAG_TK_FC2',
             tab3 LIKE sy-ucomm VALUE 'TAG_TK_FC3',
           END OF c_tag_tk.
*&SPWIZARD: DATA FOR TABSTRIP 'TAG_TK'
CONTROLS:  tag_tk TYPE TABSTRIP.
DATA: BEGIN OF g_tag_tk,
        subscreen   LIKE sy-dynnr,
        prog        LIKE sy-repid VALUE 'ZRED0040',
        pressed_tab LIKE sy-ucomm VALUE c_tag_tk-tab1,
      END OF g_tag_tk.
*DATA:      OK_CODE LIKE SY-UCOMM.

*&SPWIZARD: OUTPUT MODULE FOR TS 'TAG_TK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: SETS ACTIVE TAB
MODULE tag_tk_active_tab_set OUTPUT.
  tag_tk-activetab = g_tag_tk-pressed_tab.
  CASE g_tag_tk-pressed_tab.
    WHEN c_tag_tk-tab1.
      g_tag_tk-subscreen = '5001'.
    WHEN c_tag_tk-tab2.
      g_tag_tk-subscreen = '5002'.
    WHEN c_tag_tk-tab3.
      g_tag_tk-subscreen = '5003'.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.

*&SPWIZARD: INPUT MODULE FOR TS 'TAG_TK'. DO NOT CHANGE THIS LINE!
*&SPWIZARD: GETS ACTIVE TAB
MODULE tag_tk_active_tab_get INPUT.
  ok_code = sy-ucomm.
  CASE ok_code.
    WHEN c_tag_tk-tab1.
      g_tag_tk-pressed_tab = c_tag_tk-tab1.
    WHEN c_tag_tk-tab2.
      g_tag_tk-pressed_tab = c_tag_tk-tab2.
    WHEN c_tag_tk-tab3.
      g_tag_tk-pressed_tab = c_tag_tk-tab3.
    WHEN OTHERS.
*&SPWIZARD:      DO NOTHING
  ENDCASE.
ENDMODULE.



*&---------------------------------------------------------------------*
*& Module STATUS_2000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_2000 OUTPUT.
  DATA: pt_extab TYPE slis_t_extab.

  REFRESH pt_extab.
  IF gv_flg_rb NE '01' AND gv_flg_rb NE '02'.
    pt_extab = VALUE #( ( fcode = 'SAVE' ) ) .
  ENDIF.

  SET PF-STATUS 'S2000' EXCLUDING pt_extab.
  SET TITLEBAR 'T2000' WITH gv_title_01 gv_title_02 .

  PERFORM frm_set_screen_2000.

  IF gv_flg_save = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_2000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_2000 INPUT.

  DATA:
    lv_mtype      TYPE bapi_mtype,
    lv_msg        TYPE bapi_msg,
    lv_index_line TYPE i.
  CLEAR:
        lv_msg,lv_mtype,lv_index_line.

  PERFORM frm_code_pro CHANGING   ok_code
                                  gv_code.

  CASE gv_code.
    WHEN 'BACK' OR 'EXIT' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      PERFORM frm_pro_data_lock               USING gs_ta01-zht_id
                                                      'X'.
      LEAVE TO SCREEN 0.

*    屏幕折叠
    WHEN 'B_HEAD'.
      PERFORM frm_set_sub_screen USING 'B_HEAD'
                                 CHANGING gv_subscreen.
*    屏幕折叠
    WHEN 'B_ITEM'.
      PERFORM frm_set_sub_screen USING 'B_ITEM'
                                 CHANGING gv_subscreen.
*    屏幕折叠
    WHEN 'B_DTL'.
      PERFORM frm_set_sub_screen USING 'B_DTL'
                                 CHANGING gv_subscreen.

    WHEN 'SAVE'.
      PERFORM frm_check_data_main CHANGING
                                           lv_mtype
                                           lv_msg
                                           gv_actvt
                                           gs_ta01.
      IF lv_mtype = 'S'.
        PERFORM frm_save_data CHANGING
                                             lv_mtype
                                             lv_msg
                                             gs_ta01.
        gv_flg_comm = 'SAVE'.
        gv_flg_save = 'X'.
      ENDIF.
      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.

*    商品组新增、修改、删除、明细
    WHEN 'B_SPZ_ADD' OR 'B_SPZ_EDIT' OR 'B_SPZ_DELE' OR 'B_SPZ_DTL' OR '&IC1'.
      gv_flg_comm = gv_code.

      PERFORM frm_check_data_spz  USING gv_code
                                        gs_ta01
                                        gt_t09
                                  CHANGING
                                   lv_mtype
                                   lv_msg.
      IF lv_mtype = 'S' .


        IF gv_code = 'B_SPZ_DELE'.
          PERFORM frm_spz_dele CHANGING gt_t09.
        ELSEIF gv_code = 'B_SPZ_ADD'  .
          SUBMIT zrem0002
                          WITH rb_add = 'X'
                          WITH rb_edit = ''
                          WITH rb_dis = ''
                          WITH p_zht_id = gs_ta01-zht_id
                          AND RETURN.

          PERFORM frm_update_gt_t09    USING gs_ta01-zht_id
                                    CHANGING gt_t09.

        ELSEIF gv_code = 'B_SPZ_EDIT'  .
          READ TABLE gt_t09 INTO DATA(ls_t09) WITH  KEY sel = 'X'.
          SUBMIT zrem0002 WITH p_zspzid = ls_t09-zspz_id
                          WITH rb_add = ''
                          WITH rb_edit = 'X'
                          WITH rb_dis = ''
                          WITH p_call = 'X'
                          AND RETURN .

          PERFORM frm_update_gt_t09    USING gs_ta01-zht_id
                                    CHANGING gt_t09.

        ELSEIF gv_code = 'B_SPZ_DTL' OR gv_code = '&IC1'  .
          CHECK gv_cursor_line IS NOT INITIAL.
          PERFORM frm_get_index_line_40 USING   gv_code
                                                gv_cursor_line
                                     CHANGING   lv_index_line.

          READ TABLE gt_t09 INTO ls_t09 INDEX lv_index_line.
          SUBMIT zrem0002 WITH p_zspzid = ls_t09-zspz_id
                          WITH rb_add = ''
                          WITH rb_edit = ''
                          WITH rb_dis = 'X'
                          AND RETURN.
        ENDIF.


      ELSE.
        MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.
        RETURN.
      ENDIF.
*    WHEN 'B_SPZ_ADD' OR 'B_SPZ_EDIT' OR 'B_SPZ_DELE' OR 'B_SPZ_DTL' OR '&IC1'.
*
*      gv_flg_comm = gv_code.
*
*      PERFORM frm_check_data_spz  USING gv_code
*                                        gs_ta01
*                                        gt_t09
*                                  CHANGING
*                                   lv_mtype
*                                   lv_msg.
*      IF lv_mtype = 'S' .
*        IF gv_code = 'B_SPZ_DELE'.
*          PERFORM frm_spz_dele CHANGING gt_t09.
*        ELSE.
*          PERFORM frm_pro_data_4100 USING   gv_code
*                                            gv_cursor_line
*                                    CHANGING
*                                               gs_ta01
*                                                gt_t09
*                                                gs_t09_sub
*                                                gt_t20
*                                                gt_t20_sub
*                                                .
*        ENDIF.
*      ELSE.
*        MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.
*        RETURN.
*      ENDIF.
*    添加条款
    WHEN 'B_TK_ADD'.
      CLEAR gs_tk_call.
      gs_tk_call-ztmpid = gs_ta01-ztmpid.

      PERFORM frm_charg_vls_zfllx     USING gs_ta01-zht_id
                                   CHANGING gt_vls_zfllx.

      CALL SCREEN '6000' STARTING AT 30 5
                       ENDING AT 80 15.
    WHEN OTHERS.
*      gv_subscreen = cv_subscreen_dft.

  ENDCASE.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_0110 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_0110 OUTPUT.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_3001 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_3001 OUTPUT.
  PERFORM frm_set_screen_pub.

  gs_ta01-zbpcode = |{ gs_ta01-zbpcode ALPHA = IN } |.
  PERFORM frm_set_zbpname  USING gs_ta01-zbpcode gs_ta01-zbptype CHANGING gs_ta01-zbpname.

  PERFORM frm_get_eknam USING gs_ta01-ekgrp CHANGING gs_ta01-eknam.

ENDMODULE.
MODULE status_3002 OUTPUT.

  PERFORM frm_get_zflzff_t USING gs_ta01-zflzff CHANGING gs_ta01-zflzff_t.

  PERFORM frm_set_list_box USING 'GS_TA01-ZJSZQ'
                                  gt_vls_zjszq.
  PERFORM frm_set_list_box USING 'GS_TA01-ZHSZQ'
                                  gt_vls_zhszq.
  PERFORM frm_set_list_box USING 'GS_TA01-ZTMPID'
                                  gt_vls_ztmpid.
  PERFORM frm_set_screen_pub.
ENDMODULE.
MODULE status_3003 OUTPUT.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_F4_ZPARTNER  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_f4_zbpcode INPUT.
  PERFORM frm_f4_zbpcode USING gs_ta01.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_4100 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Module MDL_SET_SCREEN_TC_SPZ OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*


*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_SPZ_MATNR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_spz_matnr INPUT.
  PERFORM frm_check_matnr USING gs_t20_sub-matnr.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_4100  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*&      Module  MDL_SET_DATA_TC_SPZ_PAI  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*&      Module  MDL_GET_CURSOR_LINE  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_get_cursor_line INPUT.
  GET CURSOR LINE gv_cursor_line.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_PRO_ZBPCODE  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Module STATUS_5001 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_5001 OUTPUT.
  PERFORM frm_alv_init USING 'JS'.
ENDMODULE.
MODULE status_5002 OUTPUT.
  PERFORM frm_alv_init USING 'GD'.
ENDMODULE.
MODULE status_5003 OUTPUT.
  PERFORM frm_alv_init USING 'SQ'.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_6000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_6000 OUTPUT.

  SET PF-STATUS 'S6000'.
  SET TITLEBAR 'T2000' WITH gv_title_01 gv_title_02 .

  IF gs_tk_call-rb_nref IS INITIAL AND gs_tk_call-rb_ref IS INITIAL.
    gs_tk_call-rb_nref = 'X'.
    gs_tk_call-rb_ref = ''.
  ENDIF.

  IF gs_tk_call-rb_ref = 'X'.
    CLEAR:
          gs_tk_call-zcnyg,
          gs_tk_call-p_tk_idy.

  ENDIF.

  IF gs_tk_call-zcnyg = ''.
    CLEAR:
      gs_tk_call-p_tk_idy.
  ENDIF.

  PERFORM frm_get_data_list_box_03.

  READ TABLE gt_vls_ztmpid_add_tk TRANSPORTING NO FIELDS WITH KEY key = gs_tk_call-ztmpid.
  IF sy-subrc NE 0.
    CLEAR gs_tk_call-ztmpid.
  ENDIF.

  READ TABLE gt_vls_zxybstyp TRANSPORTING NO FIELDS WITH KEY key = gs_tk_call-zxybstyp.
  IF sy-subrc NE 0.
    CLEAR gs_tk_call-zxybstyp.
  ENDIF.

  IF gs_tk_call-ztmpid IS INITIAL.
    READ TABLE gt_vls_ztmpid_add_tk INTO DATA(ls_vls_ztmpid_add_tk) INDEX 1.
    gs_tk_call-ztmpid = ls_vls_ztmpid_add_tk-key.
  ENDIF.


  PERFORM frm_set_list_box USING 'GS_TK_CALL-ZXYBSTYP'
                                  gt_vls_zxybstyp.

  PERFORM frm_set_list_box USING 'GS_TK_CALL-ZTMPID'
                                  gt_vls_ztmpid_add_tk.

  PERFORM frm_set_list_box USING 'GS_TK_CALL-ZFLLX'
                                  gt_vls_zfllx.

  LOOP AT SCREEN.

    IF screen-name = 'GS_TK_CALL-P_TK_IDY' .
      IF gs_tk_call-zcnyg = 'X'.
        screen-active = 1.
      ELSE.
        screen-active = 0.
      ENDIF.
    ENDIF.

    IF gs_tk_call-rb_nref = 'X'.
      IF screen-name = 'GS_TK_CALL-P_TK_IDR' OR screen-name = 'GS_TK_CALL-P_TK_IDRT'.
        screen-active = 0.
      ENDIF.

      IF screen-group4 = '501'.
        screen-input = 1.
      ENDIF.
    ELSE.
      IF screen-name = 'GS_TK_CALL-P_TK_IDR' OR screen-name = 'GS_TK_CALL-P_TK_IDRT'.
        screen-active = 1.
      ENDIF.

      IF screen-group4 = '501'.
        screen-input = 0.
      ENDIF.

    ENDIF.




    MODIFY SCREEN.

  ENDLOOP.


ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_6000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_6000 INPUT.

  PERFORM frm_code_pro CHANGING   ok_code
                                  gv_code.

  CASE gv_code.
    WHEN 'B_CANCEL' .      LEAVE TO SCREEN 0.
    WHEN 'B_OK'.



      IF gs_ta01-zht_id IS INITIAL .
        MESSAGE s888(sabapdocu) WITH '没有返利合同号需先保存返利合同！' DISPLAY LIKE 'E'.
        RETURN.
      ELSE.

        IF gs_tk_call-rb_nref = 'X'.
          IF gs_tk_call-zfjtk = 'X'.
            SELECT SINGLE COUNT(*) FROM zretc001 WHERE ztmpid = gs_tk_call-ztmpid AND ztktype = 'P' .
            IF sy-subrc NE  0.
              MESSAGE s888(sabapdocu) WITH '条款编码与附加条款不匹配' DISPLAY LIKE 'E'.
              RETURN.
            ENDIF.
          ELSEIF gs_tk_call-zfjtk = ''.
            SELECT SINGLE COUNT(*) FROM zretc001 WHERE ztmpid = gs_tk_call-ztmpid AND ztktype = '' .
            IF sy-subrc NE  0.
              MESSAGE s888(sabapdocu) WITH '条款编码与附加条款不匹配' DISPLAY LIKE 'E'.
              RETURN.
            ENDIF.
          ENDIF.

        ELSE.
          PERFORM frm_check_ref USING gs_tk_call-p_tk_idr.
        ENDIF.

        IF gs_tk_call-zcnyg = 'X'.
          IF gs_tk_call-p_tk_idy IS INITIAL.
            MESSAGE s888(sabapdocu) WITH '次年预估原参考条款必填' DISPLAY LIKE 'E'.
            RETURN.
          ELSE.
            PERFORM frm_check_ztk_id_yg USING gs_tk_call-p_tk_idy.
          ENDIF.
        ENDIF.




*        调用条款程序
        IF gs_tk_call-rb_nref = 'X'.
          SUBMIT zred0041
            WITH p_zfllx = gs_tk_call-zfllx
            WITH p_zxybtp = gs_tk_call-zxybstyp
            WITH p_zht_id = gs_ta01-zht_id
            WITH p_ztmpid = gs_tk_call-ztmpid
            WITH cb_fjtk = gs_tk_call-zfjtk
            WITH p_call = 'X'
            WITH rb_nref = 'X'
            WITH rb_ref = ''
            WITH cb_yg = gs_tk_call-zcnyg
            WITH p_tk_idy = gs_tk_call-p_tk_idy
            AND RETURN .
        ELSE.
          SUBMIT zred0041
            WITH p_tk_idr = gs_tk_call-p_tk_idr
            WITH p_call = 'X'
            WITH rb_ref = 'X'
            WITH rb_nref = ''
            WITH cb_yg = gs_tk_call-zcnyg
            WITH p_tk_idy = gs_tk_call-p_tk_idy
            AND RETURN .
        ENDIF.
*        刷新条款数据
        PERFORM frm_get_data_tk USING gs_ta01
                                CHANGING gt_tk_js
                                         gt_tk_gd
                                         gt_tk_sq.

        LEAVE TO SCREEN 0.
      ENDIF.
  ENDCASE.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_4000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_4000 OUTPUT.

  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Module STATUS_5000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_5000 OUTPUT.
  PERFORM frm_set_screen_pub.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_ZHTID  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_zhtid INPUT.
*  PERFORM frm_screen_check_zhtid USING gs_ta01-zhtid.
ENDMODULE.

MODULE mdl_pro_zbpcode INPUT.
  gs_ta01-zbpcode = |{ gs_ta01-zbpcode ALPHA = IN } |.
  PERFORM frm_set_zbpname  USING gs_ta01-zbpcode gs_ta01-zbptype CHANGING gs_ta01-zbpname.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_CHECK_EKORG  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_check_ekorg_01 INPUT.

  PERFORM frm_pro_screen_ekgrp USING gs_ta01-ekgrp CHANGING gs_ta01-eknam.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZFLZFF  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zflzff INPUT.

  PERFORM frm_alpha_in_lifnr CHANGING gs_ta01-zflzff.

  PERFORM frm_set_screen_zflzff USING gs_ta01-zflzff CHANGING gs_ta01-zflzff_t.

  PERFORM frm_check_status_zflzff USING gs_ta01-zflzff.
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '该支付方' && gs_ta01-zflzff && '已被冻结或删除，请检查主数据!'.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_MODFIY_YEAR  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_modfiy_year INPUT.
  gs_ta01-zbegin  = gs_ta01-zhtyear && '0101'  .
  gs_ta01-zend    = gs_ta01-zhtyear && '1231'  .
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MDL_SET_ZJSZQ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mdl_set_zjszq INPUT.
  PERFORM frm_set_zjszq USING gs_ta01-zhstype gs_ta01-zhszq CHANGING gs_ta01-zjszq.
ENDMODULE.