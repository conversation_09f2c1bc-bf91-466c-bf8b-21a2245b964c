*&---------------------------------------------------------------------*
*& 包含               ZRED0035_F02
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_SET_TITLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_TITLE
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_LIST_BOX
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_VLS_ZFLLX
*&      <-- GT_VLS_ZJSFF
*&      <-- GT_VLS_ZHSZQ
*&      <-- GT_VLS_ZJSZQ
*&      <-- GT_VLS_ZHSJZ
*&      <-- GT_VLS_ZHTLX
*&---------------------------------------------------------------------*
FORM frm_get_data_list_box  CHANGING pt_vls_zfllx   TYPE vrm_values
                                     pt_vls_zjsff   TYPE vrm_values
                                     pt_vls_zhszq   TYPE vrm_values
                                     pt_vls_zjszq   TYPE vrm_values
                                     pt_vls_zhsjz   TYPE vrm_values
                                     pt_vls_zhtlx   TYPE vrm_values.

  SELECT
    zfllx  AS  key ,
    zfllx_txt      AS  text
    FROM zret0002
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zfllx.

  SELECT
    zhszq  AS  key ,
    zhszq_txt      AS  text
    FROM zret0005
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhszq.

  SELECT
    zjszq  AS  key ,
    zjszq_txt      AS  text
    FROM zret0004
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjszq.

  SELECT
    zhsjz  AS  key ,
    zhsjz_txt      AS  text
    FROM zret0003
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhsjz.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZREM_ZHTLX'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zhtlx.

  SELECT
    domvalue_l  AS  key ,
    ddtext      AS  text
    FROM dd07t
    WHERE domname = 'ZREM_ZJSFF'
    AND   ddlanguage = @sy-langu
    INTO CORRESPONDING FIELDS OF TABLE @pt_vls_zjsff.

ENDFORM.



*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZFLSQF
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZBUKRS
*&---------------------------------------------------------------------*
FORM frm_check_zflsqf  USING    pv_zflsqf TYPE zret0006-zflsqf.

  IF pv_zflsqf IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '合同主体必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.
    SELECT COUNT(*)
      FROM t001
      WHERE bukrs = pv_zflsqf.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH '公司代码不存在' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZTMPID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_TMPIDR
*&---------------------------------------------------------------------*
*FORM frm_check_zht_id  USING pv_flg TYPE char1 pv_flg_rb TYPE char2  pv_zht_id TYPE zreta001-zht_id.
*  SELECT SINGLE *  FROM zreta001 INTO @DATA(ls_ta01) WHERE zht_id = @pv_zht_id.
*  IF sy-subrc NE 0.
*    MESSAGE s888(sabapdocu) WITH '合同编码不存在' DISPLAY LIKE 'E'.
*    LEAVE LIST-PROCESSING.
*  ENDIF.
*
*ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_ZTMPID
*&      <-- GT_TC01
*&      <-- GT_TC02
*&---------------------------------------------------------------------*
FORM frm_get_data  USING    pv_zht_id TYPE zreta001-zht_id
                   CHANGING
                            ps_ta01 TYPE LINE OF tt_ta01
                            pt_t09 TYPE tt_t09
                            pt_t20 TYPE tt_t20
                            pt_tk_js TYPE  tt_tk
                            pt_tk_gd TYPE  tt_tk
                            pt_tk_sq TYPE  tt_tk .

  SELECT SINGLE
    *
    INTO CORRESPONDING FIELDS OF ps_ta01
    FROM zreta001
    WHERE zht_id = pv_zht_id.

  IF ps_ta01 IS  INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有对应的数据！' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_t09
    FROM zret0009
    WHERE zht_id = pv_zht_id.

  SELECT
    a~*
    FROM @pt_t09 AS i JOIN zret0020 AS a
                        ON i~zspz_id = a~zspz_id
    INTO CORRESPONDING FIELDS OF TABLE @pt_t20.

*  获取计算类条款数据
  PERFORM frm_get_data_tk USING ps_ta01
                          CHANGING pt_tk_js
                                   pt_tk_gd
                                   pt_tk_sq.


ENDFORM.
FORM frm_get_data_tk USING ps_ta01 TYPE LINE OF tt_ta01
                     CHANGING pt_tk_js TYPE  tt_tk
                              pt_tk_gd TYPE  tt_tk
                              pt_tk_sq TYPE  tt_tk.

*  CLEAR pt_tk_js.
*  CLEAR pt_tk_GD.

  PERFORM frm_get_data_tk_exe USING 'JS'
                                    ps_ta01
                              CHANGING pt_tk_js.

  PERFORM frm_get_data_tk_exe USING 'GD'
                                    ps_ta01
                              CHANGING pt_tk_gd.

  PERFORM frm_get_data_tk_exe USING 'SQ'
                                    ps_ta01
                              CHANGING pt_tk_sq.
*  计算类
  SORT pt_tk_js BY zcjrq ASCENDING zcjsj ASCENDING  .
  PERFORM frm_set_seq TABLES pt_tk_js USING 'SEQ'.

  LOOP AT pt_tk_js INTO DATA(ls_tk_js).
    PERFORM frm_get_anbtr CHANGING ls_tk_js.
    ls_tk_js-icon_edit = '@3I@'.
    ls_tk_js-icon_dis = '@10@'.
    MODIFY pt_tk_js FROM ls_tk_js.
  ENDLOOP.

*  固定类
  SORT pt_tk_gd BY zcjrq ASCENDING zcjsj ASCENDING  .
  PERFORM frm_set_seq TABLES pt_tk_gd USING 'SEQ'.

  LOOP AT pt_tk_gd INTO DATA(ls_tk_gd).
    SELECT SINGLE
      SUM( a~zje ) AS zje
      SUM( a~zsl ) AS zsl
       INTO ( ls_tk_gd-anbtr  ,ls_tk_gd-zsl )
      FROM zret0008 AS a JOIN zret0006 AS b
                           ON a~zxy_id = b~zxy_id
      WHERE b~ztk_id = ls_tk_gd-ztk_id
      GROUP BY a~zxy_id.

    ls_tk_gd-icon_edit = '@3I@'.
    ls_tk_gd-icon_dis = '@10@'.
    MODIFY pt_tk_gd FROM ls_tk_gd.
  ENDLOOP.

*  计算类
  SORT pt_tk_sq BY zcjrq ASCENDING zcjsj ASCENDING  .
  PERFORM frm_set_seq TABLES pt_tk_sq USING 'SEQ'.

  LOOP AT pt_tk_sq INTO DATA(ls_tk_sq).
    PERFORM frm_get_anbtr CHANGING ls_tk_sq.
    ls_tk_sq-icon_edit = '@3I@'.
    ls_tk_sq-icon_dis = '@10@'.
    MODIFY pt_tk_sq FROM ls_tk_sq.
  ENDLOOP.

ENDFORM.

FORM frm_get_data_tk_exe USING pv_flg TYPE char4
                              ps_ta01 TYPE LINE OF tt_ta01
                     CHANGING pt_tk_data TYPE  tt_tk.

  DATA:
        lv_where TYPE char80.

  CLEAR pt_tk_data.
*----------------------------------------------------------------------*
*    BEGIN MODIFY BY XYLIU1   12.03.2021 16:29:40
*    <NOTES>
*    <OLD CODES>
*  IF pv_flg = 'JS'.
*    lv_where = |  A~ZFLLX IN ( 'RB01', 'RB05', 'RB06' ) |.
*  ELSEIF pv_flg = 'GD'.
*    lv_where = |  A~ZFLLX IN ( 'RB02', 'RB03', 'RB04' ) |.
*  ENDIF.
*    </OLD CODES>
*    <NEW CODES>
  IF pv_flg = 'JS'.
    lv_where = |  A~ZXYBSTYP IN ( 'A', 'T', 'V','P' ) AND ZLEIB <> 'R' |.
  ELSEIF pv_flg = 'GD'.
    lv_where = |  A~ZXYBSTYP IN ( 'F', 'Q' ) |.
  ELSEIF pv_flg = 'SQ'.
    lv_where = |  A~ZXYBSTYP IN ( 'A', 'T', 'V','P' ) AND ZLEIB = 'R' |.
  ENDIF.
*    </NEW CODES>
*    END MODIFY BY XYLIU1
*----------------------------------------------------------------------*

  SELECT
    a~zcjrq               ,
    a~zcjsj               ,
    a~zcjr               ,
    a~zfllx               ,
    a~ztktype             ,
    a~ztk_id              ,
    a~ztk_txt             ,
    a~zspz_id             ,
    a~zflspz_id           ,
    a~zbegin              ,
    a~zend                ,
    a~ztmpid              ,
    a~zxyzt               ,
    a~zclrid              ,
    a~zleib               ,
*    b~zrlid             ,
    c~zhsjz               ,
    c~zflxs
    FROM zreta002 AS a
*                  LEFT JOIN zreta003 AS b   ON a~ztk_id = b~ztk_id
                     LEFT JOIN zretc005 AS c
                         ON a~zclrid = c~zclrid
    WHERE a~zht_id = @ps_ta01-zht_id
*    AND   a~zfllx IN ( 'RB01', 'RB05', 'RB06' )
    AND   (lv_where)
    INTO CORRESPONDING FIELDS OF TABLE @pt_tk_data.




  SELECT
    a~*
    FROM @pt_tk_data AS i   JOIN zret0006 AS a
                           ON i~ztk_id = a~ztk_id
    INTO TABLE @DATA(lt_t006).
  DATA(lt_t006_tmp) =  lt_t006[].

  LOOP AT lt_t006 INTO DATA(ls_t006).

    AUTHORITY-CHECK OBJECT 'ZREAR009'
                        ID 'BUKRS' FIELD ls_t006-zbukrs
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc NE 0.
      DELETE lt_t006 .
      CONTINUE.
    ENDIF.

  ENDLOOP.


  SORT lt_t006_tmp BY ztk_id.
  SORT lt_t006 BY ztk_id.

  LOOP AT pt_tk_data INTO DATA(ls_tk_data).

    READ TABLE lt_t006_tmp TRANSPORTING NO FIELDS WITH KEY ztk_id = ls_tk_data-ztk_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      READ TABLE lt_t006 TRANSPORTING NO FIELDS WITH KEY ztk_id = ls_tk_data-ztk_id BINARY SEARCH.
      IF sy-subrc NE 0.
        DELETE pt_tk_data.
        CONTINUE.
      ENDIF.
    ENDIF.

    SELECT SINGLE zrlid INTO ls_tk_data-zrlid FROM zreta003 WHERE ztk_id = ls_tk_data-ztk_id AND zrlid NE ''.
    MODIFY pt_tk_data FROM ls_tk_data.

  ENDLOOP.


ENDFORM.



FORM frm_pro_data  CHANGING
                            ps_ta01 TYPE LINE OF tt_ta01
                            pt_tk_js TYPE  tt_tk.


*      SELECT SINGLE zxybstyp
*        FROM    zret0002
*        WHERE   zfllx = @p_zfllx
*        INTO    @gv_zxybstyp.

  LOOP AT pt_tk_js INTO DATA(ls_tk_js).
    PERFORM frm_get_anbtr CHANGING ls_tk_js.
    ls_tk_js-icon_edit = '@3I@'.
    ls_tk_js-icon_dis = '@10@'.
    MODIFY pt_tk_js FROM ls_tk_js.
  ENDLOOP.

ENDFORM.

FORM frm_pro_data_ref  CHANGING
                            pt_ta01 TYPE LINE OF tt_ta01.
*
*  LOOP AT pt_tc01 INTO DATA(ls_tc01).
*    ls_tc01-ztmpid_bak = ls_tc01-ztmpid.
*    CLEAR ls_tc01-ztmpid.
*    MODIFY pt_tc01 FROM ls_tc01.
*  ENDLOOP.
*
*  LOOP AT pt_tc02 INTO DATA(ls_tc02).
*    ls_tc02-ztmpid_bak = ls_tc02-ztmpid.
*    CLEAR ls_tc02-ztmpid.
*    MODIFY pt_tc02 FROM ls_tc02.
*  ENDLOOP.
*
*  LOOP AT pt_tc03 INTO DATA(ls_tc03).
*    ls_tc03-ztmpid_bak = ls_tc03-ztmpid.
*    CLEAR ls_tc03-ztmpid.
*    MODIFY pt_tc03 FROM ls_tc03.
*  ENDLOOP.
*
*  LOOP AT pt_tc04 INTO DATA(ls_tc04).
*    ls_tc04-ztmpid_bak = ls_tc04-ztmpid.
*    CLEAR ls_tc04-ztmpid.
*    MODIFY pt_tc04 FROM ls_tc04.
*  ENDLOOP.

ENDFORM.



*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&      <-- GS_TC01
*&      <-- GT_TC02
*&      <-- GT_TC03
*&      <-- GT_TC04_SET
*&      <-- GT_BUKRS_SET
*&      <-- GT_DCWRK_SET
*&      <-- GT_WERKS_SET
*&      <-- GT_EKORG_SET
*&---------------------------------------------------------------------*
FORM frm_check_data_main  CHANGING pv_mtype       TYPE bapi_mtype
                                   pv_msg         TYPE bapi_msg
                                   pv_actvt       TYPE activ_auth
                                   ps_ta01        TYPE LINE OF tt_ta01.

  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.
*  DATA:
*        lv_msg TYPE scp1_general_error-msgv1.
  DATA:
        lv_msgv1 TYPE scp1_general_error-msgv1.

  DATA:
    lv_flg_err     TYPE char1.

  CLEAR: pv_mtype,pv_msg.

  IF pv_mtype NE 'E'.
    pv_mtype = 'S'.
  ELSE.
    pv_mtype = 'E'.
  ENDIF.

  PERFORM frm_clear_data_null   CHANGING ps_ta01.

  PERFORM frm_check_zbpcode USING ps_ta01 CHANGING lt_msglist.

  PERFORM frm_check_inital USING ps_ta01-zht_txt    '合同描述' CHANGING lt_msglist .
  PERFORM frm_check_inital USING ps_ta01-zhtid    '关联合同号' CHANGING lt_msglist .
  PERFORM frm_check_inital USING ps_ta01-ztmpid    '组织模板号' CHANGING lt_msglist .
  PERFORM frm_check_inital USING ps_ta01-zflzff    '支付方' CHANGING lt_msglist .
  PERFORM frm_check_inital USING ps_ta01-zpayday    '付款期间' CHANGING lt_msglist .

  IF ps_ta01-zhtlx = '0003' OR ps_ta01-zhtlx = '0004'.
    PERFORM frm_check_double USING ps_ta01 CHANGING lt_msglist.
  ENDIF.

  IF ps_ta01-zbegin > ps_ta01-zend.
    PERFORM frm_add_msg USING '开始日期不能大于结束日期'  CHANGING lt_msglist.
  ENDIF.

  IF ps_ta01-ekgrp IS NOT INITIAL.
    SELECT SINGLE COUNT(*) FROM t024 WHERE ekgrp = ps_ta01-ekgrp .
    IF sy-subrc NE 0.
      PERFORM frm_add_msg USING '采购组不存在'  CHANGING lt_msglist.
    ENDIF.

  ENDIF.

  PERFORM frm_check_status_zflzff USING ps_ta01-zflzff.
  IF sy-subrc NE 0.

    CLEAR lv_msgv1. lv_msgv1 = '该支付方' && gs_ta01-zflzff && '已被冻结或删除，请检查主数据!'.
    PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.

  ENDIF.



*  截止日期检查
  DATA: lv_tmp_date TYPE d.
  PERFORM frm_check_zbegin USING ps_ta01-zbegin
                           CHANGING lv_tmp_date.
  IF sy-subrc NE 0.
    CLEAR lv_msgv1. lv_msgv1 = '只允许创建' && lv_tmp_date && '之后的合同' .
    PERFORM frm_add_msg USING lv_msgv1  CHANGING lt_msglist.

  ENDIF.

*  权限检查
  DATA:
    lv_mtype_auth TYPE bapi_mtype,
    lv_msg_auth   TYPE scp1_general_error-msgv1.

  PERFORM frm_author_check_ht USING ps_ta01
                                    pv_actvt
                                    'B'
                              CHANGING lt_msglist
                                       lv_mtype_auth
                                       lv_msg_auth.


*  PERFORM frm_author_check_ekgrp USING ps_ta01-ekgrp
*                                     pv_actvt
*                               CHANGING lv_flg_err.
*  IF lv_flg_err = 'E'.
*    lv_msg =  '您没有采购组:' && ps_ta01-ekgrp && ' 的权限 !'.
*    PERFORM frm_add_msg USING lv_msg  CHANGING lt_msglist.
*  ENDIF.




  IF lt_msglist[] IS NOT INITIAL.

    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].

    pv_mtype = 'E'.
    pv_msg = '数据检查未通过，操作已终止!'.
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK_EKGRP
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T06_EKGRP
*&---------------------------------------------------------------------*
*FORM frm_author_check_ekgrp  USING    pv_ekgrp TYPE ekgrp
*                                       pv_actvt TYPE activ_auth
*                             CHANGING  pv_flg_err TYPE char1.
*
*
*
*  DATA:lv_subrc TYPE c,
*       lv_mess  TYPE bapiret2-message.
*
**  RANGES:
**    s_ekgrp FOR t024-ekgrp.
*
*  DATA:
*        s_ekgrp TYPE TABLE OF zbcs0003.
*
*  CLEAR pv_flg_err.
*  IF pv_ekgrp IS NOT INITIAL.
*
*    APPEND VALUE #( sign  = 'I'
*                    option = 'EQ'
*                    low  = pv_ekgrp
*                    high  = ''
*                  ) TO s_ekgrp .
*
*    CLEAR:lv_subrc,
*          lv_mess.
*
*    CALL FUNCTION 'ZBCFM0001'
*      EXPORTING
**       iv_object = 'M_BEST_EKG'
*        iv_object = 'ZREAR001'
*        iv_field  = 'EKGRP'
*        iv_actvt  = pv_actvt
*      IMPORTING
*        ex_subrc  = lv_subrc
*        ex_mess   = lv_mess
*      TABLES
*        it_tab    = s_ekgrp.
*
*    IF lv_subrc = 'E'.
*      pv_flg_err = 'E'.
*    ELSE.
*      pv_flg_err = 'S'.
*    ENDIF.
*  ENDIF.
*
*ENDFORM.


FORM frm_save_data        CHANGING pv_mtype       TYPE bapi_mtype
                                   pv_msg         TYPE bapi_msg
                                   ps_ta01        TYPE LINE OF tt_ta01.


  PERFORM frm_clear_data_null   CHANGING ps_ta01    .



  PERFORM frm_pro_before_save CHANGING ps_ta01.

  PERFORM frm_save_data_into_db CHANGING
                                       pv_mtype
                                       pv_msg
                                       ps_ta01.



ENDFORM.

FORM frm_save_data_into_db CHANGING
                                   pv_mtype       TYPE bapi_mtype
                                   pv_msg         TYPE bapi_msg
                                   ps_ta01        TYPE LINE OF tt_ta01.

  DATA:
        ls_zreta0001 TYPE zreta001.

  MOVE-CORRESPONDING ps_ta01 TO ls_zreta0001.
  MODIFY zreta001 FROM ls_zreta0001.
  COMMIT WORK AND WAIT.

  pv_mtype = 'S'.
  pv_msg = '模板保存成功！'.

ENDFORM.

FORM frm_pro_before_save CHANGING
                                   ps_ta01        TYPE LINE OF tt_ta01.

*  zretA001 处理
  IF ps_ta01-zht_id IS INITIAL.
    PERFORM frm_get_num USING 'ZRE0009' '01' CHANGING ps_ta01-zht_id.
  ENDIF.

  IF ps_ta01-zcjr IS INITIAL.
    ps_ta01-zcjrq  = sy-datum.
    ps_ta01-zcjsj  = sy-uzeit.
    ps_ta01-zcjr   = sy-uname.
  ELSE.
    ps_ta01-zxgrq  = sy-datum.
    ps_ta01-zxgsj  = sy-uzeit.
    ps_ta01-zxgr   = sy-uname.
  ENDIF.

*
**  zretc002 处理
*  DELETE pt_tc02 WHERE zflg_null = 'X'.
*  ls_tc02-ztmpid = ps_tc01-ztmpid.
*  MODIFY pt_tc02 FROM ls_tc02 TRANSPORTING ztmpid WHERE ztmpid NE ps_tc01-ztmpid.
*
**  zretc003 处理
*  ls_tc03-ztmpid = ps_tc01-ztmpid.
*  MODIFY pt_tc03 FROM ls_tc03 TRANSPORTING ztmpid WHERE ztmpid NE ps_tc01-ztmpid.
*  CLEAR lv_int.
*  LOOP AT pt_tc03 INTO ls_tc03.
*    lv_int = lv_int + 1.
*    ls_tc03-zitems = lv_int.
*    MODIFY pt_tc03 FROM ls_tc03.
*  ENDLOOP.
*
**  zretc004 处理
*  ls_tc04-ztmpid = ps_tc01-ztmpid.
*  MODIFY pt_tc04 FROM ls_tc04 TRANSPORTING ztmpid WHERE ztmpid NE ps_tc01-ztmpid.
*


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_CLEAR_DATA_NULL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_TC04
*&      <-- PT_BUKRS
*&      <-- PT_DCWRK
*&      <-- PT_WERKS
*&      <-- PT_EKORG
*&---------------------------------------------------------------------*
FORM frm_clear_data_null  CHANGING ps_ta01    TYPE LINE OF tt_ta01.

ENDFORM.

FORM frm_set_sub_screen  USING    pv_flg TYPE char20
                         CHANGING pv_subscreen TYPE sy-dynnr.


  CASE pv_flg.
    WHEN 'B_HEAD'.
      CASE pv_subscreen.
        WHEN '2100'. pv_subscreen = '2200'.
        WHEN '2200'. pv_subscreen = '2100'.

        WHEN '2300'. pv_subscreen = '2500'.
        WHEN '2500'. pv_subscreen = '2300'.

        WHEN '2400'. pv_subscreen = '2600'.
        WHEN '2600'. pv_subscreen = '2400'.

        WHEN '2700'. pv_subscreen = '2800'.
        WHEN '2800'. pv_subscreen = '2700'.

        WHEN OTHERS.
      ENDCASE.
    WHEN 'B_ITEM'.
      CASE pv_subscreen.
        WHEN '2100'. pv_subscreen = '2300'.
        WHEN '2300'. pv_subscreen = '2100'.

        WHEN '2200'. pv_subscreen = '2500'.
        WHEN '2500'. pv_subscreen = '2200'.

        WHEN '2400'. pv_subscreen = '2700'.
        WHEN '2700'. pv_subscreen = '2400'.

        WHEN '2600'. pv_subscreen = '2800'.
        WHEN '2800'. pv_subscreen = '2600'.

        WHEN OTHERS.
      ENDCASE.

    WHEN 'B_DTL'.

      CASE pv_subscreen.
        WHEN '2100'. pv_subscreen = '2400'.
        WHEN '2400'. pv_subscreen = '2100'.

        WHEN '2200'. pv_subscreen = '2600'.
        WHEN '2600'. pv_subscreen = '2200'.

        WHEN '2300'. pv_subscreen = '2700'.
        WHEN '2700'. pv_subscreen = '2300'.

        WHEN '2500'. pv_subscreen = '2800'.
        WHEN '2800'. pv_subscreen = '2500'.

        WHEN OTHERS.
      ENDCASE.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_F4_ZBPCODE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01
*&---------------------------------------------------------------------*
FORM frm_f4_zbpcode  USING    ps_ta01 TYPE LINE OF tt_ta01.

  DATA dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.
  DATA:lt_ret_tab TYPE TABLE OF ddshretval WITH HEADER LINE.
  DATA:lv_zbptype TYPE ty_ta01-zbptype.

  dynpfields-fieldname = 'GS_TA01-ZBPTYPE'.
  APPEND dynpfields.
  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-cprog
      dynumb               = sy-dynnr
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      invalid_parameter    = 7
      undefind_error       = 8
      double_conversion    = 9
      stepl_not_found      = 10
      OTHERS               = 11.
  IF sy-subrc EQ 0.
    READ TABLE dynpfields WITH KEY 'GS_TA01-ZBPTYPE'.
    IF sy-subrc = 0.
      lv_zbptype  = dynpfields-fieldvalue.
      TRANSLATE lv_zbptype TO UPPER CASE.
    ENDIF.
  ELSE.

  ENDIF.

  IF lv_zbptype = 'S'.

    DATA: ls_return TYPE           ddshretval,
          lt_return TYPE TABLE OF  ddshretval.
    DATA: ls_shlp   TYPE           shlp_descr,
          wa_selopt LIKE LINE OF   ls_shlp-selopt.

    CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
      EXPORTING
        tabname           = 'LFA1'
        fieldname         = 'LIFNR'
*       searchhelp        = 'SH_AFASL_SPEC'
        callback_program  = sy-repid
*       callback_form     = 'SET_VALUES_FOR_F4_AFASL_304'
      TABLES
        return_tab        = lt_return
      EXCEPTIONS
        field_not_found   = 1
        no_help_for_field = 2
        inconsistent_help = 3
        no_values_found   = 4
        OTHERS            = 5.

    READ TABLE lt_return INDEX 1 INTO ls_return.
    CHECK sy-subrc = 0.
    ps_ta01-zbpcode = ls_return-fieldval.

    CLEAR sy-ucomm.

******
******    SELECT
******      lifnr,
******      name1
******      FROM lfa1
******      INTO TABLE @DATA(lt_lfa1).
******
******    CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
******      EXPORTING
******        retfield        = 'LIFNR'
******        value_org       = 'S'
******        dynpprog        = sy-repid
******        dynpnr          = sy-dynnr
******        dynprofield     = 'GS_TA01-ZBPCODE'
*******       callback_program = sy-repid
*******       callback_form   = 'USER_FORM'
******      TABLES
******        value_tab       = lt_lfa1[]
******        return_tab      = lt_ret_tab[]
******      EXCEPTIONS
******        parameter_error = 1
******        no_values_found = 2
******        OTHERS          = 3.

  ELSEIF lv_zbptype = 'M'.
*    SELECT
*       a~guidkey ,
*       a~codeitemid ,
*       a~codeitemdesc ,
*       a~b0105 ,
*       a~parentid ,
*       b~b0105 AS b0105_p
*      FROM zmmt0345 AS a LEFT JOIN zmmt0345 AS b
*                                ON a~parentid = b~codeitemid
*      WHERE a~status = '1'
*      INTO TABLE @DATA(lt_zmmt0345).

    SELECT partner, name_org1 INTO TABLE @DATA(lt_but000) FROM but000 WHERE bu_group = 'BP07'.


    CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
      EXPORTING
        retfield        = 'PARTNER'
        value_org       = 'S'
        dynpprog        = sy-repid
        dynpnr          = sy-dynnr
        dynprofield     = 'GS_TA01-ZBPCODE'
*       callback_program = sy-repid
*       callback_form   = 'USER_FORM'
      TABLES
        value_tab       = lt_but000[]
        return_tab      = lt_ret_tab[]
      EXCEPTIONS
        parameter_error = 1
        no_values_found = 2
        OTHERS          = 3.
  ENDIF.

  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_SCREEN_ATTR
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_DATA_SCREEN
*&---------------------------------------------------------------------*
FORM frm_pro_data_scn_ctrl CHANGING ps_data_scn_ctrl TYPE ty_data_scn_ctrl.

  ps_data_scn_ctrl-zxybstyp = ''.
  ps_data_scn_ctrl-ztktype = ''.
  ps_data_scn_ctrl-zfllx = ''.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_ATTR
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_DATA_SCREEN
*&      <-- SCREEN
*&---------------------------------------------------------------------*
*FORM frm_set_screen_attr  USING    pt_data_scn TYPE tt_data_scn
*                          CHANGING ps_screen TYPE screen.
*
*  READ TABLE pt_data_scn INTO DATA(ls_data_scn) WITH KEY zfdid = ps_screen-name.
*  IF sy-subrc EQ 0 AND ls_data_scn-zfdatb IS NOT INITIAL.
*    CASE ls_data_scn-zfdatb.
*      WHEN '4'. ps_screen-active = 0.             "隐藏
*      WHEN '3'. ps_screen-input = 0.             "显示
*      WHEN '2'. ps_screen-required = 1.           "必输
*      WHEN '1'. ps_screen-input = 1.              "编辑
*      WHEN OTHERS.
*    ENDCASE.
*  ENDIF.
*ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DATA_4100
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_CURSOR_LINE
*&      <-- GT_T09
*&---------------------------------------------------------------------*
FORM frm_pro_data_4100  USING   pv_code TYPE sy-ucomm
                                 pv_cursor_line TYPE i
                        CHANGING
                                 ps_ta01         TYPE LINE OF tt_ta01
                                 pt_t09          TYPE tt_t09
                                 ps_t09_sub      TYPE LINE OF tt_t09
                                 pt_t20          TYPE tt_t20
                                 pt_t20_sub      TYPE tt_t20
  .

  DATA: lv_index_line     TYPE i,
        lv_code_tmp       TYPE sy-ucomm,
        lv_index_line_tmp TYPE i.

*  执行前需要先备份 如果不保存的话可以还原
  DATA(lt_t09_bak) = pt_t09[].
  DATA(lt_t20_bak) = pt_t20[].

*  商品组的不是针对行直接操作，因此不能获取光标行

  IF pv_code = 'B_SPZ_DTL' OR pv_code = '&IC1'.
    CHECK pv_cursor_line IS NOT INITIAL.
    PERFORM frm_get_index_line_40 USING      pv_code
                                          pv_cursor_line
                               CHANGING   lv_index_line.
  ELSE.
    READ TABLE pt_t09 TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
    IF sy-subrc EQ 0.
      lv_index_line = sy-tabix.
    ENDIF.
  ENDIF.

  IF pv_code NE 'B_SPZ_ADD'.
    CHECK   lv_index_line IS NOT INITIAL.
  ENDIF.

  PERFORM frm_set_data_sub USING 'PBO'
                                 lv_index_line
                                 pv_code
                           CHANGING
                                 ps_ta01
                                 pt_t09
                                 pt_t20
                                 ps_t09_sub
                                 pt_t20_sub.


  IF pv_code NE 'B_SPZ_ADD'.
    CHECK ps_t09_sub-zspz_id IS NOT INITIAL.
  ENDIF.



*  记录屏幕跳转前的command
  lv_code_tmp = pv_code.
  lv_index_line_tmp = lv_index_line.

  CALL SCREEN '4100'.

  IF pv_code = 'B_SPZ_SAVE'.

    PERFORM frm_set_data_sub USING 'PAI'
                                   lv_index_line_tmp
                                   lv_code_tmp
                             CHANGING
                                   ps_ta01
                                   pt_t09
                                   pt_t20
                                   ps_t09_sub
                                   pt_t20_sub.
  ELSE.
    pt_t09[] = lt_t09_bak[].
    pt_t20[] = lt_t20_bak[].

  ENDIF.

  PERFORM frm_pro_data_lock               USING ps_t09_sub-zspz_id
                                                  'X'.
ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_SUB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LV_INDEX_LINE
*&      --> PT_T09
*&      --> PT_T20
*&      <-- PS_T09_SUB
*&      <-- PT_T20_SUB
*&---------------------------------------------------------------------*
FORM frm_set_data_sub  USING   pv_flg TYPE char4
                                pv_index_line TYPE i
                                pv_code TYPE sy-ucomm
                       CHANGING
                                 ps_ta01 TYPE LINE OF tt_ta01
                                pt_t09  TYPE tt_t09
                                pt_t20  TYPE tt_t20
                                ps_t09_sub  TYPE LINE OF tt_t09
                                pt_t20_sub  TYPE tt_t20.

  IF pv_flg = 'PBO'.

    CLEAR: ps_t09_sub,pt_t20_sub.
    pt_t20_sub[] = pt_t20[].

    IF pv_code = 'B_SPZ_EDIT'.
      READ TABLE pt_t09 INTO ps_t09_sub WITH KEY sel = 'X'.

      PERFORM frm_pro_data_lock               USING ps_t09_sub-zspz_id
                                                      ''.

      DELETE pt_t20 WHERE zspz_id = ps_t09_sub-zspz_id.

    ELSEIF ( pv_code = 'B_SPZ_DTL' OR pv_code = '&IC1' ).
      READ TABLE pt_t09 INTO ps_t09_sub INDEX pv_index_line.
    ELSEIF pv_code = 'B_SPZ_ADD'.
*      IF ps_t09_sub-zspz_id IS INITIAL.
*        PERFORM frm_get_num USING 'ZRE0003' '01' CHANGING ps_t09_sub-zspz_id.
*      ENDIF.
    ENDIF.

    DELETE pt_t20_sub WHERE zspz_id NE ps_t09_sub-zspz_id.
    ps_t09_sub-zht_id = ps_ta01-zht_id.

    PERFORM frm_set_seq TABLES pt_t20_sub USING 'SEQ'.
  ELSE.

    LOOP AT pt_t20_sub INTO DATA(ls_t20_sub).
      ls_t20_sub-zht_id = ps_ta01-zht_id.
      ls_t20_sub-zspz_id = ps_t09_sub-zspz_id.
      MODIFY pt_t20_sub FROM ls_t20_sub.
    ENDLOOP.

    IF pv_code = 'B_SPZ_EDIT'.
      MODIFY pt_t09 FROM  ps_t09_sub INDEX pv_index_line.
      APPEND LINES OF pt_t20_sub TO pt_t20.
    ELSEIF pv_code = 'B_SPZ_ADD'.
      APPEND LINES OF pt_t20_sub TO pt_t20.
      APPEND ps_t09_sub TO pt_t09.

    ENDIF.
    PERFORM frm_set_seq TABLES pt_t09 USING 'SEQ'.

  ENDIF.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_DATA_TC_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_T09_SUB
*&      <-- GS_T20_SUB
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA_SPZ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_CODE
*&      --> GT_T09
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_check_data_spz  USING    pv_code TYPE sy-ucomm
                                  ps_ta01  TYPE LINE OF tt_ta01
                                  pt_t09  TYPE tt_t09
                         CHANGING pv_mtype  TYPE bapi_mtype
                                  pv_msg  TYPE bapi_msg.

  CLEAR: pv_mtype,pv_msg.


  IF pv_mtype NE 'E'.
    pv_mtype = 'S'.
  ELSE.
    pv_mtype = 'E'.
  ENDIF.

  CASE pv_code.
    WHEN 'B_SPZ_ADD'.
      IF ps_ta01-zht_id IS  INITIAL.
        pv_mtype = 'E'.
        pv_msg = '没有返利合同号需先保存返利合同！'.
      ENDIF.
    WHEN 'B_SPZ_EDIT'.
      READ TABLE pt_t09 TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
      IF sy-subrc NE 0.
        pv_mtype = 'E'.
        pv_msg = '请选择一条记录'.
      ENDIF.
      LOOP AT pt_t09 TRANSPORTING NO FIELDS WHERE sel = 'X'.
        DATA(lv_tabix) = sy-tabix.
        lv_tabix = lv_tabix + 1.
        LOOP AT pt_t09 TRANSPORTING NO FIELDS FROM lv_tabix WHERE sel = 'X'.
          EXIT.
        ENDLOOP.
        IF sy-subrc EQ 0.
          pv_mtype = 'E'.
          pv_msg = '只能选择一条记录'.
        ENDIF.
      ENDLOOP.
    WHEN 'B_SPZ_DELE'.
      READ TABLE pt_t09 TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
      IF sy-subrc NE 0.
        pv_mtype = 'E'.
        pv_msg = '请选择一条记录'.
      ENDIF.
      LOOP AT pt_t09 INTO DATA(ls_t09) WHERE sel = 'X'.
        IF ls_t09-zspz_id IS NOT INITIAL.
          SELECT COUNT(*) FROM zreta002 WHERE zspz_id = ls_t09-zspz_id.
          IF sy-subrc EQ 0.
            pv_mtype = 'E'.
            pv_msg = '商品组在返利条款中存在，不允许删除'.
          ENDIF.

          SELECT COUNT(*) FROM zreta002 WHERE zflspz_id = ls_t09-zspz_id.
          IF sy-subrc EQ 0.
            pv_mtype = 'E'.
            pv_msg = '商品组在返利条款中存在，不允许删除'.
          ENDIF.
        ENDIF.

      ENDLOOP.
    WHEN OTHERS.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZBPCODE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA01
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_zbpcode  USING    ps_ta01  TYPE LINE OF tt_ta01
                        CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
      ls_msglist TYPE LINE OF scp1_general_errors.

  CHECK ps_ta01-zbpcode IS NOT INITIAL.



*  IF ps_ta01-zbptype = 'S'.
*    SELECT SINGLE COUNT(*) FROM lfa1 WHERE lifnr = ps_ta01-zbpcode .
*    IF sy-subrc NE 0.
*      ls_msglist-msgv1 = '伙伴不存在!'.
*      APPEND ls_msglist TO pt_msglist.
*    ENDIF.
*  ELSEIF ps_ta01-zbptype = 'M'.
*    ps_ta01-zbpcode = |{ ps_ta01-zbpcode ALPHA = OUT } |.
*    SELECT SINGLE COUNT(*) FROM zmmt0345 WHERE codeitemid = ps_ta01-zbpcode AND status = '1'.
*    IF sy-subrc NE 0.
*      ls_msglist-msgv1 = '伙伴不存在!'.
*      APPEND ls_msglist TO pt_msglist.
*    ENDIF.
*  ENDIF.

  IF ps_ta01-zbptype = 'S'.
    SELECT SINGLE COUNT(*) FROM lfa1 WHERE lifnr = ps_ta01-zbpcode .
    IF sy-subrc NE 0.
      ls_msglist-msgv1 = '伙伴不存在!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.
  ELSEIF ps_ta01-zbptype = 'M'.
    SELECT SINGLE  COUNT(*)  FROM but000 WHERE partner = ps_ta01-zbpcode.
    IF sy-subrc NE 0.
      ls_msglist-msgv1 = '伙伴不存在!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DOUBLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_TA01
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_double   USING    ps_ta01  TYPE LINE OF tt_ta01
                        CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
      ls_msglist TYPE LINE OF scp1_general_errors.

  SELECT SINGLE zht_id FROM zreta001 WHERE
                                            zbukrs = @ps_ta01-zbukrs
                                        AND zbptype = @ps_ta01-zbptype
                                        AND zbpcode = @ps_ta01-zbpcode
                                        AND zht_id  NE @ps_ta01-zht_id
                                        AND zhtlx  EQ @ps_ta01-zhtlx
                                        AND ( ( zbegin <= @ps_ta01-zbegin AND zend >= @ps_ta01-zbegin ) OR
                                              ( zbegin >= @ps_ta01-zbegin AND zbegin <= @ps_ta01-zend ) )
                                        INTO @DATA(lv_tmp_zht_id).
  IF sy-subrc EQ 0.
    ls_msglist-msgv1 = '日期范围存在交叉! 合同号：' && lv_tmp_zht_id.
    APPEND ls_msglist TO pt_msglist.
  ENDIF.


ENDFORM.



FORM frm_alv_display  USING   pv_flg TYPE char2
                              prf_alv TYPE REF TO cl_gui_alv_grid.

  DATA: lt_fieldcat TYPE TABLE OF lvc_s_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_layout   TYPE TABLE OF lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  DATA:
        lv_tabname TYPE tabname.

  IF pv_flg = 'JS'.
    lv_tabname = 'GT_TK_JS'.
  ELSEIF pv_flg = 'GD'..
    lv_tabname = 'GT_TK_GD'.
  ELSEIF pv_flg = 'SQ'.
    lv_tabname = 'GT_TK_SQ'.
  ENDIF.

  PERFORM frm_set_catalog USING pv_flg CHANGING lt_fieldcat.

  PERFORM frm_set_layout CHANGING ls_layout.

  PERFORM frm_set_ex_fcode TABLES lt_ex_fcode.

  PERFORM frm_set_variant CHANGING  ls_variant.

*ALV显示

  PERFORM frm_set_alv USING
                            lv_tabname
                            ls_variant
                            ls_layout
                            lt_ex_fcode
                      CHANGING
                            lt_fieldcat
                            prf_alv.

*  事件注册
  DATA:
        lv_objid TYPE char10 .

  IF pv_flg = 'JS'.
    lv_objid = 'JS'.
  ELSEIF pv_flg = 'GD'..
    lv_objid = 'GD'.
  ELSEIF pv_flg = 'SQ'..
    lv_objid = 'SQ'.
  ENDIF.
  DATA lrf_event TYPE REF TO sec_lcl_event_receiver.


  CREATE OBJECT lrf_event
    EXPORTING
      i_objid = lv_objid.


  PERFORM frm_set_event_handler USING    lv_objid
                                CHANGING lrf_event
                                         prf_alv.
ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LS_VARIANT  TEXT
*      -->P_LS_LAYOUT  TEXT
*      -->P_LT_EX_FCODE  TEXT
*      <--P_LT_FIELDCAT  TEXT
*      <--P_'GT_DATA_T'  TEXT
*----------------------------------------------------------------------*
FORM frm_set_alv  USING    pv_tname TYPE any
                           ps_variant TYPE disvariant
                           ps_layout TYPE lvc_s_layo
                           pt_ex_fcode TYPE ui_functions
                  CHANGING pt_fieldcat TYPE lvc_t_fcat
                           prf_alv_grid TYPE REF TO cl_gui_alv_grid.


  DATA lv_table  LIKE feld-name.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.

  CONCATENATE pv_tname '[]' INTO lv_table.
  ASSIGN (lv_table) TO <lt_table>.

  CALL METHOD prf_alv_grid->set_table_for_first_display
    EXPORTING
*     I_BUFFER_ACTIVE               =
*     I_BYPASSING_BUFFER            =
*     I_CONSISTENCY_CHECK           =
*     I_STRUCTURE_NAME              = 'IT_ITAB'
      is_variant                    = ps_variant
      i_save                        = 'A'
*     I_DEFAULT                     = 'X'
      is_layout                     = ps_layout
*     IS_PRINT                      =
*     IT_SPECIAL_GROUPS             =
      it_toolbar_excluding          = pt_ex_fcode
*     IT_HYPERLINK                  =
*     IT_ALV_GRAPHICS               =
*     IT_EXCEPT_QINFO               =
*     IR_SALV_ADAPTER               =
    CHANGING
      it_outtab                     = <lt_table>
      it_fieldcatalog               = pt_fieldcat
*     IT_SORT                       =
*     IT_FILTER                     =
    EXCEPTIONS
      invalid_parameter_combination = 1
      program_error                 = 2
      too_many_lines                = 3
      OTHERS                        = 4.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
               WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.

FORM frm_set_catalog USING pv_flg TYPE char2  CHANGING   pt_fieldcat TYPE lvc_t_fcat.
  DATA: lw_fieldcat TYPE lvc_s_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.
  DEFINE add_fcat.
    CLEAR lw_fieldcat.

    IF lw_fieldcat IS INITIAL.
      lw_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO lw_fieldcat-col_pos.
    ENDIF.
    lw_fieldcat-outputlen = &1.                 "
    lw_fieldcat-fieldname = &2.                 "
    lw_fieldcat-coltext = &3.                 "

    CASE &2.
      WHEN 'MATNR' .
      lw_fieldcat-ref_table = 'MARA'.
      lw_fieldcat-ref_field = 'MATNR'.
      lw_fieldcat-edit = 'X'.
      WHEN 'ZFLLX'  .
      lw_fieldcat-convexit = 'ZFLLX'.

      WHEN 'ZTKTYPE'  .
      lw_fieldcat-convexit = 'ZTKTY'.

      WHEN 'ZTK_ID'  .
      lw_fieldcat-hotspot = 'X'.
      WHEN 'ZSPZ_ID' OR 'ZFLSPZ_ID'  .
      lw_fieldcat-hotspot = 'X'.
      WHEN 'ZRLID'  .
      lw_fieldcat-hotspot = 'X'.
      WHEN 'ZXYZT'  .
      lw_fieldcat-convexit = 'ZXYZT'.
      WHEN OTHERS.
    ENDCASE.

    APPEND lw_fieldcat TO pt_fieldcat.
    CLEAR lw_fieldcat.
  END-OF-DEFINITION.

  add_fcat  '10' 'SEQ    '    '序号'  .
  add_fcat  '10' 'ZFLLX    '    '返利类型'  .
  IF pv_flg = 'JS' OR pv_flg = 'SQ'.
    add_fcat  '10' 'ZTKTYPE  '    '是否附加条款'  .
  ENDIF.

  add_fcat  '10' 'ZTK_ID   '    '返利条款号码'  .
  add_fcat  '10' 'ZTK_TXT  '    '返利条款描述'  .
  IF pv_flg = 'JS' OR pv_flg = 'SQ' .
    add_fcat  '10' 'ZSPZ_ID  '    '任务商品组'  .
  ENDIF.
*  IF pv_flg = 'GD'.
*  add_fcat  '10' 'ZFLSPZ_ID'    '返利商品组'  .
*  ENDIF.

  add_fcat  '10' 'ZBEGIN   '    '开始日期'  .
  add_fcat  '10' 'ZEND     '    '结束日期'  .
  IF pv_flg = 'GD'.
    add_fcat  '10' 'ANBTR     '   '已累计金额'  .
*    add_fcat  '10' 'ZSL     '   '已累计数量'  .
  ENDIF.
  add_fcat  '10' 'ZTMPID   '    '组织模板'  .
  add_fcat  '10' 'ZRLID  '    '附加条款'  .
  add_fcat  '10' 'ZLEIB  '    '类别'  .
  add_fcat  '10' 'ZXYZT    '    '状态'  .

  IF gv_flg_rb = '01' OR gv_flg_rb = '02'.
    CLEAR ls_fieldcat.
    ls_fieldcat-fieldname = 'ICON_EDIT'.
    ls_fieldcat-col_pos = 3.
    ls_fieldcat-coltext = '修改'.
    ls_fieldcat-icon = 'X'.
    ls_fieldcat-hotspot = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
  ENDIF.

  CLEAR ls_fieldcat.
  ls_fieldcat-fieldname = 'ICON_DIS'.
  ls_fieldcat-col_pos = 3.
  ls_fieldcat-coltext = '显示'.
  ls_fieldcat-icon = 'X'.
  ls_fieldcat-hotspot = 'X'.
  APPEND ls_fieldcat TO pt_fieldcat.



ENDFORM.                    " FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.
  ps_layout-box_fname = 'SEL_ALV'.
  ps_layout-sel_mode = 'D'.
  ps_layout-zebra     = 'X'.
  ps_layout-cwidth_opt  = 'X'.
ENDFORM.                    "FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EVENT_HANDLER
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      <--P_LO_EVENT  TEXT
*      <--P_GO_ALV_TOP  TEXT
*----------------------------------------------------------------------*
FORM frm_set_event_handler USING pv_objid TYPE char10
                            CHANGING prf_event TYPE REF TO sec_lcl_event_receiver
                                     prf_grid  TYPE REF TO cl_gui_alv_grid.


  CALL METHOD prf_grid->set_ready_for_input
    EXPORTING
      i_ready_for_input = 1.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_enter.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_modified.

*  CREATE OBJECT PRF_EVENT.
  SET HANDLER prf_event->sec_handle_bef_user_command         FOR prf_grid.
  SET HANDLER prf_event->sec_handle_user_command             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_toolbar                  FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed_fin         FOR prf_grid.
  SET HANDLER prf_event->sec_handle_hotspot_click            FOR prf_grid.
  SET HANDLER prf_event->sec_handle_double_click             FOR prf_grid.


  CALL METHOD prf_grid->set_toolbar_interactive.

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_EXCLUDE TEXT
*----------------------------------------------------------------------*
FORM frm_set_ex_fcode TABLES pt_exclude.
  DATA ls_exclude TYPE ui_func.
  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_insert_row .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_delete_row .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_refresh.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_detail.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_paste.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_undo.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_cut.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_append_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_move_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print_prev .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_graph .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_check .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_view .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_help .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_info .
  APPEND ls_exclude TO pt_exclude.

ENDFORM.                    "FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant CHANGING ps_variant TYPE disvariant.
  ps_variant-report = sy-repid.
  ps_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT

FORM frm_check_changed_data USING prf_alv TYPE REF TO cl_gui_alv_grid.
  IF prf_alv IS NOT INITIAL.
    CALL METHOD prf_alv->check_changed_data.
  ENDIF.
ENDFORM.

FORM frm_refresh_alv USING prf_alv TYPE REF TO cl_gui_alv_grid.
  DATA:
         ls_stable TYPE lvc_s_stbl.

  ls_stable-row = ls_stable-col = 'X'.
  CALL METHOD prf_alv->refresh_table_display
    EXPORTING
      is_stable = ls_stable
*     I_SOFT_REFRESH = ''
    EXCEPTIONS
      finished  = 1
      OTHERS    = 2.
  IF sy-subrc <> 0.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_ANBTR
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LS_TK_JS
*&---------------------------------------------------------------------*
FORM frm_get_anbtr  CHANGING ps_tk_js TYPE LINE OF tt_tk.

  SELECT SINGLE * INTO @DATA(ls_s03) FROM zret0003 WHERE zhsjz = @ps_tk_js-zhsjz.

  SELECT
*    SUM( a~zpurjzsl ) AS zpurjzsl   ,
*    SUM( a~zpurjzje ) AS zpurjzje   ,
*    SUM( a~zrpurjzje ) AS zrpurjzje   ,
*    SUM( a~zpuryssl ) AS zpuryssl   ,
    SUM( a~zpurysje ) AS zpurysje   ,
*    SUM( a~zdistjzsl ) AS zdistjzsl   ,
*    SUM( a~zdistjzje ) AS zdistjzje   ,
*    SUM( a~zrdistjzje ) AS zrdistjzje   ,
*    SUM( a~zdistyssl ) AS zdistyssl   ,
    SUM( a~zdistysje ) AS zdistysje   ,
*    SUM( a~zsalejzsl ) AS zsalejzsl   ,
*    SUM( a~zsalejzje ) AS zsalejzje   ,
*    SUM( a~zrsalejzje ) AS zrsalejzje   ,
*    SUM( a~zsaleyssl ) AS zsaleyssl   ,
    SUM( a~zsaleysje ) AS zsaleysje   ,
*    SUM( a~zduepayje ) AS zduepayje   ,
    SUM( a~zduercv ) AS zduercv   ,
*    SUM( a~zactlpayje ) AS zactlpayje   ,
    SUM( a~zactlrcv ) AS zactlrcv

    FROM zret0017 AS a
    WHERE zxy_id = @ps_tk_js-ztk_id
    GROUP BY a~zxy_id
    INTO TABLE @DATA(lt_t17_sum).

  READ TABLE lt_t17_sum INTO DATA(ls_t17_sum) INDEX 1.

  IF ls_s03-zlower = 'X'.
    ps_tk_js-anbtr = nmin(
    val1 = ls_t17_sum-zpurysje
    val2 = ls_t17_sum-zdistysje
    val3 = ls_t17_sum-zsaleysje
    val4 = ls_t17_sum-zactlrcv
    ).
  ELSEIF ls_s03-zhigher = 'X'.
    ps_tk_js-anbtr = nmax(
    val1 = ls_t17_sum-zpurysje
    val2 = ls_t17_sum-zdistysje
    val3 = ls_t17_sum-zsaleysje
    val4 = ls_t17_sum-zactlrcv
    ).
  ELSEIF ls_s03-zpur = 'X' AND ps_tk_js-zflxs = 'M'.
    ps_tk_js-anbtr = ls_t17_sum-zpurysje.
  ELSEIF ls_s03-zdist = 'X' AND ps_tk_js-zflxs = 'M'.
    ps_tk_js-anbtr = ls_t17_sum-zdistysje.
  ELSEIF ls_s03-zsale = 'X' AND ps_tk_js-zflxs = 'M'.
    ps_tk_js-anbtr = ls_t17_sum-zsaleysje.
  ELSEIF ls_s03-zpur = 'X' AND ps_tk_js-zflxs = 'M'.
    ps_tk_js-anbtr = ls_t17_sum-zpurysje.
  ELSEIF ls_s03-zduepay = 'X' .
    ps_tk_js-anbtr = ls_t17_sum-zduercv.
  ELSEIF ls_s03-zactlpay = 'X' .
    ps_tk_js-anbtr = ls_t17_sum-zactlrcv.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SPZ_DELE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_T09
*&---------------------------------------------------------------------*
FORM frm_spz_dele  CHANGING pt_t09 TYPE tt_t09.

  DATA lv_answer TYPE char1.

  PERFORM frm_are_you_sure USING    '确定删除商品组?'
                                    sy-title
                           CHANGING  lv_answer.
  IF lv_answer NE 'J'.
    RETURN.
  ENDIF.

  LOOP AT pt_t09 INTO DATA(ls_t09) WHERE sel = 'X'.
    DELETE FROM zret0020      WHERE zspz_id = ls_t09-zspz_id.
    DELETE FROM zret0009      WHERE zspz_id = ls_t09-zspz_id.
    DELETE FROM zret0020_item WHERE zspz_id = ls_t09-zspz_id.
    DELETE pt_t09.
    CONTINUE.
  ENDLOOP.
  COMMIT WORK AND WAIT.
  MESSAGE s888(sabapdocu) WITH '商品组删除成功' DISPLAY LIKE 'S'.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SCREEN_CHECK_ZHTID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZHTID
*&---------------------------------------------------------------------*
FORM frm_screen_check_zhtid  USING    pv_zhtid TYPE zreta001-zhtid.
  SELECT SINGLE COUNT(*) FROM zmmt0230 WHERE ztzhtbh =  pv_zhtid.
  IF sy-subrc NE 0.
    MESSAGE e888(sabapdocu) WITH '合同不存在' DISPLAY LIKE 'E'.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_UPDATE_GT_T09
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GS_TA01_ZHT_ID
*&      <-- GT_T09
*&---------------------------------------------------------------------*
FORM frm_update_gt_t09  USING    pv_zht_id
                        CHANGING pt_t09 TYPE tt_t09.


  SELECT
    *
    INTO CORRESPONDING FIELDS OF TABLE pt_t09
    FROM zret0009
    WHERE zht_id = pv_zht_id.

ENDFORM.