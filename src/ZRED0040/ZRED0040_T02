*&---------------------------------------------------------------------*
*& 包含               ZRED0040_T02
*&---------------------------------------------------------------------*


TYPES:
  BEGIN OF ty_ta01.
    INCLUDE TYPE zreta001.
TYPES:
  sel        TYPE char1,
  zht_id_bak TYPE zreta001-zht_id,
  zxybstyp   TYPE zretc009-zxybstyp,
  zbpname    TYPE lfa1-name1,
  eknam      TYPE t024-eknam,
  zflzff_t   TYPE lfa1-name1,
  END OF ty_ta01,
  tt_ta01 TYPE TABLE OF ty_ta01.

*商品组抬头数据
TYPES:
  BEGIN OF ty_t09.
    INCLUDE TYPE zret0009.
TYPES:
  sel         TYPE char1,
  seq         TYPE i,
  lines_sum   TYPE i,
  lines_cur   TYPE i,
  zspz_id_tmp TYPE zret0009-zspz_id,
  zht_txt     TYPE zreta001-zht_txt,
  zbpcode     TYPE zreta001-zbpcode,
  zflg_call   TYPE char1,       "调用标识

  END OF ty_t09,
  tt_t09 TYPE TABLE OF ty_t09.

*商品组明细
TYPES:
  BEGIN OF ty_t20.
    INCLUDE TYPE zret0020.
TYPES:
  sel   TYPE char1,
  seq   TYPE i,
  maktx TYPE makt-maktx,
  meins TYPE mara-meins,
  fdhsj TYPE zrei0006,
  END OF ty_t20,
  tt_t20 TYPE TABLE OF ty_t20.

TYPES: BEGIN OF ty_t20_item.
    INCLUDE TYPE zret0020_item.
TYPES: END OF ty_t20_item,
tt_t20_item TYPE TABLE OF ty_t20_item.



TYPES:BEGIN OF ty_fdhsj,
        sel    TYPE char1,
        seq    TYPE i,
        zbegdt TYPE zret0020_item-zbegdt,
        zenddt TYPE zret0020_item-zenddt,
        zacpr  TYPE zret0020_item-zacpr,
      END OF ty_fdhsj,
      tt_fdhsj TYPE TABLE OF ty_fdhsj.

DATA:
  gt_t09     TYPE tt_t09,
  gs_t09     TYPE LINE OF tt_t09,
  gs_t09_sub TYPE LINE OF tt_t09,
  gt_t20     TYPE tt_t20,
  gs_t20_sub TYPE LINE OF tt_t20,
  gt_t20_sub TYPE tt_t20,
  gs_fdhsj   TYPE LINE OF tt_fdhsj,
  gt_fdhsj   TYPE  tt_fdhsj.


TYPES:
  BEGIN OF ty_mara,
    sel   TYPE char1,
    matnr TYPE mara-matnr,
    maktx TYPE makt-maktx,
    meins TYPE mara-meins,
  END OF ty_mara,
  tt_mara TYPE TABLE OF ty_mara.

DATA:
      gt_mara TYPE tt_mara.

DATA:gv_fdhsj_mx TYPE icon-id.