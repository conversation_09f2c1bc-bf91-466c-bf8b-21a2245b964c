*&---------------------------------------------------------------------*
*& 包含               ZRED0040_C01
*&---------------------------------------------------------------------*
DATA:
  grf_container_js     TYPE REF TO cl_gui_custom_container,
  grf_alv_js           TYPE REF TO cl_gui_alv_grid,
  gv_container_name_js TYPE c LENGTH 20 VALUE 'CON_JS',
  grf_container_gd     TYPE REF TO cl_gui_custom_container,
  grf_alv_gd           TYPE REF TO cl_gui_alv_grid,
  gv_container_name_gd TYPE c LENGTH 20 VALUE 'CON_GD',
  grf_container_sq     TYPE REF TO cl_gui_custom_container,
  grf_alv_sq           TYPE REF TO cl_gui_alv_grid,
  gv_container_name_sq TYPE c LENGTH 20 VALUE 'CON_SQ'.

CLASS sec_lcl_event_receiver DEFINITION DEFERRED.


CLASS sec_lcl_event_receiver DEFINITION.
  PUBLIC SECTION.
    DATA:
      objid    TYPE char10.

    METHODS:
      constructor
        IMPORTING
          i_objid TYPE char10 OPTIONAL,
      sec_handle_toolbar
                    FOR EVENT toolbar OF cl_gui_alv_grid
        IMPORTING e_object e_interactive,
      sec_handle_bef_user_command
                    FOR EVENT before_user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_user_command
                    FOR EVENT user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_hotspot_click
                    FOR EVENT hotspot_click OF cl_gui_alv_grid
        IMPORTING e_row_id e_column_id es_row_no,
      sec_handle_double_click
                    FOR EVENT double_click OF cl_gui_alv_grid
        IMPORTING e_row e_column es_row_no,
      sec_handle_data_changed
                    FOR EVENT data_changed OF cl_gui_alv_grid
        IMPORTING er_data_changed e_onf4 e_onf4_before e_onf4_after,
      sec_handle_data_changed_fin
                    FOR EVENT data_changed_finished OF cl_gui_alv_grid
        IMPORTING e_modified et_good_cells.
ENDCLASS.

CLASS sec_lcl_event_receiver IMPLEMENTATION.

  METHOD constructor.
    objid = i_objid.
  ENDMETHOD.
  METHOD sec_handle_toolbar.
    PERFORM handle_toolbar USING e_object e_interactive objid.
  ENDMETHOD.
  METHOD sec_handle_bef_user_command.
    PERFORM handle_bef_user_command USING e_ucomm.
  ENDMETHOD.
  METHOD sec_handle_user_command.
    PERFORM handle_user_commmand CHANGING e_ucomm objid.
  ENDMETHOD.
  METHOD sec_handle_hotspot_click.
    PERFORM handle_hotspot_click USING e_row_id e_column_id es_row_no objid.
  ENDMETHOD.
  METHOD sec_handle_double_click.
    PERFORM handle_double_click USING e_row e_column es_row_no objid.
  ENDMETHOD.
  METHOD sec_handle_data_changed.
    PERFORM handle_data_changed USING er_data_changed objid.
  ENDMETHOD.
  METHOD sec_handle_data_changed_fin.
    PERFORM handle_data_changed_fin USING e_modified et_good_cells objid.
  ENDMETHOD.
ENDCLASS.


FORM handle_data_changed USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               pv_objid TYPE char10.

*  DATA:
*    LW_MOD_CELL TYPE LVC_S_MODI,
*    LT_MOD_CELL TYPE LVC_T_MODI.
*  FIELD-SYMBOLS:
*                 <FS_ANY> TYPE ANY.
*  DATA:
*  LW_DATA TYPE TY_DATA.
*
*  LOOP AT PRF_DATA_CHANGED->MT_MOD_CELLS INTO LW_MOD_CELL WHERE FIELDNAME = 'MATNR' .
*    APPEND LW_MOD_CELL TO LT_MOD_CELL.
*  ENDLOOP.
*  IF LT_MOD_CELL[] IS NOT INITIAL.
*
*    LOOP AT LT_MOD_CELL INTO LW_MOD_CELL.
*      READ TABLE GT_DATA INTO LW_DATA INDEX LW_MOD_CELL-ROW_ID.
*      IF SY-SUBRC EQ 0.
*        UNASSIGN <FS_ANY>.
*        ASSIGN COMPONENT LW_MOD_CELL-FIELDNAME OF STRUCTURE LW_DATA TO  <FS_ANY>.
*        IF <FS_ANY> IS ASSIGNED.
*          <FS_ANY> = LW_MOD_CELL-VALUE.
*        ENDIF.
*        SELECT SINGLE MAKTX  INTO LW_DATA-MAKTX FROM MAKT WHERE MATNR = LW_DATA-MATNR.
*        SELECT SINGLE MEINS  INTO LW_DATA-MEINS FROM MARA WHERE MATNR = LW_DATA-MATNR.
*        MODIFY GT_DATA FROM LW_DATA INDEX LW_MOD_CELL-ROW_ID.
*        GV_FLG_CHANGED = 'X'.
*      ENDIF.
*    ENDLOOP.
*  ENDIF.

ENDFORM.

FORM handle_data_changed_fin USING pv_modified TYPE char01
                                    pt_good_cells  TYPE lvc_t_modi
                                    pv_objid TYPE char10 .
*  IF GV_FLG_CHANGED = 'X'.
*  PERFORM FRM_REFRESH_ALV USING GRF_ALV.
*  CLEAR:
*        GV_FLG_CHANGED.
*  ENDIF.

ENDFORM.

FORM handle_toolbar USING prf_object TYPE REF TO cl_alv_event_toolbar_set
                          pv_interactive
                          pv_objid TYPE char10.
  DATA: lw_toolbar TYPE stb_button.

  DEFINE add_toolbar.
    lw_toolbar-butn_type  = &1.
    lw_toolbar-function   = &2.
    lw_toolbar-icon       = &3.
    lw_toolbar-quickinfo  = &4.
    lw_toolbar-text       = &4.
    lw_toolbar-disabled   = &5.
    APPEND lw_toolbar TO prf_object->mt_toolbar .
    CLEAR lw_toolbar.
  END-OF-DEFINITION.


  DELETE prf_object->mt_toolbar WHERE function = '&GRAPH'
                                 OR function = '&INFO'
                                 OR function = '&REFRESH'
                                 OR function = '&CHECK'
                                 OR function = '&LOCAL&CUT'
                                 OR function = '&LOCAL&COPY'
                                 OR function = '&LOCAL&PASTE'
                                 OR function = '&LOCAL&UNDO'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&INSERT_ROW'
                                 OR function = '&LOCAL&DELETE_ROW'
                                 OR function = '&LOCAL&COPY_ROW'
                                 .
*  add_toolbar:
*    '0' 'ALL'  '@4B@' '全选' '' ,
*    '0' 'DALL' '@4D@' '取消' ''  ,
*    '0' 'EXP' '@49@' 'EXCEL模板下载' ''  ,
*    '0' 'IMP' '@48@' 'EXCEL数据上载' ''  ,
*    '0' 'INS' '@17@' '新增行' ''  ,
*    '0' 'DEL' '@18@' '删除行' ''  .

ENDFORM.

FORM handle_bef_user_command  USING pv_ucomm.



ENDFORM.

FORM handle_user_commmand CHANGING pv_ucomm
                          pv_objid TYPE char10.

*  DATA:LT_ROWS TYPE LVC_T_ROW,
*       LW_ROWS TYPE LVC_S_ROW.
*
*  CALL METHOD GRF_ALV->GET_SELECTED_ROWS
*    IMPORTING
*      ET_INDEX_ROWS = LT_ROWS.
*
*  CASE PV_UCOMM.
*    WHEN 'INS'.
*    WHEN OTHERS.
*  ENDCASE.
*
*  PERFORM FRM_REFRESH_ALV USING GRF_ALV.
ENDFORM.

FORM handle_hotspot_click  USING   ps_row_id TYPE lvc_s_row
                                    ps_column_id TYPE lvc_s_col
                                    ps_row_no TYPE lvc_s_roid
                                    pv_objid TYPE char10.

  DATA: lv_tab TYPE char20.
  DATA:
    lv_ztk_id    TYPE zreta002-ztk_id,
    lv_zrlid     TYPE zreta003-zrlid,
    lv_zflspz_id TYPE zret0009-zspz_id,
    lv_zspz_id   TYPE zret0009-zspz_id.

  FIELD-SYMBOLS: <fs_tab> TYPE STANDARD TABLE,
                 <fs_wa>  TYPE ty_tk,
                 <field>  TYPE any.

  IF pv_objid = 'JS'.
    lv_tab = 'GT_TK_JS[]'.
  ELSEIF pv_objid = 'GD'..
    lv_tab = 'GT_TK_GD[]'.
  ELSEIF pv_objid = 'SQ'..
    lv_tab = 'GT_TK_SQ[]'.
  ENDIF.

  ASSIGN (lv_tab) TO <fs_tab>.
  CHECK <fs_tab> IS ASSIGNED.


  READ TABLE <fs_tab> ASSIGNING <fs_wa> INDEX ps_row_no-row_id. "
  IF sy-subrc EQ 0 AND <fs_wa> IS ASSIGNED.
    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZTK_ID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_ztk_id = <field>.
    ENDIF.

    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZSPZ_ID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_zspz_id = <field>.
    ENDIF.

    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZRLID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_zrlid = <field>.
    ENDIF.

    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZFLSPZ_ID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_zflspz_id = <field>.
    ENDIF.



    CASE ps_column_id-fieldname.
      WHEN 'ICON_EDIT'.
        PERFORM frm_call_transaction_tk USING lv_ztk_id
                                              'ZRED0041B'.

      WHEN 'ICON_DIS'.
        PERFORM frm_call_transaction_tk USING lv_ztk_id
                                              'ZRED0041C'.
      WHEN 'ZTK_ID'.
        PERFORM frm_call_transaction_tk USING lv_ztk_id
                                              'ZRED0041C'.
      WHEN 'ZRLID'.
        PERFORM frm_call_transaction_tk USING lv_zrlid
                                              'ZRED0041C'.
      WHEN 'ZSPZ_ID'.

        PERFORM frm_call_transaction_spz USING lv_zspz_id
                                              ''.

      WHEN 'ZFLSPZ_ID'.
        PERFORM frm_call_transaction_spz USING lv_zflspz_id
                                              ''.

      WHEN OTHERS.

    ENDCASE.

    SET PARAMETER ID 'ZTK_ID' FIELD ''.

  ENDIF.

ENDFORM.




FORM handle_double_click  USING   ps_row_id TYPE lvc_s_row
                                   ps_column_id TYPE lvc_s_col
                                   ps_row_no TYPE lvc_s_roid
                                   pv_objid TYPE char10.

*  READ TABLE GT_DATA INTO DATA(LW_DATA) INDEX PS_ROW_NO-ROW_ID.
*  IF SY-SUBRC EQ 0.
*
*  ENDIF.

  DATA: lv_tab TYPE char20.
  DATA:
    lv_ztk_id  TYPE zreta002-ztk_id,
    lv_zrlid   TYPE zreta003-zrlid,
    lv_zspz_id TYPE zret0009-zspz_id.

  FIELD-SYMBOLS: <fs_tab> TYPE STANDARD TABLE,
                 <fs_wa>  TYPE ty_tk,
                 <field>  TYPE any.

  IF pv_objid = 'JS'.
    lv_tab = 'GT_TK_JS[]'.
  ELSEIF pv_objid = 'GD'..
    lv_tab = 'GT_TK_GD[]'.
  ELSEIF pv_objid = 'SQ'..
    lv_tab = 'GT_TK_SQ[]'.
  ENDIF.

  ASSIGN (lv_tab) TO <fs_tab>.
  CHECK <fs_tab> IS ASSIGNED.


  READ TABLE <fs_tab> ASSIGNING <fs_wa> INDEX ps_row_no-row_id. "
  IF sy-subrc EQ 0 AND <fs_wa> IS ASSIGNED.
    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZTK_ID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_ztk_id = <field>.
    ENDIF.

    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZSPZ_ID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_zspz_id = <field>.
    ENDIF.

    UNASSIGN <field>.
    ASSIGN COMPONENT 'ZRLID' OF STRUCTURE <fs_wa> TO <field>.
    IF <field> IS ASSIGNED.
      lv_zrlid = <field>.
    ENDIF.



    CASE ps_column_id-fieldname.
      WHEN 'ZTK_ID'.
        PERFORM frm_call_transaction_tk USING lv_ztk_id
                                              'ZRED0041C'.
      WHEN 'ZSPZ_ID'.

        PERFORM frm_call_transaction_spz USING lv_zspz_id
                                              ''.
      WHEN 'ZRLID'.
        PERFORM frm_call_transaction_tk USING lv_zrlid
                                              'ZRED0041C'.

      WHEN OTHERS.

    ENDCASE.


  ENDIF.

ENDFORM.