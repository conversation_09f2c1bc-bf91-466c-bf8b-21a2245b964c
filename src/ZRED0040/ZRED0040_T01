*&---------------------------------------------------------------------*
*& 包含               ZRED0035_T01
*&---------------------------------------------------------------------*

TYPE-POOLS:slis,vrm.



TYPES:
  BEGIN OF ty_data_scn.
    INCLUDE TYPE zres0040.
TYPES:
END OF ty_data_scn,
tt_data_scn TYPE TABLE OF ty_data_scn.

TYPES:
  BEGIN OF ty_data_scn_ctrl,
    zxybstyp  TYPE  zretc009-zxybstyp,
    ztktype   TYPE  zretc001-ztktype,
    tcodetype TYPE  char1,
    zfllx     TYPE zreta002-zfllx,
  END OF ty_data_scn_ctrl.




TYPES:
  BEGIN OF ty_tk,
    seq       TYPE i,
    zcjrq     TYPE zreta002-zcjrq,
    zcjsj     TYPE zreta002-zcjsj,
    zcjr      TYPE zreta002-zcjr,
    zfllx     TYPE zreta002-zfllx,
    ztktype   TYPE zreta002-ztktype,
    ztk_id    TYPE zreta002-ztk_id,
    ztk_txt   TYPE zreta002-ztk_txt,
    zspz_id   TYPE zreta002-zspz_id,
    zflspz_id TYPE zreta002-zflspz_id,
    zbegin    TYPE zreta002-zbegin,
    zend      TYPE zreta002-zend,
    ztmpid    TYPE zreta002-ztmpid,
    zxyzt     TYPE zreta002-zxyzt,
    zclrid    TYPE zreta002-zclrid,
    zleib     TYPE zreta002-zleib,
    zrlid     TYPE zreta003-zrlid,
    zhsjz     TYPE zretc005-zhsjz,
    zflxs     TYPE zretc005-zflxs,
    anbtr     TYPE anbtr,
    zsl       TYPE zret0008-zsl,
    icon_edit TYPE c LENGTH 20,
    icon_dis  TYPE c LENGTH 20,


  END OF ty_tk,
  tt_tk TYPE TABLE OF ty_tk.


TYPES:
  BEGIN OF ty_tk_call,
    zfllx    TYPE zreta002-zfllx,
    zxybstyp TYPE zretc009-zxybstyp,
    ztmpid   TYPE zretc001-ztmpid,
    zfjtk    TYPE char1,
    rb_nref  TYPE char1,
    rb_ref   TYPE char1,
    p_tk_idr TYPE zreta002-ztk_id,    "参考条款
    zcnyg    TYPE char1,
    p_tk_idy TYPE zreta002-ztk_id,    "次年预估参考条款

  END OF ty_tk_call.