*&---------------------------------------------------------------------*
*& 包含               ZRED0035_S01
*&---------------------------------------------------------------------*

TABLES:
  zreta001.

DATA:
  ok_code          TYPE sy-ucomm,                          "
  gv_code          TYPE sy-ucomm,                          "
  gv_zjt_fz_t      TYPE char10,                            "分子
  gv_zjt_fm_t      TYPE char10,                            "分母
  gv_zxybstyp      TYPE zretc009-zxybstyp,                 "协议类型
  gv_actvt         TYPE activ_auth,                        "权限actvt
  gv_flg_rb        TYPE char2,                             "单选按钮
  gv_flg_call      TYPE char2,                             "调用标识
  gv_cursor_line   TYPE i,                                 "光标行
  gv_index_line    TYPE i,                                 "内表行
  gv_flg_comm      TYPE char10,                            "command 标识
  gv_flg_spz_exist TYPE char1,                             "商品组新增标识
  gv_title         TYPE char20,                            "程序标题
  gv_title_01      TYPE char20,                            "主屏幕标题1
  gv_title_02      TYPE char20.                            "主屏幕标题2

CONSTANTS:
  cv_subscreen_dft  TYPE sy-dynnr VALUE '2800'.


DATA:
  gv_subscreen TYPE sy-dynnr,
  gv_flg_save  TYPE char1.


DATA:
  gt_vls_ztmpid_add_tk TYPE vrm_values,
  gt_vls_zxybstyp      TYPE vrm_values,
  gt_vls_ztmpid        TYPE vrm_values,
  gt_vls_zjsff         TYPE vrm_values,
  gt_vls_zhtlx         TYPE vrm_values,
  gt_vls_zfllx         TYPE vrm_values,
  gt_vls_zhszq         TYPE vrm_values,
  gt_vls_zjszq         TYPE vrm_values,
  gt_vls_zhsjz         TYPE vrm_values.

DATA:

  gt_ta01          TYPE tt_ta01,
  gs_ta01          TYPE LINE OF tt_ta01,
  gs_data_scn_ctrl TYPE  ty_data_scn_ctrl,
  gt_data_scn      TYPE tt_data_scn,
  gt_tk_js         TYPE tt_tk,
  gt_tk_gd         TYPE tt_tk,
  gt_tk_sq         TYPE tt_tk.

DATA:
      gs_tk_call TYPE ty_tk_call.





SELECTION-SCREEN BEGIN OF BLOCK b01 WITH FRAME TITLE TEXT-001.

PARAMETERS:

  p_zhtlx  TYPE zretc001-zhtlx AS LISTBOX  VISIBLE LENGTH 20  DEFAULT '' USER-COMMAND uc02 MODIF ID m01,
  p_zbukrs TYPE zretc001-zbukrs MODIF ID m01  MATCHCODE OBJECT c_t001.

PARAMETERS:
  p_ht_idr TYPE zreta001-zht_id MODIF ID m01 MATCHCODE OBJECT zresh0016 MEMORY ID zht_idr NO-DISPLAY.

PARAMETERS:
  p_zht_id TYPE zreta001-zht_id MODIF ID m02 MATCHCODE OBJECT zresh0016 MEMORY ID zht_id.



SELECTION-SCREEN END OF BLOCK b01.

SELECTION-SCREEN BEGIN OF BLOCK b02 WITH FRAME TITLE TEXT-002 .

PARAMETERS:
  rb_add  TYPE char1 RADIOBUTTON GROUP g1 USER-COMMAND uc01 MODIF ID m10,      "新增
  rb_edit TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,                        "修改
  rb_dis  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10.                        "显示


SELECTION-SCREEN END OF BLOCK b02.


SELECTION-SCREEN BEGIN OF BLOCK b03 WITH FRAME TITLE TEXT-002 .
PARAMETERS:
  cb_yg TYPE char1 AS CHECKBOX DEFAULT '' USER-COMMAND uc03 MODIF ID m01.

PARAMETERS:
  p_ht_idy TYPE zreta001-zht_id MODIF ID m01 MATCHCODE OBJECT zresh0016 MEMORY ID zht_idy .

SELECTION-SCREEN END OF BLOCK b03.


INITIALIZATION.


  PERFORM frm_screen_init.

  PERFORM frm_get_data_list_box        CHANGING   gt_vls_zfllx
                                                  gt_vls_zjsff
                                                  gt_vls_zhszq
                                                  gt_vls_zjszq
                                                  gt_vls_zhsjz
                                                  gt_vls_zhtlx.

  PERFORM frm_set_list_box             USING      'P_ZHTLX'
                                                  gt_vls_zhtlx.

AT SELECTION-SCREEN OUTPUT.

  PERFORM frm_set_screen.



AT SELECTION-SCREEN.

START-OF-SELECTION.

  PERFORM frm_main.