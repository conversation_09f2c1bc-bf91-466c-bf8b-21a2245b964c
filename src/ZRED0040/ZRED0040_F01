*&---------------------------------------------------------------------*
*& 包含               ZRED0035_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_SCREEN_INIT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_screen_init .
  CLEAR:
        rb_add,
        rb_edit,
        rb_dis.

*  根据事务码控制进入后的界面显示
  CASE sy-tcode.
    WHEN 'ZRED0040A'.
      rb_add = 'X'.
      gv_title = '返利平台：返利合同创建'.
    WHEN 'ZRED0040B'.
      rb_edit = 'X'.
      gv_title = '返利平台：返利合同修改'.
    WHEN 'ZRED0040C'.
      rb_dis = 'X'.
      gv_title = '返利平台：返利合同显示'.
    WHEN OTHERS.
      rb_add = 'X'.
      gv_title = '返利平台：返利合同创建'.
  ENDCASE.

  PERFORM frm_set_title  USING gv_title.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen .

*  M01--新增
*  M02--修改，显示，审批

  CASE 'X'.
    WHEN rb_add.
      LOOP AT SCREEN.
        IF screen-group1 = 'M02' .
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.

        IF screen-name CP '*P_HT_IDY*'.
          IF cb_yg = 'X'.
            screen-active = '1'.
          ELSE.
            CLEAR p_ht_idy.
            screen-active = '0'.
          ENDIF.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN rb_edit.
      LOOP AT SCREEN.
        IF screen-group1 = 'M01' .
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.
    WHEN rb_dis.
      LOOP AT SCREEN.
        IF screen-group1 = 'M01' .
          screen-active = '0'.
        ELSE.
          screen-active = '1'.
        ENDIF.
        MODIFY SCREEN.
      ENDLOOP.

    WHEN OTHERS.
  ENDCASE.

*  通过事务码进入后隐藏单选按钮
  IF sy-tcode NE 'SE38'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M10'.
        screen-active = '0'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_main .

  PERFORM frm_init_data.

  PERFORM frm_author_check.

  PERFORM frm_check_screen .

  CASE gv_flg_rb.
    WHEN '01'.

      IF p_ht_idr IS NOT INITIAL.
        PERFORM frm_get_data USING p_ht_idr
                             CHANGING
                                      gs_ta01
                                      gt_t09
                                      gt_t20
                                      gt_tk_js
                                      gt_tk_gd
                                      gt_tk_sq.

        PERFORM frm_pro_data_ref CHANGING gs_ta01.

      ENDIF.
      PERFORM frm_data_dft ..
    WHEN '02'.
      PERFORM frm_pro_data_lock               USING p_zht_id
                                                      ''.
      PERFORM frm_get_data USING p_zht_id
                             CHANGING
                                      gs_ta01
                                      gt_t09
                                      gt_t20
                                      gt_tk_js
                                      gt_tk_gd
                                      gt_tk_sq.

    WHEN '03'.
      PERFORM frm_get_data USING p_zht_id
                             CHANGING
                                      gs_ta01
                                      gt_t09
                                      gt_t20
                                      gt_tk_js
                                      gt_tk_gd
                                      gt_tk_sq.
    WHEN OTHERS.
  ENDCASE.

*  IF gv_flg_rb = '01' OR gv_flg_rb = '02'.
*    PERFORM frm_add_init_lines CHANGING gt_tc02.
*  ENDIF.

*  PERFORM frm_pro_data CHANGING
*                                gs_ta01
*                                gt_tk_js .


*  获取屏幕控制参数
  PERFORM frm_pro_data_scn_ctrl  CHANGING gs_data_scn_ctrl.
*  获取屏幕控制数据
  PERFORM frm_get_data_screen_attr  USING gs_data_scn_ctrl
                                    CHANGING gt_data_scn.

  PERFORM frm_get_data_list_box_02     .

*  权限检查
  DATA: lt_msglist    TYPE scp1_general_errors,
        lv_mtype_auth TYPE bapi_mtype,
        lv_msg_auth   TYPE scp1_general_error-msgv1.

  PERFORM frm_author_check_ht USING gs_ta01
                                    gv_actvt
                                    'A'
                              CHANGING lt_msglist
                                       lv_mtype_auth
                                       lv_msg_auth.


  PERFORM frm_call_screen.


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_CHECK_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_check_screen .

  CASE gv_flg_rb.
    WHEN '01'.

      IF p_zhtlx IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '合同类型必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.

      ENDIF.

      IF p_zbukrs IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '合同主体必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
      ELSE.
        SELECT SINGLE COUNT(*) FROM t001 WHERE bukrs = p_zbukrs.
        IF sy-subrc NE 0.
          MESSAGE s888(sabapdocu) WITH '合同主体不存在' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        ENDIF.
      ENDIF.

*      PERFORM frm_check_zflsqf USING p_zbukrs.

      IF p_ht_idr IS NOT INITIAL.
        PERFORM frm_check_zht_id USING p_ht_idr.
        IF sy-subrc NE 0.
          MESSAGE s888(sabapdocu) WITH '合同编码不存在' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        ENDIF.
      ENDIF.

      IF cb_yg = 'X' .
        IF p_ht_idy IS INITIAL.
          MESSAGE s888(sabapdocu) WITH '次年预估原参考合同必填' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING.
        ELSE.
          PERFORM frm_check_zht_id USING p_ht_idy.
          IF sy-subrc NE 0.
            MESSAGE s888(sabapdocu) WITH '合同编码不存在' DISPLAY LIKE 'E'.
            LEAVE LIST-PROCESSING.
          ENDIF.
        ENDIF.
      ENDIF.


    WHEN '02' OR '03'.

      IF p_zht_id IS INITIAL.

        MESSAGE s888(sabapdocu) WITH '合同编码必填' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.

      ENDIF.
      PERFORM frm_check_zht_id USING p_zht_id.
      IF sy-subrc NE 0.
        MESSAGE s888(sabapdocu) WITH '合同编码不存在' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
      ENDIF.

    WHEN OTHERS.
  ENDCASE.



ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHOR_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_author_check .

*****  DATA:lv_subrc TYPE c,
*****       lv_mess  TYPE bapiret2-message.
*****  DATA:
*****    lv_flg_err1 TYPE char1,
*****    ls_zreta001 TYPE zreta001.
*****
*****  CASE gv_flg_rb .
*****    WHEN '01'.
*****      ls_zreta001-zhtlx = p_zhtlx.
*****      ls_zreta001-zbukrs = p_zbukrs.
*****
*****    WHEN OTHERS.
*****      SELECT SINGLE * INTO @ls_zreta001 FROM zreta001 WHERE zht_id = @p_zht_id.
*****  ENDCASE.
*****
*****
*****  PERFORM frm_author_check_zhtlx USING ls_zreta001-zhtlx
*****                                       gv_actvt
*****                                 CHANGING lv_flg_err1.
*****  IF lv_flg_err1 = 'E'.
*****    MESSAGE s888(sabapdocu) WITH '您没有合同类型:' && ls_zreta001-zhtlx && ' 的操作权限 !' DISPLAY LIKE 'E'.
*****    LEAVE LIST-PROCESSING.
*****  ENDIF.
*****
*****
*****  PERFORM frm_author_check_zbukrs USING ls_zreta001-zbukrs
*****                                       gv_actvt
*****                                 CHANGING lv_flg_err1.
*****  IF lv_flg_err1 = 'E'.
*****    MESSAGE s888(sabapdocu) WITH '您没有合同主体:' && ls_zreta001-zbukrs && ' 的操作权限 !' DISPLAY LIKE 'E'.
*****    LEAVE LIST-PROCESSING.
*****  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_INIT_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_init_data .

  CLEAR gv_flg_rb.
  CLEAR gv_flg_save.
  CASE 'X'.
    WHEN rb_add .   gv_flg_rb = '01'.  gv_title_02 = '创建'. gv_actvt = '01'     .  gs_data_scn_ctrl-tcodetype = 'N'.
    WHEN rb_edit .  gv_flg_rb = '02'.  gv_title_02 = '修改'.  gv_actvt = '02'    .  gs_data_scn_ctrl-tcodetype = 'U'.
    WHEN rb_dis .   gv_flg_rb = '03'.  gv_title_02 = '显示'.  gv_actvt = '03'    .  gs_data_scn_ctrl-tcodetype = 'D'.
    WHEN OTHERS.
  ENDCASE.

  gv_title_01 = '返利合同'.
  gv_subscreen = cv_subscreen_dft.


  PERFORM frm_clear_data.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CALL_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_call_screen .
  CALL SCREEN 2000.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_DFT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_data_dft .

  gs_ta01-zhtlx = p_zhtlx.
  gs_ta01-zbukrs = p_zbukrs.
  gs_ta01-zhszq = '1M'.
  gs_ta01-zdffs = 'O'.
  gs_ta01-zhstype = 'A'.

  PERFORM frm_set_zjszq USING gs_ta01-zhstype gs_ta01-zhszq CHANGING gs_ta01-zjszq.

*  IF gs_ta01-zhstype = 'A'.
*    gs_ta01-zjszq = '12M'.
*  ELSEIF gs_ta01-zhstype = 'B'.
*    gs_ta01-zjszq = gs_ta01-zhszq.
*  ENDIF.

  gs_ta01-zhtyear = sy-datum+0(4).
  gs_ta01-zbegin  = gs_ta01-zhtyear && '0101'.
  gs_ta01-zend    = gs_ta01-zhtyear && '1231'.
  IF cb_yg = 'X'.
    gs_ta01-zcnygsg   = 'X'.
    gs_ta01-zcnyght_id   = p_ht_idy.
  ENDIF.

  DATA: ls_z630 TYPE zmmt0630.
  IMPORT ls_z630 FROM MEMORY ID 'ZRED0065_CREATE'.
  CHECK ls_z630-zhtbh IS NOT INITIAL.
  "合同类型
  "合同主体
  "合同描述
  gs_ta01-zht_txt = ls_z630-zhtms.
  "采购组
  gs_ta01-ekgrp = ls_z630-ekgrp.
  "签署年度
  gs_ta01-zhtyear = gs_ta01-zbegin+0(4).
  "开始日期
  gs_ta01-zbegin = ls_z630-zhtdatb.
  "结束日期
  gs_ta01-zend = ls_z630-zhtdate.
  "关联合同号
  gs_ta01-zhtid = ls_z630-zhtbh.
  "联系人
  gs_ta01-zlxr = ls_z630-zzqyflxr.
  "联系方式
  gs_ta01-zlxfs = ls_z630-zzqyftel.
  "主签约方类型   ->  伙伴类型
  gs_ta01-zbptype = ls_z630-zzqyflx.
  "主签约方编码   ->  伙伴代码
  gs_ta01-zbpcode = ls_z630-zzqyfbm.
  "返利支付方式   ->  兑付方式
  gs_ta01-zdffs = ls_z630-zflzffs.
  "返利支付方    ->  支付方
  gs_ta01-zflzff = ls_z630-zflzff.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_2100
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_2000 .
  PERFORM frm_set_screen_pub.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_PUB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_pub .


  LOOP AT SCREEN.
    IF screen-group1 = '101' OR screen-group1 = '102' .

      PERFORM frm_set_screen_attr USING gt_data_scn
                                  CHANGING screen.
      MODIFY SCREEN.
    ENDIF.
  ENDLOOP.


  IF gv_flg_rb = '03'.
*    OR gv_flg_comm = 'B_SPZ_DTL'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' OR screen-group1 = '102'.
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

*  IF gv_flg_comm = 'SAVE'.

  IF gv_flg_save = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_2200
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_2200 .
  PERFORM frm_set_screen_pub.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_TC_ITEM_STYLE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_style_tc_item .

*  DATA: ls_cols TYPE cx_tableview_column.
*
*  LOOP AT tc_item-cols INTO ls_cols.
*    IF gv_flg_rb = '01'.
*      IF ls_cols-screen-name = 'GS_TC02-ZSTATUS'.
*        ls_cols-invisible = 1.
*        MODIFY tc_item-cols FROM ls_cols.
*      ENDIF.
*    ENDIF.
*  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CLEAR_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_clear_data .

  CLEAR:
        gs_ta01.


ENDFORM.

FORM frm_get_data_list_box_02 .

  SELECT
    DISTINCT
    a~ztmpid  AS  key ,
    a~ztmptxt      AS  text
    FROM zretc001 AS a JOIN zretc002 AS b
                         ON a~ztmpid = b~ztmpid
                        AND b~zstatus NE 'D'
    WHERE ( a~zbukrs = @gs_ta01-zbukrs OR a~zbukrs = '' )
    AND   a~zhtlx = @gs_ta01-zhtlx
    AND   a~ztktype = ''
    AND   a~ztmpty = 'S'
    INTO CORRESPONDING FIELDS OF TABLE @gt_vls_ztmpid.
ENDFORM.

FORM frm_get_data_list_box_03 .

  DATA: lv_where TYPE char50.


  CLEAR gt_vls_ztmpid_add_tk.

  IF gs_tk_call-zfjtk = 'X'.
    lv_where = | ZTKTYPE = 'P' |.
  ELSE.
    lv_where = | ZTKTYPE = '' |.
  ENDIF.

  SELECT
    ztmpid  AS  key ,
    ztmptxt      AS  text
    FROM zretc001
    WHERE ( zbukrs = @gs_ta01-zbukrs OR zbukrs = '' )
    AND   zhtlx = @gs_ta01-zhtlx
*    AND   ztktype = ''
    AND   (lv_where)
    AND   ztmpty = 'S'
    INTO CORRESPONDING FIELDS OF TABLE @gt_vls_ztmpid_add_tk.


  SELECT
    a~zxybstyp  AS  key ,
    b~ddtext      AS  text
    FROM zretc009 AS a JOIN dd07t AS b
                         ON a~zxybstyp = b~domvalue_l
                        AND b~domname = 'ZREM_ZXYBSTYP'
                        AND b~ddlanguage = @sy-langu
    WHERE a~zfllx = @gs_tk_call-zfllx
    INTO CORRESPONDING FIELDS OF TABLE @gt_vls_zxybstyp.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9001
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9001 .
  PERFORM frm_set_screen_pub.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_9002
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_9002 .
  PERFORM frm_set_screen_pub.
ENDFORM.
FORM frm_set_screen_9003 .
  PERFORM frm_set_screen_pub.
ENDFORM.

FORM frm_set_screen_9004 .
  PERFORM frm_set_screen_pub.
ENDFORM.

FORM frm_set_screen_9005 .
  PERFORM frm_set_screen_pub.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ALV_DISPLAY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_alv_init  USING  pv_flg TYPE char2  .
  IF pv_flg = 'JS'.
    IF grf_container_js IS INITIAL.
      CREATE OBJECT grf_container_js
        EXPORTING
          container_name = gv_container_name_js.
      IF grf_alv_js IS INITIAL.
        CREATE OBJECT grf_alv_js
          EXPORTING
            i_parent = grf_container_js.
        PERFORM frm_alv_display USING pv_flg grf_alv_js .
      ELSE.
        PERFORM frm_refresh_alv USING grf_alv_js.
      ENDIF.
    ELSE.
      PERFORM frm_refresh_alv USING grf_alv_js.
    ENDIF.
  ELSEIF pv_flg = 'GD'.
    IF grf_container_gd IS INITIAL.
      CREATE OBJECT grf_container_gd
        EXPORTING
          container_name = gv_container_name_gd.
      IF grf_alv_gd IS INITIAL.
        CREATE OBJECT grf_alv_gd
          EXPORTING
            i_parent = grf_container_gd.
        PERFORM frm_alv_display USING pv_flg grf_alv_gd .
      ELSE.
        PERFORM frm_refresh_alv USING grf_alv_gd.
      ENDIF.
    ELSE.
      PERFORM frm_refresh_alv USING grf_alv_gd.
    ENDIF.
  ELSEIF pv_flg = 'SQ'.
    IF grf_container_sq IS INITIAL.
      CREATE OBJECT grf_container_sq
        EXPORTING
          container_name = gv_container_name_sq.
      IF grf_alv_sq IS INITIAL.
        CREATE OBJECT grf_alv_sq
          EXPORTING
            i_parent = grf_container_sq.
        PERFORM frm_alv_display USING pv_flg grf_alv_sq .
      ELSE.
        PERFORM frm_refresh_alv USING grf_alv_sq.
      ENDIF.
    ELSE.
      PERFORM frm_refresh_alv USING grf_alv_sq.
    ENDIF.
  ENDIF.

ENDFORM.