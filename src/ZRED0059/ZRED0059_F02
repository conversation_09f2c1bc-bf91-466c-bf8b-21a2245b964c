*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_F02
*&---------------------------------------------------------------------*

FORM frm_pro_data_end       USING ps_globe    TYPE ty_globe
                         CHANGING pt_data     TYPE tt_data
                                  pt_data_dtl TYPE tt_data.



  DATA:lv_zflzff TYPE zret0044-zflzff.
  DATA(lt_data) = pt_data[].
  SORT lt_data BY zxy_id.
  DELETE ADJACENT DUPLICATES FROM lt_data COMPARING zxy_id.


  SELECT
    a~zht_id,
    a~zht_txt,
    a~zhtyear,
    b~ztk_id,
    b~ztk_txt,
*    b~zhsjz,
    c~zxy_id,
    c~zxy_txt,
    c~zbuk<PERSON>,
    c~zetsplr,
    c~zbegin,
    c~zend,
    c~frgsx,
    c~kolnr,
    c~zctgr
    FROM zret0006 AS c JOIN @lt_data AS i
                         ON c~zxy_id = i~zxy_id
                       JOIN zreta002 AS b
                         ON c~ztk_id = b~ztk_id
                       JOIN zreta001 AS a
                         ON b~zht_id = a~zht_id
    INTO  TABLE @DATA(lt_t06).

  SELECT
    lifnr,
    name1
    FROM lfa1
    INTO TABLE @gt_lfa1.

  SELECT
    bukrs,
    butxt
    FROM t001
    INTO TABLE @gt_t001.

  SELECT
    *
    FROM zretc007
    INTO TABLE @DATA(lt_zretc007).



  SORT lt_t06 BY zxy_id.
  SORT gt_t001 BY bukrs.
  SORT gt_lfa1 BY lifnr.
  SORT lt_zretc007 BY frgsx  kolnr .
  LOOP AT pt_data INTO DATA(ls_data).
    READ TABLE lt_t06 INTO DATA(ls_t06) WITH KEY zxy_id = ls_data-zxy_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zxy_txt = ls_t06-zxy_txt.
      ls_data-zbukrs  = ls_t06-zbukrs.
      ls_data-ztk_id  = ls_t06-ztk_id.
      ls_data-zbegin  = ls_t06-zbegin.
      ls_data-zend    = ls_t06-zend.
      ls_data-zht_id  = ls_t06-zht_id.
      ls_data-zht_txt = ls_t06-zht_txt.
      ls_data-zhtyear = ls_t06-zhtyear.
      ls_data-frgsx   = ls_t06-frgsx.
      ls_data-zctgr   = ls_t06-zctgr.
      ls_data-ztk_txt = ls_t06-ztk_txt.
    ENDIF.

    READ TABLE gt_t001 INTO DATA(ls_t001) WITH KEY bukrs = ls_data-zbukrs BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-butxt = ls_t001-butxt.
    ENDIF.

    READ TABLE gt_lfa1 INTO DATA(ls_lfa1) WITH KEY lifnr = ls_data-zghf BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-name1 = ls_lfa1-name1.
    ENDIF.

    READ TABLE gt_lfa1 INTO ls_lfa1 WITH KEY lifnr = ls_data-lifnr BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-lifnrt = ls_lfa1-name1.
    ENDIF.


    lv_zflzff =  |{ ls_data-zflzff ALPHA = OUT }|.
    IF strlen( lv_zflzff ) = 4 . .
      READ TABLE gt_t001 INTO ls_t001 WITH KEY bukrs = lv_zflzff BINARY SEARCH.
      IF sy-subrc EQ 0.
        ls_data-zflzfft = ls_t001-butxt.
      ENDIF.
    ELSE.
      READ TABLE gt_lfa1 INTO ls_lfa1 WITH KEY lifnr = ls_data-zflzff BINARY SEARCH.
      IF sy-subrc EQ 0.
        ls_data-zflzfft = ls_lfa1-name1.
      ENDIF.
    ENDIF.

    READ TABLE lt_zretc007 INTO DATA(ls_zretc007) WITH KEY frgsx = ls_data-frgsx  kolnr = ls_data-kolnr BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-frgc1 = ls_zretc007-frgc1.
      ls_data-zfrgtx = ls_zretc007-zfrgtx.
    ENDIF.



    IF ls_data-zxyzt = 'A'.
      ls_data-status = '3'.
    ELSEIF ls_data-zmtype = 'E' .
      ls_data-status = '1'.
    ELSE.
      ls_data-status = '2'.
    ENDIF.

    MODIFY pt_data FROM ls_data.
  ENDLOOP.


  SELECT
    a~matnr,
    a~maktx
    FROM @pt_data_dtl AS i JOIN makt AS a
                             ON i~matnr = a~matnr
    INTO TABLE @DATA(lt_mara).


  SORT lt_mara BY matnr.
  LOOP AT pt_data_dtl INTO DATA(ls_data_dtl).
    READ TABLE lt_mara INTO DATA(ls_mara) WITH  KEY matnr = ls_data_dtl-matnr BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data_dtl-maktx = ls_mara-maktx.
    ENDIF.
    READ TABLE gt_lfa1 INTO ls_lfa1 WITH KEY lifnr = ls_data_dtl-zghf BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data_dtl-name1 = ls_lfa1-name1.
    ENDIF.
    MODIFY pt_data_dtl FROM ls_data_dtl.
  ENDLOOP.

  PERFORM frm_set_seq(zbcs0001) TABLES pt_data USING 'SEQ'.

ENDFORM.






*&---------------------------------------------------------------------*
*&      FORM  FRM_ALV_INIT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_alv_init .

  IF grf_container IS INITIAL.

    CREATE OBJECT grf_container
      EXPORTING
        side                        = cl_gui_docking_container=>dock_at_bottom
        extension                   = 2500
      EXCEPTIONS
        cntl_error                  = 1
        cntl_system_error           = 2
        create_error                = 3
        lifetime_error              = 4
        lifetime_dynpro_dynpro_link = 5
        OTHERS                      = 6.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
    ENDIF.

    IF grf_alv IS INITIAL.
      CREATE OBJECT grf_alv
        EXPORTING
          i_parent          = grf_container
        EXCEPTIONS
          error_cntl_create = 1
          error_cntl_init   = 2
          error_cntl_link   = 3
          error_dp_create   = 4
          OTHERS            = 5.
      IF sy-subrc <> 0.
        MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
      ENDIF.

      PERFORM frm_alv_display USING grf_alv gs_globe .
    ELSE.
      PERFORM frm_refresh_alv USING grf_alv.
    ENDIF.
  ELSE.
    PERFORM frm_refresh_alv USING grf_alv.
  ENDIF.

ENDFORM.

FORM frm_refresh_alv USING prf_alv TYPE REF TO cl_gui_alv_grid.
  DATA:
    ls_stable TYPE lvc_s_stbl,
    ls_layout TYPE lvc_s_layo.

  ls_stable-row = ls_stable-col = 'X'.

  PERFORM frm_set_layout CHANGING ls_layout.

  IF prf_alv IS NOT INITIAL.

    CALL METHOD prf_alv->set_frontend_layout
      EXPORTING
        is_layout = ls_layout.

    CALL METHOD prf_alv->refresh_table_display
      EXPORTING
        is_stable = ls_stable
*       i_soft_refresh = 'X'
      EXCEPTIONS
        finished  = 1
        OTHERS    = 2.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                 WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
    ENDIF.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  FRM_DISPLAY
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_GO_ALV  TEXT
*----------------------------------------------------------------------*
FORM frm_alv_display  USING  prf_alv TYPE REF TO cl_gui_alv_grid
                             ps_globe TYPE ty_globe.

  DATA: lt_fieldcat TYPE TABLE OF lvc_s_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_layout   TYPE TABLE OF lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.


  PERFORM frm_set_catalog USING ps_globe CHANGING lt_fieldcat.

  PERFORM frm_set_layout  CHANGING ls_layout.

*  PERFORM frm_set_ex_fcode TABLES lt_ex_fcode.

*  PERFORM frm_set_variant CHANGING  ls_variant.


  PERFORM frm_set_alv USING
                            'GT_DATA'
                            ls_variant
                            ls_layout
                            lt_ex_fcode
                      CHANGING
                            lt_fieldcat
                            prf_alv.


  PERFORM frm_set_event_handler USING 'MAIN'   CHANGING   prf_alv.


ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LS_VARIANT  TEXT
*      -->P_LS_LAYOUT  TEXT
*      -->P_LT_EX_FCODE  TEXT
*      <--P_LT_FIELDCAT  TEXT
*      <--P_'GT_DATA_T'  TEXT
*----------------------------------------------------------------------*
FORM frm_set_alv  USING    pv_tname TYPE any
                           ps_variant TYPE disvariant
                           ps_layout TYPE lvc_s_layo
                           pt_ex_fcode TYPE ui_functions
                  CHANGING pt_fieldcat TYPE lvc_t_fcat
                           prf_alv_grid TYPE REF TO cl_gui_alv_grid.


  DATA lv_table  LIKE feld-name.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.

  CONCATENATE pv_tname '[]' INTO lv_table.
  ASSIGN (lv_table) TO <lt_table>.

  CALL METHOD prf_alv_grid->set_table_for_first_display
    EXPORTING
*     I_BUFFER_ACTIVE               =
*     I_BYPASSING_BUFFER            =
*     I_CONSISTENCY_CHECK           =
*     I_STRUCTURE_NAME              = 'IT_ITAB'
      is_variant                    = ps_variant
      i_save                        = 'A'
*     I_DEFAULT                     = 'X'
      is_layout                     = ps_layout
*     IS_PRINT                      =
*     IT_SPECIAL_GROUPS             =
      it_toolbar_excluding          = pt_ex_fcode
*     IT_HYPERLINK                  =
*     IT_ALV_GRAPHICS               =
*     IT_EXCEPT_QINFO               =
*     IR_SALV_ADAPTER               =
    CHANGING
      it_outtab                     = <lt_table>
      it_fieldcatalog               = pt_fieldcat
*     IT_SORT                       =
*     IT_FILTER                     =
    EXCEPTIONS
      invalid_parameter_combination = 1
      program_error                 = 2
      too_many_lines                = 3
      OTHERS                        = 4.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
               WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.

FORM frm_set_catalog  USING      ps_globe TYPE ty_globe
                      CHANGING   pt_fieldcat TYPE lvc_t_fcat.

  DATA: lt_fieldcat_tmp TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.
  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
     ls_fieldcat-edit      = &1.
     ls_fieldcat-outputlen = &2.
     ls_fieldcat-fieldname = &3.
     ls_fieldcat-coltext   = &4. .
     ls_fieldcat-scrtext_l = &4. .
     ls_fieldcat-scrtext_m = &4. .
     ls_fieldcat-scrtext_s = &4. .

    CASE &3.
      WHEN 'BUKRS' .
      ls_fieldcat-ref_table = 'T001'.
      ls_fieldcat-ref_field = 'BUKRS'.
      WHEN 'ZXYZT' .
      ls_fieldcat-ref_table = 'ZRET0006'.
      ls_fieldcat-ref_field = 'ZXYZT'.
      WHEN 'WERKS'  .
      ls_fieldcat-f4availabl = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN 'ZPAYTP'.
      ls_fieldcat-ref_table = 'ZRET0044'.
      ls_fieldcat-ref_field = 'ZPAYTP'.
      WHEN 'SEL_MAN'.
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.

  IF ps_globe-zflg_rb = '01' OR ps_globe-zflg_rb = '02' OR   ps_globe-zflg_rb = '03'.
    add_fcat ''  '10' 'STATUS'    '指示灯'  .
    add_fcat ''  '10' 'ZMSG'      '指示灯'  .
  ENDIF.
  add_fcat ''  '10' 'ZGHFTJSQ'   '供货方添加申请号'  .
  add_fcat ''  '10' 'ZTK_ID'     '条款号'  .
  add_fcat ''  '10' 'ZTK_TXT'    '条款描述'  .
  add_fcat ''  '10' 'ZXY_ID'     '协议号'  .
  add_fcat ''  '10' 'ZXY_TXT'    '协议描述'  .
  add_fcat ''  '10' 'ZBUKRS'     '协议主体'  .
  add_fcat ''  '10' 'BUTXT'      '协议主体名称'  .
  add_fcat ''  '10' 'ZGHF'       '供货方'  .
  add_fcat ''  '20' 'NAME1'      '供货方描述'  .
  add_fcat ''  '10' 'LIFNR'      '供应商'  .
  add_fcat ''  '20' 'LIFNRT'     '供应商描述'  .

  IF ps_globe-zflg_rb = '01'  .
    add_fcat 'X'  '10' 'ZPAYTP'     '付款方级别'  .
    add_fcat 'X'  '10' 'ZFLZFF'     '付款方'  .
  ELSE.
    add_fcat ''  '10' 'ZPAYTP'     '付款方级别'  .
    add_fcat ''  '10' 'ZFLZFF'     '付款方'  .
  ENDIF.

  add_fcat ''  '10' 'ZFLZFFT'      '返利支付方描述'  .
  add_fcat ''  '10' 'ZXYZT'      '申请状态'  .
  add_fcat ''  '10' 'ZHTYEAR'    '签署年度'  .
  add_fcat ''  '10' 'ZHT_ID'     '合同号'  .
  add_fcat ''  '10' 'ZHT_TXT'    '合同描述'  .
  add_fcat ''  '10' 'ZBEGIN'     '开始日期'  .
  add_fcat ''  '10' 'ZEND'       '结束日期'  .
  add_fcat ''  '10' 'ZCTGR'      '协议组织级别'  .



*  PERFORM frm_get_edit_fieldname CHANGING lt_fieldcat_tmp.
*
*  LOOP AT pt_fieldcat INTO ls_fieldcat.
*    READ TABLE lt_fieldcat_tmp TRANSPORTING NO FIELDS WITH KEY fieldname = ls_fieldcat-fieldname BINARY SEARCH.
*    IF sy-subrc EQ 0.
*      ls_fieldcat-edit = 'X'.
*    ENDIF.
*
*    MODIFY pt_fieldcat FROM ls_fieldcat.
*  ENDLOOP.

ENDFORM.                    " FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout  CHANGING ps_layout TYPE lvc_s_layo.
  ps_layout-sel_mode = 'D'.
  ps_layout-zebra     = 'X'.
  ps_layout-cwidth_opt  = 'X'.
*  ps_layout-ctab_fname = 'CELLCOLOR'.
  ps_layout-excp_fname = 'STATUS'.
*  ps_layout-stylefname  = 'FIELD_STYLE'.

ENDFORM.                    "FRM_SET_LAYOUT




FORM frm_set_event_handler USING pv_objid TYPE char10
                            CHANGING prf_grid  TYPE REF TO cl_gui_alv_grid.


  DATA:
    prf_event TYPE REF TO sec_lcl_event_receiver,
    lt_f4     TYPE lvc_t_f4.

  CREATE OBJECT prf_event EXPORTING i_objid = pv_objid.

  lt_f4 = VALUE #( ( fieldname  = 'WERKS' register   = 'X' getbefore  = 'X' chngeafter = 'X' ) ).
  prf_grid->register_f4_for_fields( EXPORTING it_f4 = lt_f4[] ).


  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_enter.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_modified.


  SET HANDLER prf_event->sec_handle_onf4                     FOR prf_grid.
  SET HANDLER prf_event->sec_handle_bef_user_command         FOR prf_grid.
  SET HANDLER prf_event->sec_handle_user_command             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_toolbar                  FOR prf_grid.
  SET HANDLER prf_event->sec_handle_hotspot_click            FOR prf_grid.
  SET HANDLER prf_event->sec_handle_double_click             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed_fin         FOR prf_grid.


  CALL METHOD prf_grid->set_toolbar_interactive.

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_EXCLUDE TEXT
*----------------------------------------------------------------------*
FORM frm_set_ex_fcode TABLES pt_exclude.
  DATA ls_exclude TYPE ui_func.
  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_insert_row .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_delete_row .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_refresh.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_detail.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_paste.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_undo.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_cut.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_copy_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_append_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_loc_move_row.
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_print_prev .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_graph .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_check .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_mb_view .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_help .
  APPEND ls_exclude TO pt_exclude.

  ls_exclude = cl_gui_alv_grid=>mc_fc_info .
  APPEND ls_exclude TO pt_exclude.

ENDFORM.                    "FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant CHANGING ps_variant TYPE disvariant.
  ps_variant-report = sy-repid.
  ps_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*&      FORM  FRM_CHECK_CHANGED_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_check_changed_data USING prf_alv TYPE REF TO cl_gui_alv_grid.
  IF prf_alv IS NOT INITIAL.
    CALL METHOD prf_alv->check_changed_data.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MAIN_CHECK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_DATA
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_main_data_check  USING    pt_data  TYPE tt_data
                                   ps_globe TYPE ty_globe
                          CHANGING pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.
  DATA:
    lt_msglist TYPE scp1_general_errors,
    ls_msglist TYPE scp1_general_error.


  PERFORM frm_check_sel USING pt_data CHANGING pv_mtype pv_msg.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.

  IF ps_globe-zflg_rb = '03' .
    PERFORM frm_check_imp_error USING pt_data CHANGING pv_mtype pv_msg.
    IF pv_mtype = 'E'.
      RETURN.
    ENDIF.
  ENDIF.


  PERFORM frm_check_data_pub  USING    pt_data
                              CHANGING lt_msglist  .

  IF lt_msglist[] IS NOT INITIAL.
    PERFORM frm_conver_msglist_2_msg USING lt_msglist CHANGING pt_data.


    PERFORM frm_pro_msglist USING lt_msglist
                            CHANGING pv_mtype
                                     pv_msg.
    RETURN.
  ENDIF.




ENDFORM.

FORM frm_check_sel         USING   pt_data  TYPE tt_data
                          CHANGING pv_mtype TYPE bapi_mtype
                                   pv_msg TYPE bapi_msg.
  CLEAR: pv_mtype,pv_msg.
  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel_man = 'X'.
  IF sy-subrc NE 0.
    pv_mtype = 'E'.
    pv_msg = '请选择需要处理的行！'.
    EXIT.
  ELSE.
    pv_mtype = 'S'.
  ENDIF.

ENDFORM.
FORM frm_check_imp_error         USING  pt_data  TYPE tt_data
                               CHANGING pv_mtype TYPE bapi_mtype
                                        pv_msg   TYPE bapi_msg.

  CLEAR: pv_mtype,pv_msg.
  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' zmtype = 'E' .
  IF sy-subrc = 0.
    pv_mtype = 'E'.
    pv_msg = '选择的行数据检查未通过，请修正！'.
    EXIT.
  ELSE.
    pv_mtype = 'S'.
  ENDIF.

ENDFORM.
FORM frm_check_data_pub  USING    pt_data  TYPE tt_data
                          CHANGING lt_msglist TYPE scp1_general_errors.

  DATA:
    lv_msgv1     TYPE scp1_general_error-msgv1.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel_man = 'X'.
    SELECT SINGLE zghftjsq FROM  zret0066 WHERE zxy_id = @ls_data-zxy_id AND zghf = @ls_data-zghf AND zxyzt IN ('N','P') INTO @DATA(lv_zghftjsq).
    IF sy-subrc EQ 0.
      CLEAR lv_msgv1. lv_msgv1 = '协议：' &&  ls_data-zxy_id &&  '供货方：'&& ls_data-zghf && '已经存在申请：' && lv_zghftjsq.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msgv1  CHANGING lt_msglist.
    ENDIF.
  ENDLOOP.


ENDFORM.

FORM frm_pro_msglist  USING    lt_msglist TYPE scp1_general_errors
                          CHANGING pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.
  IF lt_msglist[] IS NOT INITIAL.

    SORT lt_msglist.
    DELETE ADJACENT DUPLICATES FROM lt_msglist.

    CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
      EXPORTING
        title_text    = '消息提示'
        sort_by_level = ' '
        show_ids      = ''
        message_list  = lt_msglist[].

    pv_mtype = 'E'.
    pv_msg = '数据检查未通过，操作已终止!'.
  ELSE.
    IF pv_mtype NE 'E'..
      pv_mtype = 'S'.
      pv_msg = '数据检查通过'.
    ENDIF.
  ENDIF.
ENDFORM.

FORM frm_conver_msglist_2_msg  USING    pt_msglist   TYPE scp1_general_errors
                               CHANGING pt_data  TYPE tt_data.

  DATA:
    lv_msg  TYPE char255,
    ls_data TYPE LINE OF tt_data.

  DATA(lt_msglist) = pt_msglist[].
  SORT lt_msglist BY msgv1.

  LOOP AT lt_msglist INTO DATA(ls_msglist).
    AT NEW msgv1.
      DATA(lv_flg_new) = 'X'.
    ENDAT.
    AT END OF msgv1.
      DATA(lv_flg_end) = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_msg.
    ENDIF.
    lv_msg = lv_msg && '/' && ls_msglist-msgv1.

    IF lv_flg_end = 'X'.
      CLEAR ls_data.
      ls_data-zmtype = 'E'.
      ls_data-zmsg = lv_msg.
      PERFORM frm_set_status USING ls_data-zmtype CHANGING ls_data-status.
      MODIFY pt_data FROM ls_data TRANSPORTING zmtype zmsg status WHERE seq = ls_msglist-msgid.
    ENDIF.
    CLEAR:lv_flg_new,lv_flg_end.
  ENDLOOP.



ENDFORM.

FORM frm_main_data_save  USING    pt_data  TYPE tt_data
                                  ps_globe TYPE ty_globe
                          CHANGING
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.



*  检查
  PERFORM frm_main_data_check USING pt_data
                                    ps_globe
                            CHANGING pv_mtype
                                     pv_msg.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.

*  保存
  PERFORM frm_data_exe_save       CHANGING pt_data
                                     pv_mtype
                                     pv_msg.



ENDFORM.

FORM frm_data_exe_save  USING    pt_data  TYPE tt_data
                          CHANGING
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.




*  保存前处理
  PERFORM frm_data_pro_bf_save CHANGING pt_data.

*  数据保存
  PERFORM frm_data_save_into_db USING pt_data
                            CHANGING pv_mtype
                                     pv_msg.


ENDFORM.

FORM frm_data_pro_bf_save  CHANGING pt_data TYPE tt_data.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel_man = 'X'.

    PERFORM frm_get_num(zbcs0001) USING 'ZRE0013' '01' CHANGING ls_data-zghftjsq.
    ls_data-zxyzt = 'N'.
*    IF ls_t30-zardid IS INITIAL.
*
*    ENDIF.
*
*    IF pv_ucomm = 'SAVE'.
*      ls_t30-zarstatus = 'N'.
*    ELSEIF pv_ucomm = 'VOID'.
*      ls_t30-zarstatus = 'D'..
*    ENDIF.
    ls_data-kolnr = '1'.
    ls_data-zcjrq = sy-datum.
    ls_data-zcjsj = sy-uzeit.
    ls_data-zcjr = sy-uname.
    ls_data-zmtype = 'S'.
    ls_data-zmsg = '申请保存成功'.

    PERFORM frm_set_status USING ls_data-zmtype CHANGING ls_data-status.

    MODIFY pt_data FROM ls_data.
  ENDLOOP.

ENDFORM.

FORM frm_data_save_into_db  USING    pt_data TYPE tt_data
                       CHANGING   pv_mtype  TYPE bapi_mtype
                                  pv_msg  TYPE bapi_msg.

  DATA:
    ls_zret0066 TYPE zret0066,
    lt_zret0066 TYPE TABLE OF zret0066.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel_man = 'X'.
    CLEAR ls_zret0066.
    MOVE-CORRESPONDING ls_data TO ls_zret0066.
    APPEND ls_zret0066 TO lt_zret0066.
  ENDLOOP.


  MODIFY zret0066 FROM TABLE lt_zret0066.
  IF sy-subrc EQ 0.
    COMMIT WORK AND WAIT.
    pv_mtype = 'S'.
    pv_msg = '保存成功！'.
  ELSE.
    ROLLBACK WORK .
    pv_mtype = 'E'.
    pv_msg = '保存失败！'.
  ENDIF.


ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_GET_WERKS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_DATA_ZXY_ID
*&      <-- LT_DATA_WERKS
*&---------------------------------------------------------------------*
FORM frm_get_zghf  CHANGING    pt_data_zxy_id TYPE tt_data
                     pt_data_zghf TYPE tt_data.

  DATA:
    pt_data_zghf_01 TYPE tt_data,
    pt_data_zghf_02 TYPE tt_data.

  SELECT
    i~zxy_id,
    d~zghf
    FROM @pt_data_zxy_id AS i JOIN zret0006 AS c
                                ON i~zxy_id = c~zxy_id
                       JOIN zret0013 AS d
                         ON c~zxy_id = d~zxy_id
    WHERE c~zetsplr = ''
    AND   d~zzzpc = ''
    INTO CORRESPONDING FIELDS OF TABLE @pt_data_zghf_01.

  SELECT
    i~zxy_id,
    d~zghf
    FROM @pt_data_zxy_id AS i JOIN zret0006 AS c
                                ON i~zxy_id = c~zxy_id
                       JOIN zret0013 AS d
                         ON c~ztk_id = d~zxy_id
    WHERE c~zetsplr = 'X'
    AND   d~zzzpc = ''
    INTO CORRESPONDING FIELDS OF TABLE @pt_data_zghf_02.

  APPEND LINES OF pt_data_zghf_01 TO pt_data_zghf.
  APPEND LINES OF pt_data_zghf_02 TO pt_data_zghf.



  SORT pt_data_zghf BY zxy_id zghf.

  LOOP AT pt_data_zxy_id INTO DATA(ls_data_zxy_id).
    READ TABLE pt_data_zghf TRANSPORTING NO FIELDS WITH KEY zxy_id = ls_data_zxy_id-zxy_id zghf = 'ALL' BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE pt_data_zxy_id.
      CONTINUE.
    ENDIF.
  ENDLOOP.

ENDFORM.

FORM frm_get_werks  USING    pt_data_zxy_id TYPE tt_data
                    CHANGING pt_data_werks TYPE tt_data.

  DATA:
        ls_data_werks TYPE LINE OF tt_data.

  SELECT
    a~*
    FROM @pt_data_zxy_id AS i JOIN zret0014 AS a
                                ON i~zxy_id = a~zxy_id
    WHERE a~zzzlx IN ('A','S')
    INTO TABLE @DATA(lt_zret0014).

  SELECT
    i~zxy_id,
    a~werks AS werks
*    FROM @lt_zret0014 AS i JOIN t001k AS a "ERP-17212  未上线批发加盟店  zrev010_wrk
    FROM @lt_zret0014 AS i JOIN zrev010_wrk_ddl AS a
                             ON i~zzzid = a~bukrs
    WHERE i~zzzlx = 'A'
    AND   i~zzzpc = ''
    INTO TABLE @DATA(lt_werks_bukrs).

  SELECT
    i~zxy_id,
    a~werks AS werks
*    FROM @lt_zret0014 AS i JOIN t001k AS a "ERP-17212  未上线批发加盟店  zrev010_wrk
    FROM @lt_zret0014 AS i JOIN zrev010_wrk_ddl AS a
                             ON i~zzzid = a~bukrs
    WHERE i~zzzlx = 'A'
    AND   i~zzzpc = 'X'
    INTO TABLE @DATA(lt_werks_bukrs_x).

  DATA(lt_werks_14) = lt_zret0014[].
  DELETE lt_werks_14 WHERE zzzlx NE 'S' OR zzzpc = 'X' .

  DATA(lt_werks_14_x) = lt_zret0014[].
  DELETE lt_werks_14_x WHERE zzzlx NE 'S' OR zzzpc = '' .


  SORT lt_werks_bukrs_x  BY zxy_id werks.
  SORT lt_werks_bukrs  BY zxy_id werks.
  SORT lt_werks_14  BY zxy_id zzzid.
  SORT lt_werks_14_x  BY zxy_id zzzid.
  LOOP AT lt_werks_bukrs INTO DATA(ls_werks_bukrs).
    READ TABLE lt_werks_bukrs_x TRANSPORTING NO FIELDS WITH KEY werks = ls_werks_bukrs-werks BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE lt_werks_bukrs.
      CONTINUE.
    ENDIF.
    READ TABLE lt_werks_14_x TRANSPORTING NO FIELDS WITH KEY zzzid = ls_werks_bukrs-werks BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE lt_werks_bukrs.
      CONTINUE.
    ENDIF.

    CLEAR:ls_data_werks.
    ls_data_werks-zxy_id = ls_werks_bukrs-zxy_id.
    ls_data_werks-werks = ls_werks_bukrs-werks.
    APPEND ls_data_werks  TO pt_data_werks.
  ENDLOOP.

  LOOP AT lt_werks_14 INTO DATA(ls_werks_14).
    READ TABLE lt_werks_bukrs_x TRANSPORTING NO FIELDS WITH KEY werks = ls_werks_14-zzzid BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE lt_werks_14.
      CONTINUE.
    ENDIF.
    READ TABLE lt_werks_14_x TRANSPORTING NO FIELDS WITH KEY zzzid = ls_werks_14-zzzid BINARY SEARCH.
    IF sy-subrc EQ 0.
      DELETE lt_werks_14.
      CONTINUE.
    ENDIF.

    CLEAR:ls_data_werks.
    ls_data_werks-zxy_id = ls_werks_14-zxy_id.
    ls_data_werks-werks = ls_werks_14-zzzid.
    APPEND ls_data_werks  TO pt_data_werks.
  ENDLOOP.

  "ERP-17212  未上线批发加盟店  zrev010_wrk
*  SELECT
*    bwkey AS werks,
*    bukrs
*    FROM t001k
*    INTO TABLE @DATA(lt_t001k).

  SELECT
    werks,
    bukrs
    FROM zrev010_wrk
    INTO TABLE @DATA(lt_t001k).


  SORT lt_t001k BY werks.

  LOOP AT pt_data_werks INTO ls_data_werks.
    READ TABLE lt_t001k INTO DATA(ls_t001k) WITH KEY werks = ls_data_werks-werks BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data_werks-bukrs = ls_t001k-bukrs.
      MODIFY pt_data_werks FROM ls_data_werks.
    ENDIF.
  ENDLOOP.




ENDFORM.


FORM frm_get_matnr  USING    pt_data_zxy_id TYPE tt_data
                    CHANGING pt_data_matnr TYPE tt_data.


  SELECT
    i~zxy_id,
    a~ztk_id,

    c~matnr
    FROM @pt_data_zxy_id AS i JOIN zret0006 AS a
                                ON i~zxy_id = a~zxy_id
                              JOIN zreta002 AS b
                                ON a~ztk_id = b~ztk_id
                              JOIN zret0020 AS c
                                ON b~zspz_id = c~zspz_id

    INTO CORRESPONDING FIELDS OF TABLE @pt_data_matnr.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_STATUS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_DATA
*&---------------------------------------------------------------------*
FORM frm_set_status USING pv_mtype TYPE bapi_mtype CHANGING pv_status TYPE char4.

  CASE pv_mtype.
    WHEN 'E'. pv_status = '1'.
    WHEN 'S'. pv_status = '3'.
    WHEN OTHERS. pv_status = '2'.
  ENDCASE.

ENDFORM.


FORM frm_main_data_approve  USING pv_ucomm TYPE sy-ucomm
                          CHANGING
                                   pt_data  TYPE tt_data
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.




*  检查
  PERFORM frm_data_check_approve USING pt_data
                                       pv_ucomm
                            CHANGING pv_mtype
                                     pv_msg.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.



*  保存
  PERFORM frm_data_exe_approve    USING pv_ucomm
                               CHANGING pt_data
                                     pv_mtype
                                     pv_msg.


* 审批后处理
  PERFORM frm_data_approve_next    USING pv_ucomm
                               CHANGING pt_data
                                     pv_mtype
                                     pv_msg.


ENDFORM.

FORM frm_data_exe_approve  USING    pv_ucomm TYPE sy-ucomm
                          CHANGING
                                   pt_data  TYPE tt_data
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.


  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE zres0088.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel_man = 'X'.

    CLEAR ls_approval.
    MOVE-CORRESPONDING ls_data TO ls_approval.
    IF pv_ucomm = 'ALLOW'.
      PERFORM frm_get_kolnr  CHANGING   ls_approval
                                         lt_msglist.

      ls_data-kolnr = ls_approval-kolnr.
      ls_data-frgsx = ls_approval-frgsx.
      ls_data-frgc1 = ls_approval-frgc1.
      ls_data-zxyzt = ls_approval-zxyzt.

      UPDATE zret0066 SET kolnr = ls_approval-kolnr
                          zxyzt = ls_approval-zxyzt
                          frgsx = ls_approval-frgsx
                          WHERE zghftjsq = ls_data-zghftjsq.
    ELSEIF pv_ucomm = 'REJECT'.

      ls_data-zxyzt = 'R'.
      UPDATE zret0066 SET
*                          zxyzt = ls_approval-zxyzt
                          zxyzt = ls_data-zxyzt
                          WHERE zghftjsq = ls_data-zghftjsq.

    ENDIF.
    IF sy-subrc EQ 0.
      ls_data-zmtype = 'S'.
      ls_data-zmsg = COND #( WHEN pv_ucomm = 'ALLOW' THEN '审批通过' ELSE '审批拒绝').
*      ls_data-zmsg = '审批成功！'.
    ELSE.
      ls_data-zmtype = 'E'.
      ls_data-zmsg = '审批操作失败！'.
    ENDIF.

    PERFORM frm_set_status USING ls_data-zmtype CHANGING ls_data-status.
    MODIFY pt_data FROM ls_data.
  ENDLOOP.


  COMMIT WORK AND WAIT.
  pv_mtype = 'S'.
  pv_msg = '保存成功！'.


ENDFORM.

FORM frm_data_approve_next  USING    pv_ucomm TYPE sy-ucomm
                          CHANGING
                                   pt_data  TYPE tt_data
                                   pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.

  DATA:
    ls_zret0013 TYPE zret0013,
    lt_zret0013 TYPE TABLE OF zret0013,

    ls_zret0044 TYPE zret0044,
    lt_zret0044 TYPE TABLE OF zret0044,

    ls_zret0084 TYPE zret0084,
    lt_zret0084 TYPE TABLE OF zret0084.

  DATA:lv_flg   TYPE c.

  IF pv_ucomm EQ 'REJECT'.
    RETURN.
  ENDIF.

  DATA(lt_data) = pt_data[].
  DELETE lt_data WHERE sel_man = '' OR zxyzt NE 'A'.

  CHECK lt_data[] IS NOT INITIAL.


  LOOP AT lt_data INTO DATA(ls_data) .
    CLEAR ls_zret0013.
    IF ls_data-zghf IS NOT INITIAL .
      ls_zret0013-zxy_id = ls_data-zxy_id.
      ls_zret0013-zghf = ls_data-zghf.
      COLLECT ls_zret0013 INTO lt_zret0013.
    ENDIF.
    CLEAR ls_zret0044.
    IF ls_data-zflzff IS NOT INITIAL .
      ls_zret0044-zxy_id = ls_data-zxy_id.
      ls_zret0044-zflzff = ls_data-zflzff.
      ls_zret0044-zpaytp = ls_data-zpaytp.
      COLLECT ls_zret0044 INTO lt_zret0044.
    ENDIF.

    IF ls_data-lifnr IS NOT INITIAL .
      ls_zret0084-zxy_id = ls_data-zxy_id.
      ls_zret0084-lifnr = ls_data-lifnr.
      COLLECT ls_zret0084 INTO lt_zret0084.
      "协议新增供应商后将外部供货方放到条款级
      PERFORM frm_get_zghf_attr USING ls_data-lifnr CHANGING lv_flg.
      IF lv_flg = 'E'.
        ls_zret0013-zxy_id = ls_data-ztk_id.
        COLLECT ls_zret0013 INTO lt_zret0013.
      ENDIF.
    ENDIF.

  ENDLOOP.

  IF lt_zret0044[] IS NOT INITIAL.
    MODIFY zret0044 FROM TABLE lt_zret0044.
  ENDIF.

  IF lt_zret0013[] IS NOT INITIAL.
    MODIFY zret0013 FROM TABLE lt_zret0013.
  ENDIF.

  IF lt_zret0084 IS NOT INITIAL .
    MODIFY zret0084 FROM TABLE lt_zret0084.
  ENDIF.

  COMMIT WORK AND WAIT.





  PERFORM frm_data_calculate_job USING lt_data.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PRO_DOUBLE_CLICK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PS_ROW_ID_INDEX
*&      --> GT_DATA
*&      --> GT_DATA_DTL
*&---------------------------------------------------------------------*
FORM frm_pro_double_click  USING    pv_index  TYPE lvc_index
                                    pt_data TYPE tt_data
                                    pt_data_dtl TYPE tt_data.
  READ TABLE pt_data INTO DATA(ls_data) INDEX pv_index.
  IF sy-subrc NE 0 OR ls_data-zxy_id IS INITIAL OR ls_data-zghf IS INITIAL.
    RETURN.
  ENDIF.

  DATA(lt_data_dtl) = pt_data_dtl[].
  DELETE lt_data_dtl WHERE zxy_id = ls_data-zxy_id AND zghf <> ls_data-zghf.

  PERFORM frm_alv_display_dtl CHANGING lt_data_dtl.


ENDFORM.

FORM frm_alv_display_dtl  CHANGING pt_dtl  TYPE tt_data.


  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.


  PERFORM frm_set_catalog_dtl     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout_dtl      CHANGING  ls_layout.


  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
*     IS_LAYOUT_LVC            = &1
      it_fieldcat_lvc          = lt_fieldcat
      is_layout_lvc            = ls_layout
      i_callback_pf_status_set = 'FRM_STATUS_ALV_DTL'
      i_callback_user_command  = 'FRM_ALV_COMMAND_DTL'
*     i_screen_start_column    = 15
*     i_screen_start_line      = 2
*     i_screen_end_column      = 130
*     i_screen_end_line        = 22
    TABLES
      t_outtab                 = pt_dtl.


ENDFORM.


FORM frm_set_catalog_dtl CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
*    ls_fieldcat-ref_table = &2.                 "
*    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &2.                 "
    ls_fieldcat-coltext = &3.                 "

    CASE &2.


      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'SEL'  .
      ls_fieldcat-checkbox = 'X'.
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.

    ls_fieldcat-no_zero = 'X'.
    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.



  add_fcat  '10' 'ZXY_ID'    '协议号'  .
*  add_fcat  '10' 'ZXY_TXT'    '协议描述'  .
*  add_fcat  '10' 'ZBUKRS'    '协议主体'  .
*  add_fcat  '10' 'BUTXT'    '协议主体名称'  .
*  add_fcat  '10' 'ZTK_ID'    '条款号'  .
*  add_fcat  '10' 'ZBEGIN'    '条款号'  .
*  add_fcat  '10' 'ZEND'    '开始日期'  .
*  add_fcat  '10' 'DOCLN'    '结束日期'  .
*  add_fcat  '10' 'ZHT_ID'    '合同号'  .
*  add_fcat  '10' 'ZHT_TXT'    '合同描述'  .
*  add_fcat  '10' 'ZHTYEAR'    '签署年度'  .

  add_fcat  '10' 'MATNR'    '物料'  .
  add_fcat  '10' 'MAKTX'    '物料描述'  .
  add_fcat  '10' 'WERKS'    '仓库'  .
  add_fcat  '10' 'BUKRS'    '公司'  .


  add_fcat  '10' 'ZGHF'    '供应商'  .
  add_fcat  '10' 'NAME1'    '供应商描述'  .




ENDFORM.                    "SET_CATALOG

FORM frm_set_layout_dtl CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
  ps_layout-box_fname = 'SEL_MAN'.
  ps_layout-sel_mode = 'D'.

ENDFORM.                    "SET_LAYOUT

FORM frm_status_alv_dtl USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.
  SET PF-STATUS 'S1000' EXCLUDING pt_extab.
ENDFORM.                    "PFSTATUS_FORM

FORM frm_alv_command_dtl USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.


  PERFORM frm_check_changed_data_dtl.


  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.


    WHEN OTHERS.
  ENDCASE.


  PERFORM frm_refresh_alv_dtl.


ENDFORM.                    "USER_COMMAND_FORM


FORM frm_check_changed_data_dtl .
  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.
  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.
  CALL METHOD lrf_alv->check_changed_data.
ENDFORM.


FORM frm_refresh_alv_dtl .

  DATA:
        lrf_alv   TYPE REF TO cl_gui_alv_grid.

  DATA:
        ls_stable TYPE lvc_s_stbl.

  ls_stable-row = 'X'.
  ls_stable-col = 'X'.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.

  IF lrf_alv IS NOT INITIAL.
    CALL METHOD lrf_alv->refresh_table_display
      EXPORTING
        is_stable = ls_stable.
  ENDIF.
ENDFORM.

FORM frm_data_check_approve  USING    pt_data  TYPE tt_data
                                      pv_ucomm TYPE sy-ucomm
                          CHANGING pv_mtype TYPE bapi_mtype
                                   pv_msg   TYPE bapi_msg.
  DATA:
    lt_msglist     TYPE scp1_general_errors,
    lt_msglist_tmp TYPE scp1_general_errors,
    ls_msglist     TYPE scp1_general_error.
  DATA:
    lv_msg     TYPE scp1_general_error-msgv1.


  PERFORM frm_check_sel USING pt_data CHANGING pv_mtype pv_msg.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel_man = 'X'.

    IF pv_ucomm = 'ALLOW'.
      IF ls_data-zxyzt = 'A'.

        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '已完全审批'.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msg  CHANGING lt_msglist.
      ELSEIF ls_data-zxyzt = 'D'.

        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '已作废'.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq  lv_msg  CHANGING lt_msglist.
      ENDIF.
    ELSEIF pv_ucomm = 'REJECT'.

      IF ls_data-zxyzt = 'N'.
*        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '未审批，无需审批拒绝'.
*        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msg  CHANGING lt_msglist.

      ELSEIF ls_data-zxyzt = 'R'.
        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '已审批拒绝，无需审批拒绝'.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msg  CHANGING lt_msglist.

      ELSEIF ls_data-zxyzt = 'D'.
        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '已作废，无法审批拒绝'.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msg  CHANGING lt_msglist.
      ELSEIF ls_data-zxyzt = 'A'.
        CLEAR lv_msg. lv_msg = '申请:' &&  ls_data-zghftjsq &&  '已审批，无法审批拒绝'.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msg  CHANGING lt_msglist.
      ENDIF.
    ENDIF.



    PERFORM frm_auth_check_zre USING ls_data CHANGING lt_msglist_tmp.
    ls_msglist-msgid = ls_data-seq.
    MODIFY lt_msglist_tmp FROM ls_msglist TRANSPORTING msgid WHERE msgid = ''.

  ENDLOOP.

  APPEND LINES OF lt_msglist_tmp[] TO lt_msglist[].



  IF lt_msglist[] IS NOT INITIAL.
    PERFORM frm_conver_msglist_2_msg USING lt_msglist CHANGING pt_data.


    PERFORM frm_pro_msglist USING lt_msglist
                            CHANGING pv_mtype
                                     pv_msg.
    RETURN.
  ENDIF.




ENDFORM.


FORM frm_data_pro_bf_approve  USING pv_ucomm TYPE sy-ucomm CHANGING pt_data TYPE tt_data.



ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_AUTH_CHECK_ZRE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_DATA_ZXY_ID
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_auth_check_zre  USING    ps_data TYPE LINE OF tt_data
                         CHANGING pt_msglist TYPE scp1_general_errors.
  DATA:
    pv_mtype TYPE bapi_mtype,
    pv_msg   TYPE scp1_general_error-msgv1.

  SELECT SINGLE * FROM zret0066 WHERE zghftjsq = @ps_data-zghftjsq INTO @DATA(ls_zret0066).
  SELECT SINGLE frgc1, zfrgtx  FROM zretc007 WHERE frgsx = @ls_zret0066-frgsx AND kolnr = @ls_zret0066-kolnr INTO ( @DATA(lv_frgc1) , @DATA(lv_zfrgtx) ).


  SELECT SINGLE * FROM zret0006 WHERE zxy_id = @ps_data-zxy_id INTO @DATA(ls_zret0006).
  SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_zret0006-ztk_id INTO @DATA(ls_zreta002).
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_zreta002-zht_id INTO @DATA(ls_zreta001).


*   合同检查
  PERFORM frm_author_check_ht_new USING ls_zreta001
*                                        '04'
                                        '03'
                                        'B'
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

*  条款检查
  PERFORM frm_author_check_tk_new USING ls_zreta002
*                                        '04'
                                        '03'
                                        'B'
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

*  协议检查
  PERFORM frm_author_check_xy_new USING ls_zret0006
*                                        '04'
                                        '02'
                                        'B'
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.


*   审批策略检查

*   合同检查
  PERFORM frm_author_check_ht_new USING ls_zreta001
                                        lv_frgc1
                                        'B'
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

*  条款检查
  PERFORM frm_author_check_tk_new USING ls_zreta002
                                        lv_frgc1
                                        'B'
                              CHANGING pt_msglist
                                       pv_mtype
                                       pv_msg.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_DATA_CALCULATE_JOB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_DATA
*&---------------------------------------------------------------------*
FORM frm_data_calculate_job  USING    pt_data TYPE tt_data.

  RANGES:
        lr_ztk_id FOR zreta002-ztk_id,
        lr_zxy_id FOR zret0006-zxy_id,
        lr_date FOR mara-ersda.

  DATA:
        lv_date TYPE d.

  DATA:
        lv_guid32 TYPE guid_32.
  DATA: lv_job_name     LIKE tbtco-jobname,
        lv_job_nr       LIKE tbtco-jobcount,
        lv_job_released TYPE c.

  DATA(lt_data_tmp) = pt_data[].
  SORT lt_data_tmp BY ztk_id.
  DELETE ADJACENT DUPLICATES FROM lt_data_tmp COMPARING ztk_id.

  LOOP AT lt_data_tmp INTO DATA(ls_data_tmp).
    CLEAR lr_ztk_id.
    lr_ztk_id-sign = 'I'.
    lr_ztk_id-option = 'EQ'.
    lr_ztk_id-low = ls_data_tmp-ztk_id.
    APPEND lr_ztk_id.
  ENDLOOP.

  lt_data_tmp[] = pt_data[].
  SORT lt_data_tmp BY zxy_id.
  DELETE ADJACENT DUPLICATES FROM lt_data_tmp COMPARING zxy_id.

  LOOP AT lt_data_tmp INTO ls_data_tmp.
    CLEAR lr_zxy_id.
    lr_zxy_id-sign = 'I'.
    lr_zxy_id-option = 'EQ'.
    lr_zxy_id-low = ls_data_tmp-zxy_id.
    APPEND lr_zxy_id.
  ENDLOOP.



  lv_date = sy-datum.
  lv_date = lv_date(6) && '01'.
  lv_date = lv_date - 1.

  lr_date-sign = 'I'.
  lr_date-option = 'EQ'.
*  lr_date-high = lv_date.
  lr_date-high = sy-datum.

  lv_date = sy-datum.
  PERFORM frm_date_calc(zbcs0001) USING 2 0 0 '-' CHANGING lv_date .
  lr_date-low = lv_date.
  APPEND lr_date.


  PERFORM frm_get_guid32(zbcs0001) CHANGING lv_guid32.

  lv_job_name = 'ZREFM0028' && lv_guid32+10(22).

  CALL FUNCTION 'JOB_OPEN'
    EXPORTING
      jobname          = lv_job_name
    IMPORTING
      jobcount         = lv_job_nr
    EXCEPTIONS
      cant_create_job  = 1
      invalid_job_data = 2
      jobname_missing  = 3
      OTHERS           = 4.
  IF syst-subrc <> 0.
    MESSAGE e001(00) WITH '打开后台作业出错'.
  ENDIF.

  SUBMIT zred0008
    WITH s_ztk_id IN lr_ztk_id
    WITH s_zxy_id IN lr_zxy_id
    WITH s_datum IN lr_date
    WITH p_back = ''
    WITH p_autadj = 'X'
    USER syst-uname          " mandatory
    VIA JOB lv_job_name NUMBER lv_job_nr AND RETURN.
  IF sy-subrc <> 0.
    MESSAGE e001(00) WITH '定义后台作业出错，已经停止运行'.
  ENDIF.


  CALL FUNCTION 'JOB_CLOSE'
    EXPORTING
      jobcount             = lv_job_nr
      jobname              = lv_job_name
*     sdlstrtdt            = lv_sdlstrtdt
*     sdlstrttm            = lv_sdlstrttm
      strtimmed            = 'X'
    IMPORTING
      job_was_released     = lv_job_released
    EXCEPTIONS
      cant_start_immediate = 1
      invalid_startdate    = 2
      jobname_missing      = 3
      job_close_failed     = 4
      job_nosteps          = 5
      job_notex            = 6
      lock_failed          = 7
      OTHERS               = 8.
  IF syst-subrc <> 0.
    MESSAGE '关闭后台作业出错' TYPE 'E'.
  ELSE.
    MESSAGE s001(00) WITH '后台作业' lv_job_nr '已经从开始运行'.
  ENDIF.

ENDFORM.


FORM frm_get_ghflistt_add            USING pv_id
                              CHANGING pt_ghflist  TYPE zrei0014.

  DATA:
          ls_zres0046 TYPE zres0046.

  SELECT SINGLE zbukrs FROM zret0006 WHERE zxy_id = @pv_id INTO @DATA(lv_zbukrs).

  SELECT b~werks
  FROM t001k AS a JOIN t001w AS b
                    ON a~bwkey = b~werks
                   AND b~vlfkz = 'B'
                    WHERE a~bukrs = @lv_zbukrs
                    INTO TABLE @DATA(lt_t001w)     .

  LOOP AT lt_t001w INTO DATA(ls_t001w).
    ls_zres0046-sign = 'I'.
    ls_zres0046-option = 'EQ'.
    ls_zres0046-low = ls_t001w-werks.
    ls_zres0046-low = |{ ls_zres0046-low ALPHA = IN }|.
    COLLECT ls_zres0046 INTO  pt_ghflist.
  ENDLOOP.

  ls_zres0046-sign = 'I'.
  ls_zres0046-option = 'EQ'.
  ls_zres0046-low = lv_zbukrs.
  ls_zres0046-low = |{ ls_zres0046-low ALPHA = IN }|.
  COLLECT ls_zres0046 INTO  pt_ghflist.


ENDFORM.