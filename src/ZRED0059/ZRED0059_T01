*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_T01
*&---------------------------------------------------------------------*


*TYPES:
*  BEGIN OF ty_head,
*    bukrs TYPE acdoca-rbukrs,
*    belnr TYPE acdoca-belnr,
*    gjahr TYPE acdoca-gjahr,
*    dmbtr TYPE anbtr,
*    erdat TYPE sy-datum,
*    ernam TYPE sy-uname,
*
*  END OF ty_head.

TYPES:
  BEGIN OF ty_data.
    INCLUDE TYPE zret0066.
TYPES:
  zht_id    TYPE zreta001-zht_id,
  zht_txt   TYPE zreta001-zht_txt,
  zhtyear   TYPE zreta001-zhtyear,
  zspz_id   TYPE zreta002-zspz_id,

  zhsjz     TYPE zret0003-zhsjz,

  zxy_txt   TYPE zret0006-zxy_txt,
  ztk_txt   TYPE zreta002-ztk_txt,

  zbegin    TYPE zret0006-zbegin,
  zend      TYPE zret0006-zend,
  zitsplr   TYPE zret0006-zitsplr,
  zetsplr   TYPE zret0006-zetsplr,
  zctgr     TYPE zret0006-zctgr,

  werks     TYPE t001w-werks,
  bukrs     TYPE t001-bukrs,
  butxt     TYPE t001-butxt,
  matnr     TYPE makt-matnr,
  maktx     TYPE makt-maktx,
  name1     TYPE lfa1-name1,
  lifnrt     TYPE lfa1-name1,
  zflzfft   TYPE lfa1-name1,
  frgc1     TYPE zretc007-frgc1,
  zfrgtx    TYPE zretc007-zfrgtx,


  seq       TYPE i,
*    sel   TYPE char1,
  sel_man   TYPE char1,
  zflg_exe  TYPE char1,
  zflg_done TYPE char1,
  status    TYPE char4,
  zmtype    TYPE bapi_mtype,
  zmsg      TYPE bapi_msg,
*  field_style TYPE lvc_t_styl,
*  cellcolor   TYPE lvc_t_scol,
  END OF ty_data,
  tt_data TYPE TABLE OF ty_data.

TYPES: BEGIN OF ty_excel_list,
         zxy_id TYPE string,
         zghf   TYPE string,
         zflzff TYPE string,
         zpaytp TYPE string,
         lifnr  TYPE string,
       END OF ty_excel_list,
       tt_excel_list TYPE TABLE OF ty_excel_list.

TYPES: BEGIN OF ty_t001,
         bukrs TYPE t001-bukrs,
         butxt TYPE t001-butxt,
       END OF ty_t001,
       tt_t001 TYPE TABLE OF ty_t001.


TYPES: BEGIN OF ty_lfa1,
         lifnr TYPE lfa1-lifnr,
         name1 TYPE lfa1-name1,
       END OF ty_lfa1,
       tt_lfa1 TYPE TABLE OF ty_lfa1.

TYPES:
  BEGIN OF ty_globe,
    title   TYPE syst_title,
    actvt   TYPE activ_auth,
    zflg_rb TYPE char2,
    ucomm   TYPE syst-ucomm,
  END OF ty_globe.

TYPES: BEGIN OF ty_zghf,
         sign   TYPE s_sign,
         option TYPE s_option,
         low    TYPE zret0046-zghf,
         high   TYPE zret0046-zghf,
       END OF ty_zghf,
       tt_zghf TYPE TABLE OF ty_zghf.