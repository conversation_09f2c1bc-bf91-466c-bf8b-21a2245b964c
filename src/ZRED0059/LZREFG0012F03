*&---------------------------------------------------------------------*
*& 包含               LZREFG0012F03
*&---------------------------------------------------------------------*


*&---------------------------------------------------------------------*
*& Form FRM_GET_GHFLISTT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_ALL_DATA_DATAA002_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_get_etsplrlistt         USING pv_id
                              CHANGING pt_ghflist TYPE zrei0014.

  REFRESH pt_ghflist.


  SELECT  'E'    AS sign,
          'EQ'   AS option,
          zghf   AS low
    APPENDING TABLE @pt_ghflist
    FROM zret0013 WHERE zxy_id = @pv_id AND zzzpc = 'X' AND zghf <> 'ALL' .

  SELECT  'I'   AS sign,
         'EQ'   AS option,
         zghf   AS low
 APPENDING TABLE @pt_ghflist
 FROM zret0013 WHERE zxy_id = @pv_id AND zzzpc = '' AND zghf <> 'ALL' .

  IF pt_ghflist[] IS NOT INITIAL.
    PERFORM frm_handle_ghflist CHANGING pt_ghflist.
  ENDIF.

  SELECT COUNT(*) FROM zret0013 WHERE zxy_id = pv_id AND zghf = 'ALL'.
  IF sy-subrc = 0.
    SELECT 'I'   AS sign,
           'BT'  AS option,
           zfrom AS low,
           zzend AS high
      APPENDING TABLE @pt_ghflist
      FROM zret0051
      WHERE  zfrom IS NOT NULL
        AND  zzend IS NOT NULL
        AND ztype  EQ 'W' .
  ENDIF.

  SORT pt_ghflist BY sign option low high.
  DELETE ADJACENT DUPLICATES FROM pt_ghflist COMPARING sign option low high.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_ZZZID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_ZXYID
*&      <-- LT_ZZZID_DATA
*&---------------------------------------------------------------------*
FORM frm_get_zzzid        USING pv_zxyid      TYPE zree_zxy_id
                       CHANGING pt_weklist    TYPE zrei0011
                                pt_ekglist    TYPE zrei0015.
  DATA:
    lv_type       TYPE bapi_mtype,
    lv_msg        TYPE bapi_msg,
    lt_zxyid      TYPE zrei0022,
    lt_zzzid_data TYPE zrei0021,
    ls_zzzid_data TYPE LINE OF zrei0021.
  DATA:
    lt_zret0116_dele TYPE TABLE OF zret0116,
    lt_zret0116_save TYPE TABLE OF zret0116.

  REFRESH:pt_weklist,pt_ekglist.

  lt_zxyid = VALUE #( ( zxy_id = pv_zxyid  ) ).

  CALL FUNCTION 'ZREFM0021'
    IMPORTING
      et_data     = lt_zzzid_data
      ev_type     = lv_type
      ev_msg      = lv_msg
    TABLES
      it_zxyid    = lt_zxyid
      ot_zret0101 = lt_zret0116_save.

  SELECT * INTO TABLE lt_zret0116_dele FROM zret0116 WHERE zxy_id = pv_zxyid.
  IF lt_zret0116_dele[] IS NOT INITIAL .
    DELETE zret0116 FROM TABLE  lt_zret0116_dele.
  ENDIF.
  IF lt_zret0116_save[] IS NOT INITIAL .
    MODIFY zret0116 FROM TABLE  lt_zret0116_save.
  ENDIF.

  COMMIT WORK.

  READ TABLE lt_zzzid_data INTO ls_zzzid_data WITH  KEY zxy_id = pv_zxyid.
  IF sy-subrc =  0.
    pt_weklist[] = ls_zzzid_data-rt_weklist[].
    pt_ekglist[] = ls_zzzid_data-rt_ekglist[].
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_GHFLISTT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_ALL_DATA_DATAA002_ZTK_ID
*&---------------------------------------------------------------------*
FORM frm_get_ghflistt            USING pv_id
                                       pv_zitsplr
                                       pv_zetsplr
                                       pt_tk_ghf   TYPE zrei0014
                              CHANGING pt_ghflist  TYPE zrei0014.

  SELECT  'E'  AS sign,
          'EQ' AS option,
         zghf  AS low
    APPENDING TABLE @pt_ghflist
    FROM zret0013 WHERE zxy_id = @pv_id AND zzzpc = 'X'.

  SELECT  'I'   AS sign,
         'EQ'   AS option,
         zghf   AS low
    APPENDING TABLE @pt_ghflist
    FROM zret0013 WHERE zxy_id = @pv_id AND zzzpc = ''.

  IF pt_ghflist[] IS NOT INITIAL.
    PERFORM frm_handle_ghflist CHANGING pt_ghflist.
  ENDIF.

  IF pv_zitsplr IS NOT INITIAL .
    SELECT 'I'   AS sign,
           'BT'  AS option,
           zfrom AS low,
           zzend AS high
      APPENDING TABLE @pt_ghflist
      FROM zret0051
      WHERE  zfrom IS NOT NULL
        AND  zzend IS NOT NULL
        AND ztype  EQ 'N' .
  ENDIF.

  IF pv_zetsplr IS NOT INITIAL .
    APPEND LINES OF pt_tk_ghf TO pt_ghflist.
  ENDIF.

  SORT pt_ghflist BY sign option low high.
  DELETE ADJACENT DUPLICATES FROM pt_ghflist COMPARING sign option low high.

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_CHULI_GHFLIST
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_GHFLIST
*&---------------------------------------------------------------------*
FORM frm_handle_ghflist  CHANGING pt_ghflist TYPE zrei0014.

  DATA:it_ghflist_new TYPE zrei0014.

  SORT pt_ghflist BY sign option low high.
  DELETE ADJACENT DUPLICATES FROM pt_ghflist COMPARING sign option low high.


  "旧编码找到新编码加入到ghflist
  SELECT  b~sign   ,
          b~option ,
          zblbp  AS low
    FROM zsdt0020     AS a
   INNER JOIN @pt_ghflist  AS b ON a~partner = b~low
   WHERE zblbp <> ''
APPENDING TABLE @it_ghflist_new.

  APPEND LINES OF  pt_ghflist TO it_ghflist_new.
  SORT it_ghflist_new BY sign option low high.
  DELETE ADJACENT DUPLICATES FROM it_ghflist_new COMPARING sign option low high.

*    新编码找到所有旧编码加入到ghflist
  SELECT
          b~sign   ,
          b~option ,
          partner AS low
    FROM zsdt0020    AS a
   INNER JOIN @it_ghflist_new AS b ON a~zblbp = b~low
   WHERE zblbp <> ''
APPENDING TABLE @pt_ghflist.

  APPEND LINES OF it_ghflist_new TO pt_ghflist.


ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_MATLISTDATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LS_ALL_DATA_DATAA002
*&      <-- LT_MATLISTDATA
*&---------------------------------------------------------------------*
FORM frm_get_matlistdata  USING    pv_zspz_id
                          CHANGING pt_matlistdata TYPE zrei0020.

  DATA: lt_matlistdata TYPE zrei0020,
        ls_zspz        TYPE zres0052,
        lt_zspz        TYPE TABLE OF zres0052,
        ev_type        TYPE bapi_mtype,
        ev_msg         TYPE bapi_msg.

  ls_zspz-zspz_id = pv_zspz_id.
  APPEND ls_zspz TO lt_zspz.
  DELETE lt_zspz WHERE zspz_id IS INITIAL.

  CHECK lt_zspz[] IS NOT INITIAL.

  CALL FUNCTION 'ZREFM0022'
    IMPORTING
      et_data = lt_matlistdata
      ev_type = ev_type
      ev_msg  = ev_msg
    TABLES
      it_zspz = lt_zspz.

  IF ev_type EQ  'E'.
    EXIT .
  ELSE.
    APPEND LINES OF lt_matlistdata TO pt_matlistdata.
  ENDIF.

ENDFORM.