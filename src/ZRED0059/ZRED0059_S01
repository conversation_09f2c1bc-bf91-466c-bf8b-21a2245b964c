*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_S01
*&---------------------------------------------------------------------*


TABLES:
  zret0006,zreta001,zret0066,zmmt0001, sscrfields..

DATA:
  grf_container TYPE REF TO cl_gui_docking_container,
  grf_alv       TYPE REF TO cl_gui_alv_grid,
  ok_code       TYPE sy-ucomm.

DATA:
*  gs_head     TYPE ty_head,
  gt_data     TYPE tt_data,
  gt_data_dtl TYPE tt_data,
  gs_globe    TYPE ty_globe,
  gt_t001     TYPE tt_t001,
  gt_lfa1     TYPE tt_lfa1.

DATA: functxt TYPE smp_dyntxt.

SELECTION-SCREEN BEGIN OF BLOCK b01 WITH FRAME TITLE TEXT-001.

SELECT-OPTIONS:
  s_zfllx FOR zret0006-zfllx MODIF ID m01 MATCHCODE OBJECT zresh0001 ,
  s_zxytyp FOR zret0006-zxybstyp MODIF ID m01 NO-DISPLAY,
  s_date   FOR zret0006-zbegin MODIF ID m01 ,
  s_zxy_id FOR zret0006-zxy_id MODIF ID m03,
  s_zbukrs FOR zret0006-zbukrs MODIF ID m03 MATCHCODE OBJECT  c_t001,
  s_ztk_id FOR zret0006-ztk_id MODIF ID m03 MATCHCODE OBJECT zresh0018,
  s_zhtlx  FOR zreta001-zhtlx  MODIF ID m01,
  s_zht_id FOR zreta001-zht_id MODIF ID m01,
  s_year   FOR zreta001-zhtyear  MODIF ID m01,
  s_matnr  FOR zmmt0001-matnr    MODIF ID m01,

  s_tjsq   FOR zret0066-zghftjsq MODIF ID m02,
  s_zcjr   FOR zret0066-zcjr MODIF ID m02,
  s_zcjrq  FOR zret0066-zcjrq MODIF ID m02.

PARAMETERS:
  p_wbghf TYPE c MODIF ID m01 AS CHECKBOX DEFAULT 'X',
  p_nbghf TYPE c MODIF ID m01 AS CHECKBOX,
  p_show  TYPE c MODIF ID m01 AS CHECKBOX DEFAULT 'X'.
PARAMETERS:
  p_file TYPE rlgrap-filename MODIF ID m04 .
SELECTION-SCREEN END OF BLOCK b01.


SELECTION-SCREEN BEGIN OF BLOCK b03 WITH FRAME TITLE TEXT-003 .

PARAMETERS:
  rb_add  TYPE char1 RADIOBUTTON GROUP g1 USER-COMMAND uc01 DEFAULT 'X' MODIF ID m10,
  rb_edit TYPE char1 RADIOBUTTON GROUP g1  MODIF ID m10,
  rb_impt TYPE char1 RADIOBUTTON GROUP g1  MODIF ID m10.
*  rb_dis  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10 ,
*  rb_rles TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10.

SELECTION-SCREEN END OF BLOCK b03.

SELECTION-SCREEN FUNCTION KEY 1.
SELECTION-SCREEN FUNCTION KEY 2.
SELECTION-SCREEN FUNCTION KEY 3.



INITIALIZATION.
  functxt-icon_id   = icon_xls.
  functxt-quickinfo = '下载模板'.
  functxt-icon_text = '下载模板'.
  sscrfields-functxt_01 = functxt.

  PERFORM frm_screen_init.

AT SELECTION-SCREEN OUTPUT.
  PERFORM frm_set_screen.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR p_file.
  PERFORM frm_select_file(zbcs0001) CHANGING p_file.

AT SELECTION-SCREEN.

  CASE sscrfields-ucomm.
    WHEN 'FC01'.
      PERFORM frm_download_template(zbcs0001) USING 'ZRED0059' '返利供货方与供货渠道对照申请模板'.
    WHEN 'UC01'.
      IF rb_add = 'X'.  p_show = 'X'. ELSE. p_show = ''. ENDIF.
    WHEN OTHERS.
  ENDCASE.

START-OF-SELECTION.



  PERFORM frm_main.