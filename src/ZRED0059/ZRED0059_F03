*----------------------------------------------------------------------*
***INCLUDE ZRED0059_F03.
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_IMPT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data_impt  CHANGING pt_data TYPE tt_data.

  DATA:lt_excel_list TYPE tt_excel_list.
  DATA:ls_excel_list TYPE ty_excel_list.
  DATA:ls_data TYPE ty_data.
  DATA:lv_zflzff TYPE zret0044-zflzff..

  PERFORM frm_excel_load_a(zbcs0001)  TABLES lt_excel_list USING p_file 2.

  LOOP AT lt_excel_list INTO ls_excel_list  .
    ls_excel_list-zxy_id = |{ ls_excel_list-zxy_id  ALPHA = IN WIDTH = 10  }|.
    ls_excel_list-zghf   = |{ ls_excel_list-zghf    ALPHA = IN WIDTH = 10  }|.
    ls_excel_list-zflzff = |{ ls_excel_list-zflzff  ALPHA = IN WIDTH = 10  }|.
    ls_excel_list-lifnr  = |{ ls_excel_list-lifnr   ALPHA = IN WIDTH = 10  }|.

    ls_data-zxy_id = ls_excel_list-zxy_id.
    ls_data-zghf   = ls_excel_list-zghf.
    ls_data-zflzff = ls_excel_list-zflzff.
    ls_data-zpaytp = ls_excel_list-zpaytp.
    ls_data-lifnr  = ls_excel_list-lifnr.

    SELECT SINGLE zxy_id INTO @DATA(lv_zxy_id) FROM zret0006 WHERE zxy_id = @ls_excel_list-zxy_id.
    IF sy-subrc <> 0.
      ls_data-zmsg   = '协议号码不存在！请检查'.
    ENDIF.

    SELECT SINGLE zxy_id INTO lv_zxy_id FROM zret0006 INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
     WHERE zxy_id = ls_excel_list-zxy_id AND zleib = 'R'.
    IF sy-subrc = 0 .
      ls_data-zmsg   = '条款申请协议不可调整！请检查'.
    ENDIF.


    IF ls_excel_list-zghf IS INITIAL AND ls_data-zflzff IS INITIAL  .
      IF ls_data-zmsg IS INITIAL .
        ls_data-zmsg   =  '供货方和付款方不可同时为空！请检查' .
      ELSE.
        ls_data-zmsg   = ls_data-zmsg && '/' && '供货方和付款方不可同时为空！请检查'.
      ENDIF.
    ENDIF.

    IF ls_excel_list-zghf IS NOT INITIAL .
      SELECT SINGLE lifnr INTO @DATA(lv_zghf) FROM lfa1 WHERE lifnr = @ls_excel_list-zghf.
      IF sy-subrc <> 0.
        IF ls_data-zmsg IS INITIAL .
          ls_data-zmsg   =  '供货方不存在系统中！请检查' .
        ELSE.
          ls_data-zmsg   = ls_data-zmsg && '/' && '供货方不存在系统中！请检查'.
        ENDIF.
      ENDIF.
    ENDIF.

    IF ls_excel_list-lifnr IS NOT INITIAL .
      SELECT SINGLE lifnr INTO @DATA(lv_lifnr) FROM lfa1 WHERE lifnr = @ls_excel_list-lifnr.
      IF sy-subrc <> 0.
        IF ls_data-zmsg IS INITIAL .
          ls_data-zmsg   =  '供应商不存在系统中！请检查' .
        ELSE.
          ls_data-zmsg   = ls_data-zmsg && '/' && '供应商不存在系统中！请检查'.
        ENDIF.
      ENDIF.
    ENDIF.

    IF  ls_data-zflzff IS NOT INITIAL.
      lv_zflzff =  |{ ls_data-zflzff ALPHA = OUT }|.
      IF strlen( lv_zflzff ) = 4 .
        SELECT SINGLE butxt INTO @DATA(lv_butxt) FROM t001 WHERE bukrs = @lv_zflzff.
        IF sy-subrc <> 0.
          IF ls_data-zmsg IS INITIAL .
            ls_data-zmsg   =  '付款方不存在系统中！请检查' .
          ELSE.
            ls_data-zmsg   = ls_data-zmsg && '/' && '付款方不存在！请检查'.
          ENDIF.
        ENDIF.
      ELSE.
        lv_zflzff =  |{ ls_data-zflzff ALPHA = IN }|.
        SELECT SINGLE lifnr INTO @lv_lifnr FROM lfa1 WHERE lifnr = @lv_zflzff.
        IF sy-subrc <> 0.
          IF ls_data-zmsg IS INITIAL .
            ls_data-zmsg   =  '付款方不存在系统中！请检查' .
          ELSE.
            ls_data-zmsg   = ls_data-zmsg && '/' && '付款方不存在系统中！请检查'.
          ENDIF.
        ENDIF.
      ENDIF.
    ENDIF.


    IF ls_data-zpaytp = '' OR ls_data-zpaytp = 'A' OR ls_data-zpaytp = 'B'      .
    ELSE.
      IF ls_data-zmsg IS INITIAL .
        ls_data-zmsg   =  '付款方级别值异常！请检查' .
      ELSE.
        ls_data-zmsg   = ls_data-zmsg && '/' && '付款方级别值异常！请检查'.
      ENDIF.
    ENDIF.
    IF ls_data-zmsg IS NOT INITIAL .
      ls_data-zmtype = 'E'.
    ENDIF.

    COLLECT ls_data INTO pt_data.
    CLEAR:ls_data.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_ZQ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_check_zq  CHANGING pv_error.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  CLEAR:pv_error.
  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.
  IF lv_moste = 'E' .
    pv_error = 'E'.
  ENDIF.
ENDFORM.