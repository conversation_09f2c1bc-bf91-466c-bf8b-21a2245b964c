*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_C01
*&---------------------------------------------------------------------*


CLASS sec_lcl_event_receiver DEFINITION DEFERRED.

CLASS sec_lcl_event_receiver DEFINITION.
  PUBLIC SECTION.
    DATA:
      objid    TYPE char10.

    METHODS:
      constructor
        IMPORTING
          i_objid TYPE char10 OPTIONAL,
      sec_handle_toolbar
                    FOR EVENT toolbar OF cl_gui_alv_grid
        IMPORTING e_object e_interactive,
      sec_handle_bef_user_command
                    FOR EVENT before_user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_user_command
                    FOR EVENT user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_hotspot_click
                    FOR EVENT hotspot_click OF cl_gui_alv_grid
        IMPORTING e_row_id e_column_id es_row_no,
      sec_handle_double_click
                    FOR EVENT double_click OF cl_gui_alv_grid
        IMPORTING e_row e_column es_row_no,
      sec_handle_data_changed
                    FOR EVENT data_changed OF cl_gui_alv_grid
        IMPORTING er_data_changed e_onf4 e_onf4_before e_onf4_after,
      sec_handle_data_changed_fin
                    FOR EVENT data_changed_finished OF cl_gui_alv_grid
        IMPORTING e_modified et_good_cells,
      sec_handle_onf4
                    FOR EVENT onf4 OF cl_gui_alv_grid
        IMPORTING e_fieldname e_fieldvalue es_row_no er_event_data et_bad_cells e_display.
ENDCLASS.

CLASS sec_lcl_event_receiver IMPLEMENTATION.

  METHOD constructor.
    objid = i_objid.
  ENDMETHOD.
  METHOD sec_handle_toolbar.
    PERFORM handle_toolbar USING e_object e_interactive objid.
  ENDMETHOD.
  METHOD sec_handle_bef_user_command.
    PERFORM handle_bef_user_command USING e_ucomm.
  ENDMETHOD.
  METHOD sec_handle_user_command.
    PERFORM handle_user_commmand CHANGING e_ucomm objid.
  ENDMETHOD.
  METHOD sec_handle_hotspot_click.
    PERFORM handle_hotspot_click USING e_row_id e_column_id es_row_no objid.
  ENDMETHOD.
  METHOD sec_handle_double_click.
    PERFORM handle_double_click USING e_row e_column es_row_no objid.
  ENDMETHOD.
  METHOD sec_handle_data_changed.
    PERFORM handle_data_changed USING er_data_changed objid.
  ENDMETHOD.
  METHOD sec_handle_data_changed_fin.
    PERFORM handle_data_changed_fin USING e_modified et_good_cells objid.
  ENDMETHOD.
  METHOD sec_handle_onf4.
    PERFORM handle_onf4 USING e_fieldname e_fieldvalue es_row_no er_event_data et_bad_cells e_display.
  ENDMETHOD.
ENDCLASS.

FORM handle_data_changed USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               pv_objid TYPE char10.

  LOOP AT prf_data_changed->mt_mod_cells INTO DATA(ls_mod_cell) WHERE fieldname = 'ZFLZFF'.

    DATA:lv_zflzff  TYPE zret0044-zflzff,
         lv_zflzfft TYPE lfa1-name1.

    TRY .
        MOVE:ls_mod_cell-value TO lv_zflzff.
      CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
        DATA(lv_error_text) = lv_error->get_text( ).
      CLEANUP.
    ENDTRY.

    IF lv_zflzff IS NOT INITIAL .



      lv_zflzff =  |{ lv_zflzff ALPHA = OUT }|.
      IF strlen( lv_zflzff ) = 4 . .
        READ TABLE gt_t001 INTO DATA(ls_t001) WITH KEY bukrs = lv_zflzff BINARY SEARCH.
        IF sy-subrc EQ 0.
          lv_zflzfft = ls_t001-butxt.
        ENDIF.
      ELSE.
        lv_zflzff =  |{ lv_zflzff ALPHA = IN }|.
        READ TABLE gt_lfa1 INTO  DATA(ls_lfa1) WITH KEY lifnr = lv_zflzff BINARY SEARCH.
        IF sy-subrc EQ 0.
          lv_zflzfft = ls_lfa1-name1.
        ENDIF.
      ENDIF.



      IF lv_zflzfft IS INITIAL .
        prf_data_changed->add_protocol_entry(  i_msgid      = 'SABAPDOCU'
                                              i_msgty      = 'E'
                                              i_msgno      = '888'
                                              i_msgv1      = ls_mod_cell-value
                                              i_msgv2      = '支付方不存在'
                                              i_msgv3      = ''
                                              i_msgv4      = ''
                                              i_fieldname  = ls_mod_cell-fieldname
                                              i_row_id     = ls_mod_cell-row_id
                                              i_tabix      = ls_mod_cell-tabix
                                            ) .
        PERFORM frm_modify_cell USING prf_data_changed  ls_mod_cell 'ZFLZFFT' lv_zflzfft .
      ELSE.
        PERFORM frm_modify_cell USING prf_data_changed  ls_mod_cell 'ZFLZFFT' lv_zflzfft .
      ENDIF.
    ELSE.
      PERFORM frm_modify_cell USING prf_data_changed  ls_mod_cell 'ZFLZFFT' '' .
    ENDIF.
  ENDLOOP.

ENDFORM.

FORM handle_data_changed_fin USING pv_modified TYPE char01
                                    pt_good_cells  TYPE lvc_t_modi
                                    pv_objid TYPE char10 .

*  DATA:lv_zflzff TYPE zret0044-zflzff .
*
*  LOOP AT gt_data ASSIGNING FIELD-SYMBOL(<lfs_data>) WHERE edit = 'X' .
*
*    lv_zflzff =  |{ <lfs_data>-zflzff ALPHA = OUT }|.
*    IF strlen( lv_zflzff ) = 4 . .
*      READ TABLE gt_t001 INTO DATA(ls_t001) WITH KEY bukrs = lv_zflzff BINARY SEARCH.
*      IF sy-subrc EQ 0.
*        <lfs_data>-zflzfft = ls_t001-butxt.
*      ENDIF.
*    ELSE.
*      READ TABLE gt_lfa1 INTO DATA(ls_lfa1) WITH KEY lifnr = <lfs_data>-zflzff BINARY SEARCH.
*      IF sy-subrc EQ 0.
*        <lfs_data>-zflzfft = ls_lfa1-name1.
*      ENDIF.
*    ENDIF.
*  ENDLOOP.

*  IF pv_modified = 'X'.
*    PERFORM frm_refresh_alv USING grf_alv.
*  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MODIFY_CELL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PRF_DATA_CHANGED
*&      --> LS_T31
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_modify_cell  USING    prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               ls_mod_cell TYPE lvc_s_modi
                               pv_fname
                               value .

  CALL METHOD prf_data_changed->modify_cell
    EXPORTING
      i_row_id    = ls_mod_cell-row_id
      i_fieldname = pv_fname
      i_value     = value.

ENDFORM.

FORM handle_toolbar USING prf_object TYPE REF TO cl_alv_event_toolbar_set
                          pv_interactive
                          pv_objid TYPE char10.
  DATA: lw_toolbar TYPE stb_button.
  DATA: lv_error   TYPE c.

  DEFINE add_toolbar.
    lw_toolbar-butn_type  = &1.
    lw_toolbar-function   = &2.
    lw_toolbar-icon       = &3.
    lw_toolbar-quickinfo  = &4.
    lw_toolbar-text       = &4.
    lw_toolbar-disabled   = &5.
    APPEND lw_toolbar TO prf_object->mt_toolbar .
    CLEAR lw_toolbar.
  END-OF-DEFINITION.


  DELETE prf_object->mt_toolbar WHERE function = '&GRAPH'
                                 OR function = '&INFO'
                                 OR function = '&REFRESH'
                                 OR function = '&CHECK'
                                 OR function = '&LOCAL&CUT'
                                 OR function = '&LOCAL&COPY'
                                 OR function = '&LOCAL&PASTE'
                                 OR function = '&LOCAL&UNDO'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&INSERT_ROW'
                                 OR function = '&LOCAL&DELETE_ROW'
                                 OR function = '&LOCAL&COPY_ROW'
                                 .


*  add_toolbar:    '0' 'ALL'  '@4B@' '全选' '' .
*  add_toolbar:    '0' 'SAL' '@4D@' '取消' ''  .
  IF p_show = '' AND (  gs_globe-zflg_rb = '01' OR  gs_globe-zflg_rb = '03' ) .
    add_toolbar:    '0' 'SAVE' '@2L@' '创建供货方添加申请' ''  .
  ENDIF.

  IF gs_globe-zflg_rb = '02'.
    PERFORM frm_check_zq CHANGING lv_error.
    IF lv_error <> 'E' .
      add_toolbar:    '0' 'ALLOW' '@8X@' '审批通过' ''  .
      add_toolbar:    '0' 'REJECT' '@8Y@' '审批拒绝' ''  .
    ENDIF.

  ENDIF.




ENDFORM.

FORM handle_bef_user_command  USING pv_ucomm.



ENDFORM.

FORM handle_user_commmand CHANGING pv_ucomm
                          pv_objid TYPE char10.

  DATA:lt_rows TYPE lvc_t_row,
       lw_rows TYPE lvc_s_row.

  DATA:
    lv_mtype_cmd TYPE bapi_mtype,
    lv_msg_cmd   TYPE bapi_msg.

  PERFORM frm_check_changed_data USING grf_alv.

  PERFORM frm_get_sel_rows(zbcs0001) TABLES    gt_data
                                         USING grf_alv
                                               'SEL_MAN'  .
  CASE pv_ucomm.
    WHEN 'ALL'.
      PERFORM frm_set_all(zbcs0001)   TABLES gt_data USING 'SEL_MAN'.
    WHEN 'SAL'.
      PERFORM frm_set_sal(zbcs0001)   TABLES gt_data USING 'SEL_MAN'.
    WHEN 'CHECK'.

*      PERFORM frm_main_data_check      USING gt_data
*                                    CHANGING lv_mtype_cmd
*                                             lv_msg_cmd.

*      MESSAGE s888(sabapdocu) WITH lv_msg_cmd DISPLAY LIKE lv_mtype_cmd.
    WHEN 'SAVE'.

      PERFORM frm_main_data_save    CHANGING gt_data
                                             gs_globe
                                             lv_mtype_cmd
                                             lv_msg_cmd.

      MESSAGE s888(sabapdocu) WITH lv_msg_cmd DISPLAY LIKE lv_mtype_cmd.

    WHEN 'ALLOW' OR 'REJECT'..

      PERFORM frm_main_data_approve     USING pv_ucomm
                                     CHANGING gt_data
                                             lv_mtype_cmd
                                             lv_msg_cmd.

      MESSAGE s888(sabapdocu) WITH lv_msg_cmd DISPLAY LIKE lv_mtype_cmd.


    WHEN OTHERS.
  ENDCASE.


  CALL METHOD cl_gui_cfw=>set_new_ok_code
    EXPORTING
      new_code = 'REFRESH'.

  PERFORM frm_refresh_alv USING grf_alv.
ENDFORM.

FORM handle_hotspot_click  USING   ps_row_id TYPE lvc_s_row
                                    ps_column_id TYPE lvc_s_col
                                    ps_row_no TYPE lvc_s_roid
                                    pv_objid TYPE char10.



ENDFORM.

FORM handle_double_click  USING   ps_row_id TYPE lvc_s_row
                                   ps_column_id TYPE lvc_s_col
                                   ps_row_no TYPE lvc_s_roid
                                   pv_objid TYPE char10.

  IF gs_globe-zflg_rb = '01'.
    PERFORM frm_pro_double_click USING ps_row_id-index
                                     gt_data
                                     gt_data_dtl.
  ENDIF.

ENDFORM.

FORM handle_onf4  USING    e_fieldname TYPE lvc_fname
                            e_fieldvalue TYPE lvc_value
                            es_row_no TYPE lvc_s_roid
                            er_event_data TYPE REF TO cl_alv_event_data
                            et_bad_cells TYPE lvc_t_modi
                            e_display TYPE char01.


ENDFORM.

FORM frm_f4_help_werks  USING    pv_bukrs TYPE t001-bukrs
                        CHANGING pv_werks TYPE t001w-werks.



ENDFORM.


FORM frm_add_protocol_entry USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                                  ps_mod_cell TYPE lvc_s_modi
                                  lv_error_text TYPE string.



ENDFORM.



FORM frm_data_changed_check  USING    prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                                      ps_mod_cell TYPE lvc_s_modi
                                      ps_data TYPE ty_data.



ENDFORM.