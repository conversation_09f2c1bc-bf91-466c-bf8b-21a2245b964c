*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_F01
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& Form FRM_SCREEN_INIT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_screen_init .

*  CLEAR:
*        rb_add,rb_edit.",rb_dis,rb_rles.


*  根据事务码控制进入后的界面显示
*  CASE sy-tcode.
*    WHEN 'ZRED0043A'.
*      rb_add = 'X'.
*      gs_globe-title = 'XXXX创建'.
*    WHEN 'ZRED0043B'.
*      rb_edit = 'X'.
*      gs_globe-title = 'XXXX修改'.
*    WHEN 'ZRED0043C'.
*      rb_dis = 'X'.
*      gs_globe-title = 'XXXX显示'.
*    WHEN 'ZRED0043D'.
*      rb_rles = 'X'.
*      gs_globe-title = 'XXXX审批'.
*    WHEN OTHERS.
*      rb_add = 'X'.
*      gs_globe-title = 'XXXX创建'.
*  ENDCASE.

  gs_globe-title = '返利供货方与供货渠道对照'.
  PERFORM frm_set_title(zbcs0001)  USING gs_globe-title.
ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen .

*  新增
  IF rb_add = 'X'.

    LOOP AT SCREEN.

      IF screen-group1 = 'M02' OR  screen-group1 = 'M04' .
        screen-active = '0'.
      ELSE.
        screen-active = '1'.
      ENDIF.

      MODIFY SCREEN.
    ENDLOOP.
  ELSEIF rb_edit = 'X'.
    LOOP AT SCREEN.
      IF screen-group1 = 'M01' OR   screen-group1 = 'M04'.
        screen-active = '0'.
      ELSEIF screen-group1 = 'M02'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ELSE.
    LOOP AT SCREEN.
      IF screen-group1 = 'M01' OR screen-group1 = 'M02' OR  screen-group1 = 'M03'.
        screen-active = '0'.
      ELSE.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

*  通过事务码进入后隐藏单选按钮
*  IF sy-tcode NE 'SE38'.
*    LOOP AT SCREEN.
*      IF screen-group1 = 'M10'.
*        screen-active = '0'.
*      ENDIF.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_main .

  PERFORM frm_init_data.

  PERFORM frm_check_screen.

  PERFORM frm_author_check.

  CASE gs_globe-zflg_rb.
    WHEN '01'.
      PERFORM frm_get_data_bdp CHANGING  gt_data gt_data_dtl.
    WHEN  '02'.
      PERFORM frm_get_data_tjsq CHANGING  gt_data.
    WHEN '03'.
      PERFORM frm_get_data_impt CHANGING  gt_data.
    WHEN OTHERS.
  ENDCASE.
  PERFORM frm_pro_data_end USING gs_globe CHANGING  gt_data gt_data_dtl.

  CALL SCREEN 2000.



ENDFORM.

FORM frm_init_data .

  PERFORM frm_clear_data.

  PERFORM frm_pro_data_globe.

  IF p_show = '' .
    PERFORM frm_pro_lock(zbcs0001) USING '返利供货方与供货渠道对照' ''.
  ENDIF.

ENDFORM.

FORM frm_clear_data .
  CLEAR: gs_globe.
ENDFORM.

FORM frm_pro_data_globe .

  CASE 'X'.
    WHEN rb_add .     gs_globe-zflg_rb = '01'.  gs_globe-title = '返利供货方与供货渠道对照'.   gs_globe-actvt = '01'    .
    WHEN rb_edit .    gs_globe-zflg_rb = '02'.  gs_globe-title = '返利供货方与供货渠道对照'.   gs_globe-actvt = '02'    .
    WHEN rb_impt .    gs_globe-zflg_rb = '03'.  gs_globe-title = '返利供货方与供货渠道对照'.   gs_globe-actvt = '03'    .
*    WHEN rb_dis .     gs_globe-zflg_rb = '03'.  gs_globe-title = 'XXXX显示'.   gs_globe-actvt = '03'    .
*    WHEN rb_rles.     gs_globe-zflg_rb = '05'.  gs_globe-title = 'XXXX审批'.   gs_globe-actvt = '04'    .
    WHEN OTHERS.
  ENDCASE.

ENDFORM.

FORM frm_check_screen .
*  IF gs_globe-zflg_rb = '01'.
*    IF p_bukrs IS INITIAL .
*      MESSAGE s888(sabapdocu) WITH '公司代码必填' DISPLAY LIKE 'E'.
*      LEAVE LIST-PROCESSING.
*    ENDIF.
*
*
*  ELSE.
*    IF p_belnr IS INITIAL .
*      MESSAGE s888(sabapdocu) WITH '凭证号码必填' DISPLAY LIKE 'E'.
*      LEAVE LIST-PROCESSING.
*    ENDIF.
*  ENDIF.

  IF s_date[] IS INITIAL AND rb_add = 'X'.
    MESSAGE s888(sabapdocu) WITH '日期必填' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  IF p_wbghf IS INITIAL AND p_nbghf IS INITIAL AND rb_add = 'X'.  .
    MESSAGE s888(sabapdocu) WITH '供货方内部外部至少选一项' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.
ENDFORM.

FORM frm_author_check .

  AUTHORITY-CHECK OBJECT 'ZREAR011'
   ID 'ZQDWH' FIELD gs_globe-zflg_rb.
  IF sy-subrc <> 0.
    MESSAGE |请申请供货渠道维护权限，权限对象ZREAR011：作业类型：{ gs_globe-zflg_rb }| TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.


*  DATA:lv_subrc TYPE c,
*       lv_mess  TYPE bapiret2-message.
*
*  CLEAR:lv_subrc,
*        lv_mess.
*  IF gs_globe-zflg_rb  = '01'.
*
*    PERFORM frm_auth_check_bukrs USING gs_globe-actvt p_bukrs.
*    IF sy-subrc NE 0.
*      MESSAGE s888(sabapdocu) WITH '没有公司代码的权限' DISPLAY LIKE 'E'.
*      LEAVE LIST-PROCESSING.
*    ENDIF.
*
*  ELSEIF gs_globe-zflg_rb  = '02' OR
*          gs_globe-zflg_rb  = '03' OR
*          gs_globe-zflg_rb  = '04' .
*
**    CALL FUNCTION 'ZBCFM0001'
**      EXPORTING
**        iv_object = 'ZREAR005'
**        iv_field  = 'BUKRS'
**        iv_actvt  = gs_globe-actvt
**      IMPORTING
**        ex_subrc  = lv_subrc
**        ex_mess   = lv_mess
**      TABLES
**        it_tab    = s_flsqf.
*
*    IF lv_subrc = 'E'.
*      MESSAGE lv_mess TYPE  lv_subrc.
*      LEAVE LIST-PROCESSING.
*    ENDIF.
*
*  ENDIF.

ENDFORM.

FORM frm_auth_check_bukrs  USING    pv_actvt TYPE activ_auth
                                    pv_bukrs TYPE t001-bukrs
                                    .
  CLEAR sy-subrc.
  AUTHORITY-CHECK OBJECT 'ZREAR005'
                      ID 'BUKRS' FIELD pv_bukrs
                      ID 'ACTVT' FIELD pv_actvt.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_SCREEN_PUB
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_set_screen_pub .

  IF gs_globe-zflg_rb = '03' .
    LOOP AT SCREEN.
      IF screen-group1 = '101' .
        screen-input = 0.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.


ENDFORM.


FORM frm_get_data_bdp  CHANGING pt_data TYPE tt_data pt_data_dtl TYPE tt_data.

  DATA:
    lt_data_zxy_id    TYPE tt_data.
  DATA:rs_zghf TYPE ty_zghf,
       rt_zghf TYPE tt_zghf.

*  获取协议信息
  PERFORM frm_get_zxy_id CHANGING lt_data_zxy_id.

  IF lt_data_zxy_id[]  IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有查询到数据' DISPLAY LIKE 'S'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  SELECT bukrs INTO TABLE @DATA(lt_t001) FROM t001 .
  SELECT werks AS bukrs APPENDING TABLE @lt_t001 FROM t001w WHERE vlfkz = 'B'.
  IF p_nbghf = 'X' AND p_wbghf = 'X' .
    "无限制
  ELSEIF p_nbghf = 'X'.
    LOOP AT lt_t001 INTO DATA(ls_t001) .
      rs_zghf-sign   = 'I'.
      rs_zghf-option = 'EQ'.
      rs_zghf-low    = |{ ls_t001-bukrs ALPHA = IN }|.
      APPEND rs_zghf TO rt_zghf.
    ENDLOOP.
  ELSEIF  p_wbghf = 'X'.
    LOOP AT lt_t001 INTO ls_t001 .
      rs_zghf-sign   = 'E'.
      rs_zghf-option = 'EQ'.
      rs_zghf-low    = |{ ls_t001-bukrs ALPHA = IN }|.
      APPEND rs_zghf TO rt_zghf.
    ENDLOOP.
  ENDIF.

  LOOP AT lt_data_zxy_id  INTO DATA(ls_data_zxy_id).

    PERFORM frm_get_data_main USING
                                       ls_data_zxy_id
                                       rt_zghf
                              CHANGING pt_data
                                       pt_data_dtl.
  ENDLOOP.


  IF pt_data[]  IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有查询到数据' DISPLAY LIKE 'S'.
    LEAVE LIST-PROCESSING.
  ENDIF.





ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_ZXY_ID
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- LT_DATA_ZXY_ID
*&---------------------------------------------------------------------*
FORM frm_get_zxy_id  CHANGING pt_data_zxy_id TYPE tt_data.


  DATA:
    lt_data_zxy_id_01 TYPE tt_data,
    lt_data_zxy_id_02 TYPE tt_data.




  SELECT
    a~zht_id,
    a~zht_txt,
    a~zhtyear,
    b~ztk_id,
    b~zspz_id,
*    b~zhsjz,
    c~zbegin,
    c~zend,
    c~zxy_id,
    c~zxy_txt,
    c~zbukrs,
    c~zitsplr,
    c~zetsplr
*    f~zhsjz,
*    f~zpur
    FROM zret0006 AS c
*                       JOIN zret0013 AS d
*                         ON c~zxy_id = d~zxy_id
                       JOIN zreta002 AS b
                         ON c~ztk_id = b~ztk_id
                       JOIN zreta001 AS a
                         ON b~zht_id = a~zht_id
                       JOIN zretc005 AS e
                         ON b~ztk_id = e~zclrid
                       JOIN zret0003 AS f
                         ON e~zhsjz = f~zhsjz
    WHERE f~zpur = 'X'
    AND   c~zfllx IN @s_zfllx
    AND   c~zxybstyp IN @s_zxytyp
    AND   c~zxy_id IN @s_zxy_id
    AND   c~zbukrs IN @s_zbukrs
    AND   b~ztk_id IN @s_ztk_id
    AND   b~zleib  <> 'R'
    AND   a~zhtlx IN @s_zhtlx
    AND   a~zhtyear IN @s_year
    AND   a~zht_id IN @s_zht_id
*    AND   c~zxyzt IN ('N','P','A')
    AND   c~zxyzt  = 'A'
    AND   c~zxybstyp IN ('V','A')
    AND   c~zend >= @s_date-low
    AND   c~zbegin <= @s_date-low
    INTO CORRESPONDING FIELDS OF TABLE @lt_data_zxy_id_01.

  SELECT
    a~zht_id,
    a~zht_txt,
    a~zhtyear,
    b~ztk_id,
    b~zspz_id,
*    b~zhsjz,
    c~zbegin,
    c~zend,
    c~zxy_id,
    c~zxy_txt,
    c~zbukrs,
    c~zitsplr,
    c~zetsplr
    FROM zret0006 AS c JOIN zreta002 AS b
                         ON c~ztk_id = b~ztk_id
                       JOIN zreta001 AS a
                         ON b~zht_id = a~zht_id
                       JOIN zreta004 AS e
                         ON c~zxy_id = e~zxy_id
    WHERE e~zatktp = 'A'
    AND   c~zfllx IN @s_zfllx
    AND   c~zxybstyp IN @s_zxytyp
    AND   c~zxy_id IN @s_zxy_id
    AND   c~zbukrs IN @s_zbukrs
    AND   b~ztk_id IN @s_ztk_id
    AND   b~zleib  <> 'R'
    AND   a~zhtlx IN @s_zhtlx
    AND   a~zhtyear IN @s_year
    AND   a~zht_id IN @s_zht_id
*    AND   c~zxyzt IN ('N','P','A')
    AND   c~zxyzt  = 'A'
    AND   c~zxybstyp IN ('V','A')
    AND   c~zend >= @s_date-low
    AND   c~zbegin <= @s_date-low
    INTO CORRESPONDING FIELDS OF TABLE @lt_data_zxy_id_02.

  APPEND LINES OF lt_data_zxy_id_01 TO pt_data_zxy_id.
  APPEND LINES OF lt_data_zxy_id_02 TO pt_data_zxy_id.

  SORT pt_data_zxy_id BY zxy_id.
  DELETE ADJACENT DUPLICATES FROM pt_data_zxy_id COMPARING zxy_id.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_TJSQ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_get_data_tjsq  CHANGING pt_data TYPE tt_data.

  SELECT
    a~*
    FROM zret0066 AS a
    INNER JOIN zret0006 AS c ON  C~zxy_id  = a~zxy_id
    WHERE a~zghftjsq IN @s_tjsq
    AND   c~zfllx    IN @s_zfllx
    AND   c~zxybstyp IN @s_zxytyp
    AND   a~zxy_id IN @s_zxy_id
    AND   a~zbukrs IN @s_zbukrs
    AND   a~ztk_id IN @s_ztk_id
    AND   a~zcjr   IN @s_zcjr
    AND   a~zcjrq  IN @s_zcjrq
    INTO CORRESPONDING FIELDS OF TABLE @pt_data.

  IF pt_data[]  IS INITIAL.
    MESSAGE s888(sabapdocu) WITH '没有查询到数据' DISPLAY LIKE 'S'.
    LEAVE LIST-PROCESSING.
  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- PT_DATA
*&      <-- PT_DATA_DTL
*&---------------------------------------------------------------------*
FORM frm_get_data_main USING
                                ps_data_zxy_id TYPE LINE OF tt_data
                                rt_zghf       TYPE tt_zghf
                       CHANGING pt_data       TYPE tt_data
                                 pt_data_dtl  TYPE tt_data.

  DATA:
    ls_data TYPE LINE OF tt_data,
    lt_data TYPE tt_data.
  DATA:ls_matlist  TYPE LINE OF zrei0013.
  DATA: rt_matnr   TYPE RANGE OF zret0046-matnr.
  DATA:lv_bwkey    TYPE  zret0044-zflzff.

  DATA:
    rt_weklist         TYPE zrei0011,
    rt_ekglist         TYPE zrei0015,
    lt_tk_ghf          TYPE zrei0014,
    lt_matlistdata_all TYPE zrei0020,
    rt_ghflist         TYPE zrei0014.

  "条款供货方
  PERFORM frm_get_etsplrlistt    USING ps_data_zxy_id-ztk_id
                              CHANGING lt_tk_ghf.

  "协议供货方
  PERFORM frm_get_ghflistt          USING ps_data_zxy_id-zxy_id
                                          ps_data_zxy_id-zitsplr
                                          ps_data_zxy_id-zetsplr
                                          lt_tk_ghf
                                 CHANGING rt_ghflist.

*  排除协议主体本身及DC
  PERFORM frm_get_ghflistt_add          USING ps_data_zxy_id-zxy_id
                                 CHANGING rt_ghflist.

*  门店
  PERFORM frm_get_zzzid              USING ps_data_zxy_id-zxy_id
                                  CHANGING rt_weklist
                                           rt_ekglist.

*  商品
  PERFORM frm_get_matlistdata USING ps_data_zxy_id-zspz_id
                           CHANGING lt_matlistdata_all.


  LOOP AT lt_matlistdata_all INTO DATA(ls_matlistdata_all).

    LOOP AT ls_matlistdata_all-matlist INTO ls_matlist .
      IF ls_matlist-matnr = 'ALL'.
        REFRESH rt_matnr.
*        lv_signall = 'X'.
        EXIT.
      ELSEIF ls_matlist-zspbc = 'X' .
        rt_matnr = VALUE #( BASE rt_matnr ( sign = 'E' option = 'EQ'  low = ls_matlist-matnr   high = '' ) ).
      ELSE.
        rt_matnr = VALUE #( BASE rt_matnr ( sign = 'I' option = 'EQ'  low = ls_matlist-matnr   high = '' ) ).
      ENDIF.
    ENDLOOP.
  ENDLOOP.


  SELECT
    DISTINCT
    a~matnr,
    a~bukrs,
    a~werks,
    a~zghf AS zghf,
    a~zghf AS zflzff
    FROM zret0046 AS a
    WHERE a~zbsart = 'P'
    AND   a~matnr IN @rt_matnr
    AND   a~matnr IN @s_matnr
    AND   a~werks IN @rt_weklist
    AND   a~zghf  NOT IN @rt_ghflist
    AND   a~zghf  IN @rt_zghf
    AND   a~budat IN @s_date
    AND   a~budat >= @ps_data_zxy_id-zbegin
    AND   a~budat <= @ps_data_zxy_id-zend
    INTO CORRESPONDING FIELDS OF TABLE @lt_data.

  "ERP-17212  未上线批发加盟店  zrev010_wrk
*  SELECT bwkey,
*         bukrs
*  INTO TABLE @DATA(lt_t001k)
*  FROM t001k
* WHERE bukrs IS NOT NULL  .

  SELECT werks AS bwkey,
         bukrs
  INTO TABLE @DATA(lt_t001k)
  FROM  zrev010_wrk
 WHERE bukrs IS NOT NULL  .

  SELECT
    *
    FROM zret0044
    INTO TABLE @DATA(lt_zret0044)
    WHERE zxy_id = @ps_data_zxy_id-zxy_id.

  LOOP AT lt_data ASSIGNING FIELD-SYMBOL(<lfs_data>) .

    <lfs_data>-zxy_id = ps_data_zxy_id-zxy_id.


    READ TABLE lt_zret0044 INTO DATA(ls_zret0044) WITH  KEY zflzff = <lfs_data>-zflzff.
    IF sy-subrc = 0.
      <lfs_data>-zpaytp = ls_zret0044-zpaytp.
    ELSE.
      CLEAR:lv_bwkey.
      lv_bwkey =  |{ <lfs_data>-zflzff ALPHA = OUT }|.
      IF strlen( lv_bwkey ) = 4 .
        READ TABLE lt_t001k INTO DATA(ls_t001k) WITH  KEY bwkey = lv_bwkey.
        IF sy-subrc = 0.
          <lfs_data>-zpaytp = 'B'.
          <lfs_data>-zflzff = ls_t001k-bukrs.
        ELSE.
          <lfs_data>-zpaytp = ''.
        ENDIF.
      ELSE.
        <lfs_data>-zpaytp = ''.
      ENDIF.
    ENDIF.

  ENDLOOP.


  APPEND LINES OF lt_data TO pt_data_dtl.
  SORT lt_data BY zghf.
  DELETE ADJACENT DUPLICATES FROM lt_data COMPARING zghf.
  APPEND LINES OF lt_data TO pt_data.



ENDFORM.