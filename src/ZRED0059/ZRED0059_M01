*&---------------------------------------------------------------------*
*& 包含               Y_LXY_ALV_OO_TMP_M01
*&---------------------------------------------------------------------*



MODULE user_command_2000 INPUT.


  DATA:
    gv_tmp_code  TYPE sy-ucomm,
    gv_tmp_mtype TYPE bapi_mtype,
    gv_tmp_msg   TYPE bapi_msg.


  CLEAR:
    gv_tmp_mtype,gv_tmp_msg.


  PERFORM frm_code_pro(zbcs0001) CHANGING   ok_code
                                            gv_tmp_code.



  CASE gv_tmp_code.



    WHEN 'BACK' OR 'EXIT' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.


      IF grf_alv IS NOT INITIAL.
        CALL METHOD grf_alv->free
          EXCEPTIONS
            cntl_error        = 1
            cntl_system_error = 2
            OTHERS            = 3.
      ENDIF.

      IF grf_container IS NOT INITIAL.
        CALL METHOD grf_container->free
          EXCEPTIONS
            cntl_error        = 1
            cntl_system_error = 2
            OTHERS            = 3.
      ENDIF.
      CLEAR:grf_container, grf_alv.
      LEAVE TO SCREEN 0.


    WHEN OTHERS.
  ENDCASE.

  PERFORM frm_refresh_alv USING grf_alv .

ENDMODULE.

*&---------------------------------------------------------------------*
*& Module MDL_PBO_2000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE mdl_pbo_2000 OUTPUT.

  SET PF-STATUS 'S1000'.
  SET TITLEBAR 'T1000' WITH gs_globe-title.


  PERFORM frm_alv_init.



ENDMODULE.