*&---------------------------------------------------------------------*
*& 包含               ZRED0056_T01
*&---------------------------------------------------------------------*


TYPES:
*@锚点TK
*按excel序号添加
  BEGIN OF ty_data_excel_01,
    f1   TYPE string,    "   合同号
    f1a  TYPE string,    "   返利类型
    f2   TYPE string,    "   条款采购组
    f3   TYPE string,    "   条款开始日期
    f4   TYPE string,    "   条款结束日期
    f5   TYPE string,    "   核算周期
    f6   TYPE string,    "   兑付方式
    f7   TYPE string,    "   付款期间(天)
    f8   TYPE string,    "   周期类型
    f8a  TYPE string,    "   采购经理
    f8b  TYPE string,    "   采购总监
    f8c  TYPE string,    "   核算厂家
    f8e  TYPE string,    "   核算厂家描述
    f8d  TYPE string,    "   兑付时间
    f9   TYPE string,    "   条款描述
    f9a  TYPE string,    "   OA流程编码
    f9b  TYPE string,    "   批发返利
    f9c  TYPE string,    "   收取部门
    f9d  TYPE string,    "   平台编码
    f9e  TYPE string,    "   收款截止日
    f10  TYPE string,    "   计算方法
    f11  TYPE string,    "   商品组描述
    f12  TYPE string,    "   商品编码-买
    f13  TYPE string,    "   商品编码-赠
    f14  TYPE string,    "   加提单价
    f15  TYPE string,    "   规则描述1
    f16  TYPE string,    "   规则买1
    f17  TYPE string,    "   规则赠1
    f18  TYPE string,    "   返利值1
    f19  TYPE string,    "   匹配比例1
    f20  TYPE string,    "   规则描述2
    f21  TYPE string,    "   规则买2
    f22  TYPE string,    "   规则赠2
    f23  TYPE string,    "   返利值2
    f24  TYPE string,    "   匹配比例2
    f25  TYPE string,    "   促销日期
    f25a TYPE string,    "   剔除促销日期
    f26  TYPE string,    "   组织级别
    f27  TYPE string,    "   协议主体
    f28  TYPE string,    "   收款方
    f29  TYPE string,    "   协议描述
    f30  TYPE string,    "   付款方
    f31  TYPE string,    "   付款方级别
    f32  TYPE string,    "   公司代码
    f32a TYPE string,    "   公司类型
    f33  TYPE string,    "   门店
    f34  TYPE string,    "   排除门店
    f35  TYPE string,    "   采购组织
    f36  TYPE string,    "   专属标识
    f37  TYPE string,    "   同享合约券
    f38  TYPE string,    "   子类
    f39  TYPE string,    "   渠道

  END OF ty_data_excel_01,

  BEGIN OF ty_data_excel_02,

    ztk_id TYPE string,    "   条款号
    f26    TYPE string,    "   组织级别
    f27    TYPE string,    "   协议主体
    f28    TYPE string,    "   收款方
    f29    TYPE string,    "   协议描述
    f30    TYPE string,    "   付款方
    f31    TYPE string,    "   付款方级别
    f32    TYPE string,    "   公司代码
    f32a   TYPE string,    "   公司类型
    f33    TYPE string,    "   门店
    f34    TYPE string,    "   排除门店
    f35    TYPE string,    "   采购组织
    f36    TYPE string,    "   专属标识
    f37    TYPE string,    "   同享合约券
    f38    TYPE string,    "   子类
    f39    TYPE string,    "   渠道
  END OF ty_data_excel_02.


TYPES:
  BEGIN OF ty_data,
    zkey TYPE string.
    INCLUDE TYPE ty_data_excel_01.
TYPES:

  zspz_id     TYPE zret0009-zspz_id,
  ztk_id      TYPE zret0006-ztk_id,
  zxy_id      TYPE zret0006-zxy_id,

  eknam       TYPE t024-eknam,
  zdffs_t     TYPE char30,
  zhstype_t   TYPE char30,
  zfllx_t     TYPE char30,

  field_style TYPE lvc_t_styl,
  cellcolor   TYPE lvc_t_scol,
*    SEL         TYPE CHAR1,
*    STATUS      TYPE CHAR1,
*    ZMSG        TYPE BAPI_MSG,
*    ZMTYPE      TYPE BAPI_MTYPE,
  seq         TYPE i,    "   序号
  seg         TYPE i,    "   分组号

  ztype_excel TYPE char1,
  zmsg_excel  TYPE char255,
  ztype_impt  TYPE char1,
  zmsg_impt   TYPE char255,
  sel         TYPE char1,
  sel_man     TYPE char1,
  status      TYPE char1,
  END OF ty_data,
  tt_data          TYPE TABLE OF ty_data,
  tt_data_excel_02 TYPE TABLE OF ty_data_excel_02,
  tt_data_excel_01 TYPE TABLE OF ty_data_excel_01.

TYPES:
  BEGIN OF ty_ta02,
    seg TYPE i.    "   分组号
    INCLUDE TYPE zreta002.
TYPES:
END OF ty_ta02,
tt_ta02 TYPE TABLE OF ty_ta02.

TYPES:
  BEGIN OF ty_t09.
    INCLUDE TYPE zret0009.
TYPES:

  seg TYPE i,    "   分组号
  END OF ty_t09,
  tt_t09 TYPE TABLE OF ty_t09.

TYPES:
  BEGIN OF ty_t20.
    INCLUDE TYPE zret0020.
TYPES:
  seg  TYPE i,    "   分组号
  zflg TYPE char1,    "区分买和赠 避免被去重掉
  END OF ty_t20,
  tt_t20 TYPE TABLE OF ty_t20.

*促销返利阶梯
TYPES:
  BEGIN OF ty_ta05.
    INCLUDE TYPE zreta005.
TYPES:
  seg TYPE i,    "   分组号
  END OF ty_ta05,
  tt_ta05 TYPE TABLE OF ty_ta05.

*促销日期
TYPES:
  BEGIN OF ty_ta06.
    INCLUDE TYPE zreta006.
TYPES:
  seg TYPE i,    "   分组号
  END OF ty_ta06,
  tt_ta06 TYPE TABLE OF ty_ta06.

*规则
TYPES:
  BEGIN OF ty_tc05.
    INCLUDE TYPE zretc005.
TYPES:
  seg TYPE i,    "   分组号
  END OF ty_tc05,
  tt_tc05 TYPE TABLE OF ty_tc05.

*商品阶梯组
TYPES:
  BEGIN OF ty_t58.
    INCLUDE TYPE zret0058.
TYPES:
  seg  TYPE i,    "   分组号
  zflg TYPE char1,
  END OF ty_t58,
  tt_t58 TYPE TABLE OF ty_t58.

TYPES:
  BEGIN OF ty_t06,
    seg TYPE i.    "   分组号
    INCLUDE TYPE zret0006.
TYPES:
  seq    TYPE i,    "   序号
  zht_id TYPE zreta001-zht_id,    "

  END OF ty_t06,
  tt_t06 TYPE TABLE OF ty_t06.

TYPES:
  BEGIN OF ty_t44.
    INCLUDE TYPE zret0044.
TYPES:
  seg TYPE i,    "   分组号
  seq TYPE i,    "   序号
  END OF ty_t44,
  tt_t44 TYPE TABLE OF ty_t44.

TYPES:
*  子类
  BEGIN OF ty_t81.
    INCLUDE TYPE zret0081.
TYPES:
  zitems_key TYPE zretc002-zitems,
  zzlms      TYPE zretcm10-zzlms,
  seg        TYPE i,    "   分组号
  seq        TYPE i,    "   序号
  END OF ty_t81,
  tt_t81 TYPE TABLE OF ty_t81,
*  渠道
  BEGIN OF ty_t82.
    INCLUDE TYPE zret0082.
TYPES:
  zitems_key TYPE zretc002-zitems,
  zqdms      TYPE zretcm11-zqdms,
  seg        TYPE i,    "   分组号
  seq        TYPE i,    "   序号
  END OF ty_t82,
  tt_t82 TYPE TABLE OF ty_t82.

*公司代码
TYPES:
  BEGIN OF ty_bukrs,
    sel     TYPE char1,
    bukrs   TYPE t001-bukrs,
    zmdsx   TYPE zret0014-zmdsx,
    butxt   TYPE t001-butxt,
    exclude TYPE char1,
    seg     TYPE i,    "   分组号
    seq     TYPE i,    "   序号

  END OF ty_bukrs,
  tt_bukrs TYPE TABLE OF ty_bukrs,


*  门店代码
  BEGIN OF ty_werks,
    sel     TYPE char1,
    werks   TYPE t001w-werks,
    name1   TYPE t001w-name1,
    exclude TYPE char1,
    seg     TYPE i,    "   分组号
    seq     TYPE i,    "   序号

  END OF ty_werks,
  tt_werks TYPE TABLE OF ty_werks,

*  采购组织
  BEGIN OF ty_ekorg,
    sel     TYPE char1,
    ekorg   TYPE t024e-ekorg,
    ekotx   TYPE t024e-ekotx,
    exclude TYPE char1,
    seg     TYPE i,    "   分组号
    seq     TYPE i,    "   序号

  END OF ty_ekorg,
  tt_ekorg TYPE TABLE OF ty_ekorg.

TYPES:
  BEGIN OF ty_t14.
    INCLUDE TYPE zret0014.
TYPES:
END OF ty_t14,
tt_t14 TYPE TABLE OF ty_t14.

TYPES:BEGIN OF typ_double_check,
        f1a  TYPE string,    "   返利类型  (RB02 / RB25 / RB26)  使用,其他都做清空处理
        f10  TYPE string,    "   计算方法
        f12  TYPE string,    "   商品编码-买
        f13  TYPE string,    "   商品编码-赠
        f25  TYPE string,    "   促销日期
        f25a TYPE string,    "   剔除促销日期
        f26  TYPE string,    "   组织级别
        f27  TYPE string,    "   协议主体
        f32  TYPE string,    "   公司代码
        f32a TYPE string,    "   公司属性
        f33  TYPE string,    "   门店
        f34  TYPE string,    "   排除门店
        f36  TYPE string,    "   专属标识
        num  TYPE sy-tabix,  "   计数器
        txt  TYPE text40,    "   文本
      END OF typ_double_check.
DATA:it_double TYPE STANDARD TABLE OF typ_double_check,
     wa_double TYPE typ_double_check.