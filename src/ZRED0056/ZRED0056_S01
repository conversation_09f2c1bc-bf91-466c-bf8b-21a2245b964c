*&---------------------------------------------------------------------*
*& 包含               ZRED0056_S01
*&---------------------------------------------------------------------*

TYPE-POOLS: slis,icon.
TABLES: sscrfields.
TABLES: mara.

DATA: functxt TYPE smp_dyntxt.



DATA:
  gt_data  TYPE tt_data,
*  gt_data_excel TYPE tt_data_excel,
  gt_ta02  TYPE tt_ta02,
  gt_ta05  TYPE tt_ta05,
  gt_ta06  TYPE tt_ta06,
  gt_tc05  TYPE tt_tc05,
  gt_t06   TYPE tt_t06,
  gt_t44   TYPE tt_t44,
  gt_t58   TYPE tt_t58,
  gt_t09   TYPE tt_t09,
  gt_t20   TYPE tt_t20,
  gt_t81   TYPE tt_t81,
  gt_t82   TYPE tt_t82,
  gt_bukrs TYPE tt_bukrs,
  gt_werks TYPE tt_werks,
  gt_ekorg TYPE tt_ekorg.

DATA:
  ok_code       TYPE sy-ucomm.



DATA:
      gv_flg TYPE char2.



SELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.

PARAMETERS:
  p_file TYPE rlgrap-filename MODIF ID m02 .

PARAMETERS:
  rb_01 TYPE char1 RADIOBUTTON GROUP g1  DEFAULT 'X',
  rb_02 TYPE char1 RADIOBUTTON GROUP g1.

SELECTION-SCREEN END OF BLOCK b1.

*SELECTION-SCREEN BEGIN OF BLOCK B2 WITH FRAME TITLE TEXT-002.
*PARAMETERS:
*  RB_ADD  TYPE CHAR1 RADIOBUTTON GROUP G1  DEFAULT 'X' USER-COMMAND FC_RAD ,
*  RB_EDIT TYPE CHAR1 RADIOBUTTON GROUP G1,
*  RB_DIS  TYPE CHAR1 RADIOBUTTON GROUP G1.
*SELECTION-SCREEN END OF BLOCK B2.

SELECTION-SCREEN FUNCTION KEY 1.
SELECTION-SCREEN FUNCTION KEY 2.
SELECTION-SCREEN FUNCTION KEY 3.

INITIALIZATION.

  functxt-icon_id   = icon_xls.
  functxt-quickinfo = '促销返利条款批量导入模板'.
  functxt-icon_text = '促销返利条款批量导入模板'.
  sscrfields-functxt_01 = functxt.

  functxt-icon_id   = icon_table_settings.
  functxt-quickinfo = '促销返利批导公司代码配置表维护'.
  functxt-icon_text = '促销返利批导公司代码配置表维护'.
  sscrfields-functxt_02 = functxt.

  functxt-icon_id   = icon_xls.
  functxt-quickinfo = '促销返利新增协议批量导入模板'.
  functxt-icon_text = '促销返利新增协议批量导入模板'.
  sscrfields-functxt_03 = functxt.



AT SELECTION-SCREEN.
  PERFORM frm_auth_check.

  CASE sscrfields-ucomm.
    WHEN 'FC01'.
      PERFORM frm_download_template USING 'ZRED0056' '促销返利条款批量导入模板'.
    WHEN 'FC03'.
      PERFORM frm_download_template USING 'ZRED0057' '促销返利新增协议批量导入模板'.
    WHEN 'FC02'.

      CALL FUNCTION 'ZBCFM0010'
        EXPORTING
          i_tablename = 'ZRETCM05'
          i_edit      = 'X'.
    WHEN OTHERS.
  ENDCASE.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR p_file.
  PERFORM frm_select_file.


START-OF-SELECTION.
  PERFORM frm_main.