*&---------------------------------------------------------------------*
*& 包含               ZRED0056_F02
*&---------------------------------------------------------------------*


*&---------------------------------------------------------------------*
*& FORM FRM_DATA_CHECK
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_data_check_01
                         USING
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                         CHANGING pt_data TYPE tt_data
                                  lt_msglist TYPE scp1_general_errors.


  DATA: lt_msglist_tmp  TYPE scp1_general_errors.
  DATA:lt_split_table   TYPE TABLE OF string,
       lt_split_table_x TYPE TABLE OF string.

  DATA:
    lv_date1 TYPE d,
    lv_date2 TYPE d.
  DATA:
    ls_zreta002   TYPE zreta002,
    lv_date_begin TYPE d,
    lv_tmp_date   TYPE d,
    lv_zcpctg_01  TYPE zreta005-zcpctg,
    lv_zcpctg_02  TYPE zreta005-zcpctg,
    lv_msgv1      TYPE scp1_general_error-msgv1.
  SORT pt_werks BY seq.
  SORT pt_bukrs BY seq.

  LOOP AT pt_data INTO DATA(ls_data).

    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '合同号'      ls_data-f1   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '条款开始日期' ls_data-f3   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '条款结束日期' ls_data-f4   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '返利类型'    ls_data-f1a   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '条款描述'    ls_data-f9   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '计算方法'    ls_data-f10   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '商品组描述'  ls_data-f11   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '商品编码-买' ls_data-f12   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '商品编码-赠' ls_data-f13   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '规则描述1'   ls_data-f15   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '规则买1'     ls_data-f16   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '规则赠1'     ls_data-f17   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '返利值1'     ls_data-f18   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '生效日期数据' ls_data-f25   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '组织级别'    ls_data-f26   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '协议主体'    ls_data-f27   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '收款方'      ls_data-f28   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '付款方'      ls_data-f30   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '子类'        ls_data-f38   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '渠道'        ls_data-f39   CHANGING lt_msglist .

    IF ls_data-f1 IS NOT INITIAL.
      PERFORM frm_check_zht_id(zbcs0001) USING ls_data-f1.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '合同号码不存在' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f1a IS NOT INITIAL.
      PERFORM frm_check_zfllx_px(zbcs0001) USING ls_data-f1a.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '该返利类型未支持促销返利协议类型，请检查！' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f1 IS NOT INITIAL AND  ls_data-f1a IS NOT INITIAL. "ERP-16863
      PERFORM frm_check_ht_fllx_yzx(zbcs0001) USING ls_data-f1
                                                    ls_data-f1a
                                           CHANGING lv_msgv1.
      IF lv_msgv1 IS NOT INITIAL.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq lv_msgv1 CHANGING lt_msglist.
      ENDIF.
      CLEAR:lv_msgv1.
    ENDIF.


    IF ls_data-f2 IS NOT INITIAL.
      PERFORM frm_check_ekgrp(zbcs0001) USING ls_data-f2.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '采购组不存在' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f3 IS NOT INITIAL.
      PERFORM frm_check_date(zbcs0001) USING ls_data-f3.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款开始日期错误' CHANGING lt_msglist.
      ENDIF.
      CONDENSE ls_data-f3.
      IF strlen( ls_data-f3 ) > 8.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款开始日期错误' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f4 IS NOT INITIAL.
      PERFORM frm_check_date(zbcs0001) USING ls_data-f4.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款结束日期错误' CHANGING lt_msglist.
      ENDIF.

      CONDENSE ls_data-f4.
      IF strlen( ls_data-f4 ) > 8.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款结束日期错误' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    PERFORM frm_check_date_begin_end(zbcs0001) USING ls_data-f3 ls_data-f4.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款开始日期不能大于结束日期' CHANGING lt_msglist.
    ENDIF.

    IF ls_data-f5 IS NOT INITIAL.
      PERFORM frm_check_zhszq(zbcs0001) USING ls_data-f5  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '核算周期错误' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f6 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f6 'ZREM_ZDFFS' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '兑付方式不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f7 IS NOT INITIAL.
      PERFORM frm_check_type_int(zbcs0001) USING ls_data-f7 '+' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '付款期间只能是数值，且是整数，是正数'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f8 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f8 'ZREM_ZHSTYPE' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '核算周期类型不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f10 IS NOT INITIAL.
      PERFORM frm_check_zclrid_p(zbcs0001) USING ls_data-f10  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '计算方法错误'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

*    IF LS_DATA-F12 IS NOT INITIAL.
*      PERFORM FRM_CHECK_MATNR(ZBCS0001) USING LS_DATA-F12  .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '商品编码-买 错误'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.
*
*    IF LS_DATA-F13 IS NOT INITIAL.
*      PERFORM FRM_CHECK_MATNR(ZBCS0001) USING LS_DATA-F13  .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '商品编码-赠 错误'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.

*    IF LS_DATA-F14 IS NOT INITIAL.
*      PERFORM FRM_CHECK_TYPE_DEC(ZBCS0001) USING LS_DATA-F14 '2' '+0' .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '加提单价只能是数值，且最多2位小数，是正数'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.

*    IF LS_DATA-F16 IS NOT INITIAL.
*      PERFORM FRM_CHECK_TYPE_INT(ZBCS0001) USING LS_DATA-F16 '+' .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '规则买1只能是数值，且是整数，是正数'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.
*
*    IF LS_DATA-F17 IS NOT INITIAL.
*      PERFORM FRM_CHECK_TYPE_INT(ZBCS0001) USING LS_DATA-F17 '+0' .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '规则赠1只能是数值，且是整数，是正数'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.
*


    IF ls_data-f18 IS NOT INITIAL.
      PERFORM frm_check_type_dec(zbcs0001) USING ls_data-f18 '2' '+0' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '返利值1只能是数值，且最多2位小数，是正数'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f19 IS NOT INITIAL.
      PERFORM frm_check_type_int(zbcs0001) USING ls_data-f19 '+' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '匹配比例1只能是数值，且是整数0~100内，是正数'  CHANGING lt_msglist.
      ELSE.
        DATA(lv_int_01) = 0.
        lv_int_01 = ls_data-f19.
        IF lv_int_01 > 100.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '匹配比例1只能是数值，且是整数0~100内，是正数'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.


*    IF LS_DATA-F21 IS NOT INITIAL.
*      PERFORM FRM_CHECK_TYPE_INT(ZBCS0001) USING LS_DATA-F21 '+' .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '规则买2只能是数值，且是整数，是正数'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.
*
*    IF LS_DATA-F22 IS NOT INITIAL.
*      PERFORM FRM_CHECK_TYPE_INT(ZBCS0001) USING LS_DATA-F22 '+0' .
*      IF SY-SUBRC NE 0.
*        PERFORM FRM_WRITE_MSG(ZBCS0001) USING  LS_DATA-SEQ '规则赠2只能是数值，且是整数，是正数'  CHANGING LT_MSGLIST.
*      ENDIF.
*    ENDIF.

    IF ls_data-f23 IS NOT INITIAL.
      PERFORM frm_check_type_dec(zbcs0001) USING ls_data-f23 '2' '+' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '返利值2只能是数值，且最多2位小数，是正数'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f24 IS NOT INITIAL.
      PERFORM frm_check_type_int(zbcs0001) USING ls_data-f24 '+' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '匹配比例2只能是数值，且是整数0~100内，是正数'  CHANGING lt_msglist.
      ELSE.
        DATA(lv_int_02) = 0.
        lv_int_02 = ls_data-f24.
        IF lv_int_02 > 100.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '匹配比例2只能是数值，且是整数0~100内，是正数'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.

    IF ( lv_int_01 + lv_int_02 ) > 100.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '匹配比例之和不能大于100'  CHANGING lt_msglist.
    ENDIF.


    IF ls_data-f26 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f26 'ZRED_CTGR' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '组织级别不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f27 IS NOT INITIAL.
      PERFORM frm_check_bukrs(zbcs0001) USING ls_data-f27  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '协议主体不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f28 IS NOT INITIAL.
      SELECT SINGLE zsfsx INTO @DATA(lv_zsfsx) FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx  WHERE zht_id = @ls_data-f1.
      IF sy-subrc = 0 AND lv_zsfsx = '2' .
        DATA:lv_partner TYPE but000-partner.
        lv_partner = ls_data-f28.
        lv_partner = |{ lv_partner ALPHA = IN }|.
        SELECT SINGLE partner INTO @DATA(lv_data) FROM but000 WHERE partner EQ @lv_partner .
        IF sy-subrc NE 0.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '收款方不存在'  CHANGING lt_msglist.
        ENDIF.
      ELSE.
        PERFORM frm_check_bukrs(zbcs0001) USING ls_data-f28  .
        IF sy-subrc NE 0.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '收款方不存在'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.

    ENDIF.

    IF ls_data-f36 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f36 'ZREM_ZZSBS' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '专属标识不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF  ls_data-f8c = '1000004716'.
      IF ls_data-f8e IS INITIAL .
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '核算厂家为 1000004716 , 核算厂家描述必输！'  CHANGING lt_msglist.
      ENDIF.
    ELSEIF ls_data-f8c IS NOT INITIAL .
      PERFORM frm_check_zhscj(zbcs0001) USING ls_data-f8c  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '核算厂家不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
    IF ls_data-f8e IS NOT INITIAL.
      IF  ls_data-f8c <> '1000004716'.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '核算厂家描述输入值！核算厂家必须为 1000004716 , '  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f32 IS INITIAL AND ls_data-f32a IS NOT INITIAL   .
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码为空时,公司类型列无需维护值'  CHANGING lt_msglist.
    ELSEIF ls_data-f32 IS NOT INITIAL AND  ls_data-f32a IS  INITIAL.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码不为空时,公司类型列需维护值'  CHANGING lt_msglist.
    ELSE.
      REFRESH:lt_split_table,lt_split_table_x.
      IF ls_data-f32 IS NOT INITIAL .
        SPLIT ls_data-f32  AT '/' INTO TABLE lt_split_table.
      ENDIF.

      IF ls_data-f32a IS NOT INITIAL .
        SPLIT ls_data-f32a AT '/' INTO TABLE lt_split_table_x.
      ENDIF.

      IF lines( lt_split_table ) <> lines( lt_split_table_x ) AND lines( lt_split_table_x ) > 1.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码列需和公司类型列一一对应(公司类型默认为1种类型除外)!'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    READ TABLE pt_werks TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
    IF sy-subrc NE 0.
      READ TABLE pt_bukrs TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司与门店两者不能同时为空'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

*  截止日期检查
    lv_date_begin = ls_data-f3.
    PERFORM frm_check_zbegin USING lv_date_begin
                             CHANGING lv_tmp_date.
    IF sy-subrc NE 0.
      CLEAR lv_msgv1. lv_msgv1 = '只允许创建' && lv_tmp_date && '之后的条款' .
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq lv_msgv1  CHANGING lt_msglist.
    ENDIF.

***----insert by jcwei 2022-4-26 ERP-13507 促销返利导入重复性校验
    "RB02  RB26  RB25 录入验重处理 20250901
    IF NOT ( ls_data-f1a = 'RB02'  OR ls_data-f1a = 'RB25' OR ls_data-f1a = 'RB26' )  .
      CLEAR:ls_data-f1a.
    ENDIF.
    CLEAR:lv_msgv1.
    READ TABLE it_double INTO wa_double
      WITH KEY f1a = ls_data-f1a
               f10 = ls_data-f10
               f12 = ls_data-f12
               f13 = ls_data-f13
               f25 = ls_data-f25
               f25a = ls_data-f25a
               f26 = ls_data-f26
               f27 = ls_data-f27
               f32 = ls_data-f32
               f33 = ls_data-f33
               f34 = ls_data-f34
               f36 = ls_data-f36 BINARY SEARCH.
    IF sy-subrc = 0.
      CONCATENATE wa_double-txt '行重复' INTO lv_msgv1.
      CONDENSE lv_msgv1.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq lv_msgv1  CHANGING lt_msglist.
    ENDIF.
***----end by jcwei 2022-4-26 ERP-13507 促销返利导入重复性校验
  ENDLOOP.

  SORT pt_t44.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING ALL FIELDS.
  LOOP AT pt_t44 INTO DATA(ls_t44).
    IF ls_t44-zflzff IS NOT INITIAL.
      PERFORM frm_check_lifnr(zbcs0001) USING ls_t44-zflzff  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '付款方不存在'  CHANGING lt_msglist.
      ENDIF.
      PERFORM frm_check_status_zflzff USING ls_t44-zflzff .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '外部支付方已被冻结或删除，请检查主数据。'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_t44-zpaytp IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_t44-zpaytp 'ZRED_ZPAYTP' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '付款方级别不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    READ TABLE pt_data INTO ls_data  WITH  KEY seq = ls_t44-seq.
    IF sy-subrc = 0 .
      CLEAR:lv_zsfsx.
      SELECT SINGLE zsfsx INTO lv_zsfsx FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx  WHERE zht_id = ls_data-f1.
    ENDIF.

    IF ls_t44-zflzff IS NOT INITIAL.
      DATA(lv_flg) = ''.
      PERFORM frm_get_zghf_attr USING ls_t44-zflzff CHANGING lv_flg.
      IF lv_flg EQ 'I'.
        IF ls_t44-zpaytp IS INITIAL AND lv_zsfsx <> '2' .
          PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '内部支付方的支付方级别不可是空'  CHANGING lt_msglist.
        ENDIF.
      ELSEIF lv_flg EQ 'E'.
        IF ls_t44-zpaytp EQ 'A' OR ls_t44-zpaytp EQ 'B'.
          PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '外部支付方，则支付方级别不可以是A或B'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_bukrs.
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING ALL FIELDS.
  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    IF ls_bukrs-bukrs IS NOT INITIAL AND ls_bukrs-bukrs NE 'ALL'.
      PERFORM frm_check_bukrs(zbcs0001) USING ls_bukrs-bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_bukrs-seq '公司代码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_bukrs-zmdsx IS NOT INITIAL.
      IF ls_bukrs-zmdsx <> '0' AND ls_bukrs-zmdsx <> '1' AND ls_bukrs-zmdsx <> '2' .
        PERFORM frm_coll_msg(zbcs0001) USING  ls_bukrs-seq '公司属性值只能维护为0/1/2'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING ALL FIELDS.
  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-werks IS NOT INITIAL AND ls_werks-werks NE 'ALL'.
      PERFORM frm_check_werks(zbcs0001) USING ls_werks-werks  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '门店不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
    IF ls_werks-exclude NE '' AND ls_werks-exclude NE 'X'..
      PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '排除门店只能是空和X'  CHANGING lt_msglist.
    ENDIF.

    LOOP AT pt_werks TRANSPORTING NO FIELDS WHERE seq = ls_werks-seq AND  werks = ls_werks-werks AND exclude NE ls_werks-exclude.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '门店不能既排除，又不排除'  CHANGING lt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_ekorg.
  DELETE ADJACENT DUPLICATES FROM pt_ekorg COMPARING ALL FIELDS.
  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    IF ls_ekorg-ekorg IS NOT INITIAL AND ls_ekorg-ekorg NE 'ALL'.
      PERFORM frm_check_ekorg(zbcs0001) USING ls_ekorg-ekorg  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_ekorg-seq '采购组织不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    IF ls_t81-zzlbm IS NOT INITIAL .
      PERFORM frm_check_zzlbm(zbcs0001) USING ls_t81-zzlbm  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    IF ls_t82-zqdbm IS NOT INITIAL .
      PERFORM frm_check_zqdbm(zbcs0001) USING ls_t82-zqdbm  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.


  SORT pt_data BY seg.
  SORT pt_ta02 BY seg.
  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    IF ls_ta06-zdates IS NOT INITIAL.
      PERFORM frm_check_date(zbcs0001) USING ls_ta06-zdates.
      IF sy-subrc NE 0.
        CLEAR ls_data.
        READ TABLE pt_data INTO ls_data WITH KEY seg = ls_ta06-seg BINARY SEARCH.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '促销日期错误' CHANGING lt_msglist.
      ENDIF.

      READ TABLE pt_ta02 INTO DATA(ls_ta02) WITH KEY seg = ls_ta06-seg   BINARY SEARCH.
      IF sy-subrc EQ 0.
        IF NOT ( ls_ta06-zdates >= ls_ta02-zbegin AND ls_ta06-zdates <= ls_ta02-zend ).
          CLEAR ls_data.
          READ TABLE pt_data INTO ls_data WITH KEY seg = ls_ta06-seg BINARY SEARCH.

          PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '促销日期必须在条款的开始日期和结束日期范围内' CHANGING lt_msglist.

        ENDIF.
      ENDIF.

    ENDIF.
  ENDLOOP.

*  新增检查 JIRA-ERP-13283
  PERFORM frm_check_zctgr_zpaytp USING pt_data pt_t44 pt_t06 'A' CHANGING lt_msglist.
  PERFORM frm_check_zctgr_zpaytp USING pt_data pt_t44 pt_t06 'B' CHANGING lt_msglist.



*  组织结构检查

  PERFORM frm_check_data_org_pd USING pt_data
                                   pt_bukrs
                                   pt_werks
                             CHANGING lt_msglist.

*  审批处理
  SORT pt_ta02.
  DELETE ADJACENT DUPLICATES FROM pt_ta02 COMPARING ALL FIELDS.
  LOOP AT pt_ta02 INTO ls_ta02.
    CLEAR ls_zreta002.
    MOVE-CORRESPONDING ls_ta02 TO ls_zreta002.
    CLEAR lt_msglist_tmp[].
    PERFORM frm_check_frgsx   CHANGING  ls_zreta002
                                        lt_msglist_tmp.

    PERFORM frm_add_msglist USING ls_ta02 pt_data lt_msglist_tmp CHANGING lt_msglist.

    MOVE-CORRESPONDING ls_zreta002 TO ls_ta02.
    MODIFY pt_ta02 FROM ls_ta02.
  ENDLOOP.

*  权限检查
  PERFORM frm_auth_check_ht USING pt_ta02  pt_data CHANGING lt_msglist.

  PERFORM frm_auth_check_tk USING pt_ta02  pt_data CHANGING lt_msglist.

  PERFORM frm_auth_check_xy USING pt_t06 pt_data CHANGING lt_msglist.

  PERFORM frm_conver_msglist_2_msg USING lt_msglist CHANGING pt_data.

ENDFORM.



FORM frm_data_check_02
                         USING
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                         CHANGING pt_data TYPE tt_data
                                  lt_msglist TYPE scp1_general_errors.


  DATA:
*    LT_MSGLIST     TYPE SCP1_GENERAL_ERRORS,
    lt_msglist_tmp TYPE scp1_general_errors.

*  DATA:
*    LV_MSG   TYPE BAPI_MSG,
*    LV_MTYPE TYPE BAPI_MTYPE.

  DATA:
    lv_date1 TYPE d,
    lv_date2 TYPE d.
  DATA:
    ls_zreta002   TYPE zreta002,
    lv_date_begin TYPE d,
    lv_tmp_date   TYPE d,
    lv_zcpctg_01  TYPE zreta005-zcpctg,
    lv_zcpctg_02  TYPE zreta005-zcpctg,
    lv_msgv1      TYPE scp1_general_error-msgv1.

  DATA:
    lt_split_table   TYPE TABLE OF string,
    lt_split_table_x TYPE TABLE OF string.

  SORT pt_werks BY seq.
  SORT pt_bukrs BY seq.

  LOOP AT pt_data INTO DATA(ls_data).

    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '条款号'  ls_data-ztk_id   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '组织级别' ls_data-f26   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '协议主体' ls_data-f27   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '收款方'   ls_data-f28   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '付款方'   ls_data-f30   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '子类'     ls_data-f38   CHANGING lt_msglist .
    PERFORM frm_check_null(zbcs0001) USING  ls_data-seq '渠道'     ls_data-f39   CHANGING lt_msglist .


    PERFORM frm_check_ztk_id(zbcs0001) USING ls_data-ztk_id.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '条款号码不存在' CHANGING lt_msglist.
    ENDIF.



    IF ls_data-f26 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f26 'ZRED_CTGR' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '组织级别不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f27 IS NOT INITIAL.
      PERFORM frm_check_bukrs(zbcs0001) USING ls_data-f27  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '协议主体不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.


    IF ls_data-f28 IS NOT INITIAL.
      SELECT SINGLE zsfsx INTO @DATA(lv_zsfsx) FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx  WHERE zht_id = @ls_data-f1.
      IF sy-subrc = 0 AND lv_zsfsx = '2' .
        DATA:lv_partner TYPE but000-partner.
        lv_partner = ls_data-f28.
        lv_partner = |{ lv_partner ALPHA = IN }|.
        SELECT SINGLE partner INTO @DATA(lv_data) FROM but000 WHERE partner EQ @lv_partner .
        IF sy-subrc NE 0.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '收款方不存在'  CHANGING lt_msglist.
        ENDIF.
      ELSE.
        PERFORM frm_check_bukrs(zbcs0001) USING ls_data-f28  .
        IF sy-subrc NE 0.
          PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '收款方不存在'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.

    IF ls_data-f36 IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_data-f36 'ZREM_ZZSBS' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '专属标识不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.


    READ TABLE pt_werks TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
    IF sy-subrc NE 0.
      READ TABLE pt_bukrs TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司与门店两者不能同时为空'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f32 IS INITIAL AND ls_data-f32a IS NOT INITIAL   .
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码为空时,公司类型列无需维护值'  CHANGING lt_msglist.
    ELSEIF ls_data-f32 IS NOT INITIAL AND  ls_data-f32a IS  INITIAL.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码不为空时,公司类型列需维护值'  CHANGING lt_msglist.
    ELSE.
      REFRESH:lt_split_table,lt_split_table_x.

      IF ls_data-f32 IS NOT INITIAL .
        SPLIT ls_data-f32  AT '/' INTO TABLE lt_split_table.
      ENDIF.

      IF ls_data-f32a IS NOT INITIAL .
        SPLIT ls_data-f32a AT '/' INTO TABLE lt_split_table_x.
      ENDIF.

      IF lines( lt_split_table ) <> lines( lt_split_table_x ) AND lines( lt_split_table_x ) > 1.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司代码列需和公司类型列一一对应(公司类型默认为1种类型除外)!'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_data-f33 IS INITIAL AND ls_data-f34 IS NOT INITIAL   .
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '门店为空时,排除门店列无需维护值'  CHANGING lt_msglist.
    ENDIF.

    READ TABLE pt_werks TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
    IF sy-subrc NE 0.
      READ TABLE pt_bukrs TRANSPORTING NO FIELDS WITH KEY seq = ls_data-seq BINARY SEARCH.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '公司与门店两者不能同时为空'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_t44.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING ALL FIELDS.
  LOOP AT pt_t44 INTO DATA(ls_t44).
    IF ls_t44-zflzff IS NOT INITIAL.
      PERFORM frm_check_lifnr(zbcs0001) USING ls_t44-zflzff  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '付款方不存在'  CHANGING lt_msglist.
      ENDIF.
      PERFORM frm_check_status_zflzff USING ls_t44-zflzff .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '外部支付方已被冻结或删除，请检查主数据。'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_t44-zpaytp IS NOT INITIAL.
      PERFORM frm_check_domname(zbcs0001) USING ls_t44-zpaytp 'ZRED_ZPAYTP' .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '付款方级别不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    READ TABLE pt_data INTO ls_data  WITH  KEY seq = ls_t44-seq.
    IF sy-subrc = 0 .
      CLEAR:lv_zsfsx.
      SELECT SINGLE zsfsx INTO lv_zsfsx FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx  WHERE zht_id = ls_data-f1.
    ENDIF.

    IF ls_t44-zflzff IS NOT INITIAL.
      DATA(lv_flg) = ''.
      PERFORM frm_get_zghf_attr USING ls_t44-zflzff CHANGING lv_flg.
      IF lv_flg EQ 'I'.
        IF ls_t44-zpaytp IS INITIAL AND lv_zsfsx <>'2' .
          PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '内部支付方的支付方级别不可是空'  CHANGING lt_msglist.
        ENDIF.
      ELSEIF lv_flg EQ 'E'.
        IF ls_t44-zpaytp EQ 'A' OR ls_t44-zpaytp EQ 'B'.
          PERFORM frm_write_msg(zbcs0001) USING  ls_t44-seq '外部支付方，则支付方级别不可以是A或B'  CHANGING lt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_bukrs.
  DELETE ADJACENT DUPLICATES FROM pt_bukrs COMPARING ALL FIELDS.
  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    IF ls_bukrs-bukrs IS NOT INITIAL AND ls_bukrs-bukrs NE 'ALL'.
      PERFORM frm_check_bukrs(zbcs0001) USING ls_bukrs-bukrs  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_bukrs-seq '公司代码不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF ls_bukrs-zmdsx IS NOT INITIAL.
      IF ls_bukrs-zmdsx <> '0' AND ls_bukrs-zmdsx <> '1' AND ls_bukrs-zmdsx <> '2' .
        PERFORM frm_coll_msg(zbcs0001) USING  ls_bukrs-seq '公司属性值只能维护为0/1/2'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.

  ENDLOOP.

  SORT pt_werks.
  DELETE ADJACENT DUPLICATES FROM pt_werks COMPARING ALL FIELDS.
  LOOP AT pt_werks INTO DATA(ls_werks).
    IF ls_werks-werks IS NOT INITIAL AND ls_werks-werks NE 'ALL'.
      PERFORM frm_check_werks(zbcs0001) USING ls_werks-werks  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '门店不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
    IF ls_werks-exclude NE '' AND ls_werks-exclude NE 'X'..
      PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '排除门店只能是空和X'  CHANGING lt_msglist.
    ENDIF.

    LOOP AT pt_werks TRANSPORTING NO FIELDS WHERE seq = ls_werks-seq AND  werks = ls_werks-werks AND exclude NE ls_werks-exclude.
      EXIT.
    ENDLOOP.
    IF sy-subrc EQ 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_werks-seq '门店不能既排除，又不排除'  CHANGING lt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_ekorg.
  DELETE ADJACENT DUPLICATES FROM pt_ekorg COMPARING ALL FIELDS.
  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    IF ls_ekorg-ekorg IS NOT INITIAL AND ls_ekorg-ekorg NE 'ALL'.
      PERFORM frm_check_ekorg(zbcs0001) USING ls_ekorg-ekorg  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_ekorg-seq '采购组织不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    IF ls_t81-zzlbm IS NOT INITIAL .
      PERFORM frm_check_zzlbm(zbcs0001) USING ls_t81-zzlbm  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t81-seq '子类不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    IF ls_t82-zqdbm IS NOT INITIAL .
      PERFORM frm_check_zqdbm(zbcs0001) USING ls_t82-zqdbm  .
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING  ls_t82-seq '渠道不存在'  CHANGING lt_msglist.
      ENDIF.
    ENDIF.
  ENDLOOP.

*  新增检查 JIRA-ERP-13283
  DATA(lt_t44_tmp) = pt_t44[].
  DATA(lt_t06_tmp) = pt_t06[].
*  将已存在的协议添加进去，合并后进行检查
  PERFORM frm_add_data_zxy USING pt_data CHANGING lt_t44_tmp lt_t06_tmp.

  PERFORM frm_check_zctgr_zpaytp USING pt_data lt_t44_tmp lt_t06_tmp 'A' CHANGING lt_msglist.
  PERFORM frm_check_zctgr_zpaytp USING pt_data lt_t44_tmp lt_t06_tmp 'B' CHANGING lt_msglist.



*  组织结构检查
  PERFORM frm_check_data_org_pd USING pt_data
                                   pt_bukrs
                                   pt_werks
                             CHANGING lt_msglist.

*  新增的协议，状态直接设置为A（审批通过）
  PERFORM frm_set_zxyzt USING pt_ta02   CHANGING pt_t06.


*  权限检查
  PERFORM frm_auth_check_ht USING pt_ta02  pt_data CHANGING lt_msglist.

  PERFORM frm_auth_check_tk USING pt_ta02  pt_data CHANGING lt_msglist.

  PERFORM frm_auth_check_xy USING pt_t06 pt_data CHANGING lt_msglist.

  PERFORM frm_conver_msglist_2_msg USING lt_msglist CHANGING pt_data.

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_EXCEL_PROCESS
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_GT_DATA_EXCEL  TEXT
*      <--P_GT_DATA  TEXT
*----------------------------------------------------------------------*
FORM frm_excel_data_get_01  USING    pt_data_excel_01 TYPE tt_data_excel_01
                        CHANGING pt_data TYPE tt_data.


  DATA:
    ls_data       TYPE LINE OF tt_data.

  LOOP AT pt_data_excel_01 INTO DATA(ls_data_excel).
    CLEAR ls_data.
    ls_data-seq = sy-tabix.
    MOVE-CORRESPONDING ls_data_excel TO ls_data.
    APPEND ls_data TO pt_data.
  ENDLOOP.

*  按抬头分组
  PERFORM frm_set_seg_01 CHANGING pt_data.

*  分组后 每个条款行项目不得超过750 行
  SELECT
    i~seg,
    COUNT(*) AS zcount
    FROM @pt_data AS i
    GROUP BY i~seg
    INTO TABLE @DATA(lt_data_count).

  LOOP AT lt_data_count TRANSPORTING NO FIELDS WHERE zcount > 750.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    MESSAGE s888(sabapdocu) WITH '单个条款的行项目不能超过750行' DISPLAY LIKE 'E' .
    LEAVE LIST-PROCESSING.
  ENDIF.

  DATA:BEGIN OF ls_key ,
         f1   TYPE zreta001-zht_id,
         zkey TYPE string,
       END OF ls_key,
       lt_key LIKE TABLE OF ls_key.

  MOVE-CORRESPONDING pt_data TO lt_key.

  SORT lt_key BY zkey.

  DELETE ADJACENT DUPLICATES FROM lt_key COMPARING zkey.
  SELECT
    i~f1 AS  zht_id,
    COUNT(*) AS zcount
    FROM @lt_key AS i
    GROUP BY i~f1
    INTO TABLE @DATA(lt_tk_count).

  LOOP AT lt_tk_count INTO DATA(ls_tk_count).
    SELECT MAX( zitem_id ) INTO @DATA(lv_max_id) FROM zreta002 WHERE zht_id = @ls_tk_count-zht_id.
    IF ls_tk_count-zcount + lv_max_id > 9999.
      MESSAGE s888(sabapdocu) WITH |合同{ ls_tk_count-zht_id }下条款数据异常,条款数超过9999个| DISPLAY LIKE 'E' .
      LEAVE LIST-PROCESSING.
    ENDIF.
    CLEAR:lv_max_id.
  ENDLOOP.


  SORT pt_data BY seq.
ENDFORM.

FORM frm_set_seg_01 CHANGING pt_data TYPE tt_data.

  DATA:lv_int TYPE i.
*@锚点TK
  LOOP AT pt_data INTO DATA(ls_data).
    ls_data-zkey =
    ls_data-f1  &&
    ls_data-f1a  &&
    ls_data-f2  &&
    ls_data-f3  &&
    ls_data-f4  &&
    ls_data-f5  &&
    ls_data-f6  &&
    ls_data-f7  &&
    ls_data-f8  &&
    ls_data-f8a  &&
    ls_data-f8b  &&
    ls_data-f8c  &&
    ls_data-f8d  &&
    ls_data-f9  &&
    ls_data-f9a &&
    ls_data-f9b &&
    ls_data-f9c &&
    ls_data-f9d &&
    ls_data-f9e &&
    ls_data-f10 &&
    ls_data-f11 &&
    ls_data-f12 &&
    ls_data-f13 &&
    ls_data-f14 &&
    ls_data-f15 &&
    ls_data-f16 &&
    ls_data-f17 &&
    ls_data-f18 &&
    ls_data-f19 &&
    ls_data-f20 &&
    ls_data-f21 &&
    ls_data-f22 &&
    ls_data-f23 &&
    ls_data-f24 &&
    ls_data-f25 &&
    ls_data-f25a .
    MODIFY pt_data FROM ls_data.
  ENDLOOP.

*    按照ZKEY字段进行分组
  SORT pt_data BY zkey.
  CLEAR lv_int.
  LOOP AT pt_data INTO ls_data.
    AT NEW zkey.
      lv_int = lv_int + 1.
    ENDAT.

    ls_data-seg = lv_int .

    MODIFY pt_data FROM ls_data.
  ENDLOOP.



ENDFORM.

FORM frm_set_seg_02 CHANGING pt_data TYPE tt_data.
  DATA:
        lv_int TYPE i.

  LOOP AT pt_data INTO DATA(ls_data).
    ls_data-zkey =
    ls_data-ztk_id .
    MODIFY pt_data FROM ls_data.
  ENDLOOP.

*    按照ZKEY字段进行分组
  SORT pt_data BY zkey.
  CLEAR lv_int.
  LOOP AT pt_data INTO ls_data.
    AT NEW zkey.
      lv_int = lv_int + 1.
    ENDAT.

    ls_data-seg = lv_int .

    MODIFY pt_data FROM ls_data.
  ENDLOOP.



ENDFORM.

FORM frm_conv_data_main_01 USING pt_data TYPE tt_data
                         CHANGING
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_tc05   TYPE tt_tc05
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                  lt_msglist     TYPE scp1_general_errors.

  LOOP AT pt_data INTO DATA(ls_data).

    PERFORM frm_pro_data_ls_data_pre CHANGING ls_data.
    MODIFY pt_data FROM ls_data.

*    条款信息
    PERFORM frm_conv_data_ta02_01 USING ls_data CHANGING pt_ta02 pt_ta06 pt_tc05 lt_msglist.

*    将商品编码及买赠按照 / 拆分
    PERFORM frm_conv_data_spz_pre USING ls_data CHANGING
                                                      pt_t09
                                                      pt_t20
                                                      pt_ta05
                                                      pt_t58
                                                      lt_msglist.

*    协议信息
    PERFORM frm_conv_data_t06_01 USING ls_data CHANGING
                                                      pt_ta02
                                                      pt_t06.

*    支付方
    PERFORM frm_conv_data_t44 USING ls_data CHANGING pt_t44 .

*    组织结构
    PERFORM frm_conv_data_org USING ls_data CHANGING pt_bukrs pt_werks pt_ekorg.

*    子类渠道
    PERFORM frm_conv_data_zlqd USING ls_data CHANGING pt_t81 pt_t82.

***----insert by jcwei 2022-4-26 ERP-13507
    MOVE-CORRESPONDING ls_data TO wa_double.
    "RB02  RB26  RB25 录入验重处理 20250901
    IF NOT ( ls_data-f1a = 'RB02'  OR ls_data-f1a = 'RB25' OR ls_data-f1a = 'RB26' )  .
      CLEAR:ls_data-f1a.
    ENDIF.
    wa_double-num = 1.
    COLLECT wa_double INTO it_double.
    CLEAR wa_double.
***----end    by jcwei 2022-4-26 ERP-13507
  ENDLOOP.

  DELETE it_double WHERE num = 1.  "删除无重复数据
  SORT it_double BY f1a f10 f12 f13 f25 f26 f27 f32 f33 f34 f36.

  DATA:lv_char TYPE char10.
  LOOP AT pt_data INTO ls_data.
    lv_char = ls_data-seq.
    "RB02  RB26  RB25 录入验重处理 20250901
    IF NOT ( ls_data-f1a = 'RB02'  OR ls_data-f1a = 'RB25' OR ls_data-f1a = 'RB26' )  .
      CLEAR:ls_data-f1a.
    ENDIF.
    READ TABLE it_double ASSIGNING FIELD-SYMBOL(<fs_double>)
      WITH KEY f1a  = ls_data-f1a
               f10  = ls_data-f10
               f12  = ls_data-f12
               f13  = ls_data-f13
               f25  = ls_data-f25
               f25a = ls_data-f25a
               f26  = ls_data-f26
               f27  = ls_data-f27
               f32  = ls_data-f32
               f33  = ls_data-f33
               f34  = ls_data-f34
               f36  = ls_data-f36 BINARY SEARCH.
    IF sy-subrc = 0.
      wa_double-txt = <fs_double>-txt.
      IF wa_double-txt IS INITIAL.
        CONCATENATE '第' lv_char INTO wa_double-txt.
      ELSE.
        CONCATENATE wa_double-txt '、' lv_char INTO wa_double-txt.
      ENDIF.
      CONDENSE wa_double-txt.
      <fs_double>-txt = wa_double-txt.
    ENDIF.
  ENDLOOP.
ENDFORM.

FORM frm_conv_data_main_02 USING pt_data TYPE tt_data
                         CHANGING
                                  pt_ta02    TYPE tt_ta02
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                  lt_msglist     TYPE scp1_general_errors.

  LOOP AT pt_data INTO DATA(ls_data).

    PERFORM frm_pro_data_ls_data_pre CHANGING ls_data.
    MODIFY pt_data FROM ls_data.

*    协议信息
    PERFORM frm_conv_data_t06_02 USING ls_data CHANGING
                                                      pt_t06.
*    支付方
    PERFORM frm_conv_data_t44 USING ls_data CHANGING pt_t44 .

*    组织结构
    PERFORM frm_conv_data_org USING ls_data CHANGING pt_bukrs pt_werks pt_ekorg.

*    子类渠道
    PERFORM frm_conv_data_zlqd USING ls_data CHANGING pt_t81 pt_t82.
  ENDLOOP.

*  条款数据
  PERFORM frm_conv_data_ta02_02 USING pt_data CHANGING pt_ta02 .

ENDFORM.

FORM frm_pro_data_ls_data_pre CHANGING ls_data TYPE LINE OF tt_data.

  IF ls_data-f32 IS INITIAL.
    SELECT
      *
      FROM zretcm05
      WHERE zbukrs = @ls_data-f27
      AND   zctgr  = @ls_data-f26
      AND   zxybstyp = 'P'
      INTO TABLE @DATA(lt_zretcm05).

    PERFORM frm_get_bukrs_x TABLES lt_zretcm05 CHANGING ls_data-f32.

*    LOOP AT LT_ZRETCM05 INTO DATA(LS_ZRETCM05).
*      IF LS_DATA-F32 IS INITIAL.
*        LS_DATA-F32 = LS_ZRETCM05-BUKRS .
*      ELSE.
*        LS_DATA-F32 = LS_DATA-F32 && '/' && LS_ZRETCM05-BUKRS.
*      ENDIF.
*    ENDLOOP.

  ENDIF.
ENDFORM.


FORM frm_conv_data_ta02_01  USING ls_data TYPE LINE OF tt_data
                         CHANGING pt_ta02 TYPE tt_ta02
                                  pt_ta06 TYPE tt_ta06
                                  pt_tc05 TYPE tt_tc05
                                  lt_msglist     TYPE scp1_general_errors.

*@锚点TK
*  条款
  DATA:ls_ta02 TYPE LINE OF tt_ta02.
*  分组
  ls_ta02-seg = ls_data-seg.
  ls_ta02-zht_id    = ls_data-f1 .   "   合同号
  ls_ta02-zfllx    = ls_data-f1a .   "   返利类型
  ls_ta02-zht_id = |{ ls_ta02-zht_id ALPHA = IN }|.
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_ta02-zht_id INTO @DATA(ls_ta01).

  ls_ta02-ekgrp     = ls_data-f2 .   "   条款采购组
  ls_ta02-zbegin    = ls_data-f3 .   "   条款开始日期
  ls_ta02-zend      = ls_data-f4 .   "   条款结束日期
  ls_ta02-zhszq     = ls_data-f5 .   "   核算周期
  ls_ta02-zdffs     = ls_data-f6 .   "   兑付方式
*  LS_TA02-ZPAYDAY   = LS_DATA-F7 .
  PERFORM frm_assign_value(zbcs0001) USING ls_data-f7 CHANGING ls_ta02-zpayday. "   付款期间(天)
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '付款期间错误' CHANGING lt_msglist.
  ENDIF.
  ls_ta02-zhstype = ls_data-f8 .   "   周期类型
  ls_ta02-zcgjl   = ls_data-f8a .   "
  ls_ta02-zcgzj   = ls_data-f8b .   "
  ls_ta02-zhscj   = ls_data-f8c .   "
  ls_ta02-zaccer  = ls_data-f8e .   "
  ls_ta02-zhscj   = |{ ls_ta02-zhscj ALPHA = IN }|.
  ls_ta02-zfldfsj = ls_data-f8d . "
  PERFORM frm_get_data_from_ht CHANGING  ls_data ls_ta02.


  ls_ta02-ztk_txt = ls_data-f9 .   "  条款描述
  ls_ta02-zlcbh   = ls_data-f9a .  "  OA流程编码
  ls_ta02-zpffl   = ls_data-f9b .  "  批发返利
  ls_ta02-zsqbm   = ls_data-f9c .  "  收取部门
  ls_ta02-zptbm   = ls_data-f9d .  "  平台编码
  ls_ta02-zzjzr   = ls_data-f9e .  "  收款截止日
  ls_ta02-zclrid  = ls_data-f10.    "  计算方法
  ls_ta02-zclrid  = |{ ls_ta02-zclrid ALPHA = IN }|.
  ls_ta02-zleib   = 'C' .   "

*  默认值
*  LS_TA02-ZFLLX = 'RB02'.
  ls_ta02-zxybstyp = 'P'.
  ls_ta02-ztktype = ''.
  ls_ta02-zjszq = '12M'.
  APPEND ls_ta02 TO pt_ta02.



*  促销日期
  DATA:
    ls_ta06     TYPE LINE OF tt_ta06,
    lt_ta06     TYPE tt_ta06,
    ls_ta06_del TYPE LINE OF tt_ta06,
    lt_ta06_del TYPE tt_ta06.

  DATA: lt_split_table     TYPE TABLE OF string,
        lt_split_table_del TYPE TABLE OF string,
        lv_date1           TYPE d,
        lv_date2           TYPE d,
        lv_str1            TYPE string,
        lv_str2            TYPE string.

  SPLIT ls_data-f25 AT '/' INTO TABLE lt_split_table.
  SPLIT ls_data-f25a AT '/' INTO TABLE lt_split_table_del.

*  时间处理
  LOOP AT lt_split_table INTO DATA(ls_split_table).

    CLEAR: lv_date1,lv_date2,ls_ta06,lv_str1,lv_str2.
    ls_ta06-seg = ls_data-seg.

    SEARCH ls_split_table FOR '-'.
    IF sy-subrc = 0.
      SPLIT ls_split_table AT '-' INTO lv_str1 lv_str2.

      CONDENSE lv_str1.
      IF strlen( lv_str1 ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      CONDENSE lv_str2.
      IF strlen( lv_str2 ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      lv_date1 = lv_str1.
      lv_date2 = lv_str2.

      WHILE lv_date1 <= lv_date2.
        ls_ta06-zdates = lv_date1.
        APPEND ls_ta06 TO lt_ta06.
        lv_date1 = lv_date1 + 1.
      ENDWHILE.
    ELSE.

      CONDENSE ls_split_table.
      IF strlen( ls_split_table ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      ls_ta06-zdates = ls_split_table.
      APPEND ls_ta06 TO lt_ta06.
    ENDIF.
  ENDLOOP.

*删除时间处理
  LOOP AT lt_split_table_del INTO DATA(ls_split_table_del).

    CLEAR: lv_date1,lv_date2,ls_ta06_del,lv_str1,lv_str2.
    ls_ta06_del-seg = ls_data-seg.

    SEARCH ls_split_table_del FOR '-'.
    IF sy-subrc = 0.
      SPLIT ls_split_table_del AT '-' INTO lv_str1 lv_str2.

      CONDENSE lv_str1.
      IF strlen( lv_str1 ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '剔除促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      CONDENSE lv_str2.
      IF strlen( lv_str2 ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '剔除促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      lv_date1 = lv_str1.
      lv_date2 = lv_str2.

      WHILE lv_date1 <= lv_date2.
        ls_ta06_del-zdates = lv_date1.
        APPEND ls_ta06_del TO lt_ta06_del.
        lv_date1 = lv_date1 + 1.
      ENDWHILE.
    ELSE.

      CONDENSE ls_split_table_del.
      IF strlen( ls_split_table_del ) > 8.
        PERFORM frm_coll_msg(zbcs0001) USING ls_data-seq '剔除促销日期日期错误' CHANGING lt_msglist.
      ENDIF.

      ls_ta06_del-zdates = ls_split_table_del.
      APPEND ls_ta06_del TO lt_ta06_del.
    ENDIF.
  ENDLOOP.

  LOOP AT lt_ta06 INTO ls_ta06  .
    READ TABLE lt_ta06_del TRANSPORTING NO FIELDS WITH  KEY zdates  = ls_ta06-zdates.
    IF sy-subrc = 0.
      DELETE lt_ta06.
    ENDIF.
  ENDLOOP.

  APPEND LINES OF lt_ta06 TO pt_ta06.


*  规则
  DATA:
        ls_tc05 TYPE LINE OF tt_tc05.

*  分组
  ls_tc05-seg = ls_data-seg.
  ls_tc05-zclrid = ls_ta02-zclrid.
  APPEND ls_tc05 TO pt_tc05.


ENDFORM.

FORM frm_conv_data_ta02_02  USING pt_data TYPE  tt_data
                         CHANGING pt_ta02 TYPE tt_ta02.
  DATA:
        ls_ta02 TYPE LINE OF tt_ta02.
  LOOP AT pt_data INTO DATA(ls_data).
    CLEAR ls_ta02.
    SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_data-ztk_id INTO CORRESPONDING FIELDS OF @ls_ta02.
    ls_ta02-seg = ls_data-seg.
*    ls_ta02-seq = ls_data-seq.
    APPEND ls_ta02 TO pt_ta02.
  ENDLOOP.

ENDFORM.

FORM frm_get_data_from_ht CHANGING ls_data TYPE LINE OF tt_data
                                    ls_ta02 TYPE LINE OF  tt_ta02.

  DATA:
        lv_zht_id TYPE zreta001-zht_id.


  lv_zht_id    = ls_data-f1 .   "   合同号
  lv_zht_id = |{ lv_zht_id ALPHA = IN }|.
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @lv_zht_id INTO @DATA(ls_ta01).

  IF ls_data-f2 IS INITIAL.
    ls_ta02-ekgrp = ls_ta01-ekgrp.
  ENDIF.
  IF ls_data-f3 IS INITIAL.
    ls_ta02-zbegin = ls_ta01-zbegin.
  ENDIF.
  IF ls_data-f4 IS INITIAL.
    ls_ta02-zend = ls_ta01-zend.
  ENDIF.
  IF ls_data-f5 IS INITIAL.
    ls_ta02-zhszq = ls_ta01-zhszq.
  ENDIF.
  IF ls_data-f6 IS INITIAL.
    ls_ta02-zdffs = ls_ta01-zdffs.
  ENDIF.
  IF ls_data-f7 IS INITIAL.
    ls_ta02-zpayday = ls_ta01-zpayday.
  ENDIF.
  IF ls_data-f8 IS INITIAL.
    ls_ta02-zhstype = ls_ta01-zhstype.
  ENDIF.

ENDFORM.

FORM frm_get_data_from_ht_02 CHANGING ls_data TYPE LINE OF tt_data.

  DATA:
        lv_zht_id TYPE zreta001-zht_id.


  lv_zht_id    = ls_data-f1 .   "   合同号
  lv_zht_id = |{ lv_zht_id ALPHA = IN }|.
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @lv_zht_id INTO @DATA(ls_ta01).

  IF ls_data-f2 IS INITIAL.
    ls_data-f2 = ls_ta01-ekgrp.
  ENDIF.
  IF ls_data-f3 IS INITIAL.
    ls_data-f3 = ls_ta01-zbegin.
  ENDIF.
  IF ls_data-f4 IS INITIAL.
    ls_data-f4 = ls_ta01-zend.
  ENDIF.
  IF ls_data-f5 IS INITIAL.
    ls_data-f5 = ls_ta01-zhszq.
  ENDIF.
  IF ls_data-f6 IS INITIAL.
    ls_data-f6 = ls_ta01-zdffs.
  ENDIF.
  IF ls_data-f7 IS INITIAL.
    ls_data-f7 = ls_ta01-zpayday.
  ENDIF.
  IF ls_data-f8 IS INITIAL.
    ls_data-f8 = ls_ta01-zhstype.
  ENDIF.

ENDFORM.

FORM frm_conv_data_spz  USING ls_data TYPE LINE OF tt_data
                         CHANGING pt_t09 TYPE tt_t09
                                  pt_t20 TYPE tt_t20
                                  pt_ta05 TYPE tt_ta05
                                  pt_t58 TYPE tt_t58
  lt_msglist     TYPE scp1_general_errors
  .

  DATA:
    ls_t09 TYPE LINE OF tt_t09.

  ls_t09-seg = ls_data-seg.
  ls_t09-zspzid_txt = ls_data-f11. "   商品组描述
  ls_t09-zht_id = ls_data-f1. "   合同号
  ls_t09-zusage = 'P'.
  APPEND ls_t09 TO pt_t09.

  IF ls_data-f12 IS NOT INITIAL.
    PERFORM frm_check_matnr(zbcs0001) USING ls_data-f12  .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '商品编码-买 错误'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f13 IS NOT INITIAL.
    PERFORM frm_check_matnr(zbcs0001) USING ls_data-f13  .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '商品编码-赠 错误'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f14 IS NOT INITIAL.
    PERFORM frm_check_type_dec(zbcs0001) USING ls_data-f14 '2' '+0' .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '加提单价只能是数值，且最多2位小数，是正数'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f16 IS NOT INITIAL.
    PERFORM frm_check_type_int(zbcs0001) USING ls_data-f16 '+' .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '规则买1只能是数值，且是整数，是正数'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f17 IS NOT INITIAL.
    PERFORM frm_check_type_int(zbcs0001) USING ls_data-f17 '+0' .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '规则赠1只能是数值，且是整数，是正数'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f21 IS NOT INITIAL.
    PERFORM frm_check_type_int(zbcs0001) USING ls_data-f21 '+' .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '规则买2只能是数值，且是整数，是正数'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

  IF ls_data-f22 IS NOT INITIAL.
    PERFORM frm_check_type_int(zbcs0001) USING ls_data-f22 '+0' .
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING  ls_data-seq '规则赠2只能是数值，且是整数，是正数'  CHANGING lt_msglist.
    ENDIF.
  ENDIF.

*  若买和赠是同一个商品则一行，否则去重会有问题
  IF ls_data-f12 = ls_data-f13.

    PERFORM frm_bin_data_t20 USING ls_data-seq
                                   ls_data-seg
                                   ls_data-f12
                                   ls_data-f14
                                   ls_data-f16
                                   ls_data-f17
                             CHANGING pt_t20
                                      lt_msglist.

  ELSE.
*  买

    PERFORM frm_bin_data_t20 USING ls_data-seq
                                   ls_data-seg
                                   ls_data-f12
                                   ls_data-f14
                                   ls_data-f16
                                   0
                             CHANGING pt_t20
                                      lt_msglist.


*  赠

    PERFORM frm_bin_data_t20 USING ls_data-seq
                                   ls_data-seg
                                   ls_data-f13
                                   0
                                   0
                                   ls_data-f17
                             CHANGING pt_t20
                                      lt_msglist.

  ENDIF.

*  规则-1
  PERFORM frm_conv_data_58 USING ls_data-seq
                                 ls_data-seg    "
                                 '1'            "
                                 ls_data-f15    " 规则描述
                                 ls_data-f18    " 返利值
                                 ls_data-f19    " 匹配比例
                                 ls_data-f12    " 买-商品
                                 ls_data-f13    " 赠-商品
                                 ls_data-f16    " 买
                                 ls_data-f17    " 赠
                           CHANGING pt_ta05     "
                                    pt_t58
                                    lt_msglist.     "

*  规则-2
  IF ls_data-f20 IS NOT INITIAL.
    PERFORM frm_conv_data_58 USING
                                   ls_data-seq
                                   ls_data-seg
                                   '2'
                                   ls_data-f20
                                   ls_data-f23
                                   ls_data-f24
                                   ls_data-f12
                                   ls_data-f13
                                   ls_data-f21
                                   ls_data-f22
                             CHANGING pt_ta05
                                      pt_t58
                                      lt_msglist.
  ENDIF.



ENDFORM.


FORM frm_conv_data_spz_pre  USING ls_data TYPE LINE OF tt_data
                         CHANGING pt_t09 TYPE tt_t09
                                  pt_t20 TYPE tt_t20
                                  pt_ta05 TYPE tt_ta05
                                  pt_t58 TYPE tt_t58
  lt_msglist     TYPE scp1_general_errors
  .


  DATA:
    lt_split_table_f12 TYPE TABLE OF string,
    lt_split_table_f13 TYPE TABLE OF string,
    lt_split_table_f14 TYPE TABLE OF string,
    lt_split_table_f16 TYPE TABLE OF string,
    lt_split_table_f17 TYPE TABLE OF string,
    lt_split_table_f21 TYPE TABLE OF string,
    lt_split_table_f22 TYPE TABLE OF string.

  DATA(ls_data_tmp) = ls_data.

  SPLIT ls_data-f12 AT '/' INTO TABLE lt_split_table_f12. "商品编码-买
  SPLIT ls_data-f13 AT '/' INTO TABLE lt_split_table_f13. "商品编码-赠
  SPLIT ls_data-f14 AT '/' INTO TABLE lt_split_table_f14. "加提单价
  SPLIT ls_data-f16 AT '/' INTO TABLE lt_split_table_f16. "规则买1
  SPLIT ls_data-f17 AT '/' INTO TABLE lt_split_table_f17. "规则赠1
  SPLIT ls_data-f21 AT '/' INTO TABLE lt_split_table_f21. "规则买2
  SPLIT ls_data-f22 AT '/' INTO TABLE lt_split_table_f22. "规则赠2

  LOOP AT lt_split_table_f12 INTO DATA(ls_split_table_f12).
    DATA(lv_index) = sy-tabix.
    READ TABLE lt_split_table_f13 INTO DATA(ls_split_table_f13) INDEX lv_index.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
    ENDIF.

    READ TABLE lt_split_table_f14 INTO DATA(ls_split_table_f14) INDEX lv_index.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
    ENDIF.

    READ TABLE lt_split_table_f16 INTO DATA(ls_split_table_f16) INDEX lv_index.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
    ENDIF.
    READ TABLE lt_split_table_f17 INTO DATA(ls_split_table_f17) INDEX lv_index.
    IF sy-subrc NE 0.
      PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
    ENDIF.

    IF lt_split_table_f21[] IS NOT INITIAL.
      READ TABLE lt_split_table_f21 INTO DATA(ls_split_table_f21) INDEX lv_index.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
      ENDIF.
    ENDIF.

    IF lt_split_table_f22[] IS NOT INITIAL.
      READ TABLE lt_split_table_f22 INTO DATA(ls_split_table_f22) INDEX lv_index.
      IF sy-subrc NE 0.
        PERFORM frm_write_msg(zbcs0001) USING ls_data-seq '【买】与【赠】的拼接不匹配' CHANGING lt_msglist.
      ENDIF.
    ENDIF.


*    替换对象，方便直接调用程序处理
    ls_data_tmp-f12 = ls_split_table_f12.
    ls_data_tmp-f13 = ls_split_table_f13.
    ls_data_tmp-f14 = ls_split_table_f14.
    ls_data_tmp-f16 = ls_split_table_f16.
    ls_data_tmp-f17 = ls_split_table_f17.
    ls_data_tmp-f21 = ls_split_table_f21.
    ls_data_tmp-f22 = ls_split_table_f22.

    PERFORM frm_conv_data_spz USING ls_data_tmp CHANGING
                                                      pt_t09
                                                      pt_t20
                                                      pt_ta05
                                                      pt_t58
                                                      lt_msglist.
    CLEAR:
        lv_index,
        ls_split_table_f13  ,
        ls_split_table_f14  ,
        ls_split_table_f16  ,
        ls_split_table_f17  ,
        ls_split_table_f21  ,
        ls_split_table_f22  .

  ENDLOOP.

ENDFORM.

FORM frm_conv_data_58 USING
                                pv_seq
                                pv_seg
                                pv_zitems
                                pv_z_text
                                pv_zrbamt
                                pv_zcpctg
                                pv_matnr_buy
                                pv_matnr_free
                                pv_zbuy
                                pv_zfree
                         CHANGING
                                  pt_ta05 TYPE tt_ta05
                                  pt_t58 TYPE tt_t58
                                  lt_msglist     TYPE scp1_general_errors.


  DATA:
    ls_ta05 TYPE LINE OF tt_ta05,
    ls_t58  TYPE LINE OF tt_t58.




*  规则-抬头
  ls_ta05-seg = pv_seg.
  ls_ta05-zitems = pv_zitems.
  ls_ta05-z_text = pv_z_text.

  PERFORM frm_assign_value(zbcs0001) USING pv_zrbamt CHANGING ls_ta05-zrbamt.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '返利金额错误' CHANGING lt_msglist.
  ENDIF.

  PERFORM frm_assign_value(zbcs0001) USING pv_zcpctg CHANGING ls_ta05-zcpctg.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '匹配比例错误' CHANGING lt_msglist.
  ENDIF.

  APPEND ls_ta05 TO pt_ta05.

  IF pv_matnr_buy = pv_matnr_free.

    PERFORM frm_bin_data_t58 USING pv_seq
                                   pv_seg
                                   pv_zitems
                                   pv_matnr_buy
                                   pv_zbuy
                                   pv_zfree
                             CHANGING pt_t58
                                      lt_msglist.

  ELSE.

*  买-规则1

    PERFORM frm_bin_data_t58 USING pv_seq
                                   pv_seg
                                   pv_zitems
                                   pv_matnr_buy
                                   pv_zbuy
                                   0
                             CHANGING pt_t58
                                      lt_msglist.


*  赠-规则1

    PERFORM frm_bin_data_t58 USING pv_seq
                                   pv_seg
                                   pv_zitems
                                   pv_matnr_free
                                   0
                                   pv_zfree
                             CHANGING pt_t58
                                      lt_msglist.


  ENDIF.




ENDFORM.



FORM frm_conv_data_t06_01  USING ls_data TYPE LINE OF tt_data
                         CHANGING
                              pt_ta02 TYPE tt_ta02
                              pt_t06 TYPE tt_t06.

*@锚点XY
  DATA: ls_t06 TYPE LINE OF tt_t06.
*  获取条款的数据
  SORT pt_ta02 BY seg.
  READ TABLE pt_ta02 INTO DATA(ls_ta02) WITH KEY seg = ls_data-seg BINARY SEARCH.
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_ta02-zht_id INTO @DATA(ls_ta01).

  ls_t06-seq     = ls_data-seq.
  ls_t06-seg     = ls_data-seg.
  ls_t06-zfllx   = ls_data-f1a.
  ls_t06-zctgr   = ls_data-f26.
  ls_t06-zbukrs  = ls_data-f27.
  ls_t06-zflsqf  = ls_data-f28.
  ls_t06-zxy_txt = ls_data-f29.
  ls_t06-zzsbs   = ls_data-f36.
  ls_t06-ztxhyq  = ls_data-f37.
  IF ls_t06-zxy_txt IS INITIAL.
    SELECT SINGLE butxt INTO ls_t06-zxy_txt FROM t001 WHERE bukrs = ls_t06-zbukrs.
  ENDIF.

*  默认值
*  LS_T06-ZFLLX = 'RB02'.
  ls_t06-zxybstyp = 'P'.
  ls_t06-ztktype = ''.
  ls_t06-zjszq = '12M'.
  ls_t06-zbegin = ls_ta02-zbegin.
  ls_t06-zend = ls_ta02-zend.
  ls_t06-zhszq = ls_ta02-zhszq.
  ls_t06-zhstype = ls_ta02-zhstype.
  ls_t06-zhtlx = ls_ta01-zhtlx.
  ls_t06-zxyzt = 'N'.
  APPEND ls_t06 TO pt_t06.


  CLEAR ls_ta02.

ENDFORM.


FORM frm_conv_data_t06_02  USING ls_data TYPE LINE OF tt_data
                         CHANGING
                              pt_t06 TYPE tt_t06.


  DATA:
    ls_ta02 TYPE LINE OF tt_ta02,
    ls_t06  TYPE LINE OF tt_t06.


*@锚点 XY
*  获取条款的数据
  SELECT SINGLE * FROM zreta002 WHERE ztk_id = @ls_data-ztk_id INTO CORRESPONDING FIELDS OF @ls_ta02.
  SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_ta02-zht_id INTO @DATA(ls_ta01).


  ls_t06-seq     = ls_data-seq.
  ls_t06-seg     = ls_data-seg.
  ls_t06-zfllx   = ls_ta02-zfllx.
  ls_t06-zctgr   = ls_data-f26.
  ls_t06-zbukrs  = ls_data-f27.
  ls_t06-zflsqf  = ls_data-f28.
  ls_t06-zxy_txt = ls_data-f29.
  ls_t06-zzsbs   = ls_data-f36.
  ls_t06-ztxhyq  = ls_data-f37.
  IF ls_t06-zxy_txt IS INITIAL.
    SELECT SINGLE butxt INTO ls_t06-zxy_txt FROM t001 WHERE bukrs = ls_t06-zbukrs.
  ENDIF.

*  默认值
*  LS_T06-ZFLLX = 'RB02'.
  ls_t06-zxybstyp = 'P'.
  ls_t06-ztktype  = ''.
  ls_t06-zjszq    = '12M'.
  ls_t06-zbegin   = ls_ta02-zbegin.
  ls_t06-zend     = ls_ta02-zend.
  ls_t06-zhszq    = ls_ta02-zhszq.
  ls_t06-zhstype  = ls_ta02-zhstype.
  ls_t06-zhtlx    = ls_ta01-zhtlx.
  ls_t06-zxyzt    = 'N'.
*  ls_t06-ztk_id = ls_data-ztk_id.  "条款
  APPEND ls_t06 TO pt_t06.


  CLEAR ls_ta02.


ENDFORM.





FORM frm_conv_data_t44  USING ls_data TYPE LINE OF tt_data
                         CHANGING pt_t44 TYPE tt_t44.

  DATA:
    lt_split_table_01 TYPE TABLE OF string,
    lt_split_table_02 TYPE TABLE OF string.
  DATA:
        ls_t44 TYPE LINE OF tt_t44.


  SPLIT ls_data-f30 AT '/' INTO TABLE lt_split_table_01.
  SPLIT ls_data-f31 AT '/' INTO TABLE lt_split_table_02.

  LOOP AT lt_split_table_01 INTO DATA(ls_split_table_01).
    DATA(lv_index) = sy-tabix.

    CLEAR ls_t44.

    ls_t44-seq = ls_data-seq.
    ls_t44-seg = ls_data-seg.
    ls_t44-zflzff = ls_split_table_01.
    READ TABLE lt_split_table_02 INTO DATA(ls_split_table_02) INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_t44-zpaytp = ls_split_table_02.
    ENDIF.

    ls_t44-zflzff = |{ ls_t44-zflzff ALPHA = IN }|.
    APPEND ls_t44 TO pt_t44.


  ENDLOOP.

ENDFORM.


FORM frm_conv_data_org  USING ls_data TYPE LINE OF tt_data
                         CHANGING
                              pt_bukrs TYPE tt_bukrs
                              pt_werks TYPE tt_werks
                              pt_ekorg TYPE tt_ekorg.

  DATA:
    ls_bukrs TYPE LINE OF tt_bukrs,
    ls_werks TYPE LINE OF tt_werks,
    ls_ekorg TYPE LINE OF tt_ekorg.

  DATA:
    lt_split_table   TYPE TABLE OF string,
    lt_split_table_x TYPE TABLE OF string.
  DATA:lv_index TYPE sy-tabix.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].
  SPLIT ls_data-f32 AT '/' INTO TABLE lt_split_table.
  SPLIT ls_data-f32a AT '/' INTO TABLE lt_split_table_x.
  LOOP AT lt_split_table INTO DATA(ls_split_table).

    CLEAR: ls_bukrs,lv_index.
    lv_index = sy-tabix.
    ls_bukrs-seq = ls_data-seq.
    ls_bukrs-seg = ls_data-seg.
    IF lines( lt_split_table_x ) = 1 .
      ls_bukrs-zmdsx = lt_split_table_x[ 1 ].
    ELSE.
      READ TABLE lt_split_table_x INTO DATA(ls_split_table_x) INDEX lv_index.
      IF sy-subrc EQ 0.
        ls_bukrs-zmdsx = ls_split_table_x.
      ENDIF.
    ENDIF.

    ls_bukrs-bukrs = ls_split_table.
    APPEND ls_bukrs TO pt_bukrs.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT ls_data-f35 AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_ekorg.
    ls_ekorg-seq = ls_data-seq.
    ls_ekorg-seg = ls_data-seg.

    ls_ekorg-ekorg = ls_split_table.
    APPEND ls_ekorg TO pt_ekorg.
  ENDLOOP.

  CLEAR lt_split_table[].
  CLEAR lt_split_table_x[].
  SPLIT ls_data-f33 AT '/' INTO TABLE lt_split_table.
  SPLIT ls_data-f34 AT '/' INTO TABLE lt_split_table_x..
  LOOP AT lt_split_table INTO ls_split_table.
    CLEAR: ls_werks,lv_index.
    lv_index = sy-tabix.
    ls_werks-seq = ls_data-seq.
    ls_werks-seg = ls_data-seg.
    READ TABLE lt_split_table_x INTO ls_split_table_x INDEX lv_index.
    IF sy-subrc EQ 0.
      ls_werks-exclude = ls_split_table_x.
    ENDIF.

    ls_werks-werks = ls_split_table.
    APPEND ls_werks TO pt_werks.
  ENDLOOP.

ENDFORM.

FORM frm_conv_data_zlqd  USING ls_data TYPE LINE OF tt_data
                         CHANGING
                              pt_t81    TYPE tt_t81
                              pt_t82    TYPE tt_t82.

  DATA:
    ls_t81 TYPE LINE OF tt_t81,
    ls_t82 TYPE LINE OF tt_t82.

  DATA:
    lt_split_table TYPE TABLE OF string,
    ls_split_table LIKE LINE OF  lt_split_table.

  CLEAR lt_split_table[].
  SPLIT ls_data-f38 AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_t81.
    ls_t81-seq = ls_data-seq.
    ls_t81-seg = ls_data-seg.

    ls_t81-zzlbm = ls_split_table.
    APPEND ls_t81 TO pt_t81.
  ENDLOOP.

  CLEAR lt_split_table[].
  SPLIT ls_data-f39 AT '/' INTO TABLE lt_split_table.
  LOOP AT lt_split_table INTO ls_split_table.

    CLEAR: ls_t82.
    ls_t82-seq = ls_data-seq.
    ls_t82-seg = ls_data-seg.
    ls_t82-zqdbm = ls_split_table.
    APPEND ls_t82 TO pt_t82.
  ENDLOOP.
ENDFORM.






*&---------------------------------------------------------------------*
*& FORM FRM_DATA_PRO
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_data_pro_end  CHANGING pt_data TYPE tt_data.



  DATA:
    lt_style     TYPE lvc_t_styl,
    lt_cellcolor TYPE lvc_t_scol.

  LOOP AT pt_data ASSIGNING FIELD-SYMBOL(<fs>).

*    回填合同信息
    PERFORM frm_get_data_from_ht_02 CHANGING  <fs> .
    PERFORM frm_get_ekgrp_text(zbcs0001) USING <fs>-f2 CHANGING <fs>-eknam.
    PERFORM frm_get_text_zfllx(zbcs0001) USING <fs>-f1a CHANGING <fs>-zfllx_t.

    PERFORM frm_get_ddtext(zbcs0001) USING 'ZREM_ZDFFS' <fs>-f6  CHANGING <fs>-zdffs_t.
    PERFORM frm_get_ddtext(zbcs0001) USING 'ZREM_ZHSTYPE' <fs>-f8  CHANGING <fs>-zhstype_t.

    PERFORM frm_set_status  CHANGING <fs>.
  ENDLOOP.

  IF gv_flg = '01'.
    DATA(lt_ht_id) = pt_data.
    SORT lt_ht_id BY f1.
    DELETE ADJACENT DUPLICATES FROM lt_ht_id COMPARING f1.
    LOOP AT lt_ht_id INTO DATA(ls_ht_id).
      PERFORM frm_pro_lock(zbcs0001)  USING ls_ht_id-f1 ''.
    ENDLOOP.
  ELSE.
    DATA(lt_tk_id) = pt_data.
    SORT lt_tk_id BY ztk_id.
    DELETE ADJACENT DUPLICATES FROM lt_tk_id COMPARING ztk_id.
    LOOP AT lt_tk_id INTO DATA(ls_tk_id).
      PERFORM frm_pro_lock(zbcs0001)  USING ls_tk_id-ztk_id ''.
    ENDLOOP.
  ENDIF.


  SORT pt_data BY seq.
ENDFORM.


*&---------------------------------------------------------------------*
*& MODULE STATUS_9000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9000 OUTPUT.
  SET PF-STATUS 'S1000'.
  SET TITLEBAR 'T1000' WITH '促销返利条款批导'.

  PERFORM frm_alv_init.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      MODULE  USER_COMMAND_9000  INPUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
MODULE user_command_9000 INPUT.
  DATA:
  gv_code  TYPE sy-ucomm.

  gv_code = sy-ucomm.

  PERFORM frm_check_changed_data USING grf_alv.

  CASE gv_code.
    WHEN 'SAVE'.
*      PERFORM FRM_SAVE_DATA.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.

      SET SCREEN 0.
      LEAVE SCREEN.
    WHEN OTHERS.
  ENDCASE.
  PERFORM frm_refresh_alv USING grf_alv.
ENDMODULE.


*&---------------------------------------------------------------------*
*&      FORM  FRM_ALV_INIT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_alv_init .

  IF grf_container IS INITIAL.

    CREATE OBJECT grf_container
      EXPORTING
        side                        = cl_gui_docking_container=>dock_at_top
        extension                   = 2000
      EXCEPTIONS
        cntl_error                  = 1
        cntl_system_error           = 2
        create_error                = 3
        lifetime_error              = 4
        lifetime_dynpro_dynpro_link = 5
        OTHERS                      = 6.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
    ENDIF.

    IF grf_alv IS INITIAL.
      CREATE OBJECT grf_alv
        EXPORTING
          i_parent          = grf_container
        EXCEPTIONS
          error_cntl_create = 1
          error_cntl_init   = 2
          error_cntl_link   = 3
          error_dp_create   = 4
          OTHERS            = 5.
      IF sy-subrc <> 0.
        MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
                    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
      ENDIF.

      PERFORM frm_alv_display USING grf_alv .
    ELSE.
      PERFORM frm_refresh_alv USING grf_alv.
    ENDIF.
  ELSE.
    PERFORM frm_refresh_alv USING grf_alv.
  ENDIF.

ENDFORM.

FORM frm_refresh_alv USING prf_alv TYPE REF TO cl_gui_alv_grid.
  DATA:
    ls_stable TYPE lvc_s_stbl,
    ls_layout TYPE lvc_s_layo.


  PERFORM frm_set_layout CHANGING ls_layout.
  CALL METHOD prf_alv->set_frontend_layout
    EXPORTING
      is_layout = ls_layout.

  ls_stable-row = ls_stable-col = 'X'.
  CALL METHOD prf_alv->refresh_table_display
    EXPORTING
      is_stable = ls_stable
*     I_SOFT_REFRESH = ''
    EXCEPTIONS
      finished  = 1
      OTHERS    = 2.
  IF sy-subrc <> 0.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  FRM_DISPLAY
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_GO_ALV  TEXT
*----------------------------------------------------------------------*
FORM frm_alv_display  USING  prf_alv TYPE REF TO cl_gui_alv_grid.

  DATA: lt_fieldcat TYPE TABLE OF lvc_s_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_layout   TYPE TABLE OF lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant.

  PERFORM frm_set_catalog CHANGING lt_fieldcat.

  PERFORM frm_set_layout CHANGING ls_layout.

  PERFORM frm_set_ex_fcode TABLES lt_ex_fcode.

  PERFORM frm_set_variant CHANGING  ls_variant.

*ALV显示

  PERFORM frm_set_alv USING
                            'GT_DATA'
                            ls_variant
                            ls_layout
                            lt_ex_fcode
                      CHANGING
                            lt_fieldcat
                            prf_alv.

*  事件注册
  DATA:
  lv_objid TYPE char10 VALUE 'MAIN'.
  DATA lrf_event TYPE REF TO sec_lcl_event_receiver.


  CREATE OBJECT lrf_event
    EXPORTING
      i_objid = lv_objid.


  PERFORM frm_set_event_handler USING lv_objid
                                CHANGING lrf_event
                                  prf_alv.
ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LS_VARIANT  TEXT
*      -->P_LS_LAYOUT  TEXT
*      -->P_LT_EX_FCODE  TEXT
*      <--P_LT_FIELDCAT  TEXT
*      <--P_'GT_DATA_T'  TEXT
*----------------------------------------------------------------------*
FORM frm_set_alv  USING    pv_tname TYPE any
                           ps_variant TYPE disvariant
                           ps_layout TYPE lvc_s_layo
                           pt_ex_fcode TYPE ui_functions
                  CHANGING pt_fieldcat TYPE lvc_t_fcat
                           prf_alv_grid TYPE REF TO cl_gui_alv_grid.


  DATA lv_table  LIKE feld-name.
  FIELD-SYMBOLS <lt_table> TYPE STANDARD TABLE.

  CONCATENATE pv_tname '[]' INTO lv_table.
  ASSIGN (lv_table) TO <lt_table>.

  CALL METHOD prf_alv_grid->set_table_for_first_display
    EXPORTING
*     I_BUFFER_ACTIVE               =
*     I_BYPASSING_BUFFER            =
*     I_CONSISTENCY_CHECK           =
*     I_STRUCTURE_NAME              = 'IT_ITAB'
      is_variant                    = ps_variant
      i_save                        = 'A'
*     I_DEFAULT                     = 'X'
      is_layout                     = ps_layout
*     IS_PRINT                      =
*     IT_SPECIAL_GROUPS             =
      it_toolbar_excluding          = pt_ex_fcode
*     IT_HYPERLINK                  =
*     IT_ALV_GRAPHICS               =
*     IT_EXCEPT_QINFO               =
*     IR_SALV_ADAPTER               =
    CHANGING
      it_outtab                     = <lt_table>
      it_fieldcatalog               = pt_fieldcat
*     IT_SORT                       =
*     IT_FILTER                     =
    EXCEPTIONS
      invalid_parameter_combination = 1
      program_error                 = 2
      too_many_lines                = 3
      OTHERS                        = 4.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
               WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.

FORM frm_set_catalog  CHANGING   pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.
  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-fieldname = &2.                 "
    ls_fieldcat-coltext = &3.                 "

    CASE &2.

      WHEN 'MATNR'  .
        ls_fieldcat-ref_field = 'MATNR'.
        ls_fieldcat-ref_table = 'MARA'.

      WHEN OTHERS.
    ENDCASE.

    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.

  add_fcat  '10' 'ZMSG_EXCEL'    '消息'  .
  IF gv_flg = '01'.
    add_fcat  '10' 'SEQ'       '序号'  .                      "ERP-13507
    add_fcat  '10' 'F1'        '合同号'  .
    add_fcat  '10' 'ZFLLX_T'   '返利类型'  .
    add_fcat  '10' 'F2'        '条款采购组'  .
    add_fcat  '10' 'EKNAM'     '条款采购组名称'  .
    add_fcat  '10' 'F3'        '条款开始日期'  .
    add_fcat  '10' 'F4'        '条款结束日期'  .
    add_fcat  '10' 'F5'        '核算周期'  .
    add_fcat  '10' 'F6'        '兑付方式'  .
    add_fcat  '10' 'ZDFFS_T'   '兑付方式名称'  .
    add_fcat  '10' 'F7'        '付款期间(天)'  .
    add_fcat  '10' 'F8'        '周期类型'  .
    add_fcat  '10' 'ZHSTYPE_T' '周期类型名称'  .
    add_fcat  '10' 'F8A'       '采购经理'  .
    add_fcat  '10' 'F8B'       '采购总监'  .
    add_fcat  '10' 'F8C'       '核算厂家'  .
    add_fcat  '10' 'F8E'       '核算厂家描述'  .
    add_fcat  '10' 'F8D'       '兑付时间'  .
    add_fcat  '10' 'F9'        '条款描述'  .
    add_fcat  '10' 'F9A'       'OA流程编码'  .
    add_fcat  '10' 'F9B'       '批发返利'  .
    add_fcat  '10' 'F9C'       '收取部门'  .
    add_fcat  '10' 'F9D'       '平台编码'  .
    add_fcat  '10' 'F10'       '计算方法'  .
    add_fcat  '10' 'F11'       '商品组描述'  .
    add_fcat  '10' 'F12'       '商品编码-买'  .
    add_fcat  '10' 'F13'       '商品编码-赠'  .
    add_fcat  '10' 'F14'       '加提单价'  .
    add_fcat  '10' 'F15'       '规则描述1'  .
    add_fcat  '10' 'F16'       '规则买1'  .
    add_fcat  '10' 'F17'       '规则赠1'  .
    add_fcat  '10' 'F18'       '返利值1'  .
    add_fcat  '10' 'F19'       '匹配比例1'  .
    add_fcat  '10' 'F20'       '规则描述2'  .
    add_fcat  '10' 'F21'       '规则买2'  .
    add_fcat  '10' 'F22'       '规则赠2'  .
    add_fcat  '10' 'F23'       '返利值2'  .
    add_fcat  '10' 'F24'       '匹配比例2'  .
    add_fcat  '10' 'F25'       '促销日期'  .
*  ELSE.
*    add_fcat  '10' 'ZTK_ID'    '条款号码'  .
  ENDIF.
  add_fcat  '10' 'F26'    '组织级别'  .
  add_fcat  '10' 'F27'    '协议主体'  .
  add_fcat  '10' 'F28'    '收款方'  .
  add_fcat  '10' 'F29'    '协议描述'  .
  add_fcat  '10' 'F30'    '付款方'  .
  add_fcat  '10' 'F31'    '付款方级别'  .
  add_fcat  '10' 'F32'    '公司代码'  .
  add_fcat  '10' 'F32A'   '公司类型'  .
  add_fcat  '10' 'F33'    '门店'  .
  add_fcat  '10' 'F34'    '排除门店'  .
  add_fcat  '10' 'F35'    '采购组织'  .
  add_fcat  '10' 'F36'    '专属标识'  .
  add_fcat  '10' 'F37'    '同享合约券'  .
  add_fcat  '10' 'F38'    '子类'  .
  add_fcat  '10' 'F39'    '渠道'  .
  add_fcat  '10' 'ZTK_ID' '条款号码'  .
  add_fcat  '10' 'ZXY_ID' '协议号码'  .

  IF gv_flg = '01'.
    add_fcat  '10' 'ZSPZ_ID'    '商品组号码'  .
  ENDIF.


ENDFORM.                    " FRM_SET_CATALOG
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.
  ps_layout-sel_mode = 'D'.
  ps_layout-zebra     = 'X'.
  ps_layout-cwidth_opt  = ''.
*  IF GV_FLG = '02'.
  ps_layout-excp_fname = 'STATUS'.
*  ENDIF.
  ps_layout-ctab_fname = 'CELLCOLOR'.
  ps_layout-box_fname = 'SEL'.


ENDFORM.                    "FRM_SET_LAYOUT
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EVENT_HANDLER
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      <--P_LO_EVENT  TEXT
*      <--P_GO_ALV_TOP  TEXT
*----------------------------------------------------------------------*
FORM frm_set_event_handler USING pv_objid TYPE char10
                            CHANGING prf_event TYPE REF TO sec_lcl_event_receiver
                                     prf_grid  TYPE REF TO cl_gui_alv_grid.


  CALL METHOD prf_grid->set_ready_for_input
    EXPORTING
      i_ready_for_input = 1.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_enter.

  CALL METHOD prf_grid->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_modified.

*  CREATE OBJECT PRF_EVENT.
  SET HANDLER prf_event->sec_handle_bef_user_command         FOR prf_grid.
  SET HANDLER prf_event->sec_handle_user_command             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_toolbar                  FOR prf_grid.
  SET HANDLER prf_event->sec_handle_hotspot_click            FOR prf_grid.
  SET HANDLER prf_event->sec_handle_double_click             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed             FOR prf_grid.
  SET HANDLER prf_event->sec_handle_data_changed_fin         FOR prf_grid.


  CALL METHOD prf_grid->set_toolbar_interactive.

ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_EXCLUDE TEXT
*----------------------------------------------------------------------*
FORM frm_set_ex_fcode TABLES pt_exclude.
*  DATA LS_EXCLUDE TYPE UI_FUNC.
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_INSERT_ROW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_DELETE_ROW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_REFRESH.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_DETAIL.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_MB_PASTE.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_UNDO.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_CUT.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_COPY.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_COPY_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_APPEND_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_LOC_MOVE_ROW.
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_PRINT .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_PRINT_PREV .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_GRAPH .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_CHECK .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_MB_VIEW .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_HELP .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.
*
*  LS_EXCLUDE = CL_GUI_ALV_GRID=>MC_FC_INFO .
*  APPEND LS_EXCLUDE TO PT_EXCLUDE.

ENDFORM.                    "FRM_SET_EX_FCODE
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant CHANGING ps_variant TYPE disvariant.
  ps_variant-report = sy-repid.
  ps_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*&      FORM  FRM_CHECK_CHANGED_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_check_changed_data USING prf_alv TYPE REF TO cl_gui_alv_grid.
  IF grf_alv IS NOT INITIAL.
    CALL METHOD grf_alv->check_changed_data.
  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_INSERT_DATA
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- GT_DATA_ALV
*&---------------------------------------------------------------------*
FORM frm_insert_data  CHANGING pt_data TYPE tt_data.
  DATA:
  ls_data TYPE LINE OF tt_data.

  INSERT ls_data INTO pt_data INDEX 1.
ENDFORM.

FORM frm_delete_data  CHANGING pt_data TYPE tt_data.
  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
  IF sy-subrc NE 0.
    MESSAGE s888(sabapdocu) WITH '请选择需要删除的记录！' DISPLAY LIKE 'E'.
    RETURN.
  ENDIF.
  DELETE pt_data WHERE sel = 'X'.
ENDFORM.


FORM frm_check_bf_save  USING    pt_data TYPE tt_data
                        CHANGING pv_mtype TYPE bapi_mtype
                                 pv_msg TYPE bapi_msg.

  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel_man = 'X'.
  IF sy-subrc NE 0.
    pv_mtype = 'E'.
    pv_msg = '请选择需要导入的数据！'.
  ENDIF.

  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' status = '1'.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '选择的数据存在错误，无法导入'.
  ENDIF.

  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel_man = 'X' status = '3'.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '选择的数据已经导入，不能重复导入'.
  ENDIF.

ENDFORM.

FORM frm_data_save_main_01 CHANGING
                                 pt_data TYPE tt_data
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_tc05   TYPE tt_tc05
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                 lv_mtype TYPE bapi_mtype
                                 lv_msg TYPE bapi_msg.

  DATA:
    lt_t14 TYPE tt_t14.

  DATA(lt_data) = pt_data[].
  DELETE lt_data WHERE sel_man = ''.

  PERFORM frm_data_save_pro_01 CHANGING lt_data
                                           pt_ta02
                                           pt_t09
                                           pt_t20
                                           pt_ta05
                                           pt_t58
                                           pt_ta06
                                           pt_tc05
                                           pt_t06
                                           pt_t44
                                           pt_t81
                                           pt_t82
                                           pt_bukrs
                                           pt_werks
                                           pt_ekorg
                                           lt_t14.


  PERFORM frm_data_save_db USING
                                           pt_ta02
                                           pt_t09
                                           pt_t20
                                           pt_ta05
                                           pt_t58
                                           pt_ta06
                                           pt_tc05
                                           pt_t06
                                           pt_t44
                                           pt_t81
                                           pt_t82
                                           lt_t14
                                           .

  PERFORM frm_data_modify_alv USING lt_data
                              CHANGING pt_data.


  PERFORM frm_update_zcs.


  lv_mtype = 'S'.
  lv_msg = '导入完成'.



ENDFORM.


FORM frm_data_save_main_02 CHANGING
                                 pt_data TYPE tt_data
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_tc05   TYPE tt_tc05
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                 lv_mtype TYPE bapi_mtype
                                 lv_msg TYPE bapi_msg.

  DATA:
    lt_t14 TYPE tt_t14.

  DATA(lt_data) = pt_data[].
  DELETE lt_data WHERE sel_man = ''.

  PERFORM frm_data_save_pro_02 CHANGING lt_data
                                           pt_ta02
                                           pt_t09
                                           pt_t06
                                           pt_t44
                                           pt_t81
                                           pt_t82
                                           pt_bukrs
                                           pt_werks
                                           pt_ekorg
                                           lt_t14.


  PERFORM frm_data_save_db USING
                                           pt_ta02
                                           pt_t09
                                           pt_t20
                                           pt_ta05
                                           pt_t58
                                           pt_ta06
                                           pt_tc05
                                           pt_t06
                                           pt_t44
                                           pt_t81
                                           pt_t82
                                           lt_t14.

  PERFORM frm_data_modify_alv USING lt_data
                              CHANGING pt_data.


  PERFORM frm_update_zcs.


  lv_mtype = 'S'.
  lv_msg = '导入完成'.



ENDFORM.


FORM frm_data_save_pro_01     CHANGING

                                   lt_data   TYPE tt_data
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_tc05   TYPE tt_tc05
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                  pt_t14   TYPE tt_t14.



  DATA:lv_zitem_id TYPE zreta002-zitem_id,
       ls_t14      TYPE LINE OF tt_t14.


*  保存商品组
  SORT pt_t09.
  SORT pt_t20.
  DELETE ADJACENT DUPLICATES FROM pt_t09 COMPARING ALL FIELDS.
  DELETE ADJACENT DUPLICATES FROM pt_t20 COMPARING ALL FIELDS.
  LOOP AT pt_t09 INTO DATA(ls_t09).
    PERFORM frm_get_num(zbcs0001) USING 'ZRE0003' '01' CHANGING ls_t09-zspz_id.
    MODIFY pt_t09 FROM ls_t09.
  ENDLOOP.
  SORT pt_t09 BY seg.
  LOOP AT pt_t20 INTO DATA(ls_t20).
    READ TABLE pt_t09 INTO ls_t09 WITH KEY seg = ls_t20-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t20-zspz_id = ls_t09-zspz_id.
      MODIFY pt_t20 FROM ls_t20.
    ENDIF.
  ENDLOOP.

*  保存条款
  SORT pt_ta02.
  DELETE ADJACENT DUPLICATES FROM pt_ta02 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  CLEAR lv_zitem_id.
  LOOP AT pt_ta02 INTO DATA(ls_ta02).
    AT NEW seg.
      DATA(lv_flg_new) = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_zitem_id.
      SELECT MAX( zitem_id ) AS zitem_id FROM zreta002 WHERE zht_id = @ls_ta02-zht_id  INTO @DATA(lv_zitem_id_db) .
      SELECT MAX( i~zitem_id ) AS zitem_id FROM @pt_ta02 AS i WHERE zht_id = @ls_ta02-zht_id  INTO @DATA(lv_zitem_id_itab) .
*      获取最大的ITEMID
      IF lv_zitem_id_db > lv_zitem_id_itab.
        lv_zitem_id = lv_zitem_id_db.
      ELSE.
        lv_zitem_id = lv_zitem_id_itab.
      ENDIF.

      lv_zitem_id = lv_zitem_id + 1.
      lv_zitem_id = |{ lv_zitem_id ALPHA = IN }|.
      CLEAR:lv_zitem_id_db,lv_zitem_id_itab.
    ENDIF.

    ls_ta02-zitem_id = lv_zitem_id.
    ls_ta02-ztk_id = ls_ta02-zht_id+4(6) && lv_zitem_id.


    READ TABLE pt_t09 INTO ls_t09 WITH KEY seg = ls_ta02-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_ta02-zspz_id = ls_t09-zspz_id.
    ENDIF.

    CLEAR lv_flg_new.
    MODIFY pt_ta02 FROM ls_ta02.
  ENDLOOP.

*  保存促销返利阶梯
  SORT pt_ta05.
  DELETE ADJACENT DUPLICATES FROM pt_ta05 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  LOOP AT pt_ta05 INTO DATA(ls_ta05).
    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_ta05-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_ta05-ztk_id = ls_ta02-ztk_id.
      MODIFY pt_ta05 FROM ls_ta05.
    ENDIF.
  ENDLOOP.

*  保存促销阶梯组
  SORT pt_t58.
  DELETE ADJACENT DUPLICATES FROM pt_t58 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  LOOP AT pt_t58 INTO DATA(ls_t58).
    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_t58-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t58-ztk_id = ls_ta02-ztk_id.
    ENDIF.
    READ TABLE pt_t09 INTO ls_t09 WITH KEY seg = ls_t58-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t58-zspz_id = ls_t09-zspz_id.
    ENDIF.
    MODIFY pt_t58 FROM ls_t58.
  ENDLOOP.

*  保存促销日期
  SORT pt_ta06.
  DELETE ADJACENT DUPLICATES FROM pt_ta06 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_ta06-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_ta06-ztk_id = ls_ta02-ztk_id.
      MODIFY pt_ta06 FROM ls_ta06.
    ENDIF.
  ENDLOOP.

*  保存规则信息
  SORT pt_tc05.
  DELETE ADJACENT DUPLICATES FROM pt_tc05 COMPARING ALL FIELDS.
  SORT pt_tc05 BY seg.
  LOOP AT pt_tc05 INTO DATA(ls_tc05).
    SELECT SINGLE * INTO CORRESPONDING FIELDS OF ls_tc05 FROM zretc005 WHERE zclrid = ls_tc05-zclrid.
    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_tc05-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_tc05-zclrid = ls_ta02-ztk_id.  "保存时 用条款号替换掉标准的规则ID
      ls_tc05-zclrtp = 'A'.
      MODIFY pt_tc05 FROM ls_tc05.
    ENDIF.
  ENDLOOP.

*  保存协议
  SORT pt_t06.
  DELETE ADJACENT DUPLICATES FROM pt_t06 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  SORT pt_t06 BY seg seq.
  LOOP AT pt_t06 INTO DATA(ls_t06).
    PERFORM frm_get_num(zbcs0001) USING 'ZRE0001' '01' CHANGING ls_t06-zxy_id.
    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_t06-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t06-ztk_id = ls_ta02-ztk_id.
      ls_t06-frgsx = ls_ta02-frgsx.
      ls_t06-kolnr = ls_ta02-kolnr.
    ENDIF.
    MODIFY pt_t06 FROM ls_t06.
  ENDLOOP.

*  更新协议行项目
  SORT pt_t06 BY seg seq.
  LOOP AT pt_t06 INTO ls_t06.
    AT NEW seg.
      lv_flg_new = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_zitem_id.
      lv_zitem_id = '199'.
    ENDIF.
    lv_zitem_id = lv_zitem_id + 1.
    lv_zitem_id = |{ lv_zitem_id ALPHA = IN }|.
    ls_t06-zitems = lv_zitem_id.

    CLEAR lv_flg_new.
    MODIFY pt_t06 FROM ls_t06.
  ENDLOOP.

*  更新协议支付方
  SORT pt_t44.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING ALL FIELDS.
  SORT pt_t06 BY seq.
  LOOP AT pt_t44 INTO DATA(ls_t44).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t44-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t44-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t44 FROM ls_t44.
  ENDLOOP.

*  更新子类
  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  SORT pt_t81 BY seq.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t81-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t81-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t81 FROM ls_t81.
  ENDLOOP.
*  更新渠道
  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  SORT pt_t82 BY seq.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t82-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t82-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t82 FROM ls_t82.
  ENDLOOP.

*  更新组织级别
  SORT pt_t06 BY seq.
  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'P'.
    ls_t14-zzzid = ls_ekorg-ekorg.
    ls_t14-zzzpc = ls_ekorg-exclude.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_ekorg-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'A'.
    ls_t14-zzzid = ls_bukrs-bukrs.
    ls_t14-zzzpc = ls_bukrs-exclude.
    ls_t14-zmdsx = ls_bukrs-zmdsx.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_bukrs-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  LOOP AT pt_werks INTO DATA(ls_werks).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'S'.
    ls_t14-zzzid = ls_werks-werks.
    ls_t14-zzzpc = ls_werks-exclude.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_werks-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.


*  更新ALV数据
  PERFORM frm_modify_alv USING   pt_ta02 pt_t06 pt_t09 CHANGING lt_data.
*  SORT pt_ta02 BY seg.
*  SORT pt_t06 BY seq.
*  SORT pt_t09 BY seg.
*  LOOP AT lt_data INTO DATA(ls_data).
*    READ TABLE pt_t09 INTO ls_t09 WITH KEY seg = ls_data-seg BINARY SEARCH.
*    IF sy-subrc EQ 0.
*      ls_data-zspz_id = ls_t09-zspz_id.
*    ENDIF.
*    READ TABLE pt_ta02 INTO ls_ta02 WITH KEY seg = ls_data-seg BINARY SEARCH.
*    IF sy-subrc EQ 0.
*      ls_data-ztk_id = ls_ta02-ztk_id.
*    ENDIF.
*    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_data-seq BINARY SEARCH.
*    IF sy-subrc EQ 0.
*      ls_data-zxy_id = ls_t06-zxy_id.
*    ENDIF.
*    ls_data-status = '3'.
*    ls_data-ztype_impt = 'S'.
*    ls_data-zmsg_excel = '导入成功！'.
*    PERFORM frm_set_status  CHANGING ls_data.
*    MODIFY lt_data FROM ls_data.
*  ENDLOOP.


ENDFORM.


FORM frm_data_save_pro_02     CHANGING

                                   lt_data   TYPE tt_data
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_bukrs  TYPE tt_bukrs
                                  pt_werks  TYPE tt_werks
                                  pt_ekorg  TYPE tt_ekorg
                                  pt_t14   TYPE tt_t14.



  DATA:lv_zitem_id TYPE zreta002-zitem_id,
       lv_flg_new  TYPE char1,
       ls_t14      TYPE LINE OF tt_t14.



*  保存协议
  SORT pt_t06.
  DELETE ADJACENT DUPLICATES FROM pt_t06 COMPARING ALL FIELDS.
  SORT pt_ta02 BY seg.
  SORT pt_t06 BY seg seq.
  LOOP AT pt_t06 INTO DATA(ls_t06).
    PERFORM frm_get_num(zbcs0001) USING 'ZRE0001' '01' CHANGING ls_t06-zxy_id.
    READ TABLE pt_ta02 INTO DATA(ls_ta02) WITH KEY seg = ls_t06-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t06-ztk_id = ls_ta02-ztk_id.
      ls_t06-frgsx = ls_ta02-frgsx.
      ls_t06-kolnr = ls_ta02-kolnr.
    ENDIF.
    MODIFY pt_t06 FROM ls_t06.
  ENDLOOP.

*  更新协议行项目
  SORT pt_t06 BY seg seq.
  LOOP AT pt_t06 INTO ls_t06.
    CLEAR lv_flg_new.

    AT NEW seg.
      lv_flg_new = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_zitem_id.
*      lv_zitem_id = '199'.
      SELECT MAX( zitems ) FROM zret0006 WHERE ztk_id = @ls_t06-ztk_id INTO @lv_zitem_id.
    ENDIF.
    lv_zitem_id = lv_zitem_id + 1.
    lv_zitem_id = |{ lv_zitem_id ALPHA = IN }|.
    ls_t06-zitems = lv_zitem_id.

    CLEAR lv_flg_new.
    MODIFY pt_t06 FROM ls_t06.
  ENDLOOP.

*  更新协议支付方
  SORT pt_t44.
  DELETE ADJACENT DUPLICATES FROM pt_t44 COMPARING ALL FIELDS.
  SORT pt_t06 BY seq.
  LOOP AT pt_t44 INTO DATA(ls_t44).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t44-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t44-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t44 FROM ls_t44.
  ENDLOOP.


*  更新组织级别
  SORT pt_t06 BY seq.
  LOOP AT pt_ekorg INTO DATA(ls_ekorg).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'P'.
    ls_t14-zzzid = ls_ekorg-ekorg.
    ls_t14-zzzpc = ls_ekorg-exclude.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_ekorg-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  LOOP AT pt_bukrs INTO DATA(ls_bukrs).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'A'.
    ls_t14-zzzid = ls_bukrs-bukrs.
    ls_t14-zzzpc = ls_bukrs-exclude.
    ls_t14-zmdsx = ls_bukrs-zmdsx.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_bukrs-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

  LOOP AT pt_werks INTO DATA(ls_werks).
    CLEAR:
          ls_t14.
    ls_t14-zzzlx = 'S'.
    ls_t14-zzzid = ls_werks-werks.
    ls_t14-zzzpc = ls_werks-exclude.
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_werks-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t14-zxy_id = ls_t06-zxy_id.
    ENDIF.
    APPEND ls_t14 TO pt_t14.
  ENDLOOP.

*  更新子类
  SORT pt_t81.
  DELETE ADJACENT DUPLICATES FROM pt_t81 COMPARING ALL FIELDS.
  SORT pt_t81 BY seq.
  LOOP AT pt_t81 INTO DATA(ls_t81).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t81-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t81-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t81 FROM ls_t81.
  ENDLOOP.
*  更新渠道
  SORT pt_t82.
  DELETE ADJACENT DUPLICATES FROM pt_t82 COMPARING ALL FIELDS.
  SORT pt_t82 BY seq.
  LOOP AT pt_t82 INTO DATA(ls_t82).
    READ TABLE pt_t06 INTO ls_t06 WITH KEY seq = ls_t82-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t82-zxy_id = ls_t06-zxy_id.
    ENDIF.
    MODIFY pt_t82 FROM ls_t82.
  ENDLOOP.

*  更新ALV数据
  PERFORM frm_modify_alv USING   pt_ta02 pt_t06 pt_t09 CHANGING lt_data.



ENDFORM.


FORM frm_data_save_db     USING
                                  pt_ta02   TYPE tt_ta02
                                  pt_t09    TYPE tt_t09
                                  pt_t20    TYPE tt_t20
                                  pt_ta05   TYPE tt_ta05
                                  pt_t58    TYPE tt_t58
                                  pt_ta06   TYPE tt_ta06
                                  pt_tc05   TYPE tt_tc05
                                  pt_t06    TYPE tt_t06
                                  pt_t44    TYPE tt_t44
                                  pt_t81    TYPE tt_t81
                                  pt_t82    TYPE tt_t82
                                  pt_t14   TYPE tt_t14.

  DATA:

    ls_db_ta02 TYPE  zreta002,
    ls_db_t09  TYPE  zret0009,
    ls_db_t20  TYPE  zret0020,
    ls_db_ta05 TYPE  zreta005,
    ls_db_ta06 TYPE  zreta006,
    ls_db_tc05 TYPE  zretc005,
    ls_db_t58  TYPE  zret0058,
    ls_db_t06  TYPE  zret0006,
    ls_db_t44  TYPE  zret0044,
    ls_db_t14  TYPE  zret0014,
    ls_db_t15  TYPE  zret0015,
    ls_db_t16  TYPE  zret0016,
    ls_db_t81  TYPE  zret0081,
    ls_db_t82  TYPE  zret0082,
    lt_db_ta02 TYPE TABLE OF zreta002,
    lt_db_t09  TYPE TABLE OF zret0009,
    lt_db_t20  TYPE TABLE OF zret0020,
    lt_db_ta05 TYPE TABLE OF zreta005,
    lt_db_ta06 TYPE TABLE OF zreta006,
    lt_db_tc05 TYPE TABLE OF zretc005,
    lt_db_t58  TYPE TABLE OF zret0058,
    lt_db_t06  TYPE TABLE OF zret0006,
    lt_db_t44  TYPE TABLE OF zret0044,
    lt_db_t14  TYPE TABLE OF zret0014,
    lt_db_t15  TYPE TABLE OF zret0015,
    lt_db_t16  TYPE TABLE OF zret0016,
    lt_db_t81  TYPE TABLE OF zret0081,
    lt_db_t82  TYPE TABLE OF zret0082.


  DATA:
    lt_t15 TYPE TABLE OF zret0015,
    lt_t16 TYPE TABLE OF zret0016.



*  核算期间结算期间
  PERFORM frm_pro_data_qj_new TABLES lt_t15
                                     lt_t16
                              USING  pt_t06.


  LOOP AT pt_ta02 INTO DATA(ls_ta02).
    CLEAR ls_db_ta02.
    MOVE-CORRESPONDING ls_ta02 TO ls_db_ta02.
    ls_db_ta02-zcjrq = sy-datum.
    ls_db_ta02-zcjsj = sy-uzeit.
    ls_db_ta02-zcjr = sy-uname.
    APPEND ls_db_ta02 TO lt_db_ta02.
  ENDLOOP.

  LOOP AT pt_t09 INTO DATA(ls_t09).
    CLEAR ls_db_t09.
    MOVE-CORRESPONDING ls_t09 TO ls_db_t09.
    ls_db_t09-zcjrq = sy-datum.
    ls_db_t09-zcjsj = sy-uzeit.
    ls_db_t09-zcjr = sy-uname.
    APPEND ls_db_t09 TO lt_db_t09.
  ENDLOOP.

  LOOP AT pt_t20 INTO DATA(ls_t20).
    CLEAR ls_db_t20.
    MOVE-CORRESPONDING ls_t20 TO ls_db_t20.
    APPEND ls_db_t20 TO lt_db_t20.
  ENDLOOP.

  LOOP AT pt_ta05 INTO DATA(ls_ta05).
    CLEAR ls_db_ta05.
    MOVE-CORRESPONDING ls_ta05 TO ls_db_ta05.
    APPEND ls_db_ta05 TO lt_db_ta05.
  ENDLOOP.

  LOOP AT pt_ta06 INTO DATA(ls_ta06).
    CLEAR ls_db_ta06.
    MOVE-CORRESPONDING ls_ta06 TO ls_db_ta06.
    APPEND ls_db_ta06 TO lt_db_ta06.
  ENDLOOP.

  LOOP AT pt_tc05 INTO DATA(ls_tc05).
    CLEAR ls_db_tc05.
    MOVE-CORRESPONDING ls_tc05 TO ls_db_tc05.
    APPEND ls_db_tc05 TO lt_db_tc05.
  ENDLOOP.

  LOOP AT pt_t58 INTO DATA(ls_t58).
    CLEAR ls_db_t58.
    MOVE-CORRESPONDING ls_t58 TO ls_db_t58.
    APPEND ls_db_t58 TO lt_db_t58.
  ENDLOOP.

  LOOP AT pt_t06 INTO DATA(ls_t06).
    CLEAR ls_db_t06.
    MOVE-CORRESPONDING ls_t06 TO ls_db_t06.
    ls_db_t06-zcjrq = sy-datum.
    ls_db_t06-zcjsj = sy-uzeit.
    ls_db_t06-zcjr = sy-uname.
    APPEND ls_db_t06 TO lt_db_t06.
  ENDLOOP.

  LOOP AT pt_t44 INTO DATA(ls_t44).
    CLEAR ls_db_t44.
    MOVE-CORRESPONDING ls_t44 TO ls_db_t44.
    APPEND ls_db_t44 TO lt_db_t44.
  ENDLOOP.

  LOOP AT pt_t14 INTO DATA(ls_t14).
    CLEAR ls_db_t14.
    MOVE-CORRESPONDING ls_t14 TO ls_db_t14.
    APPEND ls_db_t14 TO lt_db_t14.
  ENDLOOP.

  LOOP AT lt_t15 INTO DATA(ls_t15).
    CLEAR ls_db_t15.
    MOVE-CORRESPONDING ls_t15 TO ls_db_t15.
    APPEND ls_db_t15 TO lt_db_t15.
  ENDLOOP.

  LOOP AT lt_t16 INTO DATA(ls_t16).
    CLEAR ls_db_t16.
    MOVE-CORRESPONDING ls_t16 TO ls_db_t16.
    APPEND ls_db_t16 TO lt_db_t16.
  ENDLOOP.

  LOOP AT pt_t81 INTO DATA(ls_t81).
    CLEAR ls_db_t81.
    MOVE-CORRESPONDING ls_t81 TO ls_db_t81.
    APPEND ls_db_t81 TO lt_db_t81.
  ENDLOOP.

  LOOP AT pt_t82 INTO DATA(ls_t82).
    CLEAR ls_db_t82.
    MOVE-CORRESPONDING ls_t82 TO ls_db_t82.
    APPEND ls_db_t82 TO lt_db_t82.
  ENDLOOP.

  MODIFY zreta002 FROM TABLE lt_db_ta02.
  MODIFY zret0009 FROM TABLE lt_db_t09.
  MODIFY zret0020 FROM TABLE lt_db_t20.
  MODIFY zreta005 FROM TABLE lt_db_ta05.
  MODIFY zret0058 FROM TABLE lt_db_t58.
  MODIFY zreta006 FROM TABLE lt_db_ta06.
  MODIFY zretc005 FROM TABLE lt_db_tc05.
  MODIFY zret0006 FROM TABLE lt_db_t06.
  MODIFY zret0044 FROM TABLE lt_db_t44.
  MODIFY zret0014 FROM TABLE lt_db_t14.
  MODIFY zret0015 FROM TABLE lt_db_t15.
  MODIFY zret0016 FROM TABLE lt_db_t16.
  MODIFY zret0081 FROM TABLE lt_db_t81.
  MODIFY zret0082 FROM TABLE lt_db_t82.

  COMMIT WORK AND WAIT.

ENDFORM.

FORM frm_set_status  CHANGING ls_data  TYPE LINE OF tt_data.
  IF ls_data-ztype_excel = 'E' OR ls_data-ztype_impt = 'E'.
    ls_data-status = '1'.
  ELSEIF ls_data-ztype_excel = '' AND ls_data-ztype_impt = ''.
    ls_data-status = '2'.
  ELSEIF  ls_data-ztype_impt = 'S'.
    ls_data-status = '3'.
  ENDIF.
ENDFORM.

FORM frm_data_modify_alv USING lt_data   TYPE tt_data
                         CHANGING pt_data   TYPE tt_data.

  SORT lt_data BY seq.
  LOOP AT pt_data INTO DATA(ps_data) WHERE sel_man = 'X'.
    READ TABLE lt_data INTO DATA(ls_data) WITH KEY seq = ps_data-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ps_data = ls_data.
*      PS_DATA-STATUS = LS_DATA-STATUS.
*      PS_DATA-ZTYPE_IMPT = LS_DATA-ZTYPE_IMPT.
*      PS_DATA-ZMSG_EXCEL = LS_DATA-ZMSG_EXCEL.
      MODIFY pt_data FROM ps_data.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_FRGSX
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      <-- LS_ZRETA002
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_frgsx  CHANGING ps_ta02  TYPE zreta002
                               pt_msglist TYPE scp1_general_errors.


  DATA:ls_zrekey TYPE zres0042.
  DATA:lv_frgsx  TYPE frgsx.
  DATA:ls_approval TYPE zres0088.



  CHECK ps_ta02-zxyzt <> 'A'.

  SELECT SINGLE * FROM zreta001 WHERE zht_id = @ps_ta02-zht_id INTO @DATA(ls_ta01).

  ls_zrekey-zhtlx   =  ls_ta01-zhtlx.
  ls_zrekey-zbukrs  =  ls_ta01-zbukrs.
  ls_zrekey-ekgrp   =  ps_ta02-ekgrp.
  ls_zrekey-zfllx   =  ps_ta02-zfllx.
  ls_zrekey-ztktype =  ps_ta02-ztktype.

  PERFORM frm_check_frgsx_exe  USING ls_zrekey CHANGING lv_frgsx pt_msglist.



  IF lv_frgsx IS NOT INITIAL.
    ps_ta02-frgsx = lv_frgsx.

    MOVE-CORRESPONDING ps_ta02 TO ls_approval.
    CLEAR:ls_approval-kolnr.
    PERFORM frm_get_kolnr  CHANGING   ls_approval
                                      pt_msglist.
    MOVE-CORRESPONDING ls_approval TO ps_ta02.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_FRGSX_EXE
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_ZREKEY
*&      <-- LV_FRGSX
*&      <-- PT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_frgsx_exe  USING    ls_zrekey TYPE zres0042
                          CHANGING lv_frgsx TYPE frgsx
                                   pt_msglist TYPE scp1_general_errors.

  DATA:lt_return TYPE TABLE OF bapiret2.
  DATA:ls_return TYPE bapiret2.
  DATA:
      ls_msglist TYPE LINE OF scp1_general_errors.

  CLEAR lv_frgsx.

  CALL FUNCTION 'ZREFM0025'
    EXPORTING
      iv_zrestype = 'RA'
      is_zrekey   = ls_zrekey
    IMPORTING
      ev_frgsx    = lv_frgsx
    TABLES
      et_return   = lt_return.

  LOOP AT lt_return INTO ls_return  WHERE  type = 'E' .
    ls_msglist-msgty = ls_return-type.
*    LS_MSGLIST-MSGID = LS_RETURN-ID.
    ls_msglist-msgno = ls_return-number.
    ls_msglist-msgv1 = ls_return-message.
    APPEND ls_msglist TO pt_msglist.
  ENDLOOP.
ENDFORM.


FORM frm_check_data_org_pd USING pt_data TYPE  tt_data
                                  pt_bukrs TYPE  tt_bukrs
                                  pt_werks TYPE  tt_werks
                       CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
    lt_bukrs TYPE TABLE OF zres0093,
    lt_dcwrk TYPE TABLE OF zres0095,
    lt_werks TYPE TABLE OF zres0094,
    ls_bukrs TYPE zres0093,
    ls_dcwrk TYPE zres0095,
    ls_werks TYPE zres0094.



  LOOP AT pt_data INTO DATA(ls_data).

    CLEAR:
    lt_bukrs ,
    lt_dcwrk ,
    lt_werks .

    DATA(lt_bukrs_tmp) = pt_bukrs[].
*    DATA(LT_DCWRK_TMP) = PT_DCWRK[].
    DATA(lt_werks_tmp) = pt_werks[].

    DELETE lt_bukrs_tmp WHERE seq NE ls_data-seq.
*    DELETE LT_DCWRK_TMP WHERE SEQ NE LS_DATA-SEQ.
    DELETE lt_werks_tmp WHERE seq NE ls_data-seq.

    LOOP AT lt_bukrs_tmp INTO DATA(ls_bukrs_tmp).
      CLEAR ls_bukrs.
      ls_bukrs-bukrs = ls_bukrs_tmp-bukrs.
      ls_bukrs-exclude = ls_bukrs_tmp-exclude.
      APPEND ls_bukrs TO lt_bukrs.
    ENDLOOP.

    LOOP AT lt_werks_tmp INTO DATA(ls_werks_tmp).
      CLEAR ls_werks.
      ls_werks-werks = ls_werks_tmp-werks.
      ls_werks-exclude = ls_werks_tmp-exclude.
      APPEND ls_werks TO lt_werks.
    ENDLOOP.




*  检查组织结构的层级关系
*________________________________________________________________________________________
*\       \ 排除                            \    \  新增                                  \
*\_______\_________________________________\____\________________________________________\
*\ 上级  \ 上级不存在、上级存在状态是排除  \    \ 上级存在（上级存在但是状态是排除允许） \
*\_______\_________________________________\____\________________________________________\
*\ 下级  \ 下级不允许排除                  \    \ 下级不允许新增                         \
*\_______\_________________________________\____\________________________________________\


    PERFORM frm_check_zzz_new TABLES      lt_bukrs
                                          lt_dcwrk
                                          lt_werks
                              USING       ls_data-seq
                           CHANGING       pt_msglist.

  ENDLOOP.


ENDFORM.


FORM frm_check_zzz_new  TABLES    pt_bukrs      STRUCTURE zres0093
                              pt_dcwrk      STRUCTURE zres0095
                              pt_werks      STRUCTURE zres0094
                        USING      pv_zitems
                     CHANGING pt_msglist    TYPE scp1_general_errors.

  DATA:
    lt_dc_buk   TYPE TABLE OF zres0094,
    lt_wrk_dc   TYPE TABLE OF  zres0094,
    ls_data_wrk TYPE zres0094,
    ls_dcwrk    TYPE zres0095.
  DATA:
        ls_msglist TYPE scp1_general_error.

  DATA(lt_bukrs) = pt_bukrs[].
  DATA(lt_dcwrk) = pt_dcwrk[].
  DATA(lt_werks) = pt_werks[].

  DELETE lt_bukrs WHERE bukrs IS INITIAL.
  DELETE lt_dcwrk WHERE dcwrk IS INITIAL.
  DELETE lt_werks WHERE werks IS INITIAL.


  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.


*  检查DC层级（与公司代码层级比较）

*********  11111 先取出公司代码层级对应的DC清单
********  READ TABLE LT_BUKRS TRANSPORTING NO FIELDS WITH KEY BUKRS = GC_ALL .
*********  IF SY-SUBRC EQ 0 OR LT_BUKRS[] IS INITIAL.
*********  若存在 ALL 的条目则无限制取数
********  IF SY-SUBRC EQ 0 .
********    SELECT
********      A~WERKS,
********      ' ' AS EXCLUDE
********      FROM T001W AS A  JOIN T001K AS B
********                       ON A~WERKS = B~BWKEY
********                       AND A~VLFKZ = 'B'
********      INTO CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
********
********  ELSE.
********
*********    取未排除的数据
********    SELECT
********      A~WERKS,
********      ' ' AS EXCLUDE
********      FROM T001W AS A  JOIN T001K AS B
********                       ON A~WERKS = B~BWKEY
********                       AND A~VLFKZ = 'B'
********                       JOIN @LT_BUKRS AS M
********                       ON B~BUKRS = M~BUKRS
********                       AND  M~EXCLUDE = ''
********      INTO CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
********
*********    取排除的数据
********    SELECT
********      A~WERKS,
********      'X' AS EXCLUDE
********      FROM T001W AS A  JOIN T001K AS B
********                       ON A~WERKS = B~BWKEY
********                       AND A~VLFKZ = 'B'
********                       JOIN @LT_BUKRS AS M
********                       ON B~BUKRS = M~BUKRS
********                       AND  M~EXCLUDE = 'X'
********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_DC_BUK.
********
********  ENDIF.
********
*********  22222  根据DC层级的DC清单 与 上一步公司代码层级对应的DC清单进行比较检查
********  SORT LT_DC_BUK BY EXCLUDE WERKS .
********  LOOP AT LT_DCWRK INTO LS_DCWRK .
********
*********    检查是否允许排除
********    IF LS_DCWRK-EXCLUDE = 'X'.
********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
********                                                              EXCLUDE = ''
********                                                              WERKS = LS_DCWRK-DCWRK
********                                                              BINARY SEARCH.
********      IF SY-SUBRC NE 0.
*********        PV_FLG_ERR = 'E'.
********        LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!'.
********        APPEND LS_MSGLIST TO PT_MSGLIST.
********      ENDIF.
********
********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
********                                                              EXCLUDE = 'X'
********                                                              WERKS = LS_DCWRK-DCWRK
********                                                              BINARY SEARCH.
********      IF SY-SUBRC EQ 0.
*********        PV_FLG_ERR = 'E'.
********        LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!'.
********        APPEND LS_MSGLIST TO PT_MSGLIST.
********      ENDIF.
********
*********    检查是否允许新增
********    ELSE.
********      READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
********                                                              EXCLUDE = 'X'
********                                                              WERKS = LS_DCWRK-DCWRK
********                                                              BINARY SEARCH.
********      IF SY-SUBRC EQ 0.
********      ELSE.
********        READ TABLE LT_DC_BUK TRANSPORTING NO FIELDS WITH KEY
********                                                                EXCLUDE = ''
********                                                                WERKS = LS_DCWRK-DCWRK
********                                                                BINARY SEARCH.
********        IF SY-SUBRC EQ 0.
*********          PV_FLG_ERR = 'E'.
********          LS_MSGLIST-MSGV1 = LS_DCWRK-DCWRK && '：存在上一级的情况下，下一级可以排除但不能被增加!'.
********          APPEND LS_MSGLIST TO PT_MSGLIST.
********        ENDIF.
********      ENDIF.
********    ENDIF.
********  ENDLOOP.

********
*********  检查门店层级（与DC代码层级比较）
********
********
*********  首先将公司代码级对应的DC清单 并入到DC级
********  LOOP AT LT_DC_BUK INTO LS_DATA_WRK.
********    CLEAR LS_DCWRK.
********    LS_DCWRK-DCWRK = LS_DATA_WRK-WERKS.
********    LS_DCWRK-EXCLUDE = LS_DATA_WRK-EXCLUDE.
********    APPEND LS_DCWRK TO LT_DCWRK.
********  ENDLOOP.
********
*********  11111 先取出DC层级对应的门店清单
********  READ TABLE LT_DCWRK TRANSPORTING NO FIELDS WITH KEY DCWRK = GC_ALL .
*********  IF SY-SUBRC EQ 0 OR LT_DCWRK[] IS INITIAL.
*********  若存在 ALL 的条目则无限制取数
********  IF SY-SUBRC EQ 0 .
********    SELECT
********      A~WERKS,
********      ' ' AS EXCLUDE
********      FROM T001W AS A
********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
********
********  ELSE.
********
*********    取未排除的数据
*********    SELECT
*********      A~WERKS,
*********      ' ' AS EXCLUDE
*********      FROM T001W AS A JOIN ZSDT0014 AS B
*********                      ON   A~WERKS = B~LOCNR
*********                      JOIN @LT_DCWRK AS M
*********                      ON   B~ZSJBM = M~DCWRK
*********                      AND  M~EXCLUDE = ''
*********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
********
********    SELECT
********      A~WERKS,
********      ' ' AS EXCLUDE
********      FROM T001W AS A JOIN WRF3 AS B
********                      ON   RIGHT( B~LOCNR,4 ) = A~WERKS
********                      JOIN @LT_DCWRK AS M
********                      ON   B~LOCLB = M~DCWRK
********                      AND  M~EXCLUDE = ''
********                      AND  A~VLFKZ = 'A'
********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
********
*********    取排除的数据
********    SELECT
********      A~WERKS,
********      'X' AS EXCLUDE
********      FROM T001W AS A JOIN WRF3 AS B
********                      ON   RIGHT( B~LOCNR,4 ) = A~WERKS
********                      JOIN @LT_DCWRK AS M
********                      ON   B~LOCLB = M~DCWRK
********                      AND  M~EXCLUDE = 'X'
********                      AND  A~VLFKZ = 'A'
********      APPENDING CORRESPONDING FIELDS OF TABLE @LT_WRK_DC.
********
********  ENDIF.


**********************************************************************

*  11111 先取出公司代码层级对应的地点清单
  READ TABLE lt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = 'ALL' .
*  IF SY-SUBRC EQ 0 OR LT_BUKRS[] IS INITIAL.
*  若存在 ALL 的条目则无限制取数
  IF sy-subrc EQ 0 .
    SELECT
      a~werks,
      ' ' AS exclude
*      FROM t001w AS a  JOIN t001k AS b ON a~werks = b~bwkey "ERP-17212  未上线批发加盟店  zrev010_wrk
      FROM zrev010_wrk_ddl AS a
*                       AND A~VLFKZ = 'B'
      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
  ELSE.

*    取未排除的数据
    SELECT
      a~werks,
      ' ' AS exclude
*      FROM t001w AS a  JOIN t001k AS b ON a~werks = b~bwkey "ERP-17212  未上线批发加盟店  zrev010_wrk
      FROM zrev010_wrk_ddl AS a
*                       AND A~VLFKZ = 'B'
                       JOIN @lt_bukrs AS m  ON a~bukrs = m~bukrs  AND  m~exclude = ''
      INTO CORRESPONDING FIELDS OF TABLE @lt_dc_buk.

**********************************************************************
    "ERP-17212  未上线批发加盟店  zrev010_wrk 已经处理 9901 逻辑
*    SELECT
*      a~werks,
*      ' ' AS exclude
*       FROM t001w     AS a JOIN t001k     AS b
*                           ON a~werks = b~bwkey AND bukrs = '9901'
*                           JOIN zsdt0014  AS d
*                           ON a~werks = d~locnr
*                           JOIN @lt_bukrs AS m
*                           ON d~bukrs = m~bukrs AND  m~exclude = ''
*      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
**********************************************************************
*    取排除的数据
    SELECT
      a~werks,
      'X' AS exclude
*      FROM t001w AS a  JOIN t001k AS b ON a~werks = b~bwkey "ERP-17212  未上线批发加盟店  zrev010_wrk
      FROM zrev010_wrk_ddl AS a
*                       AND A~VLFKZ = 'B'
                       JOIN @lt_bukrs AS m ON a~bukrs = m~bukrs  AND  m~exclude = 'X'
      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.

**********************************************************************
    "ERP-17212  未上线批发加盟店  zrev010_wrk 已经处理 9901 逻辑
*    SELECT
*      a~werks,
*      'X' AS exclude
*      FROM t001w AS a  JOIN t001k AS b
*                       ON a~werks = b~bwkey AND bukrs = '9901'
*                       JOIN zsdt0014  AS d
*                       ON a~werks = d~locnr
*                       JOIN @lt_bukrs AS m
*                       ON d~bukrs = m~bukrs
*                       AND  m~exclude = 'X'
*      APPENDING CORRESPONDING FIELDS OF TABLE @lt_dc_buk.
**********************************************************************
  ENDIF.

  CLEAR:lt_wrk_dc,lt_wrk_dc[].

  lt_wrk_dc[] = lt_dc_buk[].

**********************************************************************


*  22222  根据门店层级的门店清单 与 上一步公司代码层级对应的门店清单进行比较检查
  SORT lt_wrk_dc BY exclude werks .
  LOOP AT lt_werks INTO DATA(ls_werks) .
*    检查是否允许排除
    IF ls_werks-exclude = 'X'.
      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = ''
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc NE 0.
*        PV_FLG_ERR = 'E'.
*        LS_MSGLIST-MSGV1 = LS_WERKS-WERKS && '：上一级不存在的情况下，下一级不能被排除，但可以被增加!' && '行项目号：' && PV_ZITEMS.
*        APPEND LS_MSGLIST TO PT_MSGLIST.
        PERFORM frm_write_msg(zbcs0001) USING pv_zitems '：上一级不存在的情况下，下一级不能被排除，但可以被增加!' CHANGING pt_msglist.
      ENDIF.

      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
*        PV_FLG_ERR = 'E'.
*        LS_MSGLIST-MSGV1 = LS_WERKS-WERKS && '：上一级被排除的情况下，下一级不能被排除，但可以被增加!' && '行项目号：' && PV_ZITEMS.
*        APPEND LS_MSGLIST TO PT_MSGLIST.
        PERFORM frm_write_msg(zbcs0001) USING pv_zitems '：上一级被排除的情况下，下一级不能被排除，但可以被增加!' CHANGING pt_msglist.
      ENDIF.

*    检查是否允许新增
    ELSE.
      READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                              exclude = 'X'
                                                              werks = ls_werks-werks
                                                              BINARY SEARCH.
      IF sy-subrc EQ 0.
      ELSE.
        READ TABLE lt_wrk_dc TRANSPORTING NO FIELDS WITH KEY
                                                                exclude = ''
                                                                werks = ls_werks-werks
                                                                BINARY SEARCH.
        IF sy-subrc EQ 0.
*          PV_FLG_ERR = 'E'.
*          LS_MSGLIST-MSGV1 = LS_WERKS-WERKS && '：存在上一级的情况下，下一级可以排除但不能被增加!' && '行项目号：' && PV_ZITEMS.
*          APPEND LS_MSGLIST TO PT_MSGLIST.
          PERFORM frm_write_msg(zbcs0001) USING pv_zitems '：存在上一级的情况下，下一级可以排除但不能被增加!' CHANGING pt_msglist.
        ENDIF.
      ENDIF.
    ENDIF.
  ENDLOOP.


  READ TABLE lt_bukrs TRANSPORTING NO FIELDS WITH KEY bukrs = 'ALL' .
  IF sy-subrc EQ 0.
    LOOP AT lt_werks INTO ls_werks WHERE exclude = ''.
*      LS_MSGLIST-MSGV1 = LS_WERKS-WERKS && '公司代码为ALL时，DC/门店只能排除!' && '行项目号：' && PV_ZITEMS.
*      APPEND LS_MSGLIST TO PT_MSGLIST.
      PERFORM frm_write_msg(zbcs0001) USING pv_zitems '公司代码为ALL时，DC/门店只能排除!' CHANGING pt_msglist.
      EXIT.
    ENDLOOP.
  ENDIF.


  IF lt_bukrs[] IS INITIAL AND lt_werks[]  IS INITIAL.
*    LS_MSGLIST-MSGV1 = '【公司代码】和【门店/DC】不能同时为空' && '行项目ID:' && PV_ZITEMS.
*    APPEND LS_MSGLIST TO PT_MSGLIST.
    PERFORM frm_write_msg(zbcs0001) USING pv_zitems '【公司代码】和【门店/DC】不能同时为空!' CHANGING pt_msglist.
  ENDIF.


ENDFORM.


*FORM FRM_GET_KOLNR   CHANGING    PS_APPROVAL    TYPE  ZRES0088
*                                  PT_MSGLIST    TYPE SCP1_GENERAL_ERRORS.
*
*
*  DATA:LT_ZRETC007  TYPE TABLE OF ZRETC007.
*  DATA:LS_ZRETC007  TYPE ZRETC007.
*  DATA:LS_MSGLIST   TYPE SCP1_GENERAL_ERROR.
*  DATA:LV_NEXTLINE  TYPE SY-TABIX.
*  DATA:LV_UPZTTLINE TYPE ZRETC007-ZSTATS.
*
*  CLEAR:LS_MSGLIST.
*  LS_MSGLIST-MSGTY = 'E'.
**  LS_MSGLIST-MSGID = '00'.
*  LS_MSGLIST-MSGNO = '001'.
*
*
*  SELECT * INTO TABLE LT_ZRETC007 FROM ZRETC007 WHERE FRGSX = PS_APPROVAL-FRGSX.
*  IF SY-SUBRC <> 0.
*    LS_MSGLIST-MSGV1 = '审批策略未找到审批策略明细！'.
*    APPEND LS_MSGLIST TO PT_MSGLIST.
*    EXIT.
*  ENDIF.
*
*  SORT LT_ZRETC007 BY KOLNR .
*  IF  PS_APPROVAL-KOLNR IS INITIAL .
*    READ TABLE LT_ZRETC007 INTO LS_ZRETC007 INDEX 1.
*    IF SY-SUBRC = 0 .
*      PS_APPROVAL-KOLNR     = LS_ZRETC007-KOLNR.
*      PS_APPROVAL-FRGC1     = LS_ZRETC007-FRGC1.
*      PS_APPROVAL-ZFRGTX    = LS_ZRETC007-ZFRGTX .
*      PS_APPROVAL-ZXYZT     = 'N'.
*    ENDIF.
*  ELSE.
*    READ TABLE LT_ZRETC007 INTO LS_ZRETC007   WITH  KEY KOLNR = PS_APPROVAL-KOLNR.
*    IF SY-SUBRC = 0.
*      IF LS_ZRETC007-ZSTATS = 'A' .
*        PS_APPROVAL-KOLNR     = LS_ZRETC007-KOLNR.
*        PS_APPROVAL-FRGC1     = LS_ZRETC007-FRGC1.
*        PS_APPROVAL-ZFRGTX    = LS_ZRETC007-ZFRGTX .
*        PS_APPROVAL-ZXYZT     = LS_ZRETC007-ZSTATS.
*      ELSE.
*        LV_UPZTTLINE = LS_ZRETC007-ZSTATS.
*        LV_NEXTLINE  =  SY-TABIX + 1.
*
*
*        READ TABLE LT_ZRETC007 INTO LS_ZRETC007 INDEX LV_NEXTLINE.
*        IF SY-SUBRC <> 0 .
*          LS_MSGLIST-MSGV1 = '最终审批节点未维护，保持当前审批状态！'.
*          APPEND LS_MSGLIST TO PT_MSGLIST.
*          EXIT.
*        ELSE.
*          PS_APPROVAL-KOLNR     = LS_ZRETC007-KOLNR.
*          PS_APPROVAL-FRGC1     = LS_ZRETC007-FRGC1.
*          PS_APPROVAL-ZFRGTX    = LS_ZRETC007-ZFRGTX .
*          PS_APPROVAL-ZXYZT     = LV_UPZTTLINE.
*        ENDIF.
*      ENDIF.
*    ELSE.
*      LS_MSGLIST-MSGV1 = '当前审批节点未知，请修改后重新保存！'.
*      APPEND LS_MSGLIST TO PT_MSGLIST.
*      EXIT.
*    ENDIF.
*
*
*  ENDIF.
*ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_AUTH_CHECK_HT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_TA02
*&      <-- PT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_auth_check_ht  USING    pt_ta02  TYPE tt_ta02
                                 pt_data TYPE tt_data
                        CHANGING pt_msglist TYPE scp1_general_errors.
  DATA:
    pv_mtype TYPE bapi_mtype,
    pv_msg   TYPE scp1_general_error-msgv1.
  DATA:
    ls_msglist TYPE scp1_general_error,
    lt_msglist TYPE scp1_general_errors.

  DATA(lt_ta02_tmp) = pt_ta02[].
  SORT lt_ta02_tmp BY zht_id.
  DELETE ADJACENT DUPLICATES FROM lt_ta02_tmp COMPARING zht_id.
  SORT pt_data BY seg.
  LOOP AT lt_ta02_tmp INTO DATA(ls_ta02_tmp).
    IF ls_ta02_tmp-zht_id IS NOT INITIAL.
      SELECT SINGLE * FROM zreta001 WHERE zht_id = @ls_ta02_tmp-zht_id INTO @DATA(ls_zreta001).
      IF sy-subrc EQ 0.
        CLEAR:pv_mtype,pv_msg,lt_msglist[].
        PERFORM frm_author_check_ht_new USING ls_zreta001
                                              '01'
                                              'B'
                                    CHANGING lt_msglist
                                             pv_mtype
                                             pv_msg.
        READ TABLE pt_data INTO DATA(ls_data) WITH KEY seg = ls_ta02_tmp-seg BINARY SEARCH.
        IF sy-subrc EQ 0.
          ls_msglist-msgid = ls_data-seq.
          MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgid WHERE msgid = ''.
        ENDIF.
        APPEND LINES OF lt_msglist[] TO pt_msglist[].

      ENDIF.

      CLEAR ls_zreta001.

    ENDIF.
  ENDLOOP.
ENDFORM.


FORM frm_auth_check_tk  USING    pt_ta02  TYPE tt_ta02
                                 pt_data TYPE tt_data
                        CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
    pv_mtype TYPE bapi_mtype,
    pv_msg   TYPE scp1_general_error-msgv1.
  DATA:
    ls_msglist TYPE scp1_general_error,
    lt_msglist TYPE scp1_general_errors.

  SORT pt_data BY seg.
  LOOP AT pt_ta02 INTO DATA(ls_ta02).
    CLEAR:pv_mtype,pv_msg,lt_msglist.
    PERFORM frm_author_check_ekgrp USING ls_ta02-ekgrp
                                         '01'
                                   CHANGING
                                         pv_mtype
                                         pv_msg.

    IF pv_mtype = 'E'.
      PERFORM frm_author_pro USING
                                   pv_mtype
                                   pv_msg
                                   'B'
                             CHANGING lt_msglist.

      READ TABLE pt_data INTO DATA(ls_data) WITH KEY seg = ls_ta02-seg BINARY SEARCH.
      IF sy-subrc EQ 0.
        ls_msglist-msgid = ls_data-seq.
        MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgid WHERE msgid = ''.
      ENDIF.
      APPEND LINES OF lt_msglist[] TO pt_msglist[].

    ENDIF.


  ENDLOOP.

ENDFORM.


FORM frm_auth_check_xy  USING    pt_t06  TYPE tt_t06
                                 pt_data TYPE tt_data
                        CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:
    pv_mtype TYPE bapi_mtype,
    pv_msg   TYPE scp1_general_error-msgv1.

  DATA:
    ls_msglist TYPE scp1_general_error,
    lt_msglist TYPE scp1_general_errors.


  LOOP AT pt_t06 INTO DATA(ls_t06).
    CLEAR:pv_mtype,pv_msg,lt_msglist.
    PERFORM frm_author_check_zbukrs_tk USING ls_t06-zbukrs
                                         '01'
                                   CHANGING
                                         pv_mtype
                                         pv_msg.
    IF pv_mtype = 'E'.
      PERFORM frm_author_pro USING
                                   pv_mtype
                                   pv_msg
                                   'B'
                             CHANGING lt_msglist.


      ls_msglist-msgid = ls_t06-seq.
      MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgid WHERE msgid = ''.
      APPEND LINES OF lt_msglist[] TO pt_msglist[].
    ENDIF.
  ENDLOOP.
ENDFORM.


FORM frm_conver_msglist_2_msg  USING    pt_msglist   TYPE scp1_general_errors
                               CHANGING pt_data  TYPE tt_data.

  DATA:
    lv_msg  TYPE char255,
    ls_data TYPE LINE OF tt_data.

  DATA(lt_msglist) = pt_msglist[].
  SORT lt_msglist BY msgid.

  LOOP AT lt_msglist INTO DATA(ls_msglist).
    AT NEW msgid.
      DATA(lv_flg_new) = 'X'.
    ENDAT.
    AT END OF msgid.
      DATA(lv_flg_end) = 'X'.
    ENDAT.

    IF lv_flg_new = 'X'.
      CLEAR lv_msg.
      CLEAR ls_data-ztype_excel.
    ENDIF.
    lv_msg = lv_msg && '/' && ls_msglist-msgv1.
    IF ls_msglist-msgty = 'E'.
      ls_data-ztype_excel = 'E'.
    ENDIF.

    IF lv_flg_end = 'X'.
      CLEAR ls_data-zmsg_excel..
*      LS_DATA-ZTYPE_EXCEL = 'E'.
      ls_data-zmsg_excel = lv_msg.
      MODIFY pt_data FROM ls_data TRANSPORTING ztype_excel zmsg_excel WHERE seq = ls_msglist-msgid.
    ENDIF.
    CLEAR:lv_flg_new,lv_flg_end.
  ENDLOOP.



ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_ADD_MSGLIST
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PS_TA02
*&      --> PT_DATA
*&      --> LT_MSGLIST
*&      <-- PT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_add_msglist  USING    ps_ta02  TYPE LINE OF tt_ta02
                               pt_data  TYPE tt_data
                               lt_msglist TYPE scp1_general_errors
                      CHANGING pt_msglist TYPE scp1_general_errors.
  DATA:
    ls_msglist TYPE scp1_general_error.

  READ TABLE pt_data INTO DATA(ls_data) WITH KEY seg = ps_ta02-seg BINARY SEARCH.
  IF sy-subrc EQ 0.
    ls_msglist-msgid = ls_data-seq.
    MODIFY lt_msglist FROM ls_msglist TRANSPORTING msgid WHERE msgid = ''.
  ENDIF.
  APPEND LINES OF lt_msglist[] TO pt_msglist[].

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_PRO_DATA_QJ_NEW
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T15
*&      --> PT_T16
*&      --> PT_T06
*&---------------------------------------------------------------------*
FORM frm_pro_data_qj_new  TABLES   pt_t15 STRUCTURE zret0015
                                   pt_t16 STRUCTURE zret0016
                          USING    pt_t06 TYPE tt_t06.

  DATA:
    lt_t06 TYPE TABLE OF zret0006,
    ls_t06 TYPE zret0006.

  LOOP AT pt_t06 INTO DATA(ps_t06).
    CLEAR ls_t06.
    MOVE-CORRESPONDING ps_t06 TO ls_t06.
    APPEND ls_t06 TO lt_t06.
  ENDLOOP.

  CHECK lt_t06[] IS NOT INITIAL.

  PERFORM frm_pro_data_qj_exe TABLES pt_t15
                                     pt_t16
                                     lt_t06.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_BIN_DATA_T20
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> LS_DATA_F12
*&      --> LS_DATA_F14
*&      --> LS_DATA_F16
*&      --> LS_DATA_F17
*&      <-- PT_T20
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_bin_data_t20  USING
                                pv_seq
                                pv_seg
                                pv_matnr
                                pv_zcmpst
                                pv_zbuy
                                pv_zfree
                       CHANGING pt_t20  TYPE tt_t20
                                lt_msglist     TYPE scp1_general_errors.

  DATA:
        ls_t20 TYPE LINE OF tt_t20.


  ls_t20-seg = pv_seg.
  ls_t20-matnr = pv_matnr.
  ls_t20-matnr = |{ ls_t20-matnr ALPHA = IN WIDTH = 18 }|.

  PERFORM frm_assign_value(zbcs0001) USING pv_zcmpst CHANGING ls_t20-zcmpst.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '加提金额错误' CHANGING lt_msglist.
  ENDIF.

  PERFORM frm_assign_value(zbcs0001) USING pv_zbuy CHANGING ls_t20-zbuy.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '【买】错误' CHANGING lt_msglist.
  ENDIF.

  PERFORM frm_assign_value(zbcs0001) USING pv_zfree CHANGING ls_t20-zfree.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '【赠】错误' CHANGING lt_msglist.
  ENDIF.

  APPEND ls_t20 TO pt_t20.
ENDFORM.


FORM frm_bin_data_t58  USING
                                pv_seq
                                pv_seg
                                pv_zitems
                                pv_matnr
                                pv_zbuy
                                pv_zfree
                       CHANGING pt_t58  TYPE tt_t58
                                lt_msglist     TYPE scp1_general_errors.

  DATA:
        ls_t58 TYPE LINE OF tt_t58.


  ls_t58-seg = pv_seg.
  ls_t58-zitems = pv_zitems.
  ls_t58-matnr = pv_matnr.
  ls_t58-matnr = |{ ls_t58-matnr ALPHA = IN WIDTH = 18 }|.

  PERFORM frm_assign_value(zbcs0001) USING pv_zbuy CHANGING ls_t58-zbuy.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '【买】错误' CHANGING lt_msglist.
  ENDIF.

  PERFORM frm_assign_value(zbcs0001) USING pv_zfree CHANGING ls_t58-zfree.
  IF sy-subrc NE 0.
    PERFORM frm_write_msg(zbcs0001) USING pv_seq '【赠】错误' CHANGING lt_msglist.
  ENDIF.

  APPEND ls_t58 TO pt_t58.
ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_ZCTGR_ZPAYTP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> PT_T44
*&      --> PT_T06
*&      <-- LT_MSGLIST
*&---------------------------------------------------------------------*
FORM frm_check_zctgr_zpaytp  USING   pt_data TYPE  tt_data
                                      pt_t44  TYPE tt_t44
                                      pt_t06  TYPE tt_t06
                                      pv_flg  TYPE char1
                             CHANGING lt_msglist TYPE scp1_general_errors.

  DATA:lv_zflzff TYPE zret0044-zflzff.
  DATA:lv_zsfsx  TYPE zretcm13-zsfsx.

*    当付款方级别为A时，必须有一行 组织级别为A&协议主体=付款方的协议
*    当付款方级别为B时，必须有一行 组织级别为R&协议主体=付款方的协议

  IF pv_flg = 'A'.
    DATA(lv_value) = 'A'.
  ELSEIF pv_flg = 'B'.
    lv_value = 'R'.
  ENDIF.


  DATA(lt_t44_tmp) = pt_t44[].
  DATA(lt_t06_tmp) = pt_t06[].

  SORT pt_t44 BY seq zpaytp.
  SORT lt_t44_tmp BY seq zflzff.
  LOOP AT pt_t06 INTO DATA(ls_t06).

    READ TABLE pt_data INTO DATA(ls_data) WITH  KEY seg = ls_t06-seg.
    IF sy-subrc = 0.
      CLEAR:lv_zsfsx.
      SELECT SINGLE zsfsx INTO lv_zsfsx FROM zretcm13 INNER JOIN zreta001 ON zretcm13~zhtlx = zreta001~zhtlx  WHERE zht_id = ls_data-f1.
    ENDIF.


    READ TABLE pt_t44 TRANSPORTING NO FIELDS WITH KEY seq = ls_t06-seq zpaytp = pv_flg BINARY SEARCH.
    IF sy-subrc EQ 0 AND lv_zsfsx <> '2' .

      LOOP AT lt_t06_tmp INTO DATA(ls_t06_tmp) WHERE seg = ls_t06-seg
                                                  AND seq NE ls_t06-seq
                                                  AND zctgr = lv_value.
        CLEAR lv_zflzff.
        lv_zflzff = ls_t06_tmp-zbukrs.
        lv_zflzff = |{ lv_zflzff ALPHA = IN }|.
        READ TABLE lt_t44_tmp TRANSPORTING NO FIELDS WITH KEY seq = ls_t06-seq zflzff = lv_zflzff BINARY SEARCH.
        IF sy-subrc EQ 0.
          DATA(lv_flg) = 'X'.
          EXIT.
        ENDIF.

      ENDLOOP.


      IF lv_flg = ''.
        IF pv_flg = 'A'.
          PERFORM frm_write_msg_new(zbcs0001) USING  ls_t06-seq '同一个条款内，当付款方级别为A时，必须有一行组织级别为A&协议主体=本协议付款方的协议' ''  CHANGING lt_msglist.
        ELSE.
          PERFORM frm_write_msg_new(zbcs0001) USING  ls_t06-seq '同一个条款内，当付款方级别为B时，必须有一行 组织级别为R&协议主体=本协议付款方的协议' '' CHANGING lt_msglist.
        ENDIF.
      ENDIF.
      CLEAR lv_flg.
    ENDIF.
  ENDLOOP.







ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GV_FLG
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_get_data  USING    pv_flg  TYPE char2
                   CHANGING pt_data TYPE tt_data.

  IF pv_flg = '01'.
    PERFORM frm_get_data_01 CHANGING gt_data.
  ELSE.
    PERFORM frm_get_data_02 CHANGING gt_data.
  ENDIF.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA_01
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&---------------------------------------------------------------------*
FORM frm_get_data_01  CHANGING pt_data TYPE tt_data.
  DATA:
        lt_data_excel_01 TYPE tt_data_excel_01.

  PERFORM frm_excel_load_a(zbcs0001)  TABLES lt_data_excel_01 USING p_file 9.

  IF lines( lt_data_excel_01[] ) > 6000.
    MESSAGE s888(sabapdocu) WITH 'EXCEL条目数不能超过6000行！' DISPLAY LIKE 'E' .
    LEAVE LIST-PROCESSING.
  ENDIF.

  PERFORM frm_excel_data_get_01 USING lt_data_excel_01
                                CHANGING pt_data.
ENDFORM.

FORM frm_get_data_02  CHANGING pt_data TYPE tt_data.

  DATA:
        lt_data_excel_02 TYPE tt_data_excel_02.

  PERFORM frm_excel_load_a(zbcs0001)  TABLES lt_data_excel_02 USING p_file 9.

  IF lines( lt_data_excel_02[] ) > 6000.
    MESSAGE s888(sabapdocu) WITH 'EXCEL条目数不能超过6000行！' DISPLAY LIKE 'E' .
    LEAVE LIST-PROCESSING.
  ENDIF.

  PERFORM frm_excel_data_get_02 USING lt_data_excel_02
                                CHANGING pt_data.
ENDFORM.

FORM frm_excel_data_get_02  USING    pt_data_excel_02 TYPE tt_data_excel_02
                        CHANGING pt_data TYPE tt_data.


  DATA:
    ls_data       TYPE LINE OF tt_data.

  LOOP AT pt_data_excel_02 INTO DATA(ls_data_excel).
    CLEAR ls_data.
    ls_data-seq = sy-tabix.
    MOVE-CORRESPONDING ls_data_excel TO ls_data.
    APPEND ls_data TO pt_data.
  ENDLOOP.


*  按抬头分组
  PERFORM frm_set_seg_02 CHANGING pt_data.

*  分组后 每个条款行项目不得超过750 行
  SELECT
    i~ztk_id,
    COUNT(*) AS zcount
    FROM @pt_data AS i
    GROUP BY i~ztk_id
    INTO TABLE @DATA(lt_data_count).

  LOOP AT lt_data_count TRANSPORTING NO FIELDS WHERE zcount > 750.
    EXIT.
  ENDLOOP.
  IF sy-subrc EQ 0.
    MESSAGE s888(sabapdocu) WITH '单个条款的行项目不能超过750行' DISPLAY LIKE 'E' .
    LEAVE LIST-PROCESSING.
  ENDIF.

  SORT pt_data BY seq.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_ZXYZT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_TA02
*&      <-- PT_T06
*&---------------------------------------------------------------------*
FORM frm_set_zxyzt  USING    pt_ta02 TYPE tt_ta02
                    CHANGING pt_t06 TYPE tt_t06.

  SORT pt_ta02 BY seg.
  LOOP AT pt_t06 INTO DATA(ls_t06).
    READ TABLE pt_ta02 INTO DATA(ls_ta02) WITH KEY seg = ls_t06-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t06-zxyzt = 'A'.
      ls_t06-kolnr = ls_ta02-kolnr.
      ls_t06-frgsx = ls_ta02-frgsx.
      MODIFY pt_t06 FROM ls_t06.
    ENDIF.
  ENDLOOP.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MODIFY_ALV
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_TA02
*&      --> PT_T06
*&      --> PT_T09
*&      <-- LT_DATA
*&---------------------------------------------------------------------*
FORM frm_modify_alv  USING    pt_ta02 TYPE tt_ta02
                              pt_t06  TYPE tt_t06
                              pt_t09  TYPE tt_t09
                     CHANGING lt_data TYPE tt_data.

  SORT pt_ta02 BY seg.
  SORT pt_t06 BY seq.
  SORT pt_t09 BY seg.
  LOOP AT lt_data INTO DATA(ls_data).
    READ TABLE pt_t09 INTO DATA(ls_t09) WITH KEY seg = ls_data-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zspz_id = ls_t09-zspz_id.
    ENDIF.
    READ TABLE pt_ta02 INTO DATA(ls_ta02) WITH KEY seg = ls_data-seg BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-ztk_id = ls_ta02-ztk_id.
    ENDIF.
    READ TABLE pt_t06 INTO DATA(ls_t06) WITH KEY seq = ls_data-seq BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zxy_id = ls_t06-zxy_id.
    ENDIF.
    ls_data-status = '3'.
    ls_data-ztype_impt = 'S'.
    ls_data-zmsg_excel = '导入成功！'.
    PERFORM frm_set_status  CHANGING ls_data.
    MODIFY lt_data FROM ls_data.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ADD_DATA_ZXY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> PT_DATA
*&      <-- LT_T44_TMP
*&      <-- LT_T06_TMP
*&---------------------------------------------------------------------*
FORM frm_add_data_zxy  USING    pt_data TYPE tt_data
                       CHANGING pt_t44  TYPE tt_t44
                                pt_t06  TYPE tt_t06.

  DATA:
    lt_t44_tmp TYPE tt_t44,
    lt_t06_tmp TYPE tt_t06.

  SELECT
    a~*
    FROM @pt_data AS i JOIN zret0006 AS a
                         ON i~ztk_id = a~ztk_id
    INTO CORRESPONDING FIELDS OF TABLE @lt_t06_tmp.

  SELECT
    b~*
    FROM @pt_data AS i JOIN zret0006 AS a
                         ON i~ztk_id = a~ztk_id
                       JOIN zret0044 AS b
                         ON a~zxy_id = b~zxy_id
    INTO CORRESPONDING FIELDS OF TABLE @lt_t44_tmp.

  DATA(lt_data_tmp) = pt_data[].
  SORT lt_data_tmp BY ztk_id.

  LOOP AT lt_t06_tmp INTO DATA(ls_t06_tmp).
    READ TABLE lt_data_tmp INTO DATA(ls_data_tmp) WITH KEY ztk_id = ls_t06_tmp-ztk_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t06_tmp-seg = ls_data_tmp-seg.
    ENDIF.
    ls_t06_tmp-seq = ls_t06_tmp-zxy_id.
    MODIFY lt_t06_tmp FROM ls_t06_tmp.
  ENDLOOP.

  SORT lt_t06_tmp BY zxy_id.
  LOOP AT lt_t44_tmp INTO DATA(ls_t44_tmp).

    READ TABLE lt_t06_tmp INTO ls_t06_tmp WITH KEY zxy_id = ls_t44_tmp-zxy_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_t44_tmp-seg = ls_t06_tmp-seg.
    ENDIF.
    ls_t44_tmp-seq = ls_t44_tmp-zxy_id.
    MODIFY lt_t44_tmp FROM ls_t44_tmp.
  ENDLOOP.

  APPEND LINES OF lt_t06_tmp TO pt_t06.
  APPEND LINES OF lt_t44_tmp TO pt_t44.



ENDFORM.


FORM frm_are_you_sure_2  USING      pv_message1 pv_message2
                                  pv_title.
*                       CHANGING   sy-subrc .
  DATA:
        pv_answer TYPE char1.
  CLEAR pv_answer.
  CALL FUNCTION 'POPUP_TO_DECIDE_INFO'
    EXPORTING
      defaultoption = 'N'
      textline1     = pv_message1
      textline2     = pv_message2
      titel         = pv_title
      start_column  = 25
      start_row     = 6
    IMPORTING
      answer        = pv_answer
    EXCEPTIONS
      OTHERS        = 1.

*  pv_answer 'J' yes 'A' no
  IF pv_answer = 'J'.
    sy-subrc = 0.
  ELSE.
    sy-subrc = '4'.
  ENDIF.

ENDFORM.