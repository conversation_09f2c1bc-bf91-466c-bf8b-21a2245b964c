*&---------------------------------------------------------------------*
*& 包含               ZRED0056_C01
*&---------------------------------------------------------------------*

DATA:
  grf_container TYPE REF TO cl_gui_docking_container,
  grf_alv       TYPE REF TO cl_gui_alv_grid.


CLASS sec_lcl_event_receiver DEFINITION DEFERRED.
*----------------------------------------------------------------------*
*       CLASS SEC_LCL_EVENT_RECEIVER DEFINITION
*----------------------------------------------------------------------*
*
*----------------------------------------------------------------------*
CLASS sec_lcl_event_receiver DEFINITION.
  PUBLIC SECTION.
    DATA:
      objid    TYPE char10.

    METHODS:
      constructor
        IMPORTING
          i_objid TYPE char10 OPTIONAL,
      sec_handle_toolbar
                    FOR EVENT toolbar OF cl_gui_alv_grid
        IMPORTING e_object e_interactive,
      sec_handle_bef_user_command
                    FOR EVENT before_user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_user_command
                    FOR EVENT user_command OF cl_gui_alv_grid
        IMPORTING e_ucomm,
      sec_handle_hotspot_click
                    FOR EVENT hotspot_click OF cl_gui_alv_grid
        IMPORTING e_row_id e_column_id es_row_no,
      sec_handle_double_click
                    FOR EVENT double_click OF cl_gui_alv_grid
        IMPORTING e_row e_column es_row_no,
      sec_handle_data_changed
                    FOR EVENT data_changed OF cl_gui_alv_grid
        IMPORTING er_data_changed e_onf4 e_onf4_before e_onf4_after,
      sec_handle_data_changed_fin
                    FOR EVENT data_changed_finished OF cl_gui_alv_grid
        IMPORTING e_modified et_good_cells.
ENDCLASS.                    "LCL_EVENT_RECEIVER DEFINITION

*----------------------------------------------------------------------*
*       CLASS SEC_LCL_EVENT_RECEIVER IMPLEMENTATION
*----------------------------------------------------------------------*
*
*----------------------------------------------------------------------*
CLASS sec_lcl_event_receiver IMPLEMENTATION.

  METHOD constructor.
    objid = i_objid.
  ENDMETHOD.                    "CONSTRUCTOR
  METHOD sec_handle_toolbar.
    PERFORM handle_toolbar USING e_object e_interactive objid.
  ENDMETHOD.                    "HANDLE_TOOLBAR
  METHOD sec_handle_bef_user_command.
    PERFORM handle_bef_user_command USING e_ucomm.
  ENDMETHOD.                "BEFORE_USER_COMMAND
  METHOD sec_handle_user_command.
    PERFORM handle_user_commmand CHANGING e_ucomm objid.
  ENDMETHOD.                   "HANDLE USER COMMAND
  METHOD sec_handle_hotspot_click.
    PERFORM handle_hotspot_click USING e_row_id e_column_id es_row_no objid.
  ENDMETHOD.                   "HANDLE_HOTSPOT_CLICK
  METHOD sec_handle_double_click.
    PERFORM handle_double_click USING e_row e_column es_row_no objid.
  ENDMETHOD.                  "HANDLE_DOUBLE_CLICK
  METHOD sec_handle_data_changed.
    PERFORM handle_data_changed USING er_data_changed objid.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED
  METHOD sec_handle_data_changed_fin.
    PERFORM handle_data_changed_fin USING e_modified et_good_cells objid.
  ENDMETHOD.                    "HANDLE_DATA_CHANGED_FINISHED
ENDCLASS.               "LCL_EVENT_RECEIVER