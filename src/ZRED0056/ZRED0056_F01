*&---------------------------------------------------------------------*
*& 包含               ZRED0056_F01
*&---------------------------------------------------------------------*

*&---------------------------------------------------------------------*
*& FORM FRM_AUTH_CHECK
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_auth_check .

  "权限检查
*  DATA:LV_SUBRC TYPE C,
*       LV_MESS  TYPE BAPIRET2-MESSAGE.
*
*  CALL FUNCTION 'ZBCFM0001'
*    EXPORTING
*      IV_OBJECT = 'ZMMAR009'
*      IV_FIELD  = 'BUKRS'
*      IV_MUST   = 'X'
*    IMPORTING
*      EX_SUBRC  = LV_SUBRC
*      EX_MESS   = LV_MESS
*    TABLES
*      IT_TAB    = S_BUKRS.
*  IF LV_SUBRC = 'E'.
*    MESSAGE LV_MESS TYPE LV_SUBRC.
*  ENDIF.

ENDFORM.




FORM frm_select_file .
  CALL FUNCTION 'WS_FILENAME_GET'
    EXPORTING
      mask             = ',EXCEL FILES,*.XLS;*.XLSX,ALL FILES,*.*.'
      title            = '选择文件'
    IMPORTING
      filename         = p_file
    EXCEPTIONS
      inv_winsys       = 1
      no_batch         = 2
      selection_cancel = 3
      selection_error  = 4
      OTHERS           = 5.
  IF sy-subrc <> 0 AND sy-subrc <> 3.
    MESSAGE e100(zdev) WITH '选择文件出错！'.
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FRM_MAIN
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*& -->  P1        TEXT
*& <--  P2        TEXT
*&---------------------------------------------------------------------*
FORM frm_main .

  DATA:
    lt_msglist     TYPE scp1_general_errors.

  PERFORM frm_init.

  PERFORM frm_get_data USING gv_flg CHANGING gt_data.

  IF gv_flg = '01'.
    PERFORM frm_conv_data_main_01 USING gt_data
                               CHANGING
                                     gt_ta02
                                     gt_t09
                                     gt_t20
                                     gt_ta05
                                     gt_t58
                                     gt_ta06
                                     gt_tc05
                                     gt_t06
                                     gt_t44
                                     gt_t81
                                     gt_t82
                                     gt_bukrs
                                     gt_werks
                                     gt_ekorg
                                     lt_msglist.

    PERFORM frm_data_check_01  USING
                                     gt_ta02
                                     gt_t09
                                     gt_t20
                                     gt_ta05
                                     gt_t58
                                     gt_ta06
                                     gt_t06
                                     gt_t44
                                     gt_t81
                                     gt_t82
                                     gt_bukrs
                                     gt_werks
                                     gt_ekorg
                            CHANGING gt_data
                                     lt_msglist.
  ELSE.

    PERFORM frm_conv_data_main_02 USING gt_data
                               CHANGING
                                     gt_ta02
                                     gt_t06
                                     gt_t44
                                     gt_t81
                                     gt_t82
                                     gt_bukrs
                                     gt_werks
                                     gt_ekorg
                                     lt_msglist.

    PERFORM frm_data_check_02  USING
                                     gt_ta02
                                     gt_t09
                                     gt_t20
                                     gt_ta05
                                     gt_t58
                                     gt_ta06
                                     gt_t06
                                     gt_t44
                                     gt_t81
                                     gt_t82
                                     gt_bukrs
                                     gt_werks
                                     gt_ekorg
                            CHANGING gt_data
                                     lt_msglist.

  ENDIF.



  PERFORM frm_data_pro_end      CHANGING gt_data.

  CALL SCREEN 9000.

  "解锁
  CALL FUNCTION 'DEQUEUE_ALL'.

ENDFORM.


FORM frm_init.

  CASE 'X'.
    WHEN rb_01.      gv_flg = '01'.
    WHEN rb_02.      gv_flg = '02'.
    WHEN OTHERS.
  ENDCASE.

*  PERFORM frm_pro_lock(zbcs0001)  USING '促销返利条款批导' ''.

  IF gv_flg = '02'.
    PERFORM frm_are_you_sure_2 USING '促销条款内批量新增协议功能仅支持不参与运算的协议新增,' '将直接被设置为审批通过状态，是否继续' sy-TITLE.
    IF sy-subrc NE 0.
      MESSAGE s888(sabapdocu) WITH  '用户取消' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  ENDIF.


ENDFORM.


*&---------------------------------------------------------------------*
*&      FORM  HANDLE_DATA_CHANGED
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->DATA_CHANGED  TEXT
*----------------------------------------------------------------------*
FORM handle_data_changed USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               pv_objid TYPE char10.

  DATA:
  ls_mod_cell TYPE lvc_s_modi.
  DATA:
  ls_data TYPE LINE OF tt_data.




ENDFORM.                    " HANDLE_DATA_CHANGED
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_DATA_CHANGED_FINISHED
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->E_MODIFIED TEXT
*----------------------------------------------------------------------*
FORM handle_data_changed_fin USING pv_modified TYPE char01
                                    pt_good_cells  TYPE lvc_t_modi
                                    pv_objid TYPE char10 .

*  PERFORM FRM_REFRESH_ALV USING GRF_ALV.
ENDFORM.                    "HANDLE_DATA_CHANGED_FINISHED
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_TOOLBAR
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_OBJECT       TEXT
*      -->P_E_INTERACTIVE  TEXT
*----------------------------------------------------------------------*
FORM handle_toolbar USING prf_object TYPE REF TO cl_alv_event_toolbar_set
                          pv_interactive
                          pv_objid TYPE char10.
  DATA: lw_toolbar TYPE stb_button.

  DEFINE add_toolbar.
    lw_toolbar-butn_type  = &1.
    lw_toolbar-function   = &2.
    lw_toolbar-icon       = &3.
    lw_toolbar-quickinfo  = &4.
    lw_toolbar-text       = &4.
    lw_toolbar-disabled   = &5.
    APPEND lw_toolbar TO prf_object->mt_toolbar .
    CLEAR lw_toolbar.
  END-OF-DEFINITION.

*删除其他按钮
  DELETE prf_object->mt_toolbar WHERE
                                    function = '&GRAPH'
                                 OR function = '&INFO'
                                 OR function = '&DETAIL'
                                 OR function = '&REFRESH'
                                 OR function = '&CHECK'
                                 OR function = '&MB_VIEW'
                                 OR function = '&PRINT_BACK'
                                 OR function = '&LOCAL&CUT'
                                 OR function = '&LOCAL&COPY'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&PASTE'
                                 OR function = '&LOCAL&UNDO'
                                 OR function = '&LOCAL&APPEND'
                                 OR function = '&LOCAL&INSERT_ROW'
                                 OR function = '&LOCAL&DELETE_ROW'
                                 OR function = '&LOCAL&COPY_ROW'
                                 .

  add_toolbar:
*    '0' 'ALL'   '@4B@' '全选' '' ,
*    '0' 'DALL'  '@4D@' '取消' ''  ,
    '0' 'IMPT'  '@48@' '导入' ''  .


ENDFORM.                    " HANDLE_TOOLBAR
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_BEFORE_USER_COMMAND
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_UCOMM  TEXT
*----------------------------------------------------------------------*
FORM handle_bef_user_command  USING pv_ucomm.

ENDFORM.                    " HANDLE_BEFORE_USER_COMMAND
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_USER_COMMMAND
*&---------------------------------------------------------------------*
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM handle_user_commmand CHANGING pv_ucomm
                          pv_objid TYPE char10.

  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  DATA:lt_rows TYPE lvc_t_row,
       lw_rows TYPE lvc_s_row.

  CALL METHOD grf_alv->get_selected_rows
    IMPORTING
      et_index_rows = lt_rows.

*  PERFORM FRM_SET_SEL TABLES LT_ROWS GT_DATA USING 'SEL_MAN'.

  CASE pv_ucomm.
    WHEN 'ALL'.
      PERFORM frm_set_all(zbcs0001) TABLES gt_data USING 'SEL_MAN' .
    WHEN 'DALL'.
      PERFORM frm_set_sal(zbcs0001) TABLES gt_data USING 'SEL_MAN' .
*    WHEN 'INS'.
*      PERFORM FRM_INSERT_DATA CHANGING GT_DATA.
*    WHEN 'DEL'.
*      PERFORM FRM_DELETE_DATA CHANGING GT_DATA.
    WHEN 'IMPT'.

*      默认全选
      PERFORM frm_set_all(zbcs0001) TABLES gt_data USING 'SEL_MAN' .

      PERFORM frm_check_bf_save USING gt_data CHANGING lv_mtype lv_msg.
      IF lv_mtype = 'E'.
        MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.


      PERFORM frm_pro_lock(zbcs0001)               USING '条款创建' ''.

      IF gv_flg = '01'.
        PERFORM frm_data_save_main_01  CHANGING
                                             gt_data
                                             gt_ta02
                                             gt_t09
                                             gt_t20
                                             gt_ta05
                                             gt_t58
                                             gt_ta06
                                             gt_tc05
                                             gt_t06
                                             gt_t44
                                             gt_t81
                                             gt_t82
                                             gt_bukrs
                                             gt_werks
                                             gt_ekorg
                                             lv_mtype
                                             lv_msg.
      ELSEIF gv_flg = '02'.
        PERFORM frm_data_save_main_02  CHANGING
                                             gt_data
                                             gt_ta02
                                             gt_t09
                                             gt_t20
                                             gt_ta05
                                             gt_t58
                                             gt_ta06
                                             gt_tc05
                                             gt_t06
                                             gt_t44
                                             gt_t81
                                             gt_t82
                                             gt_bukrs
                                             gt_werks
                                             gt_ekorg
                                             lv_mtype
                                             lv_msg.

      ENDIF.


      PERFORM frm_pro_lock(zbcs0001)               USING '条款创建' 'X'.
      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.


  ENDCASE.

  PERFORM frm_refresh_alv USING grf_alv.
ENDFORM.                    " HANDLE_USER_COMMMAND
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_HOTSPOT_CLICK
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_ROW_ID  TEXT
*      -->P_E_COLUMN_ID  TEXT
*      -->P_ES_ROW_NO  TEXT
*----------------------------------------------------------------------*
FORM handle_hotspot_click  USING   ps_row_id TYPE lvc_s_row
                                    ps_column_id TYPE lvc_s_col
                                    ps_row_no TYPE lvc_s_roid
                                    pv_objid TYPE char10.

ENDFORM.                    " HANDLE_HOTSPOT_CLICK
*&---------------------------------------------------------------------*
*&      FORM  HANDLE_DOUBLE_CLICK
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_E_ROW_ID  TEXT
*      -->P_E_COLUMN_ID  TEXT
*      -->P_ES_ROW_NO  TEXT
*----------------------------------------------------------------------*
FORM handle_double_click  USING   ps_row_id TYPE lvc_s_row
                                   ps_column_id TYPE lvc_s_col
                                   ps_row_no TYPE lvc_s_roid
                                   pv_objid TYPE char10.
ENDFORM.                    " HANDLE_DOUBLE_CLICK