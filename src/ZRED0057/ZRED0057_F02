*&---------------------------------------------------------------------*
*& 包含               ZRED0057_F02
*&---------------------------------------------------------------------*


FORM frm_caller_exit USING e_grid TYPE slis_data_caller_exit.

  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.

  CHECK sy-batch = ''.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR'
    IMPORTING
      e_grid = lrf_alv.

  CALL METHOD lrf_alv->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_enter
    EXCEPTIONS
      error      = 1
      OTHERS     = 2.

  CALL METHOD lrf_alv->register_edit_event
    EXPORTING
      i_event_id = cl_gui_alv_grid=>mc_evt_modified
    EXCEPTIONS
      error      = 1
      OTHERS     = 2.

  DATA: lrf_event TYPE REF TO sec_lcl_event_receiver .
  CREATE OBJECT lrf_event.
  SET HANDLER lrf_event->sec_handle_data_changed FOR lrf_alv.
  SET HANDLER lrf_event->sec_handle_data_changed_fin FOR lrf_alv.


ENDFORM.                    "FM_BUTTON

FORM handle_data_changed USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol.

*****  DATA:
*****    ls_mod_cell TYPE lvc_s_modi,
*****    lt_mod_cell TYPE lvc_t_modi.
*****  DATA:
*****    lv_error      TYPE REF TO cx_sy_conversion_no_number,
*****    lv_error_text TYPE string.
*****  FIELD-SYMBOLS:
*****                 <fs_any> TYPE any.
*****  DATA:
*****        ls_data TYPE ty_data.
*****
*****  CLEAR: ls_data.
*****
*****  SORT prf_data_changed->mt_mod_cells BY row_id.
*****
*****
*****
*****  LOOP AT prf_data_changed->mt_mod_cells INTO     ls_mod_cell
*****                                          WHERE   fieldname = 'WERKS'.
*****
*****    AT NEW row_id.
*****      DATA(lv_flg_new) = 'X'.
*****    ENDAT.
*****
*****    AT END OF row_id.
*****      DATA(lv_flg_end) = 'X'.
*****    ENDAT.
*****
*****
*****    IF ls_mod_cell-fieldname = 'WERKS'.
*****
*****      READ TABLE gt_data ASSIGNING FIELD-SYMBOL(<fs_data>) INDEX ls_mod_cell-row_id.
*****      IF sy-subrc EQ 0 .
*****        PERFORM frm_data_changed_check USING prf_data_changed
*****                                             ls_mod_cell
*****                                             <fs_data>.
*****
*****      ENDIF.
*****    ENDIF.
*****
*****    IF lv_flg_new = 'X'.
*****      CLEAR ls_data.
*****      READ TABLE gt_data INTO ls_data INDEX ls_mod_cell-row_id.
*****
*****    ENDIF.
*****
*****
*****    CASE ls_mod_cell-fieldname.
*****      WHEN 'WERKS'.
*****
*****        CLEAR: lv_error,lv_error_text.
*****
*****        TRY .
*****            MOVE:ls_mod_cell-value TO ls_data-werks.
*****          CATCH cx_sy_conversion_no_number INTO lv_error.
*****            lv_error_text = lv_error->get_text( ).
*****            lv_error_text       = |数据类型不匹配|  .
*****          CLEANUP.
*****        ENDTRY.
*****
*****        IF lv_error_text IS NOT INITIAL.
*****          PERFORM frm_add_protocol_entry USING prf_data_changed
*****                                               ls_mod_cell
*****                                               lv_error_text.
*****        ENDIF.
*****
*****      WHEN OTHERS.
*****    ENDCASE.
*****
*****    IF lv_flg_end = 'X'.
*****
*****      CLEAR ls_data-name1.
*****      SELECT SINGLE name1 INTO ls_data-name1 FROM t001w WHERE werks = ls_data-werks.
*****
*****      PERFORM frm_modify_cell USING prf_data_changed  ls_mod_cell ls_data 'NAME1'.
*****
*****
*****      CLEAR:lv_flg_new,lv_flg_end.
*****
*****    ENDIF.
*****  ENDLOOP.



ENDFORM.

FORM frm_add_protocol_entry USING prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                                  ps_mod_cell TYPE lvc_s_modi
                                  lv_error_text TYPE string.


  prf_data_changed->add_protocol_entry(  i_msgid      = 'SABAPDOCU'
                                        i_msgty      = 'E'
                                        i_msgno      = '888'
                                        i_msgv1      = ps_mod_cell-value
                                        i_msgv2      = lv_error_text
                                        i_msgv3      = ''
                                        i_msgv4      = ''
                                        i_fieldname  = ps_mod_cell-fieldname
                                        i_row_id     = ps_mod_cell-row_id
                                        i_tabix      = ps_mod_cell-tabix
                                      ) .

ENDFORM.

FORM handle_data_changed_fin USING pv_modified TYPE char01
                                    pt_good_cells  TYPE lvc_t_modi .
  IF pv_modified = 'X'.
    PERFORM frm_refresh_alv.
  ENDIF.

ENDFORM.
FORM frm_data_changed_check  USING    prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                                      ps_mod_cell TYPE lvc_s_modi
                                      ps_data TYPE ty_data.
  DATA:
    lv_werks      TYPE t001w-werks,
    lv_error_text TYPE string.

  TRY .
      MOVE:ps_mod_cell-value TO lv_werks.
    CATCH cx_sy_conversion_no_number INTO DATA(lv_error).
      lv_error_text = lv_error->get_text( ).
    CLEANUP.
  ENDTRY.



  CHECK lv_werks IS NOT INITIAL.
*  SELECT SINGLE * FROM t001w WHERE werks = @lv_werks  INTO @DATA(ls_t001w).""ERP-17212  未上线批发加盟店  zrev010_wrk_DDL
  SELECT SINGLE * FROM zrev010_wrk_DDL WHERE werks = @lv_werks  INTO @DATA(ls_t001w).
  IF sy-subrc NE 0.
    lv_error_text = '工厂不存在'.

    IF lv_error_text IS NOT INITIAL.
      PERFORM frm_add_protocol_entry USING prf_data_changed
                                           ps_mod_cell
                                           lv_error_text.
    ENDIF.

  ENDIF.


ENDFORM.

FORM frm_modify_cell  USING    prf_data_changed TYPE REF TO cl_alv_changed_data_protocol
                               ls_mod_cell TYPE lvc_s_modi
                               ps_data TYPE ty_data
                               pv_fname TYPE lvc_fname.
  FIELD-SYMBOLS:
                 <fs_any> TYPE any.

  UNASSIGN <fs_any>.
  ASSIGN COMPONENT pv_fname OF STRUCTURE ps_data TO <fs_any>.

  IF <fs_any> IS ASSIGNED.
    CALL METHOD prf_data_changed->modify_cell
      EXPORTING
        i_row_id    = ls_mod_cell-row_id
        i_fieldname = pv_fname
        i_value     = <fs_any>.
  ENDIF.

ENDFORM.