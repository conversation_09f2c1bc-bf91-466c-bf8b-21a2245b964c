*&---------------------------------------------------------------------*
*& REPORT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
REPORT zred0057.

TABLES:
  mara,
  zreta001,
  zreta002.


TYPES:
  BEGIN OF ty_globe,
    title   TYPE syst_title,
    actvt   TYPE activ_auth,
    zflg_rb TYPE char2,
    ucomm   TYPE syst-ucomm,
  END OF ty_globe.


INCLUDE zrer0104_t01.

DATA: lv_title  TYPE lvc_title.
DATA:
      gt_data   TYPE  tt_data.

DATA: gs_globe TYPE ty_globe.


CLASS sec_lcl_event_receiver DEFINITION DEFERRED.

CLASS sec_lcl_event_receiver DEFINITION.

  PUBLIC SECTION.

    METHODS:
      sec_handle_data_changed
                    FOR EVENT data_changed OF cl_gui_alv_grid
        IMPORTING er_data_changed e_onf4 e_onf4_before e_onf4_after,
      sec_handle_data_changed_fin
                    FOR EVENT data_changed_finished OF cl_gui_alv_grid
        IMPORTING e_modified et_good_cells.
ENDCLASS.

CLASS sec_lcl_event_receiver IMPLEMENTATION.

  METHOD sec_handle_data_changed.
    PERFORM handle_data_changed USING er_data_changed .
  ENDMETHOD.
  METHOD sec_handle_data_changed_fin.
    PERFORM handle_data_changed_fin USING e_modified et_good_cells .
  ENDMETHOD.
ENDCLASS.



SELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.

SELECT-OPTIONS:
    s_htyear FOR zreta001-zhtyear ,
    s_zht_id FOR zreta001-zht_id MATCHCODE OBJECT zresh0016 MEMORY ID zht_id ,
    s_zhtlx  FOR zreta001-zhtlx ,
    s_zbukrs FOR zreta001-zbukrs MATCHCODE OBJECT  c_t001,
    s_bptype FOR zreta001-zbptype NO-EXTENSION NO INTERVALS,
    s_bpcode FOR zreta001-zbpcode,
    s_tktype FOR zreta002-ztktype,
    s_ztk_id FOR zreta002-ztk_id MATCHCODE OBJECT zresh0018 MEMORY ID ztk_id,
    s_zfllx  FOR zreta002-zfllx MATCHCODE OBJECT zresh0011,
    s_zstyp  FOR zreta002-zxybstyp ,
    s_ekgrp  FOR zreta002-ekgrp MATCHCODE OBJECT h_t024,
    s_tmpid  FOR zreta002-ztmpid MATCHCODE OBJECT zresh0012,
    s_spz_id FOR zreta002-zspz_id MATCHCODE OBJECT zresh0006,
    s_zxyzt  FOR zreta002-zxyzt,
    s_matnr  FOR mara-matnr,
    s_zcnyg  FOR zreta002-zcnyg ,
    s_zcnfyg  FOR zreta002-zcnfyg,
    s_zleib  FOR zreta002-zleib   NO-DISPLAY ,
    s_zzjzr  FOR zreta002-zleib   NO-DISPLAY .

SELECTION-SCREEN END OF BLOCK b1.

*SELECTION-SCREEN BEGIN OF BLOCK b02 WITH FRAME TITLE TEXT-002.
*
*PARAMETERS:
*  p_belnr     TYPE acdoca-belnr   MODIF ID m02  DEFAULT '0100000280'         .
*
*SELECTION-SCREEN END OF BLOCK b02.

SELECTION-SCREEN BEGIN OF BLOCK b03 WITH FRAME TITLE TEXT-003 .

PARAMETERS:
  rb_add  TYPE char1 RADIOBUTTON GROUP g1 USER-COMMAND uc01 DEFAULT 'X' MODIF ID m10,
  rb_edit TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,
*  rb_dis  TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10,
  rb_post TYPE char1 RADIOBUTTON GROUP g1 MODIF ID m10.

SELECTION-SCREEN END OF BLOCK b03.

INCLUDE zrer0104_f01.
INCLUDE zred0057_f01.
INCLUDE zred0057_f02.

INITIALIZATION.
  PERFORM frm_screen_init.


AT SELECTION-SCREEN .

AT SELECTION-SCREEN ON VALUE-REQUEST FOR s_bpcode-low.
  PERFORM fm_zpbcode_help USING  'LOW'.

AT SELECTION-SCREEN ON VALUE-REQUEST FOR s_bpcode-high.
  PERFORM fm_zpbcode_help USING  'HIGH'.

AT SELECTION-SCREEN OUTPUT.
  PERFORM frm_set_screen.

START-OF-SELECTION.
  PERFORM frm_main.

END-OF-SELECTION.
  PERFORM frm_alv_display.