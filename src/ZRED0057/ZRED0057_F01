*&---------------------------------------------------------------------*
*& 包含               ZRED0057_F01
*&---------------------------------------------------------------------*


FORM frm_screen_init .


*  根据事务码控制进入后的界面显示
*  CLEAR:
*        rb_add,rb_edit,rb_dis,rb_rles.
*  CASE sy-tcode.
*    WHEN 'ZRED0043A'.
*      rb_add = 'X'.
*      gs_globe-title = 'XXXX创建'.
*    WHEN 'ZRED0043B'.
*      rb_edit = 'X'.
*      gs_globe-title = 'XXXX修改'.
*    WHEN 'ZRED0043C'.
*      rb_dis = 'X'.
*      gs_globe-title = 'XXXX显示'.
*    WHEN 'ZRED0043D'.
*      rb_rles = 'X'.
*      gs_globe-title = 'XXXX审批'.
*    WHEN OTHERS.
*      rb_add = 'X'.
*      gs_globe-title = 'XXXX创建'.
*  ENDCASE.

*  PERFORM frm_set_title(zbcs0001)  USING gs_globe-title.
ENDFORM.


FORM frm_set_screen .

*  新增
  IF rb_add = 'X'.

    LOOP AT SCREEN.
      IF screen-group1 = 'M02' .
        screen-active = '0'.
      ELSE.
        screen-active = '1'.
      ENDIF.

      MODIFY SCREEN.
    ENDLOOP.
  ELSE.
    LOOP AT SCREEN.
      IF screen-group1 = 'M01'.
        screen-active = '0'.
      ELSEIF screen-group1 = 'M02'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDLOOP.
  ENDIF.

**  通过事务码进入后隐藏单选按钮
*  IF sy-tcode NE 'SE38'.
*    LOOP AT SCREEN.
*      IF screen-group1 = 'M10'.
*        screen-active = '0'.
*      ENDIF.
*      MODIFY SCREEN.
*    ENDLOOP.
*  ENDIF.
ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_INIT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_init .

  CLEAR: gs_globe.

  CASE 'X'.
    WHEN rb_add .     gs_globe-zflg_rb = '01'.
    WHEN rb_edit .    gs_globe-zflg_rb = '02'.
*    WHEN rb_dis .     gs_globe-zflg_rb = '03'.
    WHEN rb_post.     gs_globe-zflg_rb = '04'.
    WHEN OTHERS.
  ENDCASE.

  PERFORM frm_pro_lock(zbcs0001)               USING '预估返利管理' ''.
ENDFORM.


*&---------------------------------------------------------------------*
*&      FORM  F_MAIN
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
FORM frm_main.

  PERFORM frm_init.

  PERFORM frm_author_check.

  PERFORM frm_get_data     USING gs_globe-zflg_rb         CHANGING gt_data.

  PERFORM frm_data_process          CHANGING gt_data.

  PERFORM frm_data_process_end      CHANGING gt_data.
ENDFORM.                    "F_MAIN


*&---------------------------------------------------------------------*
*&      FORM  CHECK_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
FORM frm_author_check.
*  AUTHORITY-CHECK OBJECT 'ZMM_RP_AU'
*                      ID 'BUKRS' FIELD P_BUKRS.
*  IF SY-SUBRC NE 0.
*    MESSAGE S888(SABAPDOCU) WITH '您没有公司' P_BUKRS '的操作权限' DISPLAY LIKE 'E'.
*    LEAVE LIST-PROCESSING.
*  ENDIF.

ENDFORM.                    "CHECK_FORM