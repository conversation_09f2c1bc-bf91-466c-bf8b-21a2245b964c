*&---------------------------------------------------------------------*
*& 包含               ZRER0104_T01
*&---------------------------------------------------------------------*


TYPES:
  BEGIN OF ty_data,

    sel         TYPE char1,

    zhtyear     TYPE zreta001-zhtyear,
    zht_id      TYPE zreta001-zht_id,
    zht_txt     TYPE zreta001-zht_txt,
    zhtlx       TYPE zreta001-zhtlx,
    zbukrs      TYPE zreta001-zbukrs,
    zbptype     TYPE zreta001-zbptype,
    zbpcode     TYPE zreta001-zbpcode,
    zhtid       TYPE zreta001-zhtid,

    ztk_id      TYPE zreta002-ztk_id,
    ztk_txt     TYPE zreta002-ztk_txt,
    zfllx       TYPE zreta002-zfllx,
    zfllx_t     TYPE zreta002-zfllx,
    zxybstyp    TYPE zreta002-zxybstyp,
    ztmpid      TYPE zreta002-ztmpid,
    zjszq       TYPE zreta002-zjszq,
    zhszq       TYPE zreta002-zhszq,
    zhstype     TYPE zreta002-zhstype,
    zbegin      TYPE zreta002-zbegin,
    zend        TYPE zreta002-zend,
    zdffs       TYPE zreta002-zdffs,
    zspz_id     TYPE zreta002-zspz_id,
    ekgrp       TYPE zreta002-ekgrp,
    zxyzt       TYPE zreta002-zxyzt,
    ztktype     TYPE zreta002-ztktype,
    zfldfsj     TYPE zreta002-zfldfsj,
    zleib       TYPE zreta002-zleib,
    zfldfsj_t   TYPE char30,


    ztmptxt     TYPE zretc001-ztmptxt,
    zspzid_txt  TYPE zret0009-zspzid_txt,
    butxt       TYPE t001-butxt,
    zbpname     TYPE lfa1-name1,
    eknam       TYPE t024-eknam,
    frgc1       TYPE zretc007-frgc1,
    zfrgtx      TYPE zretc007-zfrgtx,

    zflg_exe    TYPE char1,
    zcnfyg      TYPE zreta002-zcnfyg,
    zcnyg       TYPE zreta002-zcnyg,
    zcnygtk_id  TYPE zreta002-zcnygtk_id,
    zcnyght_id  TYPE zreta001-zcnyght_id,

    zzjzr       TYPE zreta002-zzjzr,
    zbukrs_ref  TYPE zret0006-zbukrs,
    zbukrs_bin  TYPE zreta001-zbukrs,
    zctgr_bin   TYPE zretc002-zctgr,
    zitems_ref  TYPE zret0006-zitems,
    zhsjz       TYPE zretc005-zhsjz,
    zdw         TYPE char200,
    zjsff       TYPE zretc005-zjsff,
    field_style TYPE lvc_t_styl,

  END OF ty_data,
  tt_data TYPE TABLE OF ty_data.