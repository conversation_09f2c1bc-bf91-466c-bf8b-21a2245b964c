*&---------------------------------------------------------------------*
*& 包含               ZRER0104_F01
*&---------------------------------------------------------------------*



*&---------------------------------------------------------------------*
*&      FORM  GET_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->ITAB       TEXT
*----------------------------------------------------------------------*
FORM frm_get_data USING pv_zflg_rb CHANGING pt_data TYPE tt_data.

  DATA:
    lt_style     TYPE lvc_t_styl,
    lt_cellcolor TYPE lvc_t_scol.

  DATA:
        ls_data TYPE LINE OF tt_data.
  DATA:
    lv_where3 TYPE string,
    lv_where1 TYPE string,
    lv_where2 TYPE string.

  CLEAR:
        pt_data,
        pt_data[].


  IF sy-cprog = 'ZRER0104'.
    lv_where1 = ||.
  ELSE.
    IF pv_zflg_rb = '01'.
      lv_where1 = |A~ZTKTYPE = '' AND A~ZXYZT = 'A' AND A~ZCNYG = '' AND A~ZCNYGSG = '' AND A~ZXYBSTYP IN ('A','V')|. "AND A~ZCNYGTK_ID = ''
    ENDIF.
  ENDIF.

  IF pv_zflg_rb = '01' OR pv_zflg_rb = ''.
    lv_where2 = ||.
  ELSE.
    lv_where2 = |A~ZCNYG = 'X' |.
  ENDIF.


  lv_where3 = COND #( WHEN pv_zflg_rb = '04' THEN |A~ZXYZT NE 'D'| ELSE || ).


  SELECT
    a~*
    FROM zreta002 AS a JOIN zreta001 AS b
                         ON a~zht_id = b~zht_id
*                       LEFT OUTER JOIN ZRET0020 AS C
*                         ON A~ZSPZ_ID = C~ZSPZ_ID
    WHERE a~ztk_id IN @s_ztk_id
    AND   a~zht_id IN @s_zht_id
    AND   b~zhtyear IN @s_htyear
    AND   a~zfllx  IN @s_zfllx
    AND   a~zxybstyp  IN @s_zstyp
    AND   a~ekgrp IN @s_ekgrp
    AND   a~zxyzt IN @s_zxyzt
    AND   b~zbukrs IN @s_zbukrs
    AND   a~ztmpid IN @s_tmpid
    AND   a~zspz_id IN @s_spz_id
    AND   b~zhtlx IN @s_zhtlx
    AND   b~zbptype IN @s_bptype
    AND   a~ztktype IN @s_tktype
*    AND   a~zcnyg   IN @s_zcnyg
    AND   a~zcnfyg   IN @s_zcnfyg
    AND   a~zleib    IN @s_zleib
    AND   a~zzjzr    IN @s_zzjzr
    AND   (lv_where1)
    AND   (lv_where2)
    AND   (lv_where3)
    INTO TABLE @DATA(lt_ta02).

  IF lt_ta02[] IS   INITIAL.
    MESSAGE s001(00) WITH '没有查询到数据'.
    LEAVE LIST-PROCESSING.
  ENDIF.



  IF s_matnr[] IS NOT INITIAL.
    LOOP AT lt_ta02 INTO DATA(ls_ta02).

      IF ls_ta02-zxybstyp = 'F' OR ls_ta02-zxybstyp = 'Q'.
        SELECT SINGLE b~matnr FROM zret0006 AS a JOIN zret0008 AS b ON a~zxy_id = b~zxy_id WHERE a~ztk_id = @ls_ta02-ztk_id AND b~matnr IN @s_matnr INTO @DATA(lv_matnr).
        IF sy-subrc NE 0.
          DELETE lt_ta02.
          CONTINUE.
        ENDIF.
      ELSE.
        SELECT SINGLE matnr FROM zret0020 WHERE zspz_id = @ls_ta02-zspz_id AND matnr IN @s_matnr INTO @lv_matnr.
        IF sy-subrc NE 0.
          DELETE lt_ta02.
          CONTINUE.
        ENDIF.
      ENDIF.

    ENDLOOP.
  ENDIF.


  SELECT
    a~*
    FROM @lt_ta02 AS i JOIN zreta001 AS a
                         ON i~zht_id = a~zht_id

    INTO TABLE @DATA(lt_ta01).

  SELECT
    a~*
    FROM @lt_ta02 AS i   JOIN zretc005 AS a
                           ON i~ztk_id = a~zclrid
    INTO TABLE @DATA(lt_c005).

  SELECT DISTINCT
    a~*
    FROM @lt_ta02 AS i   JOIN zret0011 AS a
                           ON i~zjt_id = a~zjt_id
    INTO TABLE @DATA(lt_0011).



  SELECT
    a~*
    FROM @lt_ta02 AS i   JOIN zret0006 AS a
                           ON i~ztk_id = a~ztk_id
    INTO TABLE @DATA(lt_t006).
  DATA(lt_t006_tmp) =  lt_t006[].

  LOOP AT lt_t006 INTO DATA(ls_t006).

    AUTHORITY-CHECK OBJECT 'ZREAR009'
                        ID 'BUKRS' FIELD ls_t006-zbukrs
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc NE 0.
      DELETE lt_t006 .
      CONTINUE.
    ENDIF.

  ENDLOOP.


  SORT lt_ta01 BY zht_id.
  SORT lt_t006_tmp BY ztk_id.
  SORT lt_t006 BY ztk_id.
  LOOP AT lt_ta02 INTO ls_ta02.
    CLEAR:
          ls_data.

    READ TABLE lt_ta01 INTO DATA(ls_ta01) WITH KEY zht_id = ls_ta02-zht_id BINARY SEARCH.

    ls_data-zhtyear    = ls_ta01-zhtyear.
    ls_data-zht_id     = ls_ta01-zht_id.
    ls_data-zht_txt    = ls_ta01-zht_txt.
    ls_data-zhtlx      = ls_ta01-zhtlx.
    ls_data-zbukrs     = ls_ta01-zbukrs.
    ls_data-zbptype    = ls_ta01-zbptype.
    ls_data-zbpcode    = ls_ta01-zbpcode.
    ls_data-zhtid      = ls_ta01-zhtid.

    ls_data-ztk_id     = ls_ta02-ztk_id.
    ls_data-ztk_txt    = ls_ta02-ztk_txt.
    ls_data-zfllx      = ls_ta02-zfllx.
    ls_data-zfllx_t    = ls_ta02-zfllx.
    ls_data-zxybstyp   = ls_ta02-zxybstyp.
    ls_data-ekgrp      = ls_ta02-ekgrp.
    ls_data-ztmpid     = ls_ta02-ztmpid.
    ls_data-zjszq      = ls_ta02-zjszq.
    ls_data-zhszq      = ls_ta02-zhszq.
    ls_data-zhstype    = ls_ta02-zhstype.
    ls_data-zbegin     = ls_ta02-zbegin.
    ls_data-zend       = ls_ta02-zend.
    ls_data-zdffs      = ls_ta02-zdffs.
    ls_data-zspz_id    = ls_ta02-zspz_id.
    ls_data-zxyzt      = ls_ta02-zxyzt.
    ls_data-ztktype    = ls_ta02-ztktype.
    ls_data-zfldfsj    = ls_ta02-zfldfsj.
    ls_data-zcnfyg      = ls_ta02-zcnfyg.
    ls_data-zcnyg      = ls_ta02-zcnyg.
    ls_data-zcnygtk_id = ls_ta02-zcnygtk_id.
    ls_data-zleib      = ls_ta02-zleib.
    ls_data-zzjzr      = ls_ta02-zzjzr.
*    LS_DATA-ZBPCODE = |{ LS_DATA-ZBPCODE ALPHA = OUT }|.

    READ TABLE lt_c005 INTO DATA(ls_c005) WITH  KEY zclrid = ls_ta02-ztk_id.
    IF sy-subrc = 0.
      ls_data-zhsjz      = ls_c005-zhsjz.
      ls_data-zjsff      = ls_c005-zjsff.
    ENDIF.

    LOOP AT lt_0011 INTO DATA(ls_0011) WHERE zjt_id = ls_ta02-zjt_id .
      ls_data-zdw =   COND #( WHEN ls_data-zdw IS INITIAL THEN |{ ls_0011-zjt_from } ~ { ls_0011-zjt_by }| ELSE | { ls_data-zdw } / { ls_0011-zjt_from } ~ { ls_0011-zjt_by }| ).
    ENDLOOP.

    APPEND ls_data TO pt_data.
    CLEAR ls_ta01.
  ENDLOOP.

  IF s_bpcode[] IS NOT INITIAL.
    DELETE pt_data WHERE zbpcode NOT IN s_bpcode.
  ENDIF.

  LOOP AT pt_data INTO ls_data.

    AUTHORITY-CHECK OBJECT 'ZREAR006'
                        ID 'ZHTLX' FIELD ls_data-zhtlx
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc NE 0.
      DELETE pt_data .
      CONTINUE.
    ENDIF.

    AUTHORITY-CHECK OBJECT 'ZREAR008'
                        ID 'EKGRP' FIELD ls_data-ekgrp
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc NE 0.
      DELETE pt_data .
      CONTINUE.
    ENDIF.

*    AUTHORITY-CHECK OBJECT 'ZREAR007'
*                        ID 'BUKRS' FIELD ls_data-zbukrs
*                        ID 'ACTVT' FIELD '03'.
*    IF sy-subrc NE 0.
*      DELETE pt_data .
*      CONTINUE.
*    ENDIF.

    READ TABLE lt_t006_tmp TRANSPORTING NO FIELDS WITH KEY ztk_id = ls_data-ztk_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      READ TABLE lt_t006 TRANSPORTING NO FIELDS WITH KEY ztk_id = ls_data-ztk_id BINARY SEARCH.
      IF sy-subrc NE 0.
        DELETE pt_data.
        CONTINUE.
      ENDIF.
    ENDIF.

  ENDLOOP.

*  IF pv_zflg_rb = '01'.
*    LOOP AT pt_data INTO ls_data.
*      CLEAR lt_style.
*      PERFORM frm_set_style(zbcs0001) USING 'ZBUKRS_REF' 'X' CHANGING lt_style.
*      PERFORM frm_set_style(zbcs0001) USING 'ZBUKRS_BIN' 'X' CHANGING lt_style.
*      PERFORM frm_set_style(zbcs0001) USING 'ZCTGR_BIN' 'X' CHANGING lt_style.
*      ls_data-field_style = lt_style.
*      MODIFY pt_data FROM ls_data.
*    ENDLOOP.
*
*  ENDIF.


ENDFORM.                    "GET_DATA


FORM frm_data_process CHANGING pt_data TYPE tt_data.

  LOOP AT pt_data ASSIGNING FIELD-SYMBOL(<fs_data>).

    SELECT SINGLE butxt INTO <fs_data>-butxt FROM t001 WHERE bukrs = <fs_data>-zbukrs.
    IF <fs_data>-zbptype = 'S'.

      SELECT SINGLE name1 INTO <fs_data>-zbpname FROM lfa1 WHERE lifnr = <fs_data>-zbpcode.
    ELSEIF <fs_data>-zbptype = 'M'.
*            <FS_DATA>-ZBPCODE = |{ <FS_DATA>-ZBPCODE ALPHA = OUT }|.
*      SELECT SINGLE B0105 INTO <FS_DATA>-ZBPNAME FROM ZMMT0345 WHERE CODEITEMID = <FS_DATA>-ZBPCODE.
      SELECT SINGLE  name_org1 INTO <fs_data>-zbpname  FROM but000 WHERE partner = <fs_data>-zbpcode.
    ENDIF.
    SELECT SINGLE eknam FROM t024 INTO <fs_data>-eknam WHERE ekgrp = <fs_data>-ekgrp .
    SELECT SINGLE ztmptxt INTO <fs_data>-ztmptxt FROM zretc001 WHERE ztmpid = <fs_data>-ztmpid.
    SELECT SINGLE zspzid_txt INTO <fs_data>-zspzid_txt FROM zret0009 WHERE zspz_id = <fs_data>-zspz_id.


    CLEAR:<fs_data>-zcnygtk_id,<fs_data>-zcnyght_id.
    SELECT SINGLE ztk_id FROM zreta002 WHERE zcnygtk_id = @<fs_data>-ztk_id INTO @<fs_data>-zcnygtk_id.
    SELECT SINGLE zht_id FROM zreta001 WHERE zcnyght_id = @<fs_data>-zht_id INTO @<fs_data>-zcnyght_id.

    IF <fs_data>-zcnygtk_id IS NOT INITIAL.
      <fs_data>-zcnyg = 'X'.
    ELSE.
      <fs_data>-zcnyg = ''.
    ENDIF.
  ENDLOOP.

  IF s_zcnyg[] IS NOT INITIAL.
    DELETE pt_data WHERE zcnyg NOT IN s_zcnyg.
  ENDIF.


  IF pt_data[] IS INITIAL.
    MESSAGE s001(00) WITH '没有查询到数据'.
    LEAVE LIST-PROCESSING.
  ENDIF.

ENDFORM.


FORM frm_data_process_end CHANGING pt_data TYPE tt_data.


  SELECT
    *
    FROM zretcm04
    INTO  TABLE @DATA(lt_zretcm04) .

  SORT lt_zretcm04 BY zfldfsj.
  LOOP AT pt_data ASSIGNING FIELD-SYMBOL(<fs_data>).

    READ TABLE lt_zretcm04 INTO DATA(ls_zretcm04) WITH KEY zfldfsj =  <fs_data>-zfldfsj BINARY SEARCH.
    IF sy-subrc = 0.
      <fs_data>-zfldfsj_t =  ls_zretcm04-zfldfsj_t.
    ENDIF.
  ENDLOOP.



ENDFORM.



*&---------------------------------------------------------------------*
*&      FORM  FRM_ALV_DISPLAY
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_alv_display .
  "ALV
  DATA: lt_fieldcat TYPE lvc_t_fcat,
        ls_fieldcat TYPE lvc_s_fcat,
        ls_layout   TYPE lvc_s_layo,
        lt_ex_fcode TYPE ui_functions,
        ls_variant  TYPE disvariant,
        lt_events   TYPE slis_t_event.

  DATA: lv_title    TYPE lvc_title.

  PERFORM frm_set_catalog     CHANGING  lt_fieldcat.

  PERFORM frm_set_layout      CHANGING  ls_layout.

  PERFORM frm_set_variant     CHANGING  ls_variant.

  PERFORM frm_set_events     CHANGING  lt_events.


  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
*     I_INTERFACE_CHECK        = ''                     "
      i_grid_title             = lv_title
      i_callback_program       = sy-repid
      i_callback_pf_status_set = 'FRM_STATUS_ALV'
      i_callback_user_command  = 'FRM_USER_COMMAND'
      is_layout_lvc            = ls_layout
      it_fieldcat_lvc          = lt_fieldcat
      i_save                   = 'A'
    TABLES
      t_outtab                 = gt_data
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  SET_CATALOG
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->PT_FIELDCAT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_catalog CHANGING pt_fieldcat TYPE lvc_t_fcat.
  DATA: ls_fieldcat TYPE lvc_s_fcat.

  DEFINE add_fcat.
    CLEAR ls_fieldcat.

    IF ls_fieldcat IS INITIAL.
      ls_fieldcat-col_pos = 1.
    ELSE.
      ADD 1 TO ls_fieldcat-col_pos.
    ENDIF.
    ls_fieldcat-outputlen = &1.                 "
    ls_fieldcat-ref_table = &2.                 "
    ls_fieldcat-ref_field = &3.                 "
    ls_fieldcat-fieldname = &4.                 "
    ls_fieldcat-coltext   = &5.                 "
    ls_fieldcat-no_zero   = 'X'.
    CASE &4.
      WHEN 'ZHTLX'.     ls_fieldcat-convexit = 'ZREHT'.
      WHEN 'ZFLLX_T'.   ls_fieldcat-convexit = 'ZFLLX'.
      WHEN 'ZXYZT'  .   ls_fieldcat-convexit = 'ZXYZT'.
      WHEN 'ZBPTYPE'.   ls_fieldcat-convexit = 'ZBPTP'.
      WHEN 'ZHSZQ' .    ls_fieldcat-convexit = 'ZHSZQ'.
      WHEN 'ZJSZQ' .    ls_fieldcat-convexit = 'ZJSZQ'.
      WHEN 'ZDFFS' .    ls_fieldcat-convexit = 'ZDFFS'.
      WHEN 'ZHSTYPE' .  ls_fieldcat-convexit = 'ZHSTP'.
      WHEN 'ZTKTYPE' .  ls_fieldcat-convexit = 'ZTKTYPE'.
      WHEN 'ZXYBSTYP'.  ls_fieldcat-convexit = 'ZXYBS'.
      WHEN 'ZHSJZ'.     ls_fieldcat-convexit = 'ZHSJZ'.
      WHEN 'ZJSFF'.     ls_fieldcat-convexit = 'ZJSFF'.
      WHEN 'ZDW'.       ls_fieldcat-no_zero = ''.

      WHEN 'FRGSX'  .
      ls_fieldcat-convexit = 'FRGSX'.
      WHEN 'ZTK_ID'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'ZHT_ID'  .
      ls_fieldcat-hotspot = 'X'.
      WHEN 'NAME1'  .
      ls_fieldcat-fix_column = 'X'.
      WHEN 'ZBUKRS_REF'  OR 'ZBUKRS_BIN' OR 'ZCTGR_BIN' OR 'ZITEMS_REF' .
      ls_fieldcat-edit = 'X'.
      WHEN OTHERS.
    ENDCASE.


    APPEND ls_fieldcat TO pt_fieldcat.
    CLEAR ls_fieldcat.
  END-OF-DEFINITION.


  DATA:
    lv_zflg_rb TYPE char2,
    lv_fname   TYPE char30 VALUE 'GS_GLOBE-ZFLG_RB'.

  ASSIGN (lv_fname) TO FIELD-SYMBOL(<fs>).
  IF <fs> IS ASSIGNED.
    lv_zflg_rb =  <fs>.
  ENDIF.


  add_fcat  '10' '' '' 'ZHTYEAR '   '合同签署年度'  .
  add_fcat  '10' '' '' 'ZHTID '     '关联合同号'  .
  add_fcat  '10' '' '' 'ZHT_ID '    '合同号码'  .
  add_fcat  '10' '' '' 'ZHT_TXT'    '返利合同描述'  .
  add_fcat  '10' '' '' 'ZHTLX  '    '合同类型'  .
  add_fcat  '10' '' '' 'ZBUKRS '    '合同主体'  .
  add_fcat  '10' '' '' 'BUTXT  '    '合同主体描述'  .
  add_fcat  '10' '' '' 'ZBPTYPE'    '伙伴类型'  .
  add_fcat  '10' '' '' 'ZBPCODE'    '伙伴编码'  .
  add_fcat  '10' '' '' 'ZBPNAME'    '伙伴编码描述'  .
  add_fcat  '10' '' '' 'ZTK_ID '    '返利条款'  .
  add_fcat  '10' '' '' 'ZTK_TXT'    '返利条款描述'  .
  add_fcat  '10' '' '' 'ZFLDFSJ_T'  '返利兑付时间'  .
  add_fcat  '10' '' '' 'ZTKTYPE'    '条款类型'  .
  add_fcat  '10' '' '' 'ZFLLX'      '返利类型'  .
  add_fcat  '10' '' '' 'ZFLLX_T'    '返利类型'  .
  add_fcat  '10' '' '' 'ZXYBSTYP'   '协议类型'  .
  add_fcat  '10' '' '' 'ZHSJZ'      '核算基准'  .
  add_fcat  '10' '' '' 'ZJSFF'      '计算方法'  .
  add_fcat  '200' '' '' 'ZDW'        '档位'  .
  add_fcat  '10' '' '' 'ZXYBSTYP'   '协议类型'  .

  add_fcat  '10' '' '' 'EKGRP'      '采购组'  .
  add_fcat  '10' '' '' 'EKNAM'      '采购组描述'  .
  add_fcat  '10' '' '' 'ZTMPID'     '组织模板'  .
  add_fcat  '10' '' '' 'ZTMPTXT'    '组织模板描述'  .
  add_fcat  '10' '' '' 'ZJSZQ  '    '结算周期'  .
  add_fcat  '10' '' '' 'ZHSZQ  '    '核算周期'  .
  add_fcat  '10' '' '' 'ZHSTYPE'    '周期类型'  .
  add_fcat  '10' '' '' 'ZBEGIN '    '开始日期'  .
  add_fcat  '10' '' '' 'ZEND   '    '结束日期'  .
  add_fcat  '10' '' '' 'ZZJZR'      '收款截止日'  .
  add_fcat  '10' '' '' 'ZDFFS  '    '兑付方式'  .
  add_fcat  '10' '' '' 'ZSPZ_ID'    '商品组'  .
  add_fcat  '10' '' '' 'ZSPZID_TXT' '商品组描述'  .
  add_fcat  '10' '' '' 'ZXYZT'      '返利条款状态'  .
  add_fcat  '10' '' '' 'ZCNFYG'     '次年预估不复制'  .
  add_fcat  '10' '' '' 'ZCNYG'      '次年预估'  .
  add_fcat  '10' '' '' 'ZCNYGHT_ID' '次年预估参考合同'  .
  add_fcat  '10' '' '' 'ZCNYGTK_ID' '次年预估参考条款'  .
  add_fcat  '10' '' '' 'ZLEIB'      '类别'  .



  IF sy-cprog = 'ZRED0057' AND lv_zflg_rb = '01'.
    add_fcat  '10' 'T001' 'BUKRS' 'ZBUKRS_REF' '协议参照条款中的协议主体'  .
    add_fcat  '10' 'T001' 'BUKRS' 'ZBUKRS_BIN' '增加协议主体'  .
    add_fcat  '10' 'ZRETC002' 'ZCTGR' 'ZCTGR_BIN' '增加协议主体类别'  .
    add_fcat  '10' 'ZRET0006' 'ZITEMS' 'ZITEMS_REF' '行项目'  .
  ENDIF.







ENDFORM.                    "SET_CATALOG

*&---------------------------------------------------------------------*
*&      FORM  SET_LAYOUT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->P_LAYOUT   TEXT
*----------------------------------------------------------------------*
FORM frm_set_layout CHANGING ps_layout TYPE lvc_s_layo.

  ps_layout-cwidth_opt = 'X'.
  ps_layout-zebra = 'X'.
  ps_layout-box_fname = 'SEL'.
  ps_layout-sel_mode = 'D'.
  ps_layout-stylefname  = 'FIELD_STYLE'.

ENDFORM.                    "SET_LAYOUT

*&---------------------------------------------------------------------*
*&      FORM  STATUS_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_status_alv USING pt_extab TYPE slis_t_extab.
  REFRESH pt_extab.

  DATA:
    lv_zflg_rb TYPE char2,
    lv_fname   TYPE char30 VALUE 'GS_GLOBE-ZFLG_RB'.

  ASSIGN (lv_fname) TO FIELD-SYMBOL(<fs>).
  IF <fs> IS ASSIGNED.
    lv_zflg_rb =  <fs>.
  ENDIF.

  IF sy-cprog = 'ZRER0104'.
    pt_extab = VALUE #(
                        ( fcode = 'CREATE' )
                        ( fcode = 'VOID' )
                        ( fcode = 'APPROVE' )
                        ( fcode = 'RECOVER' )
                        ( fcode = 'ZCNFYG' )
                            ) .
  ENDIF.
  CASE lv_zflg_rb.
    WHEN '01'.
      pt_extab = VALUE #(
*                        ( fcode = 'CREATE' )
                          ( fcode = 'VOID' )
                          ( fcode = 'APPROVE' )
                          ( fcode = 'RECOVER' )
                              ) .
    WHEN '02'.
      pt_extab = VALUE #(
                          ( fcode = 'CREATE' )
*                        ( fcode = 'VOID' )
                          ( fcode = 'APPROVE' )
*                        ( fcode = 'RECOVER' )
                        ( fcode = 'ZCNFYG' )
                              ) .
    WHEN '04'.
      pt_extab = VALUE #(
                          ( fcode = 'CREATE' )
                          ( fcode = 'VOID' )
*                        ( fcode = 'APPROVE' )
                          ( fcode = 'RECOVER' )
                        ( fcode = 'ZCNFYG' )
                              ) .
    WHEN OTHERS.
  ENDCASE.

  SET PF-STATUS 'S1000' EXCLUDING pt_extab.


ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*&      FORM  USER_COMMAND_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->R_UCOMM      TEXT
*      -->RS_SELFIELD  TEXT
*----------------------------------------------------------------------*
FORM frm_user_command USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.
*  RS_SELFIELD-REFRESH = 'X'.
  DATA:
    lv_mtype TYPE bapi_mtype,
    lv_msg   TYPE bapi_msg.

  PERFORM frm_check_changed_data.

  CASE rv_ucomm.
    WHEN 'BACK' OR 'CLOSE' OR 'CANCEL' OR '&F12' OR '&F03' OR '&F15'.
      CLEAR rv_ucomm.
      LEAVE TO SCREEN 0.
    WHEN '&IC1'.
      CLEAR rv_ucomm.
      READ TABLE gt_data INTO DATA(ls_data) INDEX rs_selfield-tabindex.
      IF sy-subrc EQ 0.
        CASE rs_selfield-fieldname.
          WHEN 'ZTK_ID' .
            CHECK ls_data-ztk_id IS NOT INITIAL.
            SET PARAMETER ID 'ZTK_ID' FIELD ls_data-ztk_id.
            CALL TRANSACTION 'ZRED0041C' AND SKIP FIRST SCREEN.
            SET PARAMETER ID 'ZTK_ID' FIELD ''.
          WHEN 'ZHT_ID'.
            CHECK ls_data-zht_id IS NOT INITIAL.
            SET PARAMETER ID 'ZHT_ID' FIELD ls_data-zht_id.
            CALL TRANSACTION 'ZRED0040C' AND SKIP FIRST SCREEN.
            SET PARAMETER ID 'ZHT_ID' FIELD ''.

          WHEN OTHERS.
        ENDCASE.
      ENDIF.
    WHEN 'CREATE'.

      PERFORM frm_main_data_create       CHANGING gt_data
                                           lv_mtype
                                           lv_msg.

      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.

    WHEN 'VOID' OR 'RECOVER' OR 'APPROVE' OR 'ZCNFYG'.

      PERFORM frm_main_data_ucomm       USING rv_ucomm
                                       CHANGING gt_data
                                           lv_mtype
                                           lv_msg.

      MESSAGE s888(sabapdocu) WITH lv_msg DISPLAY LIKE lv_mtype.


    WHEN OTHERS.
  ENDCASE.

  PERFORM frm_refresh_alv.

ENDFORM.                    "USER_COMMAND_FORM
*&---------------------------------------------------------------------*
*&      FORM  FRM_SET_VARIANT
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->LW_VARIANT  TEXT
*----------------------------------------------------------------------*
FORM frm_set_variant CHANGING ls_variant TYPE disvariant.
  ls_variant-report = sy-repid.
  ls_variant-handle = 1.
ENDFORM.                    "FRM_SET_VARIANT


FORM frm_set_events CHANGING pt_events   TYPE slis_t_event.

  CLEAR:pt_events,pt_events[].
  pt_events =  VALUE #( BASE pt_events
                     ( name = slis_ev_caller_exit_at_start  form = 'FRM_CALLER_EXIT'   )
                                ).
ENDFORM.

*&---------------------------------------------------------------------*
*&      FORM  FRM_CHECK_CHANGED_DATA
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_check_changed_data .
  DATA: lrf_alv TYPE REF TO cl_gui_alv_grid.
  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.
  CALL METHOD lrf_alv->check_changed_data.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  FRM_REFRESH_ALV
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*  -->  P1        TEXT
*  <--  P2        TEXT
*----------------------------------------------------------------------*
FORM frm_refresh_alv .

  DATA:
        lrf_alv   TYPE REF TO cl_gui_alv_grid.

  DATA:
        ls_stable TYPE lvc_s_stbl.

  ls_stable-row = 'X'.
  ls_stable-col = 'X'.

  CALL FUNCTION 'GET_GLOBALS_FROM_SLVC_FULLSCR' "
    IMPORTING
      e_grid = lrf_alv.

  IF lrf_alv IS NOT INITIAL.
    CALL METHOD lrf_alv->refresh_table_display
      EXPORTING
        is_stable = ls_stable.
  ENDIF.
ENDFORM.


*&---------------------------------------------------------------------*
*& FORM FM_ZPBCODE_HELP
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&---------------------------------------------------------------------*
FORM fm_zpbcode_help  USING VALUE(p_flag).

  DATA:  lt_ret_tab TYPE TABLE OF ddshretval  WITH HEADER LINE.
  DATA: dynpfields TYPE TABLE OF dynpread WITH HEADER LINE.

  DATA:
        lv_zbptype TYPE zreta001-zbptype.



  CLEAR: dynpfields, dynpfields[].
  dynpfields-fieldname = 'S_BPTYPE-LOW'.
  dynpfields-fieldvalue = s_bptype-low.
  APPEND dynpfields.

  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-repid
      dynumb               = sy-dynnr
      translate_to_upper   = 'X'
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      undefind_error       = 7
      OTHERS               = 8.
  IF sy-subrc = 0.
    READ TABLE dynpfields WITH KEY fieldname = 'S_BPTYPE-LOW'.
    lv_zbptype = dynpfields-fieldvalue.
  ENDIF.
  IF lv_zbptype IS INITIAL.
    RETURN.
  ENDIF.



  dynpfields-fieldname = 'GS_TA01-ZBPTYPE'.
  APPEND dynpfields.
  CALL FUNCTION 'DYNP_VALUES_READ'
    EXPORTING
      dyname               = sy-cprog
      dynumb               = sy-dynnr
    TABLES
      dynpfields           = dynpfields
    EXCEPTIONS
      invalid_abapworkarea = 1
      invalid_dynprofield  = 2
      invalid_dynproname   = 3
      invalid_dynpronummer = 4
      invalid_request      = 5
      no_fielddescription  = 6
      invalid_parameter    = 7
      undefind_error       = 8
      double_conversion    = 9
      stepl_not_found      = 10
      OTHERS               = 11.
  IF sy-subrc EQ 0.
    READ TABLE dynpfields WITH KEY 'GS_TA01-ZBPTYPE'.
    IF sy-subrc = 0.
      lv_zbptype  = dynpfields-fieldvalue.
      TRANSLATE lv_zbptype TO UPPER CASE.
    ENDIF.
  ELSE.

  ENDIF.

  IF lv_zbptype = 'S'.

    DATA: ls_return TYPE           ddshretval,
          lt_return TYPE TABLE OF  ddshretval.
    DATA: ls_shlp   TYPE           shlp_descr,
          wa_selopt LIKE LINE OF   ls_shlp-selopt.



    CALL FUNCTION 'F4IF_FIELD_VALUE_REQUEST'
      EXPORTING
        tabname           = 'LFA1'
        fieldname         = 'LIFNR'
*       SEARCHHELP        = 'SH_AFASL_SPEC'
        callback_program  = sy-repid
*       CALLBACK_FORM     = 'SET_VALUES_FOR_F4_AFASL_304'
      TABLES
        return_tab        = lt_return
      EXCEPTIONS
        field_not_found   = 1
        no_help_for_field = 2
        inconsistent_help = 3
        no_values_found   = 4
        OTHERS            = 5.

    READ TABLE lt_return INDEX 1 INTO ls_return.
    CHECK sy-subrc = 0.
    IF p_flag = 'LOW'.
      s_bpcode-low = ls_return-fieldval.
    ELSE.
      s_bpcode-high = ls_return-fieldval.
    ENDIF.


    CLEAR sy-ucomm.


  ELSEIF lv_zbptype = 'M'.

    DATA:
          lv_dynprofield TYPE help_info-dynprofld.

    IF p_flag = 'LOW'.
      lv_dynprofield = 'S_BPCODE-LOW'.
    ELSE.
      lv_dynprofield = 'S_BPCODE-HIGH'.
    ENDIF.

*    SELECT
*       A~GUIDKEY ,
*       A~CODEITEMID ,
*       A~CODEITEMDESC ,
*       A~B0105 ,
*       A~PARENTID ,
*       B~B0105 AS B0105_P
*      FROM ZMMT0345 AS A LEFT JOIN ZMMT0345 AS B
*                                ON A~PARENTID = B~CODEITEMID
*      WHERE A~STATUS = '1'
*      INTO TABLE @DATA(LT_ZMMT0345).

    SELECT partner, name_org1 INTO TABLE @DATA(lt_but000) FROM but000 WHERE bu_group = 'BP07'.

    CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
      EXPORTING
        retfield        = 'PARTNER'
        value_org       = 'S'
        dynpprog        = sy-repid
        dynpnr          = sy-dynnr
        dynprofield     = lv_dynprofield
*       CALLBACK_PROGRAM = SY-REPID
*       CALLBACK_FORM   = 'USER_FORM'
      TABLES
*       VALUE_TAB       = LT_ZMMT0345[]
        value_tab       = lt_but000[]
        return_tab      = lt_ret_tab[]
      EXCEPTIONS
        parameter_error = 1
        no_values_found = 2
        OTHERS          = 3.
  ENDIF.

  IF sy-subrc = 0.
    READ TABLE lt_ret_tab INDEX 1.
    CHECK sy-subrc = 0.
  ENDIF.

ENDFORM.


*&---------------------------------------------------------------------*
*& Form FRM_MAIN_DATA_CREATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      <-- GT_DATA
*&      <-- LV_MTYPE
*&      <-- LV_MSG
*&---------------------------------------------------------------------*
FORM frm_main_data_create  CHANGING pt_data TYPE tt_data
                                    pv_mtype  TYPE bapi_mtype
                                    pv_msg  TYPE bapi_msg.

  PERFORM frm_check_sel USING pt_data CHANGING pv_mtype pv_msg.

  LOOP AT pt_data INTO DATA(ls_data) WHERE sel = 'X'.

    IF ls_data-zcnygtk_id IS NOT INITIAL.
      pv_mtype = 'E'.
      pv_msg = '条款已经存在预估条款，不允许重复创建'.
      EXIT.
    ENDIF.

    IF ls_data-zcnfyg IS NOT INITIAL.
      pv_mtype = 'E'.
      pv_msg = '该条款确认不复制，不能生成次年预估返利条款'.
      EXIT.
    ENDIF.

    IF NOT ( ls_data-zbukrs_ref IS INITIAL AND ls_data-zbukrs_bin IS INITIAL AND ls_data-zctgr_bin IS INITIAL ).
      IF NOT ( ls_data-zbukrs_ref IS NOT INITIAL AND ls_data-zbukrs_bin IS NOT INITIAL AND ls_data-zctgr_bin IS NOT INITIAL ).
        pv_mtype = 'E'.
        pv_msg = '[协议参照条款中的协议主体]、[增加协议主体]、[增加协议主体类别]必须同时为空或者同时有值'.
        EXIT.
      ENDIF.
    ENDIF.

    IF NOT ( ls_data-zbukrs_ref IS INITIAL AND ls_data-zbukrs_bin IS INITIAL AND ls_data-zctgr_bin IS INITIAL ).

      SELECT COUNT(*) FROM zret0006 WHERE ztk_id = @ls_data-ztk_id AND zbukrs = @ls_data-zbukrs_ref INTO @DATA(lv_count).
      IF lv_count = 0.
        pv_mtype = 'E'.
        pv_msg = '[协议参照条款中的协议主体]:' &&  ls_data-zbukrs_ref  &&  '在条款中不存在'.
        EXIT.
      ELSEIF lv_count > 1.
        IF ls_data-zitems_ref IS INITIAL OR ls_data-zitems_ref = ''..
          pv_mtype = 'E'.
          pv_msg = '[协议参照条款中的协议主体]:' &&  ls_data-zbukrs_ref  &&  '在条款中存在多行，请输入行项目号'.
          EXIT.
        ENDIF.
      ENDIF.

      IF ls_data-zitems_ref IS NOT INITIAL OR ls_data-zitems_ref NE ''..
        ls_data-zitems_ref = |{ ls_data-zitems_ref ALPHA = IN }|.
        CLEAR lv_count.
        SELECT COUNT(*) FROM zret0006 WHERE ztk_id = @ls_data-ztk_id AND zbukrs = @ls_data-zbukrs_ref AND zitems = @ls_data-zitems_ref INTO @lv_count.
        IF lv_count < 0.
          pv_mtype = 'E'.
          pv_msg = '[协议参照条款中的协议主体]:' &&  ls_data-zbukrs_ref  &&  '行项目：' && ls_data-zitems_ref &&  '在条款中不存在'..
          EXIT.
        ENDIF.

      ENDIF.
    ENDIF.
    CLEAR lv_count.

  ENDLOOP.


  DATA(lt_data_tmp) = pt_data[].
  DELETE lt_data_tmp WHERE sel = ''.
  DESCRIBE TABLE lt_data_tmp LINES DATA(lv_lines) .
  IF lv_lines > 3000.
    pv_mtype = 'E'.
    pv_msg = '最大选择条目数不能超过3000行' .
  ENDIF.

  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.



  PERFORM frm_pro_lock(zbcs0001)               USING '条款创建' ''.

  PERFORM frm_data_create_exe       CHANGING pt_data
                                       pv_mtype
                                       pv_msg.

  PERFORM frm_pro_lock(zbcs0001)               USING '条款创建' 'X'.

  pv_mtype = 'S'.
  pv_msg = '处理成功,请手工计算返利数据'.

ENDFORM.

FORM frm_data_create_exe  CHANGING pt_data TYPE tt_data
                                    pv_mtype  TYPE bapi_mtype
                                    pv_msg  TYPE bapi_msg.

  DATA:
    lt_zht_id   TYPE zrei0035,
    lt_ztk_id   TYPE zrei0036,
    lt_zxy_id   TYPE zrei0037,
    lt_zspz_id  TYPE zrei0038,
    lt_039      TYPE zrei0039,
    ls_039      TYPE LINE OF zrei0039,
    lt_data_bin TYPE zrei0040,
    ls_data_bin TYPE LINE OF zrei0040.


  LOOP AT pt_data INTO DATA(ls_data) WHERE sel = 'X'.
    CLEAR ls_039.
    ls_039-ztk_id = ls_data-ztk_id.
    COLLECT ls_039 INTO lt_039.
  ENDLOOP.


  LOOP AT pt_data INTO ls_data WHERE sel = 'X'.
    CLEAR ls_data_bin.

    IF  ( ls_data-zbukrs_ref IS INITIAL AND ls_data-zbukrs_bin IS INITIAL AND ls_data-zctgr_bin IS INITIAL ).
      CONTINUE.
    ENDIF.

    ls_data_bin-zbukrs_ref = ls_data-zbukrs_ref.
    ls_data_bin-zbukrs_bin = ls_data-zbukrs_bin.
    ls_data_bin-zctgr_bin = ls_data-zctgr_bin.
    ls_data_bin-zitems_ref = ls_data-zitems_ref.
    ls_data_bin-zitems_ref = |{ ls_data_bin-zitems_ref ALPHA = IN }|.
    IF ls_data_bin-zitems_ref IS INITIAL OR ls_data_bin-zitems_ref = ''.
      SELECT SINGLE zitems  FROM zret0006 WHERE ztk_id = @ls_data-ztk_id AND zbukrs = @ls_data-zbukrs_ref INTO ( @ls_data_bin-zitems_ref  ).
    ENDIF.

    SELECT SINGLE zxy_id ,ztk_id FROM zret0006 WHERE ztk_id = @ls_data-ztk_id AND zitems = @ls_data_bin-zitems_ref INTO  ( @ls_data_bin-zxy_id_ref ,@ls_data_bin-ztk_id_ref ).

    COLLECT ls_data_bin INTO lt_data_bin.
  ENDLOOP.


*  复制条款
  CALL FUNCTION 'ZREFM0055'
    EXPORTING
*     IV_COMMIT  =
      it_data    = lt_039
    IMPORTING
*     EV_MTYPE   =
*     EV_MSG     =
      et_zht_id  = lt_zht_id
      et_ztk_id  = lt_ztk_id
      et_zxy_id  = lt_zxy_id
      et_zspz_id = lt_zspz_id.

*  新增行项目-需要用到上一步新生成的条款和协议
  CALL FUNCTION 'ZREFM0057'
    EXPORTING
*     IV_COMMIT   =
      it_data_bin = lt_data_bin
      it_ztk_id   = lt_ztk_id
      it_zxy_id   = lt_zxy_id
* IMPORTING
*     EV_MTYPE    =
*     EV_MSG      =
    .



  SORT lt_zht_id BY zht_id.
  SORT lt_ztk_id BY ztk_id.
  LOOP AT pt_data INTO ls_data WHERE sel = 'X'.

    READ TABLE lt_zht_id INTO DATA(ls_zht_id) WITH KEY zht_id = ls_data-zht_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zcnyght_id = ls_zht_id-zcnyght_id.
    ENDIF.

    READ TABLE lt_ztk_id INTO DATA(ls_ztk_id) WITH KEY ztk_id = ls_data-ztk_id BINARY SEARCH.
    IF sy-subrc EQ 0.
      ls_data-zcnygtk_id = ls_ztk_id-zcnygtk_id.
    ENDIF.
    IF ls_data-zcnygtk_id IS NOT INITIAL.
      ls_data-zcnyg = 'X'.
    ENDIF.

    ls_data-zflg_exe = 'X'.
    MODIFY pt_data FROM ls_data .
  ENDLOOP.


ENDFORM.


FORM frm_check_sel  USING    pt_data  TYPE tt_data
                          CHANGING pv_mtype TYPE bapi_mtype
                                   pv_msg TYPE bapi_msg.
  CLEAR: pv_mtype,pv_msg.
  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY sel = 'X'.
  IF sy-subrc NE 0.
    pv_mtype = 'E'.
    pv_msg = '请选择需要操作的行！'.
  ENDIF.

  READ TABLE pt_data TRANSPORTING NO FIELDS WITH KEY zflg_exe = 'X'.
  IF sy-subrc EQ 0.
    pv_mtype = 'E'.
    pv_msg = '选择的行已经处理完成，不允许重复处理'.
  ENDIF.


ENDFORM.

FORM frm_main_data_ucomm  USING rv_ucomm    TYPE sy-ucomm
                        CHANGING pt_data TYPE tt_data
                                    pv_mtype  TYPE bapi_mtype
                                    pv_msg  TYPE bapi_msg.

  PERFORM frm_check_sel USING pt_data CHANGING pv_mtype pv_msg.
  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.

  IF rv_ucomm = 'ZCNFYG'.
    LOOP AT pt_data INTO DATA(ls_data) WHERE sel = 'X'.
      IF ls_data-zcnyg IS NOT INITIAL.
        pv_mtype = 'E'.
        pv_msg = '该条款已生成次年预估返利条款，不能操作确认不复制动作'.
        EXIT.
      ENDIF.
    ENDLOOP.

  ENDIF.

  IF pv_mtype = 'E'.
    RETURN.
  ENDIF.



  LOOP AT pt_data INTO ls_data WHERE sel = 'X'.
    CASE rv_ucomm .
      WHEN 'VOID'.
        PERFORM frm_save_rles_log USING ls_data-ztk_id
                                        ''
                                        ls_data-zxyzt
                                        ''
                                        'D'
                                        ''.
        UPDATE zreta002 SET zxyzt = 'D' WHERE ztk_id = ls_data-ztk_id.
        UPDATE zret0006 SET zxyzt = 'D' WHERE ztk_id = ls_data-ztk_id.
      WHEN 'RECOVER'.
        PERFORM frm_save_rles_log USING ls_data-ztk_id
                                        ''
                                        ls_data-zxyzt
                                        ''
                                        'N'
                                        ''.
        UPDATE zreta002 SET zxyzt = 'N' WHERE ztk_id = ls_data-ztk_id.
        UPDATE zret0006 SET zxyzt = 'N' WHERE ztk_id = ls_data-ztk_id.
      WHEN 'APPROVE'.
        UPDATE zreta002 SET zcnyg = '' WHERE ztk_id = ls_data-ztk_id.
      WHEN 'ZCNFYG'.
        UPDATE zreta002 SET zcnfyg = 'X' WHERE ztk_id = ls_data-ztk_id.
        ls_data-zcnfyg = 'X'.
        MODIFY pt_data FROM ls_data.
      WHEN OTHERS.
    ENDCASE.
  ENDLOOP.


  pv_mtype = 'S'.
  pv_msg = '处理成功'.

ENDFORM.
FORM frm_save_rles_log USING  pv_ztk_id
                              pv_zxy_id
                              pv_zxyzt
                              pv_frgc1
                              ps_zxyzt_n
                              ps_frgc1_n.

  DATA: ls_t50 TYPE zret0050.

  ls_t50-ztk_id     = pv_ztk_id.
  ls_t50-zxy_id     = pv_zxy_id.
  ls_t50-timestamp  = sy-datum && sy-uzeit.
  ls_t50-zxyzt_o    = pv_zxyzt.
  ls_t50-frgc1_o    = pv_frgc1.
  ls_t50-zxyzt_n    = ps_zxyzt_n.
  ls_t50-frgc1_n    = ps_frgc1_n.
  ls_t50-zcjrq      = sy-datum.
  ls_t50-zcjsj      = sy-uzeit.
  ls_t50-zcjr       = sy-uname.

  MODIFY zret0050 FROM ls_t50.

ENDFORM.