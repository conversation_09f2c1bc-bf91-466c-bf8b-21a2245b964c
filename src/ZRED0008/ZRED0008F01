*&---------------------------------------------------------------------*
*& 包含               ZRED0008F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_INTIAL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_intial .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHORITY_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_authority_check .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_VALIDATE_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_validate_check .

  DATA:lv_yestdat TYPE sy-datum.

  lv_yestdat = sy-datum - 1.

  IF s_datum IS INITIAL .
    MESSAGE '计算日期必输！' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  IF s_datum-low > lv_yestdat .
    MESSAGE '计算日期开始日超出运算日期！' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.

  IF s_datum-high IS NOT INITIAL AND s_datum-high > lv_yestdat .
    s_datum-high = lv_yestdat.
    MODIFY s_datum FROM s_datum TRANSPORTING high  WHERE  high IS NOT INITIAL .
  ENDIF.

  CHECK p_autadj = 'X'.

  DATA:ls_zret0075 TYPE zret0075.
  DATA:lv_zsy_day TYPE dats.
  SELECT SINGLE * INTO ls_zret0075 FROM zret0075 WHERE zjobhtlx = '1' AND tbdat IN ( '','00000000' )  AND exeed = '' .
  IF sy-subrc <> 0.
    MESSAGE '执行日期表数据异常，请检查！' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ELSE.

    lv_zsy_day = ls_zret0075-stadt - ls_zret0075-ztqts.
    IF s_datum-high > lv_zsy_day.
      s_datum-high = lv_zsy_day.
      MODIFY s_datum FROM s_datum TRANSPORTING high  WHERE  high <> lv_zsy_day.
    ENDIF.

  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data .

*TYPES:BEGIN OF ty_ztk_id,
*        zhtlx    TYPE zreta001-zhtlx,
*        ekgrp    TYPE zreta001-ekgrp,
*        zbukrs   TYPE zreta001-zbukrs,
*        zht_id   TYPE zreta001-zht_id,
*        ztktype  TYPE zreta002-ztktype,
*        ztk_id   TYPE zreta002-ztk_id,
*        zxybstyp TYPE zreta002-zxybstyp,
*        zlevel   TYPE /bdl/_level,
*        zrtetp   TYPE zree_zrtetp.
*TYPES END OF ty_ztk_id.

*TYPES:BEGIN OF ty_ztk_map,
*        zhtlx    TYPE zreta001-zhtlx,
*        ekgrp    TYPE zreta001-ekgrp,
*        zbukrs   TYPE zreta001-zbukrs,
*        zht_id   TYPE zreta001-zht_id,
*        ztktype  TYPE zreta002-ztktype,
*        ztk_id   TYPE zreta002-ztk_id,
*        zxybstyp TYPE zreta002-zxybstyp,
*        zrlid    TYPE zreta003-zrlid.
*TYPES   END OF ty_ztk_map.

*DATA:
*  gs_ztk_id     TYPE ty_ztk_id,
*  gt_ztk_id     TYPE TABLE OF ty_ztk_id,
*  gt_ztk_id_nts TYPE TABLE OF ty_ztk_id, "非附加条款
*  gt_ztk_id_ats TYPE TABLE OF ty_ztk_id, "附加条款数据
*  gs_ztk_id_map TYPE ty_ztk_map,
*  gt_ztk_id_map TYPE TABLE OF ty_ztk_map.

**********************************************************************

*  DATA:lt_ztk_id  TYPE TABLE OF ty_ztk_id.
*  DATA:ls_ztk_id  TYPE ty_ztk_id.
*  DATA:lt_sel_id  TYPE TABLE OF ty_ztk_id.
*  DATA:lv_zlevel  TYPE /bdl/_level VALUE 10 .
*
*  "条款数据
*  SELECT DISTINCT
*         zreta001~zhtlx,
*         zreta001~ekgrp,
*         zreta001~zbukrs,
*         zreta001~zht_id,
*         zreta002~ztktype,
*         zreta002~ztk_id,
*         zreta002~zxybstyp,
*         10 AS zlevel
*    INTO TABLE @gt_ztk_id
*    FROM zreta002
*    INNER JOIN zreta001 ON zreta001~zht_id  = zreta002~zht_id
*    INNER JOIN zret0006 ON zret0006~ztk_id  = zreta002~ztk_id
*    WHERE zreta001~zht_id   IN @s_zht_id "合同号码
*      AND zreta001~zhtlx    IN @s_zhtlx  "合同类型
*      AND zreta001~zbukrs   IN @s_zbukrs "合同主体公司
*      AND zreta001~zbpcode  IN @s_bpcode "伙伴ID
*      AND zreta002~ztk_id   IN @s_ztk_id "条款号码
*      AND zreta002~zfllx    IN @s_zfllx  "返利类型
*      AND zret0006~zxy_id   IN @s_zxy_id "协议号码
*      AND zret0006~zxybstyp IN ('V','A','T','P')
*      AND zret0006~zxybstyp IN @s_zxybtp
*      AND ( ( zreta002~zxyzt    EQ 'A' AND zreta002~zcnyg = '' ) OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  ) )  .
*
*  "权限检查
*  DATA:lv_result TYPE c.
*  LOOP AT gt_ztk_id INTO gs_ztk_id .
*    PERFORM frm_check_authority      USING gs_ztk_id
*                                  CHANGING lv_result.
*    IF lv_result = 'E'.
*      DELETE gt_ztk_id.
*    ENDIF.
*  ENDLOOP.
*
*  lt_sel_id = gt_ztk_id.
*
*  "获取条款的附加条款
*  DO 10 TIMES .
*
*    IF lt_sel_id IS INITIAL.
*      EXIT.
*    ENDIF.
*
*    SELECT zreta001~zhtlx,
*           zreta001~ekgrp,
*           zreta001~zbukrs,
*           zreta001~zht_id,
*           zreta002~ztktype,
*           zreta003~zrlid AS ztk_id,
*           zreta002~zxybstyp
*      INTO TABLE @lt_ztk_id
*      FROM zreta003
*      INNER JOIN zreta002 ON zreta003~zrlid  = zreta002~ztk_id
*      INNER JOIN zreta001 ON zreta001~zht_id = zreta002~zht_id
*      FOR ALL ENTRIES IN @lt_sel_id
*      WHERE zreta003~ztk_id  = @lt_sel_id-ztk_id
*      AND zreta003~zatktp = 'P'
*      AND zreta003~zatkpi = 'C'.
*    IF sy-subrc <> 0.
*      EXIT.
*    ENDIF.
*
*    lv_zlevel = lv_zlevel + 10.
*    ls_ztk_id-zlevel = lv_zlevel.
*    ls_ztk_id-zrtetp = 'P'.
*    MODIFY lt_ztk_id FROM ls_ztk_id   TRANSPORTING zlevel zrtetp  WHERE zlevel IS INITIAL .
*    APPEND LINES OF lt_ztk_id TO gt_ztk_id.
*    lt_sel_id = lt_ztk_id.
*    REFRESH lt_ztk_id.
*    CLEAR:ls_ztk_id.
*  ENDDO.
*
*  SORT gt_ztk_id BY ztk_id.
*  DELETE ADJACENT DUPLICATES FROM gt_ztk_id COMPARING ztk_id.
*  SORT gt_ztk_id BY zht_id ASCENDING ztktype DESCENDING .
*
*  gt_ztk_id_nts = gt_ztk_id.
*  DELETE gt_ztk_id_nts WHERE ztktype = 'P'.
*  gt_ztk_id_ats = gt_ztk_id.
*  DELETE gt_ztk_id_ats WHERE ztktype = ''.
*
*
**  获取附加条款对应正常条款
*  IF gt_ztk_id_ats[] IS NOT INITIAL.
*
*    REFRESH gt_ztk_id.
*
*    SELECT zreta001~zhtlx,
*           zreta001~ekgrp,
*           zreta001~zbukrs,
*           zreta001~zht_id,
*           zreta002~ztktype,
*           zreta003~ztk_id,
*           zreta002~zxybstyp,
*           zreta003~zrlid
*      APPENDING TABLE @gt_ztk_id_map
*      FROM zreta003
*      INNER JOIN zreta002 ON zreta003~ztk_id = zreta002~ztk_id
*      INNER JOIN zreta001 ON zreta001~zht_id = zreta002~zht_id
*      FOR ALL ENTRIES IN @gt_ztk_id_ats
*      WHERE zreta003~zrlid  = @gt_ztk_id_ats-ztk_id
*       AND zreta003~zatktp = 'P'
*       AND zreta003~zatkpi = 'C'
*       AND zreta002~ztktype = ''.
*    IF sy-subrc = 0.
*
*      MOVE-CORRESPONDING gt_ztk_id_map TO gt_ztk_id.
*      ls_ztk_id-zlevel = 10.
*      ls_ztk_id-zrtetp = 'R'.
*      MODIFY gt_ztk_id FROM ls_ztk_id TRANSPORTING zlevel zrtetp WHERE zlevel  IS INITIAL.
*      APPEND LINES OF gt_ztk_id TO gt_ztk_id_nts.
*
*      SORT gt_ztk_id_nts BY ztk_id.
*      DELETE ADJACENT DUPLICATES FROM gt_ztk_id_nts COMPARING ztk_id.
*
*    ENDIF.
*  ENDIF.
*
*  DATA(lv_error) = ''.
*  DATA:it_ztk_id TYPE TABLE OF ty_ztk_id.
*  it_ztk_id = gt_ztk_id_nts[].
*
*  APPEND LINES OF gt_ztk_id_ats TO it_ztk_id.
*
*  PERFORM frm_check_date   TABLES it_ztk_id[]
*                         CHANGING lv_error .


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_AUTHORITY
*&---------------------------------------------------------------------*
*& 检查权限
*&---------------------------------------------------------------------*
*&      --> LS_ZRET0006
*&      <-- LV_SUBRC
*&---------------------------------------------------------------------*
FORM frm_check_authority1  USING    ps_data TYPE ty_first_id
                          CHANGING pv_result.

  DATA:lr_bukrs TYPE RANGE OF t001-bukrs.
  DATA:lr_ekgrp TYPE TABLE OF zbcs0003.
  DATA:lr_zhtlx TYPE TABLE OF zbcs0003.
  DATA:lv_mess  TYPE bapiret2-message.

  CLEAR:lr_bukrs[],
        pv_result,
        lv_mess.

  lr_bukrs[] = VALUE #( ( sign = 'I'
                          option = 'EQ'
                          low = ps_data-zbukrs
                      ) ).
  "公司代码的权限
  CALL FUNCTION 'ZBCFM0001'
    EXPORTING
      iv_object = 'ZMMAR005'
      iv_field  = 'BUKRS'
      iv_actvt  = '03'
    IMPORTING
      ex_subrc  = pv_result
      ex_mess   = lv_mess
    TABLES
      it_tab    = lr_bukrs[].

  IF pv_result = 'E'.
    RETURN.
  ENDIF.

  IF ps_data-ekgrp IS NOT INITIAL.
    CLEAR:lr_ekgrp[],
          pv_result,
          lv_mess.

    lr_ekgrp[] = VALUE #( ( sign = 'I'
                            option = 'EQ'
                            low = ps_data-ekgrp
                         ) ).

    CALL FUNCTION 'ZBCFM0001'
      EXPORTING
        iv_object = 'ZREAR001'
        iv_field  = 'EKGRP'
        iv_actvt  = '03'
      IMPORTING
        ex_subrc  = pv_result
        ex_mess   = lv_mess
      TABLES
        it_tab    = lr_ekgrp[].

    IF pv_result = 'E'.
      RETURN.
    ENDIF.
  ENDIF.

  IF ps_data-zhtlx IS NOT INITIAL.
    CLEAR:lr_zhtlx[],
          pv_result,
          lv_mess.

    lr_zhtlx[] = VALUE #( ( sign = 'I'
                            option = 'EQ'
                            low = ps_data-zhtlx
                         ) ).

    CALL FUNCTION 'ZBCFM0001A'
      EXPORTING
        iv_object = 'ZREAR002'
        iv_field  = 'ZHTLX'
        iv_actvt  = '03'
      IMPORTING
        ex_subrc  = pv_result
        ex_mess   = lv_mess
      TABLES
        it_tab    = lr_zhtlx[].

    IF pv_result = 'E'.
      RETURN.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_process_data .

  "附加条款执行
  PERFORM frm_process_ats_tk.
  "正常条款执行
  PERFORM frm_process_nts_tk.

  IF s_zxy_id[] IS NOT INITIAL.
    DELETE gt_alv WHERE zxy_id NOT IN s_zxy_id.
  ENDIF.

ENDFORM.
**&---------------------------------------------------------------------*
**& Form FRM_CHECK_AUTHORITY
**&---------------------------------------------------------------------*
**& 检查权限
**&---------------------------------------------------------------------*
**&      --> LS_ZRET0006
**&      <-- LV_SUBRC
**&---------------------------------------------------------------------*
*FORM frm_check_authority  USING    ps_data TYPE ty_ztk_id
*                          CHANGING pv_result.
*
*  DATA:lr_bukrs TYPE RANGE OF t001-bukrs.
*  DATA:lr_ekgrp TYPE TABLE OF zbcs0003.
*  DATA:lr_zhtlx TYPE TABLE OF zbcs0003.
*  DATA:lv_mess  TYPE bapiret2-message.
*
*  CLEAR:lr_bukrs[],
*        pv_result,
*        lv_mess.
*
*  lr_bukrs[] = VALUE #( ( sign = 'I'
*                          option = 'EQ'
*                          low = ps_data-zbukrs
*                      ) ).
*  "公司代码的权限
*  CALL FUNCTION 'ZBCFM0001'
*    EXPORTING
*      iv_object = 'ZMMAR005'
*      iv_field  = 'BUKRS'
*      iv_actvt  = '03'
*    IMPORTING
*      ex_subrc  = pv_result
*      ex_mess   = lv_mess
*    TABLES
*      it_tab    = lr_bukrs[].
*
*  IF pv_result = 'E'.
*    RETURN.
*  ENDIF.
*
*  IF ps_data-ekgrp IS NOT INITIAL.
*    CLEAR:lr_ekgrp[],
*          pv_result,
*          lv_mess.
*
*    lr_ekgrp[] = VALUE #( ( sign = 'I'
*                            option = 'EQ'
*                            low = ps_data-ekgrp
*                         ) ).
*
*    CALL FUNCTION 'ZBCFM0001'
*      EXPORTING
*        iv_object = 'ZREAR001'
*        iv_field  = 'EKGRP'
*        iv_actvt  = '03'
*      IMPORTING
*        ex_subrc  = pv_result
*        ex_mess   = lv_mess
*      TABLES
*        it_tab    = lr_ekgrp[].
*
*    IF pv_result = 'E'.
*      RETURN.
*    ENDIF.
*  ENDIF.
*
*  IF ps_data-zhtlx IS NOT INITIAL.
*    CLEAR:lr_zhtlx[],
*          pv_result,
*          lv_mess.
*
*    lr_zhtlx[] = VALUE #( ( sign = 'I'
*                            option = 'EQ'
*                            low = ps_data-zhtlx
*                         ) ).
*
*    CALL FUNCTION 'ZBCFM0001A'
*      EXPORTING
*        iv_object = 'ZREAR002'
*        iv_field  = 'ZHTLX'
*        iv_actvt  = '03'
*      IMPORTING
*        ex_subrc  = pv_result
*        ex_mess   = lv_mess
*      TABLES
*        it_tab    = lr_zhtlx[].
*
*    IF pv_result = 'E'.
*      RETURN.
*    ENDIF.
*  ENDIF.
*
*ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SHOW_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_show_data .
  DATA:ls_layout   TYPE lvc_s_layo.
  DATA:lt_fieldcat TYPE lvc_t_fcat.
  DATA:lt_event    TYPE slis_t_event.

  PERFORM frm_layout_data CHANGING ls_layout.

  PERFORM frm_fieldcate_data CHANGING lt_fieldcat.

  PERFORM frm_event_data CHANGING lt_event.

  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program = sy-repid
      is_layout_lvc      = ls_layout
      it_fieldcat_lvc    = lt_fieldcat
      i_default          = 'X'
      i_save             = 'A'
      it_events          = lt_event
    TABLES
      t_outtab           = gt_alv
    EXCEPTIONS
      program_error      = 1
      OTHERS             = 2.

  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SCREEN_OUTPUT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_screen_output .


  LOOP AT SCREEN.
    IF p_bptype IS INITIAL.
      REFRESH s_bpcode[].
      IF screen-group1 = 'A1'.
        screen-active = '0'.
      ENDIF.
      MODIFY SCREEN.
    ELSE.
      IF screen-group1 = 'A1'.
        screen-active = '1'.
      ENDIF.
      MODIFY SCREEN.
    ENDIF.

    IF screen-group1 = 'A2'.
      screen-required = '2'.
      MODIFY SCREEN.
    ENDIF.
    IF sy-tcode = 'ZRED0008' AND screen-group1 = 'A3'.
      screen-active = '0'.
      MODIFY SCREEN.
    ENDIF.

    IF sy-tcode = 'ZRED0008' AND screen-group1 = 'A4'.
      screen-active = '0'.
      MODIFY SCREEN.
    ENDIF.

  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_LIST_SET
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_list_set .
  DATA:
    list  TYPE vrm_values,
    value LIKE LINE OF list.

  SELECT domvalue_l AS key
         ddtext   AS text
    INTO TABLE list
    FROM dd07t
    WHERE domname = 'ZRED_ZBPTYPE'.


**调用下拉框赋值函数
  CALL FUNCTION 'VRM_SET_VALUES'
    EXPORTING
      id     = 'P_BPTYPE'
      values = list.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_LAYOUT_DATA
*&---------------------------------------------------------------------*
*&设置Layout
*&---------------------------------------------------------------------*
*&      <-- LS_LAYOUT
*&---------------------------------------------------------------------*
FORM frm_layout_data  CHANGING ls_layout TYPE lvc_s_layo.

  ls_layout-zebra      = 'X'.       "X:行项目带颜色标识
  ls_layout-sel_mode   = 'A'.       "A:显示alv的选择行按
  ls_layout-cwidth_opt = 'X'.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_FIELDCATE_DATA
*&---------------------------------------------------------------------*
*& 设置FIeldcat
*&---------------------------------------------------------------------*
FORM frm_fieldcate_data  CHANGING pt_fieldcat  TYPE lvc_t_fcat.

  PERFORM frm_set_fieldcat USING 'ICON' '状态'                 CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'MESSAGE' '消息'              CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZXY_ID' '协议号码 '          CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZBUDAT' '日期'               CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZPURJZSL' '当日基准采购数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZPURJZJE' '当日基准采购总额' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZPURYSSL' '当日采购应收数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZPURYSJE' '当日采购应收金额' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZDISTJZSL' '当日基准配送数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZDISTJZJE' '当日基准配送总额' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZDISTYSSL' '当日配送应收数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZDISTYSJE' '当日配送应收金额' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZSALEJZSL' '当日基准销售数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZSALEJZJE' '当日基准销售总额' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZSALEYSSL' '当日销售应收数量' CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZSALEYSJE' '当日销售应收金额' CHANGING pt_fieldcat.

  PERFORM frm_set_fieldcat USING 'ZDUEPAYJE' '应付金额'         CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZDUERCV' '应收金额(基于应付)' CHANGING pt_fieldcat.

  PERFORM frm_set_fieldcat USING 'ZACTLPAYJE' '实付金额'         CHANGING pt_fieldcat.
  PERFORM frm_set_fieldcat USING 'ZACTLRCV' '应收金额(基于实付)' CHANGING pt_fieldcat.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_EVENT_DATA
*&---------------------------------------------------------------------*
*& 事件数据
*&---------------------------------------------------------------------*
*&      --> LT_EVENT
*&---------------------------------------------------------------------*
FORM frm_event_data  CHANGING lt_event TYPE slis_t_event.

  lt_event = VALUE #( ( name = slis_ev_user_command         form = 'FRM_USER_COMMAND' )
                      ( name = slis_ev_pf_status_set        form = 'FRM_SET_STATUS'   )
                      ).

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  FRM_SET_FIELDCAT
*&---------------------------------------------------------------------*
*       设置Fieldcat
*----------------------------------------------------------------------*
*      -->P_1271   text
*      -->P_1272   text
*----------------------------------------------------------------------*
FORM frm_set_fieldcat USING i_fieldname
                            i_seltext
                      CHANGING pt_fieldcat TYPE lvc_t_fcat.

  DATA:ls_fieldcat TYPE lvc_s_fcat.

  ls_fieldcat-fieldname = i_fieldname.
  ls_fieldcat-scrtext_l = i_seltext.

  IF i_fieldname = 'ICON'.
    ls_fieldcat-icon = 'X'.
  ENDIF.

  IF i_fieldname CS 'ZPUR'.
    ls_fieldcat-emphasize = 'C710'.
    ls_fieldcat-do_sum = 'X'.
  ENDIF.

  IF i_fieldname CS 'ZDIST'.
    ls_fieldcat-emphasize = 'C610'.
    ls_fieldcat-do_sum = 'X'.
  ENDIF.

  IF i_fieldname CS 'ZSALE'.
    ls_fieldcat-emphasize = 'C510'.
    ls_fieldcat-do_sum = 'X'.
  ENDIF.

  IF i_fieldname CS 'ZACT'.
    ls_fieldcat-emphasize = 'C410'.
    ls_fieldcat-do_sum = 'X'.
  ENDIF.

  IF i_fieldname CS 'ZDUE'.
    ls_fieldcat-emphasize = 'C310'.
    ls_fieldcat-do_sum = 'X'.
  ENDIF.

  APPEND ls_fieldcat TO pt_fieldcat.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  FRM_SET_STATUS
*&---------------------------------------------------------------------*
*       设置Status
*----------------------------------------------------------------------*
*      -->IT_EXTAB   text
*----------------------------------------------------------------------*
FORM frm_set_status USING it_extab TYPE slis_t_extab.

  SET PF-STATUS 'S1000' EXCLUDING it_extab.
  SET TITLEBAR 'T1000'.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  FRM_USER_COMMAND
*&---------------------------------------------------------------------*
*       User Command事件
*----------------------------------------------------------------------*
FORM frm_user_command USING i_ucomm      TYPE sy-ucomm
                            re_selfield  TYPE slis_selfield.

  re_selfield-refresh    = 'X'.
  re_selfield-row_stable = 'X'.
  re_selfield-col_stable = 'X'.

  CASE i_ucomm.
    WHEN '&IC1'.
      PERFORM frm_usercomm_ic1 USING re_selfield.
  ENDCASE.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_USERCOMM_IC1
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> RE_SELFIELD
*&---------------------------------------------------------------------*
FORM frm_usercomm_ic1  USING    re_selfield  TYPE slis_selfield.

  CHECK re_selfield-fieldname = 'ZXY_ID' OR re_selfield-fieldname = 'ZJSD_ID'.

  CASE re_selfield-fieldname.
    WHEN 'ZXY_ID'.
      SET PARAMETER ID : 'ZXYID' FIELD re_selfield-value.
      CALL TRANSACTION 'ZRED0001C' AND SKIP FIRST SCREEN.
    WHEN 'ZJSD_ID'.


    WHEN OTHERS.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SET_BEGIN_END_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_ZXYID
*&      <-- LV_BEGIN
*&      <-- LV_END
*&---------------------------------------------------------------------*
FORM frm_set_begin_end_date  TABLES  pt_zxyid STRUCTURE zres0055
                               USING pv_tk_id TYPE zree_ztk_id.

  DATA:lt_zreta004 TYPE TABLE OF zreta004.
  DATA:lv_yestdat    TYPE dats.
  DATA:lv_date_begin TYPE dats.
  DATA:lv_date_end   TYPE dats.


  lv_date_begin = s_datum-low.
  lv_date_end   = COND #( WHEN s_datum-high IS NOT INITIAL THEN s_datum-high ELSE s_datum-low ).


  SELECT * INTO TABLE lt_zreta004 FROM zreta004
     FOR ALL ENTRIES IN pt_zxyid
   WHERE zxy_id = pt_zxyid-zxy_id AND zatktp = 'A'.


  SELECT SINGLE zrlid
    INTO @DATA(lv_zrlid)
    FROM zreta003
   WHERE ztk_id = @pv_tk_id
     AND zreta003~zatktp = 'P'
     AND zreta003~zatkpi = 'C'.
  IF sy-subrc <> 0.
    CLEAR:lv_zrlid.
  ENDIF.

  LOOP AT pt_zxyid ASSIGNING FIELD-SYMBOL(<lfs_data>)  .

    "如果协议起始日期小于屏幕起始日期，则有效日期起始日期为屏幕日期
    READ TABLE lt_zreta004 INTO DATA(ls_zreta004)  WITH  KEY zxy_id = <lfs_data>-zxy_id.
    IF sy-subrc = 0.
      DELETE pt_zxyid.
      CONTINUE.
    ENDIF.

    IF lv_zrlid IS NOT INITIAL .
      READ TABLE gt_exe_tk TRANSPORTING NO FIELDS WITH KEY  ztk_id = lv_zrlid.
*    不等于0，表示该条款没有执行附加条款，开始日期按照屏幕的日期执行跑数，
*    等于  0，表示该条款执行了附加条款，  开始日期从协议的起始日开始
      IF sy-subrc <> 0.
        IF <lfs_data>-zbegin < lv_date_begin.
          <lfs_data>-zbegin = lv_date_begin.
        ENDIF.
      ENDIF.
    ELSE.
      IF <lfs_data>-zbegin < lv_date_begin.
        <lfs_data>-zbegin = lv_date_begin.
      ENDIF.
    ENDIF.

*  READ TABLE gt_ztk_id_map INTO DATA(ls_ztk_id_map)  WITH  KEY ztk_id = pv_tk_id.
**    不等于0，表示该条款没有执行附加条款，开始日期按照屏幕的日期执行跑数，
**    等于  0，表示该条款执行了附加条款，  开始日期从协议的起始日开始
*  IF sy-subrc <> 0.
*    IF <lfs_data>-zbegin < lv_date_begin.
*      <lfs_data>-zbegin = lv_date_begin.
*    ENDIF.
*  ENDIF.

    "如果协议终止日期大于屏幕终止日期，则有效日期终止日期为屏幕终止日期

    IF <lfs_data>-zend > lv_date_end.
      <lfs_data>-zend = lv_date_end.
    ENDIF.


    IF <lfs_data>-zbegin > <lfs_data>-zend.
      DELETE pt_zxyid.
    ENDIF.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_ATS_TK
*&---------------------------------------------------------------------*
*& text 附加条款执行
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_process_ats_tk .
  DATA:"协议清单
    lt_zxyid TYPE TABLE OF zres0055,
    lt_retab TYPE TABLE OF zres0054.
  DATA:
    lv_yestdat TYPE sy-datum.

  lv_yestdat    = sy-datum - 1.

  SORT gt_ztk_id_ats BY zlevel DESCENDING.

  LOOP AT gt_ztk_id_ats INTO DATA(ls_ztk_id) .

    SELECT zret0006~zxy_id
           zret0006~zbegin
           zret0006~zend
      INTO TABLE lt_zxyid
      FROM zret0006
     INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
     WHERE zret0006~ztk_id = ls_ztk_id-ztk_id
       AND zret0006~zbegin <= lv_yestdat
    AND ( ( zreta002~zcnyg = '' AND zret0006~zxyzt  = 'A' ) OR ( zreta002~zcnyg = 'X' AND zret0006~zxyzt  <> 'D' ) ).


    PERFORM frm_set_begin_end_date TABLES lt_zxyid
                                    USING ls_ztk_id-ztk_id  .


    MESSAGE |开始执行附加条款{ ls_ztk_id-ztk_id }下协议条目{ lines( lt_zxyid[] ) } | TYPE 'S'.

    IF lt_zxyid[] IS NOT INITIAL .

      IF p_yjyx = ''.
        IF ls_ztk_id-zxybstyp = 'P' .
          CALL FUNCTION 'ZREFM0048'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ELSE.
          CALL FUNCTION 'ZREFM0026'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
              iv_zjzrq   = p_zjzrq
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ENDIF.
      ELSE.
        IF ls_ztk_id-zxybstyp = 'P' .
          CALL FUNCTION 'ZREFMA048'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ELSE.
          CALL FUNCTION 'ZREFMA026'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ENDIF.

      ENDIF.

      IF p_back = ''.
        APPEND LINES OF lt_retab TO gt_alv.
      ENDIF.
      REFRESH lt_retab.
    ENDIF.

    MESSAGE |结束执行附加条款{ ls_ztk_id-ztk_id }下协议条目{ lines( lt_zxyid[] ) } | TYPE 'S'.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_NTS_TK
*&---------------------------------------------------------------------*
*& text 正常条款执行
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_process_nts_tk .

  DATA:"协议清单
    lt_zxyid TYPE TABLE OF zres0055,
    lt_retab TYPE TABLE OF zres0054.
  DATA:
    lv_yestdat TYPE sy-datum.


  lv_yestdat    = sy-datum - 1.

  SORT gt_ztk_id_nts BY zlevel DESCENDING.

  LOOP AT gt_ztk_id_nts INTO DATA(ls_ztk_id) .

    SELECT zret0006~zxy_id
           zret0006~zbegin
           zret0006~zend
      INTO TABLE lt_zxyid
      FROM zret0006
     INNER JOIN zreta002 ON zret0006~ztk_id = zreta002~ztk_id
     WHERE zret0006~ztk_id  = ls_ztk_id-ztk_id
       AND zret0006~zbegin <= lv_yestdat
      AND ( ( zreta002~zcnyg = '' AND zret0006~zxyzt  = 'A' ) OR ( zreta002~zcnyg = 'X' AND zret0006~zxyzt  <> 'D' ) ).

    PERFORM frm_set_begin_end_date TABLES lt_zxyid
                                    USING ls_ztk_id-ztk_id .

    MESSAGE |开始执行条款{ ls_ztk_id-ztk_id }下协议条目{ lines( lt_zxyid[] ) } | TYPE 'S'.

    IF lt_zxyid[] IS NOT INITIAL .
      IF  p_yjyx = ''.
        IF ls_ztk_id-zxybstyp = 'P' .
          CALL FUNCTION 'ZREFM0048'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ELSE.
          CALL FUNCTION 'ZREFM0026'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
              iv_zjzrq   = p_zjzrq
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ENDIF.
      ELSE.
        IF ls_ztk_id-zxybstyp = 'P' .
          CALL FUNCTION 'ZREFMA048'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ELSE.
          CALL FUNCTION 'ZREFMA026'
            EXPORTING
              iv_ztk_id  = ls_ztk_id-ztk_id
              iv_messgae = p_smeg
            TABLES
              it_zxyid   = lt_zxyid
              et_retab   = lt_retab.
        ENDIF.
      ENDIF.
      IF p_back = ''.
        APPEND LINES OF lt_retab TO gt_alv.
      ENDIF.
      REFRESH lt_retab.
    ENDIF.
    MESSAGE |结束执行条款{ ls_ztk_id-ztk_id }下协议条目{ lines( lt_zxyid[] ) } | TYPE 'S'.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ZTK_ID
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_check_date  TABLES   pt_ztk_id TYPE tt_exe_id
                     CHANGING pv_error.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  DATA:BEGIN OF lt_zbukrs OCCURS 0,
         zbukrs TYPE zree_zbukrs,
       END OF lt_zbukrs.

  RANGES:rt_bukrs FOR zreta001-zbukrs.
  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:lv_str TYPE string.

  CHECK p_autadj IS INITIAL.

  MOVE-CORRESPONDING pt_ztk_id[] TO lt_zbukrs[].

  SORT lt_zbukrs BY zbukrs.
  DELETE ADJACENT DUPLICATES FROM lt_zbukrs COMPARING zbukrs.

  LOOP AT lt_zbukrs  .
    CALL FUNCTION 'ZREFM0049'
      EXPORTING
        iv_bukrs = lt_zbukrs-zbukrs
        iv_datum = sy-datum
      IMPORTING
        ev_moste = lv_moste
        ev_meg   = lv_meg.

    IF lv_moste = 'E' .
      rt_bukrs-sign   = 'I' .
      rt_bukrs-option = 'EQ'.
      rt_bukrs-low    = lt_zbukrs-zbukrs .
      COLLECT rt_bukrs.
    ENDIF.
    CLEAR:lv_moste,lv_meg.
  ENDLOOP.


  IF rt_bukrs[] IS NOT INITIAL .
    DELETE pt_ztk_id WHERE zbukrs NOT IN  rt_bukrs .
    SORT pt_ztk_id BY zbukrs.
    LOOP AT pt_ztk_id.
      lv_str = |条款{ pt_ztk_id-ztk_id }对应的合同主体{ pt_ztk_id-zbukrs }在其锁定期内，请检查！|.
      ls_msglist-msgty = 'E'.
      ls_msglist-msgid = '00'.
      ls_msglist-msgno = '001'.
      ls_msglist-msgv1 = lv_str.
      APPEND ls_msglist TO lt_msglist.
    ENDLOOP.

    IF lt_msglist[] IS NOT INITIAL .
      IF sy-batch IS INITIAL.
        SORT lt_msglist.
        DELETE ADJACENT DUPLICATES FROM lt_msglist.
        CALL FUNCTION 'SCPR_SV_SHOW_MESSAGE_LIST'
          EXPORTING
            title_text    = '消息提示'
            sort_by_level = ' '
            show_ids      = ''
            message_list  = lt_msglist[].

        LEAVE LIST-PROCESSING.
      ELSE.
        MESSAGE '有条款处于月结锁定期间，禁止返利计算运行' TYPE  'S' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING.
      ENDIF.
    ENDIF.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA1
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data1 .

  DATA:lt_first_id TYPE tt_first_id.
  DATA:lt_sel_id  TYPE zre03_tt_tk_id.
  DATA:lt_tk_id   TYPE zre03_tt_tk_id.
  DATA:lt_tk_id_q TYPE zre03_tt_tk_id. "前序条款
  DATA:lt_tk_id_h TYPE zre03_tt_tk_id. "后续条款
  DATA:ls_exe_tk  TYPE ty_exe_id.      "执行条款
  DATA:lt_exe_tk  TYPE tt_exe_id.      "执行条款
  DATA:lv_zlevel  TYPE /bdl/_level VALUE 0 .

  "条款数据
  SELECT DISTINCT
         zreta001~zhtlx,
         zreta001~ekgrp,
         zreta001~zbukrs,
         zreta001~zht_id,
         zreta002~ztk_id
    INTO TABLE @lt_first_id
    FROM zreta002
    INNER JOIN zreta001 ON zreta001~zht_id  = zreta002~zht_id
    INNER JOIN zret0006 ON zret0006~ztk_id  = zreta002~ztk_id
    WHERE zreta001~zht_id   IN @s_zht_id "合同号码
      AND zreta001~zhtlx    IN @s_zhtlx  "合同类型
      AND zreta001~zbukrs   IN @s_zbukrs "合同主体公司
      AND zreta001~zbpcode  IN @s_bpcode "伙伴ID
      AND zreta002~ztk_id   IN @s_ztk_id "条款号码
      AND zreta002~zfllx    IN @s_zfllx  "返利类型
      AND zreta002~zleib    <> 'R'       "类别
      AND zret0006~zxy_id   IN @s_zxy_id "协议号码
      AND zret0006~zxybstyp IN ('V','A','T','P')
      AND zret0006~zxybstyp IN @s_zxybtp
      AND zreta002~zend     >= @s_datum-low
      AND zreta002~zbegin   <= @s_datum-high
      AND ( ( zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = ''   ) OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  )
          )  .
  "权限检查
  DATA:lv_result TYPE c.
  LOOP AT lt_first_id INTO DATA(ls_first_id) .
    PERFORM frm_check_authority1      USING ls_first_id
                                  CHANGING lv_result.
    IF lv_result = 'E'.
      DELETE lt_first_id.
    ENDIF.
  ENDLOOP.


  MOVE-CORRESPONDING lt_first_id TO lt_tk_id_q.
  MOVE-CORRESPONDING lt_first_id TO lt_sel_id.

  "条款的前序条款
  DO 10 TIMES .
    IF lt_sel_id IS INITIAL.
      EXIT.
    ENDIF.

    SORT  lt_sel_id BY ztk_id.
    DELETE ADJACENT DUPLICATES FROM lt_sel_id COMPARING ztk_id.

    SELECT zreta003~zrlid AS ztk_id
      INTO TABLE @lt_tk_id
      FROM zreta003
      INNER JOIN zreta002 ON zreta003~zrlid = zreta002~ztk_id
       FOR ALL ENTRIES IN @lt_sel_id
     WHERE zreta003~ztk_id  = @lt_sel_id-ztk_id
       AND zreta003~zatktp = 'P'
       AND zreta003~zatkpi = 'C'
       AND zreta002~zleib  <> 'R'       "类别
       AND ( ( zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = '') OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  ) ).
    IF sy-subrc <> 0.
      EXIT.
    ELSE.
      APPEND LINES OF lt_tk_id TO lt_tk_id_q.
      lt_sel_id = lt_tk_id.
    ENDIF.

  ENDDO.

  "条款的后续条款
  MOVE-CORRESPONDING lt_tk_id_q TO lt_tk_id_h.
  MOVE-CORRESPONDING lt_tk_id_q TO lt_sel_id.

  DO 10 TIMES .

    IF lt_sel_id IS INITIAL.
      EXIT.
    ENDIF.

    SORT  lt_sel_id BY ztk_id.
    DELETE ADJACENT DUPLICATES FROM lt_sel_id COMPARING ztk_id.

    SELECT zreta003~ztk_id
      INTO TABLE @lt_tk_id
      FROM zreta003
     INNER JOIN zreta002 ON zreta003~ztk_id = zreta002~ztk_id
       FOR ALL ENTRIES IN @lt_sel_id
     WHERE zreta003~zrlid  = @lt_sel_id-ztk_id
       AND zreta003~zatktp = 'P'
       AND zreta003~zatkpi = 'C'
       AND zreta002~zleib  <> 'R'       "类别
       AND ( ( zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = '') OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  ) ).

    IF sy-subrc <> 0.
      EXIT.
    ELSE.
      APPEND LINES OF lt_tk_id TO lt_tk_id_h.
      lt_sel_id = lt_tk_id.
    ENDIF.

  ENDDO.

**********************************************************************
***条款默认优先级 10 包含正常条款和附加条款
  MOVE-CORRESPONDING lt_tk_id_h TO lt_sel_id.
  SORT  lt_sel_id BY ztk_id.
  DELETE ADJACENT DUPLICATES FROM lt_sel_id COMPARING ztk_id.
  IF lt_sel_id[] IS NOT INITIAL .
    SELECT zreta002~ztk_id,
           zreta002~zxybstyp,
           zreta002~ztktype,
           zreta001~zbukrs
      INTO CORRESPONDING FIELDS OF TABLE @lt_exe_tk
      FROM zreta002
     INNER JOIN zreta001 ON zreta001~zht_id  = zreta002~zht_id
       FOR ALL ENTRIES IN @lt_sel_id
     WHERE zreta002~ztk_id  = @lt_sel_id-ztk_id
*       AND zreta002~ztktype = ''
       AND zreta002~zleib  <> 'R'       "类别
       AND ( ( zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = '') OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  ) ).
    IF sy-subrc = 0.
      lv_zlevel = lv_zlevel + 10.
      ls_exe_tk-zlevel = lv_zlevel.
      MODIFY lt_exe_tk FROM ls_exe_tk   TRANSPORTING zlevel WHERE zlevel IS INITIAL .
      APPEND LINES OF lt_exe_tk TO gt_exe_tk.
    ENDIF.
  ENDIF.
**********************************************************************
***附加条款数据提升优先级
  DO 10 TIMES.

    IF lt_sel_id IS INITIAL.
      EXIT.
    ENDIF.

    SORT  lt_sel_id BY ztk_id.
    DELETE ADJACENT DUPLICATES FROM lt_sel_id COMPARING ztk_id.

    SELECT zreta003~zrlid AS ztk_id,
           zreta002~zxybstyp,
           zreta002~ztktype,
           zreta001~zbukrs
      INTO CORRESPONDING FIELDS OF TABLE @lt_exe_tk
      FROM zreta003
      INNER JOIN zreta002 ON zreta002~ztk_id = zreta003~zrlid
      INNER JOIN zreta001 ON zreta001~zht_id  = zreta002~zht_id
       FOR ALL ENTRIES IN @lt_sel_id
     WHERE zreta003~ztk_id  = @lt_sel_id-ztk_id
       AND zreta003~zatktp = 'P'
       AND zreta003~zatkpi = 'C'
       AND zreta002~zleib <> 'R'       "类别
       AND ( ( zreta002~zxyzt EQ 'A' AND zreta002~zcnyg = '') OR ( zreta002~zcnyg = 'X' AND zreta002~zxyzt <> 'D'  ) ).
    IF sy-subrc <> 0.
      EXIT.
    ELSE.
      lv_zlevel = lv_zlevel + 10.
      ls_exe_tk-zlevel = lv_zlevel.
      MODIFY lt_exe_tk FROM ls_exe_tk   TRANSPORTING zlevel WHERE zlevel IS INITIAL .
      APPEND LINES OF lt_exe_tk TO gt_exe_tk.
      lt_sel_id = lt_exe_tk.
    ENDIF.
  ENDDO.
**********************************************************************
*按照优先级排序去重
  SORT gt_exe_tk BY ztk_id ASCENDING  zlevel DESCENDING.
  DELETE ADJACENT DUPLICATES FROM gt_exe_tk COMPARING ztk_id.
**********************************************************************
*    期间锁定检查
  IF p_yjyx = ''.
    DATA:lv_error.
    PERFORM frm_check_date   TABLES gt_exe_tk[]
                           CHANGING lv_error .
  ENDIF.


  gt_ztk_id_nts = gt_exe_tk.
  DELETE gt_ztk_id_nts WHERE ztktype = 'P'.
  DELETE ADJACENT DUPLICATES FROM gt_ztk_id_nts COMPARING ztk_id.
  gt_ztk_id_ats = gt_exe_tk.
  DELETE gt_ztk_id_ats WHERE ztktype = ''.
  DELETE ADJACENT DUPLICATES FROM gt_ztk_id_nts COMPARING ztk_id.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_INTIAL_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_intial_data .

  IF p_gwzx = ''.
    p_autadj = 'X'.
  ENDIF.

  IF p_zjzrq IS INITIAL .
    p_zjzrq = '99991231'.
  ENDIF.
ENDFORM.