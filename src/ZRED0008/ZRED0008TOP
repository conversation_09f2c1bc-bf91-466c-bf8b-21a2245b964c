*&---------------------------------------------------------------------*
*& Report ZRED0015
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
REPORT ZRER0102 MESSAGE-ID zre002.
*----------------------------------------------------------------------*
* <1.1-声明 包含程序> Include Programs                                  *
*----------------------------------------------------------------------*
***********《所有全局变量、类,选择屏幕 都写在  DEFINE include 程序中》*********
INCLUDE ZRER0102TOP.
INCLUDE ZRER0102SEL.
INCLUDE ZRER0102F01.
*----------------------------------------------------------------------*
* <2.2-选择屏幕事件>                                                    *
* Events That Occur While The Selection Screen Is Bing Processed       *
*----------------------------------------------------------------------*
INITIALIZATION.


* 所有选择屏幕数据传送到程序中之后触发的事件
AT SELECTION-SCREEN.
  "权限检查
*  PERFORM FRM_AUTHORITY_CHECK.



*选择屏幕PBO事件，在显示选择屏幕前触发
AT SELECTION-SCREEN OUTPUT.
*----------------------------------------------------------------------*
* <2.3-在选择屏幕被处理后触发的事件,程序默认的开始事件>                  *
* Event Occurs After The Selection Screen Has Been Processed           *
*----------------------------------------------------------------------*
START-OF-SELECTION.
  "获取数据
  PERFORM FRM_GET_DATA.
  "处理数据

  PERFORM FRM_PROCESS_DATA.

*----------------------------------------------------------------------*
* <2.4-最后被触发的事件>                                                *
* The Last Of The Events Called By The Runtime Environment To Occur    *
*----------------------------------------------------------------------*
END-OF-SELECTION.
  "展示数据
    IF GT_ALV IS NOT INITIAL.
      SORT  GT_ALV by ZXY_ID ZHSQJ_ID.
      PERFORM FRM_SHOW_DATA.
    ELSE.
      MESSAGE '未查询到明细数据' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.