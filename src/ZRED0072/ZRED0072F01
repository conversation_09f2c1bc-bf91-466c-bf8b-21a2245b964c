*&---------------------------------------------------------------------*
*& 包含               ZRED0072F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_INIT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_init .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_MAIN
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_main .
  SELECT zreta002~ztk_id
         zreta002~ztk_txt
         zreta002~ekgrp
         zret0006~zxy_id
         zret0006~zxy_txt
         zret0006~zhtlx
         zret0006~zxyzt
         zret0006~zbukrs
         zret0006~frgsx
         zret0006~kolnr
         zret0006~zbegin
         zret0006~zend
         zret0006~zxybstyp
    INTO CORRESPONDING FIELDS OF TABLE gt_alv
    FROM  zret0006
    INNER JOIN zreta002 ON zreta002~ztk_id = zret0006~ztk_id
    INNER JOIN zreta001 ON zreta001~zht_id = zreta002~zht_id
    WHERE zret0006~zxy_id IN s_zxy_id
      AND zreta002~ztk_id IN s_ztk_id
      AND zreta001~zht_id IN s_zht_id
      AND zreta001~zhtlx  IN s_zhtlx
      AND zret0006~zxyzt NOT IN ( 'A','D' )
      AND zreta002~zxyzt = 'A'
      AND zret0006~zxyzt = p_zxyzt
      AND zret0006~frgsx = p_frgsx .


  LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<lfs_alv>).
    SELECT SINGLE frgc1 zfrgtx
      INTO (<lfs_alv>-frgc1,<lfs_alv>-zfrgtx)
      FROM zretc007
     WHERE frgsx = <lfs_alv>-frgsx
       AND kolnr = <lfs_alv>-kolnr .

    AUTHORITY-CHECK OBJECT 'ZREAR006'
                ID 'ZHTLX' FIELD <lfs_alv>-zhtlx
                ID 'ACTVT' FIELD '03'.
    IF sy-subrc <> 0.
      DELETE gt_alv.
    ENDIF.

    AUTHORITY-CHECK OBJECT 'ZREAR008'
                        ID 'EKGRP' FIELD <lfs_alv>-ekgrp
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc <> 0.
      DELETE gt_alv.
    ENDIF.

    AUTHORITY-CHECK OBJECT 'ZREAR009'
                        ID 'BUKRS' FIELD <lfs_alv>-zbukrs
                        ID 'ACTVT' FIELD '03'.
    IF sy-subrc NE 0.
      DELETE gt_alv.
    ENDIF.



    AUTHORITY-CHECK OBJECT 'ZREAR006'
                ID 'ZHTLX' FIELD <lfs_alv>-zhtlx
                ID 'ACTVT' FIELD <lfs_alv>-frgc1.
    IF sy-subrc <> 0.
      DELETE gt_alv.
    ENDIF.


    AUTHORITY-CHECK OBJECT 'ZREAR008'
                        ID 'EKGRP' FIELD <lfs_alv>-ekgrp
                        ID 'ACTVT' FIELD <lfs_alv>-frgc1.
    IF sy-subrc <> 0.
      DELETE gt_alv.
    ENDIF.

  ENDLOOP.

  DELETE gt_alv WHERE frgc1 <> p_frgc1.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_ALV_DISPLAY
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_alv_display .

  DATA:ls_layout   TYPE lvc_s_layo,
       lt_fieldcat TYPE lvc_t_fcat,
       ls_variant  TYPE disvariant.

  ls_layout-cwidth_opt = 'X'.
  ls_layout-zebra      = 'X'.
  ls_layout-sel_mode   = 'D'.
  ls_layout-box_fname  = 'SEL'.
  ls_variant-report    = sy-repid.
  ls_variant-handle    = 1.
  ls_variant-username  = sy-uname.

  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZTK_ID'       '返利条款'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZTK_TXT'      '条款描述'             '50'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZXY_ID'       '返利协议'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZXY_TXT'      '协议名称'             '30'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZXYZT'        '协议状态'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'FRGSX'        '审批策略'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'KOLNR'        '访问顺序-访问编号'    '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZFRGTX'       '审批节点描述'         '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'FRGC1'        '审批代码'             '10'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZBEGIN'       '开始日期'             '18'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'ZEND'         '结束日期'             '18'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'EXEBS'        '已执行标识'           '18'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'STATE'        '状态'                 '18'.
  PERFORM frm_set_fieldcat TABLES lt_fieldcat USING 'MESSAGE'      '消息文本'             '18'.


  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = ls_layout
      it_fieldcat_lvc          = lt_fieldcat
      is_variant               = ls_variant
      i_save                   = 'U'
      i_callback_pf_status_set = 'FRM_SET_STATUS'
      i_callback_user_command  = 'FRM_USER_COMMAND'
    TABLES
      t_outtab                 = gt_alv
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_SET_FIELDCAT
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> P_
*&      --> P_
*&---------------------------------------------------------------------*
FORM frm_set_fieldcat  TABLES pt_fieldcat TYPE lvc_t_fcat
                        USING i_fieldname TYPE char20
                              i_seltext   TYPE char40
                              i_outputlen TYPE lvc_outlen.

  DATA:ls_fieldcat TYPE lvc_s_fcat.
  ls_fieldcat-fieldname = i_fieldname.
  ls_fieldcat-coltext   = i_seltext .
  ls_fieldcat-scrtext_l = i_seltext .
  ls_fieldcat-scrtext_m = i_seltext .
  ls_fieldcat-scrtext_s = i_seltext .
  ls_fieldcat-outputlen = i_outputlen.

  CASE i_fieldname.
    WHEN 'ZPAYTP' . ls_fieldcat-convexit  = 'ZPAYT'.ls_fieldcat-ref_field  = 'ZPAYTP'.   ls_fieldcat-ref_table  = 'ZRET0044'.
    WHEN 'ZHTLX'  . ls_fieldcat-convexit  = 'ZREHT'.ls_fieldcat-ref_field  = 'ZHTLX'.    ls_fieldcat-ref_table  = 'ZRETA001'.
    WHEN OTHERS.
  ENDCASE.
  APPEND ls_fieldcat TO pt_fieldcat.

ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  PFSTATUS_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->RT_EXTAB   TEXT
*----------------------------------------------------------------------*
FORM frm_set_status USING rt_extab TYPE slis_t_extab.

  "ERP-17167-月结锁定期间---新月份返利的计算与分摊
*  DATA:lv_error.
*  PERFORM frm_check_gkq  CHANGING lv_error.

*  IF lv_error = 'E'.
*    rt_extab = VALUE #( ( fcode =  'SPTG' ) ( fcode =  'SPJJ' )  ).
*  ENDIF.
  SET PF-STATUS 'S1000' EXCLUDING rt_extab .

ENDFORM.                    "PFSTATUS_FORM
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATE
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ZTK_ID
*&      <-- LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_check_gkq  CHANGING pv_error.

  DATA:lv_moste TYPE zree_moste,
       lv_meg   TYPE bapi_msg.

  CALL FUNCTION 'ZREFM0049'
    EXPORTING
      iv_bukrs = 'ALL'
      iv_datum = sy-datum
    IMPORTING
      ev_moste = lv_moste
      ev_meg   = lv_meg.

  pv_error =  lv_moste.

ENDFORM.
**&---------------------------------------------------------------------*
**&      FORM  PROCESS_COMMAND
**&---------------------------------------------------------------------*
**       TEXT
**----------------------------------------------------------------------*
**      -->RT_COM       TEXT
**      -->RS_SELFIELD  TEXT
**----------------------------------------------------------------------*
FORM frm_user_command USING rt_com LIKE sy-ucomm
      rs_selfield TYPE slis_selfield.

  CASE rt_com.

    WHEN '&IC1'.

    WHEN 'SPTG'.
      PERFORM frm_sptg_data .
    WHEN 'SPJJ'.
      PERFORM frm_spjj_data .
  ENDCASE.

  rs_selfield-refresh    = 'X'.
  rs_selfield-col_stable = 'X'.
  rs_selfield-row_stable = 'X'.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SPTG_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ALV
*&---------------------------------------------------------------------*
FORM frm_sptg_data  .

  DATA:lv_error.

  PERFORM frm_sptg_check_data CHANGING lv_error.

  CHECK lv_error <> 'E'.

  PERFORM frm_exe_sptg.

  COMMIT WORK.
  MESSAGE '审批执行完成，状态已更新！' TYPE 'S'.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SPJJ_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ALV
*&---------------------------------------------------------------------*
FORM frm_spjj_data .

  DATA:lv_error.

  PERFORM frm_spjj_check_data CHANGING lv_error.

  CHECK lv_error <> 'E'.

  PERFORM frm_exe_spjj.

  COMMIT WORK.
  MESSAGE '审批执行完成，状态已更新！' TYPE 'S'.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SPJJ_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ALV
*&---------------------------------------------------------------------*
FORM frm_get_kolnr   CHANGING    ps_approval    TYPE  zres0088
                                   pt_msglist    TYPE scp1_general_errors.


  DATA:ls_msglist   TYPE scp1_general_error.
  DATA:lv_nextline  TYPE sy-tabix.
  DATA:lv_upzttline TYPE zretc007-zstats.

  CLEAR:ls_msglist.
  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.


  SELECT * INTO TABLE @DATA(lt_zretc007) FROM zretc007 WHERE frgsx = @ps_approval-frgsx.
  IF sy-subrc <> 0.
    ls_msglist-msgv1 = '审批策略未找到审批策略明细！'.
    APPEND ls_msglist TO pt_msglist.
    EXIT.
  ENDIF.

  SORT lt_zretc007 BY kolnr .
  IF  ps_approval-kolnr IS INITIAL .
    READ TABLE lt_zretc007 INTO DATA(ls_zretc007) INDEX 1.
    IF sy-subrc = 0 .
      ps_approval-kolnr     = ls_zretc007-kolnr.
      ps_approval-frgc1     = ls_zretc007-frgc1.
      ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
      ps_approval-zxyzt     = 'N'.
    ENDIF.
  ELSE.
    READ TABLE lt_zretc007 INTO ls_zretc007   WITH  KEY kolnr = ps_approval-kolnr.
    IF sy-subrc = 0.
      IF ls_zretc007-zstats = 'A' .
        ps_approval-kolnr     = ls_zretc007-kolnr.
        ps_approval-frgc1     = ls_zretc007-frgc1.
        ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
        ps_approval-zxyzt     = ls_zretc007-zstats.
      ELSE.
        lv_upzttline = ls_zretc007-zstats.
        lv_nextline  =  sy-tabix + 1.


        READ TABLE lt_zretc007 INTO ls_zretc007 INDEX lv_nextline.
        IF sy-subrc <> 0 .
          ls_msglist-msgv1 = '最终审批节点未维护，保持当前审批状态！'.
          APPEND ls_msglist TO pt_msglist.
          EXIT.
        ELSE.
          ps_approval-kolnr     = ls_zretc007-kolnr.
          ps_approval-frgc1     = ls_zretc007-frgc1.
          ps_approval-zfrgtx    = ls_zretc007-zfrgtx .
          ps_approval-zxyzt     = lv_upzttline.
        ENDIF.
      ENDIF.
    ELSE.
      ls_msglist-msgv1 = '当前审批节点未知，请修改后重新保存！'.
      APPEND ls_msglist TO pt_msglist.
      EXIT.
    ENDIF.


  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SPJJ_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> GT_ALV
*&---------------------------------------------------------------------*
FORM frm_save_rles_log USING  pv_ztk_id
                              pv_zxy_id
                              pv_zxyzt
                              pv_frgc1
                              ps_approval  TYPE zre10_ty_approval.

  DATA: ls_t50 TYPE zret0050.

  ls_t50-ztk_id     = pv_ztk_id.
  ls_t50-zxy_id     = pv_zxy_id.
  ls_t50-timestamp  = sy-datum && sy-uzeit.
  ls_t50-zxyzt_o    = pv_zxyzt.
  ls_t50-frgc1_o    = pv_frgc1.
  ls_t50-zxyzt_n    = ps_approval-zxyzt.
  ls_t50-frgc1_n    = ps_approval-frgc1.
  ls_t50-zcjrq  = sy-datum.
  ls_t50-zcjsj  = sy-uzeit.
  ls_t50-zcjr   = sy-uname.

  MODIFY zret0050 FROM ls_t50.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_EXE_SPTG
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_exe_sptg .

  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE zre10_ty_approval.

  DATA:lt_ztk_spdata TYPE zre10_tt_tk_sp.
  DATA:ls_ztk_spdata TYPE zre10_ty_tk_sp.


  DATA:lv_zlock      TYPE c.
  DATA:lv_sylsd      TYPE datum.

  PERFORM frm_get_zlock(zre0001) CHANGING lv_zlock IF FOUND. "ERP-17167-月结锁定期间---新月份返利的计算与分摊
  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND. "ERP-17167-月结锁定期间---新月份返利的计算与分摊

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<lfs_alv>) WHERE sel = 'X'.

    IF lv_zlock = 'Y' AND <lfs_alv>-zbegin < lv_sylsd .
      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      ls_msglist-msgv1 = '月结锁定期不可审批月结期间协议'.
      APPEND ls_msglist TO lt_msglist.
    ELSE.
      MOVE-CORRESPONDING <lfs_alv> TO ls_approval.
      PERFORM frm_get_kolnr   CHANGING   ls_approval
                                         lt_msglist.
    ENDIF.

    IF lt_msglist[] IS NOT INITIAL .
      LOOP AT  lt_msglist INTO ls_msglist .
        <lfs_alv>-message = <lfs_alv>-message && ls_msglist-msgv1.
      ENDLOOP.
      <lfs_alv>-state = icon_led_red.
    ELSE.

      UPDATE zret0006    SET kolnr  = ls_approval-kolnr zxyzt  = ls_approval-zxyzt
                       WHERE ztk_id = <lfs_alv>-ztk_id AND zxy_id = <lfs_alv>-zxy_id .

      PERFORM frm_save_rles_log USING <lfs_alv>-ztk_id
                                      <lfs_alv>-zxy_id
                                      <lfs_alv>-zxyzt
                                      <lfs_alv>-frgc1
                                      ls_approval.

      MOVE-CORRESPONDING ls_approval TO <lfs_alv>.
      <lfs_alv>-exebs = 'X' .
      <lfs_alv>-zxyzt = ls_approval-zxyzt .
      <lfs_alv>-state = icon_led_green.
      IF <lfs_alv>-zxyzt = 'P' .
        <lfs_alv>-message = '审批中，等待下一级审批！'.
      ELSEIF <lfs_alv>-zxyzt = 'A'.
        <lfs_alv>-message = '审批完成'.
      ENDIF.

      IF    <lfs_alv>-zxyzt = 'A' .
        ls_ztk_spdata-ztk_id = <lfs_alv>-ztk_id.
        ls_ztk_spdata-zxybstyp = <lfs_alv>-zxybstyp.
        ls_ztk_spdata-zbegin = <lfs_alv>-zbegin.
        ls_ztk_spdata-zend   = <lfs_alv>-zend.
        COLLECT ls_ztk_spdata INTO lt_ztk_spdata.
      ENDIF.

    ENDIF.

  ENDLOOP.

  COMMIT WORK AND WAIT.

  SORT lt_ztk_spdata BY ztk_id.

  LOOP AT lt_ztk_spdata INTO DATA(ls_ztk_sldata) .
    IF ls_ztk_sldata-zxybstyp = 'P'.
      PERFORM frm_pro_data_101_102(zbcs0002) USING ls_ztk_sldata-ztk_id.
      PERFORM frm_pro_data_bdp(zbcs0002)     USING ls_ztk_sldata-ztk_id
                                                   ls_ztk_sldata-zbegin
                                                   ls_ztk_sldata-zend.
    ELSE.
      PERFORM frm_pro_data_116_117(zbcs0002) USING ls_ztk_sldata-ztk_id.
    ENDIF.
    PERFORM frm_data_calculate(zbcs0002)   USING ls_ztk_sldata-ztk_id  ''.
  ENDLOOP.


ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_sptg_check_data  CHANGING    pv_error.


  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY sel = 'X' exebs = 'X'.
  IF sy-subrc = 0.
    MESSAGE '选择的数据包含已执行完成的数据,请检查' TYPE  'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.
  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY sel = 'X' zxyzt = 'A'.
  IF sy-subrc = 0.
    MESSAGE '选择的数据包含已审批完成的数据,请检查' TYPE  'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY  sel = 'X' zxyzt = 'D'.
  IF sy-subrc = 0.
    MESSAGE '该返利条款已作废，无法审通过' TYPE 'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CHECK_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LV_ERROR
*&---------------------------------------------------------------------*
FORM frm_spjj_check_data  CHANGING  pv_error .

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY sel = 'X' exebs = 'X'.
  IF sy-subrc = 0.
    MESSAGE '选择的数据包含已执行完成的数据,请检查' TYPE  'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY  sel = 'X' zxyzt = 'N'.
  IF sy-subrc = 0.

    MESSAGE '选择的返利条款未审批，无需审批拒绝' TYPE 'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY  sel = 'X' zxyzt = 'R'.
  IF sy-subrc = 0.
    MESSAGE '该返利条款已审批拒绝，无需审批拒绝' TYPE 'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

  READ TABLE gt_alv TRANSPORTING NO FIELDS WITH  KEY  sel = 'X' zxyzt = 'D'.
  IF sy-subrc = 0.
    MESSAGE '该返利条款已作废，无法审批拒绝' TYPE 'S' DISPLAY LIKE 'E'.
    pv_error = 'E'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_EXE_SPJJ
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_exe_spjj .


  DATA: lt_msglist TYPE scp1_general_errors,
        ls_msglist TYPE scp1_general_error.
  DATA:ls_approval TYPE zre10_ty_approval.
  DATA:lv_zlock      TYPE c.
  DATA:lv_sylsd      TYPE datum.

  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  PERFORM frm_get_zlock(zre0001) CHANGING lv_zlock IF FOUND. "ERP-17167-月结锁定期间---新月份返利的计算与分摊
  PERFORM frm_get_lasdy(zre0001) CHANGING lv_sylsd IF FOUND. "ERP-17167-月结锁定期间---新月份返利的计算与分摊

  LOOP AT gt_alv ASSIGNING FIELD-SYMBOL(<lfs_alv>) WHERE sel = 'X'.

    IF lv_zlock = 'Y' AND <lfs_alv>-zbegin < lv_sylsd .
      "ERP-17167-月结锁定期间---新月份返利的计算与分摊
      ls_msglist-msgv1 = '月结锁定期不可审批月结期间协议'.
      APPEND ls_msglist TO lt_msglist.
    ELSE.

      PERFORM frm_check_item_before_rles_c      USING  <lfs_alv>-zxy_id
                                             CHANGING  lt_msglist .
    ENDIF.

    IF lt_msglist[] IS INITIAL  .

      MOVE-CORRESPONDING <lfs_alv> TO ls_approval.
      PERFORM frm_get_kolnr   CHANGING   ls_approval
                                         lt_msglist.
    ENDIF.

    IF lt_msglist[] IS NOT INITIAL .
      LOOP AT  lt_msglist INTO ls_msglist .
        <lfs_alv>-message = <lfs_alv>-message && ls_msglist-msgv1.
      ENDLOOP.
      <lfs_alv>-state = icon_led_red.
    ELSE.
      ls_approval-zxyzt = 'R'.

      UPDATE zret0006    SET kolnr  = ls_approval-kolnr
                             zxyzt  = ls_approval-zxyzt
                       WHERE ztk_id = <lfs_alv>-ztk_id
                         AND zxy_id = <lfs_alv>-zxy_id .

      PERFORM frm_save_rles_log USING <lfs_alv>-ztk_id
                                      <lfs_alv>-zxy_id
                                      <lfs_alv>-zxyzt
                                      <lfs_alv>-frgc1
                                      ls_approval.

      MOVE-CORRESPONDING ls_approval TO <lfs_alv>.
      <lfs_alv>-exebs = 'X' .
      <lfs_alv>-zxyzt = ls_approval-zxyzt .
      <lfs_alv>-state = icon_led_green.
      <lfs_alv>-message = '审批拒绝完成'.

    ENDIF.

  ENDLOOP.
  COMMIT WORK.

ENDFORM.
*&---------------------------------------------------------------------*
*& FORM FRM_CHECK_BEFORE_RLES_C
*&---------------------------------------------------------------------*
*& TEXT
*&---------------------------------------------------------------------*
*&      --> GS_T06
*&      <-- LV_FLG_ERR
*&---------------------------------------------------------------------*
FORM frm_check_item_before_rles_c     USING pv_xy_id
                                   CHANGING pt_msglist TYPE scp1_general_errors.

  DATA:ls_msglist TYPE scp1_general_error.

  CLEAR:ls_msglist.
  ls_msglist-msgty = 'E'.
  ls_msglist-msgid = '00'.
  ls_msglist-msgno = '001'.

  SELECT  zxy_id INTO TABLE @DATA(lt_zxy_id) FROM zret0006 WHERE zxy_id = @pv_xy_id.

  LOOP AT lt_zxy_id INTO DATA(ls_xy_id).

    SELECT COUNT(*)
      FROM zret0006
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs = 'X'.
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
      FROM zret0016
      WHERE zxy_id = ls_xy_id-zxy_id
      AND   zjsbs NE '' .
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

    SELECT COUNT(*)
        FROM zret0018
        WHERE zxy_id = ls_xy_id-zxy_id
        AND zjsstate IN ('N','1','2','R').
    IF sy-subrc EQ 0.
      ls_msglist-msgv1 = '该返利条款下所属返利协议中存在有效结算单或结算单已兑付，无法审批拒绝。!'.
      APPEND ls_msglist TO pt_msglist.
    ENDIF.

  ENDLOOP.

  SORT pt_msglist  BY msgv1.
  DELETE ADJACENT DUPLICATES FROM pt_msglist COMPARING msgv1.

ENDFORM.