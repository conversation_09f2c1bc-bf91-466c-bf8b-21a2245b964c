*&---------------------------------------------------------------------*
*& 包含               ZRER0005F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Form FRM_INTIAL
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_intial .

*  LOOP AT SCREEN .
*    IF screen-name = 'S_DATUM-HIGH' .
*      screen-required = '1'.
*      MODIFY SCREEN.
*    ENDIF.
*  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_AUTHORITY_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_authority_check .


ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_VALIDATE_CHECK
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_validate_check .

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data .


  "获取数据
  SELECT *
    INTO CORRESPONDING FIELDS OF TABLE gt_data
    FROM zreta001   AS a
    LEFT JOIN t024 AS b ON a~ekgrp  = b~ekgrp
    LEFT JOIN t001 AS c ON a~zbukrs = c~bukrs
   WHERE zhtlx   IN s_zhtlx
     AND zbukrs  IN s_zbukrs
     AND zbptype IN s_bptype
     AND zbpcode IN s_bpcode
     AND a~ekgrp IN s_ekgrp
     AND zht_id  IN s_zht_id
     AND zhtyear IN s_htyear
     AND zht_txt IN s_ht_txt
     AND zbegin  IN s_zbegin
     AND zend    IN s_zend.

  IF sy-subrc <> 0 .
    MESSAGE '没有符合条件的数据' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.


  SELECT * INTO TABLE gt_dd07t FROM dd07t WHERE domname = 'ZRED_ZBPTYPE' .

ENDFORM.

*&---------------------------------------------------------------------*
*& Form FRM_PROCESS_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_process_data.
  LOOP AT gt_data ASSIGNING FIELD-SYMBOL(<lfs_data>)  .

    READ TABLE gt_dd07t INTO gs_dd07t WITH KEY domvalue_l =  <lfs_data>-zbptype.
    IF sy-subrc = 0.
      <lfs_data>-zbptypet =  gs_dd07t-ddtext.
    ENDIF.

    IF <lfs_data>-zbptype = 'S'.
      SELECT SINGLE name1 INTO <lfs_data>-zbpcodet   FROM lfa1 WHERE lifnr = <lfs_data>-zbpcode.
    ELSE.
*      SELECT SINGLE codeitemdesc INTO <lfs_data>-zbpcodet   FROM zmmt0345 WHERE codeitemid = <lfs_data>-zbpcode.

      SELECT SINGLE  name_org1 INTO <lfs_data>-zbpcodet  FROM but000 WHERE partner = <lfs_data>-zbpcode.
    ENDIF.
  ENDLOOP.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_SHOW_DATA
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_show_data .
  gs_layout-zebra      = 'X'.       "X:行项目带颜色标识
  gs_layout-sel_mode   = 'A'.       "A:显示alv的选择行按
  gs_layout-cwidth_opt = 'X'.

  PERFORM frm_set_fieldcat USING 'ZHTYEAR'      '合同签署年度' '' ''.
  PERFORM frm_set_fieldcat USING 'ZHT_ID'       '返利合同号码' '' ''.
  PERFORM frm_set_fieldcat USING 'ZHT_TXT'      '合同描述'     'ZRETA001' 'ZHT_TXT'.
  PERFORM frm_set_fieldcat USING 'ZHTLX'        '合同类型 '    'ZRETA001' 'ZHTLX'.
  PERFORM frm_set_fieldcat USING 'ZBUKRS'       '合同主体'     'ZRETA001' 'ZBUKRS'.
  PERFORM frm_set_fieldcat USING 'BUTXT'        '合同主体描述' '' ''.
  PERFORM frm_set_fieldcat USING 'ZBPTYPE'      '伙伴类型'     'ZRETA001' 'ZBPTYPE'.
  PERFORM frm_set_fieldcat USING 'ZBPTYPET'     '伙伴类型描述'    '' ''.
  PERFORM frm_set_fieldcat USING 'ZBPCODE'      '伙伴编码'     'ZRETA001' 'ZBPCODE'.
  PERFORM frm_set_fieldcat USING 'ZBPCODET'     '伙伴编码名称'     '' ''.
  PERFORM frm_set_fieldcat USING 'EKGRP'        '采购组'       ''   ''.
  PERFORM frm_set_fieldcat USING 'EKNAM'        '采购组描述'   'ZRETA001' 'EKNAM'.
  PERFORM frm_set_fieldcat USING 'ZBEGIN'       '开始日期'     'ZRETA001' 'ZBEGIN'.
  PERFORM frm_set_fieldcat USING 'ZEND'         '结束日期'     'ZRETA001' 'ZEND'.
  PERFORM frm_set_fieldcat USING 'ZHTID'        '关联合同号'    'ZRETA001' 'ZHTID'.
  PERFORM frm_set_fieldcat USING 'ZCNYGHT_ID'   '次年预估参考合同'   'ZRETA001' 'ZCNYGHT_ID'.
  PERFORM frm_set_fieldcat USING 'ZCJR'         '创建人'     'ZRETA001' 'ZCJR'.
  PERFORM frm_set_fieldcat USING 'ZCJRQ'         '创建日期'     'ZRETA001' 'ZCJRQ'.
  PERFORM frm_set_fieldcat USING 'ZCJSJ'         '创建时间'     'ZRETA001' 'ZCJSJ'.
  CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
    EXPORTING
      i_callback_program       = sy-repid
      is_layout_lvc            = gs_layout
      it_fieldcat_lvc          = gt_fieldcat[]
      i_callback_pf_status_set = 'FRM_SET_STATUS'
      i_callback_user_command  = 'FRM_USER_COMMAND'
      i_default                = 'X'
      i_save                   = 'U'
    TABLES
      t_outtab                 = gt_data[]
    EXCEPTIONS
      program_error            = 1
      OTHERS                   = 2.

  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  FRM_SET_FIELDCAT
*&---------------------------------------------------------------------*
*       设置Fieldcat
*----------------------------------------------------------------------*
*      -->P_1271   text
*      -->P_1272   text
*----------------------------------------------------------------------*
FORM frm_set_fieldcat USING i_fieldname
                             i_seltext
                            i_ref_table
                            i_ref_field.

  DATA:ls_fieldcat TYPE lvc_s_fcat.

  ls_fieldcat-fieldname = i_fieldname.
  ls_fieldcat-scrtext_l = i_seltext.
  ls_fieldcat-ref_field = i_ref_field.
  ls_fieldcat-ref_table = i_ref_table.


  CASE i_fieldname .
    WHEN 'ZHT_ID' .
      ls_fieldcat-hotspot   = 'X'.
    WHEN 'ZHTLX' . ls_fieldcat-convexit  = 'ZREHT'.
    WHEN 'ZBPCODE' .
      ls_fieldcat-convexit  = 'ZHTLX'.
      ls_fieldcat-convexit = 'ALPHA'.
    WHEN OTHERS.
  ENDCASE.

  APPEND ls_fieldcat TO gt_fieldcat.

ENDFORM.
*&---------------------------------------------------------------------*
*&      Form  FRM_SET_STATUS
*&---------------------------------------------------------------------*
*       设置Status
*----------------------------------------------------------------------*
*      -->IT_EXTAB   text
*----------------------------------------------------------------------*
FORM frm_set_status USING it_extab TYPE slis_t_extab.
  SET PF-STATUS 'S1000'.
ENDFORM.
*&---------------------------------------------------------------------*
*&      FORM  USER_COMMAND_FORM
*&---------------------------------------------------------------------*
*       TEXT
*----------------------------------------------------------------------*
*      -->R_UCOMM      TEXT
*      -->RS_SELFIELD  TEXT
*----------------------------------------------------------------------*
FORM frm_user_command USING rv_ucomm    TYPE sy-ucomm
                            rs_selfield TYPE slis_selfield.


  CASE rv_ucomm.
    WHEN '&IC1'.
      READ TABLE gt_data INTO DATA(ls_data) INDEX rs_selfield-tabindex.
      CHECK sy-subrc EQ 0 AND rs_selfield-value IS NOT INITIAL.

      SET PARAMETER ID 'ZHT_ID' FIELD ls_data-zht_id.
      CALL TRANSACTION 'ZRED0040C' AND SKIP FIRST SCREEN.
      SET PARAMETER ID 'ZHT_ID' FIELD ''.

    WHEN OTHERS.
  ENDCASE.

ENDFORM.                    "USER_COMMAND_FORM