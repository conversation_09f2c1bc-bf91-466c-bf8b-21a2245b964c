*&---------------------------------------------------------------------*
*& Report ZRER0005
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
REPORT zrer0103.
*----------------------------------------------------------------------*
* <1.1-声明 包含程序> Include Programs                                  *
*----------------------------------------------------------------------*
***********《所有全局变量、类,选择屏幕 都写在  DEFINE include 程序中》*********
INCLUDE ZRER0103TOP.
INCLUDE ZRER0103SEL.
INCLUDE ZRER0103F01.


*----------------------------------------------------------------------*
* <2.2-选择屏幕事件>                                                    *
* Events That Occur While The Selection Screen Is Bing Processed       *
*----------------------------------------------------------------------*
INITIALIZATION.
  "初始化处理
  PERFORM frm_intial.
* 所有选择屏幕数据传送到程序中之后触发的事件
AT SELECTION-SCREEN.
  "权限检查
  PERFORM frm_authority_check.

*选择屏幕PBO事件，在显示选择屏幕前触发
AT SELECTION-SCREEN OUTPUT.
*----------------------------------------------------------------------*
* <2.3-在选择屏幕被处理后触发的事件,程序默认的开始事件>                  *
* Event Occurs After The Selection Screen Has Been Processed           *
*----------------------------------------------------------------------*
START-OF-SELECTION.
  "输入数据合法性检查
  PERFORM frm_validate_check.
  "获取数据
  PERFORM frm_get_data.
  "处理数据

  PERFORM frm_process_data.


*----------------------------------------------------------------------*
* <2.4-最后被触发的事件>                                                *
* The Last Of The Events Called By The Runtime Environment To Occur    *
*----------------------------------------------------------------------*
END-OF-SELECTION.
  "展示数据
  IF gt_data[] IS NOT INITIAL.
    SORT  gt_data BY zht_id .
    PERFORM frm_show_data.
  ELSE.
    MESSAGE '未查询到明细数据' TYPE 'S' DISPLAY LIKE 'E'.
    LEAVE LIST-PROCESSING.
  ENDIF.